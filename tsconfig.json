{
  "compilerOptions": {
    "target": "ESNext",
    "downlevelIteration": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": false,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"],
      "@core/*": ["./src/@core/*"],
      "@layouts/*": ["./src/@layouts/*"],
      "@menu/*": ["./src/@menu/*"],
      "@assets/*": ["./src/assets/*"],
      "@components/*": ["./src/components/*"],
      "@configs/*": ["./src/configs/*"],
      "@views/*": ["./src/views/*"],
    }
  },
  "include": [
    "next.config.ts",
    "tailwind.config.ts",
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts", "public/sw.js",
  ],
  "exclude": ["node_modules"]
}
