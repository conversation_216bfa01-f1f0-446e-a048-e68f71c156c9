{"name": "one-society", "version": "0.0.1", "license": "Commercial", "private": true, "scripts": {"dev": "next dev --experimental-https --hostname dev.cubeone.in", "prebuild": "npx tsx scripts/toAJV.ts && jest", "build": "next build", "start": "next start -H 0.0.0.0 --port 3001", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "postinstall": "npx tsx src/assets/iconify-icons/bundle-icons-css.ts", "test": "jest --watch"}, "dependencies": {"@babel/runtime-corejs2": "^7.27.1", "@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@floating-ui/react": "0.27.8", "@hookform/resolvers": "5.0.1", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@mui/icons-material": "^6.4.11", "@mui/lab": "6.0.0-beta.19", "@mui/material": "^6.4.11", "@mui/material-nextjs": "6.2.1", "@mui/system": "^6.4.11", "@mui/x-charts": "7.29.1", "@mui/x-data-grid": "7.29.3", "@mui/x-date-pickers": "7.29.3", "@mui/x-tree-view": "7.29.1", "@next/mdx": "^15.1.8", "@rjsf/core": "^6.0.0-beta.6", "@rjsf/mui": "^6.0.0-beta.6", "@rjsf/utils": "^6.0.0-beta.6", "@rjsf/validator-ajv8": "^6.0.0-beta.6", "@sentry/nextjs": "8", "@tanstack/react-query": "^5.75.7", "@types/mdx": "^2.0.13", "@types/react-window": "^1.8.8", "axios": "^1.9.0", "classnames": "2.5.1", "cmdk": "1.1.1", "consola": "^3.4.2", "dayjs": "^1.11.13", "fs-extra": "^11.3.0", "html-react-parser": "^5.2.4", "jose": "^6.0.11", "json-rules-engine": "^7.3.1", "json-rules-engine-simplified": "^0.2.0", "jspdf": "^3.0.1", "keen-slider": "6.8.6", "leaflet": "^1.9.4", "material-react-table": "^3.2.1", "mathjs": "^14.4.0", "next": "15.3.2", "next-auth": "4.24.11", "next-nprogress-bar": "^2.4.7", "react": "^19.1.0", "react-colorful": "5.6.1", "react-dom": "^19.1.0", "react-hook-form": "7.56.4", "react-hot-toast": "^2.5.2", "react-leaflet": "^5.0.0", "react-perfect-scrollbar": "1.5.8", "react-toastify": "11.0.5", "react-use": "17.6.0", "react-window": "^1.8.11", "rjsf-conditionals": "^1.5.0", "server-only": "0.0.1", "yup": "^1.6.1"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.26.0", "@iconify/json": "2.2.341", "@iconify/react": "^6.0.0", "@iconify/tools": "4.1.2", "@iconify/types": "2.0.0", "@iconify/utils": "2.3.0", "@next/eslint-plugin-next": "^15.3.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.17", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@typescript-eslint/eslint-plugin": "8.32.0", "@typescript-eslint/parser": "8.32.1", "autoprefixer": "10.4.21", "babel-plugin-react-compiler": "^19.1.0-rc.2", "cli-progress": "^3.12.0", "eslint": "9.26.0", "eslint-config-next": "15.3.2", "eslint-config-prettier": "10.1.5", "eslint-import-resolver-typescript": "4.3.5", "eslint-plugin-import": "2.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsonc": "^2.0.0", "minimist": "^1.2.8", "p-limit": "^6.2.0", "postcss": "8.5.3", "postcss-styled-syntax": "0.7.1", "prettier": "3.5.3", "rehype-stringify": "^10.0.1", "stylelint": "16.19.1", "stylelint-use-logical-spec": "5.0.1", "stylis": "4.3.6", "stylis-plugin-rtl": "2.1.1", "tailwindcss": "3.4.17", "tailwindcss-logical": "3.0.1", "ts-jest": "^29.3.2", "tsx": "4.19.4", "typescript": "5.8.3"}, "resolutions": {"rimraf": "^5.0.7"}, "overrides": {"rimraf": "^5.0.7"}}