// import fs from 'fs';
// import path from 'path';

// import verticalMenuData from "@/data/navigation/verticalMenuData";
// import { deduplicateVerticalMenu, getPaths, findItemWithBreadcrumb } from "@/utils/menu";

// async function handler() {
//   // Get the menu data for both society and gate modes
//   const societyData = verticalMenuData('society');
//   const gateData = verticalMenuData('gate');

//   // Combine and deduplicate
//   const allMenus = [...societyData, ...gateData];
//   const uniqueMenus = deduplicateVerticalMenu(allMenus);
//   const paths = getPaths(uniqueMenus);

//   // Base output directory
// //   /home/<USER>/society-app/src/app/admin/[mode]/@modal/(.)help/vendorbill/vendorBill/page.mdx
//   const baseDir = path.join(process.cwd(), 'src', 'app', 'admin', '[mode]', '@modal', '(.)help');

//   for (const item of paths) {
//     const { mode, slug } = item;

//     // Compute directory: src/data/content/<mode>/...slug
//     const outDir = path.join(baseDir, ...slug);

//     fs.mkdirSync(outDir, { recursive: true });

//     // Retrieve pageData for this slug
//     const breadcrumbSlug = ['admin', mode, ...slug];
//     const { pageData } = findItemWithBreadcrumb(uniqueMenus, breadcrumbSlug) as any

//     // Fallback if no pageData
//     const title = pageData?.label || "";
//     const description = pageData?.description;

//     // Generate MDX content: title as h1, and description as h3 only if it exists
//     let mdxContent = `# ${title}`;

//     if (description) {
//       mdxContent += `\n\n### ${description}`;
//     }
    
//     mdxContent += `\n`;

//     // Write index.mdx
//     fs.writeFileSync(path.join(outDir, 'page.mdx'), mdxContent, 'utf8');
//     console.log(`Generated: ${path.join(outDir, 'page.mdx')}`);
//   }
// }

// // handler().catch(console.error);

console.error("Dangerous! Do not run!")