#!/usr/bin/env tsx

import fs from "fs/promises";
import os from "os";
import path from "path";

import { jsonc } from "jsonc";
import cliProgress from "cli-progress";
import pLimit from "p-limit";

import { generateSchema } from "@/utils/generateSchema";

// -----------------------------------------------------------------------------
// Types for strong schema validation
// -----------------------------------------------------------------------------
interface SchemaFlowData {
  title?: string;
  children: Record<string, any>;
  required?: string[];
  order?: string[];
  uiSchema?: Record<string, any>;
}

interface FullSchemaData {
  schema: {
    flow: SchemaFlowData;
  };
  [key: string]: any;
}

// -----------------------------------------------------------------------------
// Utility: extract dynamic segment names ([foo] => foo)
// -----------------------------------------------------------------------------
function getDynamicParts(filePath: string): string[] {
  const matches: string[] = [];

  for (const match of filePath.matchAll(/\[([^\]]+)\]/g)) {
    matches.push(match[1]);
  }

  return matches;
}

// -----------------------------------------------------------------------------
// ANSI color helper
// -----------------------------------------------------------------------------
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  cyan: "\x1b[36m",
};

function colorize(text: string, color: keyof typeof colors): string {
  return `${colors[color]}${text}${colors.reset}`;
}

// -----------------------------------------------------------------------------
// Process a single schema file: parse, transform, write
// -----------------------------------------------------------------------------
async function processFile(
  filePath: string,
  rootDir: string,
  outRoot: string
): Promise<string | null> {
  try {
    // Read & parse JSON or JSONC
    let data: FullSchemaData;

    if (filePath.endsWith(".jsonc")) {
      const parsed = await jsonc.read(filePath);

      if (!parsed) throw new Error("JSONC parse failure");
      data = parsed as FullSchemaData;
    } else {
      const raw = await fs.readFile(filePath, "utf-8");

      data = JSON.parse(raw) as FullSchemaData;
    }

    // Ensure children object exists
    data.schema.flow.children = data.schema.flow.children || {};

    // Mutate children for dynamic parts safely
    const dynamicKeys = getDynamicParts(filePath);

    for (const key of dynamicKeys) {
      const existing = data.schema.flow.children[key] ?? {};

      data.schema.flow.children[key] = {
        hidden: true,
        type: ["number", "string"],
        required: true,
        ...existing,
      };
    }

    // Generate schema + uiSchema and rules
    let schemaProps: any, uiProps: any, rules: any[];

    try {
      [schemaProps, uiProps, rules] = generateSchema(data.schema.flow.children);

      if (!Array.isArray([schemaProps, uiProps, rules]) || rules === undefined) {
        throw new Error('generateSchema returned unexpected tuple');
      }
    } catch (err: any) {
      throw new Error(`generateSchema failed: ${err.message}`);
    }

    // Apply generated schema
    data.schema.flow.children = {
      // use .properties from the returned schema object
      ...(schemaProps.properties ?? {}),
      allowUndefinedFacts: true,
      title: data.schema.flow.title || "",
      required: [
        ...(data.schema.flow.required ?? []),
        ...(schemaProps.required ?? []),
      ],
    };
    data.schema.flow.uiSchema = {
      ...uiProps,
      "ui:order": data.schema.flow.order ?? ["*"],
    };

    // Compute output directory and path and path
    const relativePath = path.relative(rootDir, filePath);
    const ext = path.extname(relativePath);
    const withoutExt = relativePath.slice(0, -ext.length);
    const baseName = path.basename(withoutExt);

    const outDir =
      baseName === "index"
        ? path.join(outRoot, path.dirname(withoutExt))
        : path.join(outRoot, withoutExt);

    await fs.mkdir(outDir, { recursive: true });
    const outPath = path.join(outDir, "index.json");

    // Write pretty-printed JSON
    await fs.writeFile(outPath, JSON.stringify(data, null, 2), "utf-8");

    return null;
  } catch (err: any) {
    console.log(err)

    return `${filePath}: ${err.message}`;
  }
}

// -----------------------------------------------------------------------------
// Recursively collect JSON/JSONC files under rootDir
// -----------------------------------------------------------------------------
async function collectFiles(dir: string, rootDir: string): Promise<string[]> {
  const entries = await fs.readdir(dir, { withFileTypes: true });
  const files: string[] = [];

  for (const ent of entries) {
    const fullPath = path.resolve(dir, ent.name);

    // Guard: must stay inside rootDir
    if (path.relative(rootDir, fullPath).startsWith(`..${path.sep}`)) continue;

    if (ent.isDirectory()) {
      files.push(...(await collectFiles(fullPath, rootDir)));
    } else if (ent.isFile() && /\.(jsonc|json)$/.test(ent.name)) {
      files.push(fullPath);
    }
  }

  return files;
}

// -----------------------------------------------------------------------------
// Main: runs before npm build, no CLI args
// -----------------------------------------------------------------------------
async function main() {
  const rootDir = path.resolve(process.cwd(), "src/data/schema");
  const outDir = path.resolve(process.cwd(), "src/data/modified");

  // Determine concurrency from env or CPU count
  const concurrency =
    parseInt(process.env.CONCURRENCY || "", 10) || Math.max(2, os.cpus().length);

  // Validate source directory
  try {
    await fs.access(rootDir);
  } catch {
    console.error(colorize(`Source not found: ${rootDir}`, "red"));
    process.exit(1);
  }

  // Clean & recreate output directory
  await fs.rm(outDir, { recursive: true, force: true });
  await fs.mkdir(outDir, { recursive: true });

  // Gather files
  const files = await collectFiles(rootDir, rootDir);

  if (files.length === 0) {
    console.warn(colorize("No JSON/JSONC files found.", "yellow"));

    return;
  }

  // Setup concurrency and progress bar
  const limit = pLimit(concurrency);

  const progress = new cliProgress.SingleBar(
    { format: `Processing [{bar}] {percentage}% | {value}/{total}` },
    cliProgress.Presets.shades_classic
  );

  progress.start(files.length, 0);
  const errors: string[] = [];

  await Promise.all(
    files.map((file) =>
      limit(async () => {
        const err = await processFile(file, rootDir, outDir);

        if (err) errors.push(err);
        progress.increment();
      })
    )
  );

  progress.stop();

  // Summary
  const total = files.length;
  const failed = errors.length;
  const succeeded = total - failed;

  console.log(
    `\n${colorize("Stats:", "cyan")} Total: ${total}, ` +
    `${colorize("Success", "green")} ${succeeded}, ` +
    `${colorize("Failed", "red")} ${failed}`
  );

  if (failed > 0) {
    console.error(colorize("\nFailed files:", "red"));
    for (const e of errors) console.error(` - ${colorize(e, "yellow")}`);
    process.exit(1);
  }

  console.log(
    colorize(`All files processed successfully. Output: ${outDir}`, "green")
  );
}

// Run before npm build
main().catch((err) => {
  console.error(colorize(`Fatal error: ${err.message}`, "red"));
  process.exit(1);
});
