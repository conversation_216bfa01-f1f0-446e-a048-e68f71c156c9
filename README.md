# One-Society

## 🚀 Features

- **📝 Dynamic Form Generation** – Supports form creation with [`R<PERSON><PERSON>`](https://github.com/rjsf-team/react-jsonschema-form), [`RJSF-Conditionals`](https://github.com/rjsf-team/react-jsonschema-form), and [`JSON Rules Engine`](https://github.com/CacheControl/json-rules-engine) for complex validation and conditional logic.
- **📊 Material React Table Integration** – Dynamically generates tables with [`Material React Table v3`](https://www.material-react-table.com/), enabling advanced data manipulation.
- **⚡ Performance Optimizations** – Implements lazy loading, optimized renders, and efficient API communication.
- **🔍 Validation & Forms** – Utilizes [`react-hook-form`](https://react-hook-form.com/) and [`yup`](https://github.com/jquense/yup) for robust form validation.
- **📅 Date Pickers** – Integrated with [`@mui/x-date-pickers`](https://mui.com/x/react-date-pickers/).
- **🔐 Authentication** – Seamless authentication using [`next-auth`](https://next-auth.js.org/).
- **📡 State Management** – Powered by [`@tanstack/react-query`](https://tanstack.com/query) for efficient data fetching and caching.
- **📑 Multi-Step Forms** – Supports step-based forms using dynamic rendering.

---

## 📜 Scripts

```sh
npm run dev        # Start the development server
npm run build      # Build the project for production
npm run start      # Start the production server
npm run lint       # Run ESLint to check code quality
npm run format     # Format the code using Prettier
```

---

## 📦 Key Dependencies

| Category           | Libraries                                                                                                                                                                                                                                                                                |
| ------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Form Handling**  | [`@rjsf/core`](https://github.com/rjsf-team/react-jsonschema-form), [`@rjsf/mui`](https://github.com/rjsf-team/react-jsonschema-form), [`@rjsf/utils`](https://github.com/rjsf-team/react-jsonschema-form), [`@rjsf/validator-ajv8`](https://github.com/rjsf-team/react-jsonschema-form) |
| **Tables**         | [`material-react-table v3`](https://www.material-react-table.com/)                                                                                                                                                                                                                       |
| **Validation**     | [`react-hook-form`](https://react-hook-form.com/), [`yup`](https://github.com/jquense/yup)                                                                                                                                                                                               |
| **Utilities**      | [`dayjs`](https://day.js.org/), [`axios`](https://axios-http.com/), [`classnames`](https://www.npmjs.com/package/classnames), [`json-rules-engine`](https://github.com/CacheControl/json-rules-engine), [`mathjs`](https://mathjs.org/)                                                  |
| **Date Pickers**   | [`@mui/x-date-pickers`](https://mui.com/x/react-date-pickers/)                                                                                                                                                                                                                           |
| **Authentication** | [`next-auth`](https://next-auth.js.org/)                                                                                                                                                                                                                                                 |

---

## 🛠️ Usage

### 🏗️ Creating Forms

Forms are dynamically generated using `RJSF`, enhanced with conditional logic through `rjsf-conditionals` and `json-rules-engine`.

```json
{
  "title": "User Form",
  "type": "object",
  "properties": {
    "name": { "type": "string", "title": "Name" },
    "age": { "type": "number", "title": "Age" }
  }
}
```

### 🔄 Conditional Logic

Conditional rules can be applied using `json-rules-engine`.

```json
{
  "conditions": {
    "all": [{ "fact": "age", "operator": "greaterThan", "value": 18 }]
  },
  "event": {
    "type": "showField",
    "params": { "field": "driving_license" }
  }
}
```

### 📋 Data Tables

Tables are dynamically generated using [`Material React Table v3`](https://www.material-react-table.com/) with customizable columns and actions.

```js
const columns = [
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'age', header: 'Age' }
]
```

### 🔄 Multi-Step Forms

Forms can be rendered in multiple steps dynamically, allowing for better user experience and data segmentation.

```js
const steps = [
  { title: 'Step 1', formSchema: step1Schema },
  { title: 'Step 2', formSchema: step2Schema }
]
```

---

## 🎨 Custom Widgets & Templates

### 📌 Custom Widgets

The form supports the following custom widgets:

- **Date Picker** – Integrated with `@mui/x-date-pickers`.
- **File Upload** – Custom implementation for handling file uploads.
- **Rich Text Editor** – Integrated with a WYSIWYG editor.
- **Checkbox Group** – Supports multiple checkbox selections.

### 🏗️ Custom Templates

- **Field Template** – Allows customization of field layouts.
- **Object Field Template** – Custom rendering for object-type fields.
- **Array Field Template** – Custom rendering for array-type fields.

### 🔄 Rule Examples & Possibilities

#### 📌 Example 1: Show a Field Based on Another Field’s Value

```json
{
  "conditions": {
    "all": [{ "fact": "employment_status", "operator": "equal", "value": "employed" }]
  },
  "event": {
    "type": "showField",
    "params": { "field": "company_name" }
  }
}
```

#### 📌 Example 2: Enable a Field When a Checkbox is Checked

```json
{
  "conditions": {
    "all": [{ "fact": "is_married", "operator": "equal", "value": true }]
  },
  "event": {
    "type": "enableField",
    "params": { "field": "spouse_name" }
  }
}
```

#### 🔄 Rule Possibilities

Rules can:

- Show or hide fields dynamically.
- Enable or disable input fields.
- Set default values based on conditions.
- Validate inputs dynamically.
- Execute custom actions like calculations or API calls.
- Remove fields based on conditions.
- Override schema properties dynamically.
- Append UI-specific attributes (errors, tooltips, etc.).
- Perform calculations based on other fields.
- Apply default rules from `rjsf-conditionals` and `json-rules-engine`, such as `schemaOverride`, `uiOverride`, `calculate`, and `update`.

This allows highly flexible and interactive forms tailored to specific needs.

---

## 🔄 Rule Examples & Possibilities

### 📌 Core Features

- **Calculations**: Uses [`mathjs`](https://mathjs.org/) for evaluating complex expressions.
- **Path Queries**: Supports JSON Path syntax via [`jsonpath-plus`](https://github.com/JSONPath-Plus/JSONPath-plus) in `rjsf-conditionals`.

---

### 📋 Rule Examples

#### 1. **Show or Hide Fields Dynamically**

```json
{
  "conditions": {
    "all": [{ "fact": "employment_status", "operator": "equal", "value": "employed" }]
  },
  "event": {
    "type": "showField",
    "params": { "field": "company_name" }
  }
}
```

#### 2. **Enable/Disable Input Fields**

```json
{
  "conditions": {
    "all": [{ "fact": "is_married", "operator": "equal", "value": true }]
  },
  "event": {
    "type": "enableField",
    "params": { "field": "spouse_name" }
  }
}
```

#### 3. **Set Default Values**

```json
{
  "conditions": {
    "all": [{ "fact": "country", "operator": "equal", "value": "USA" }]
  },
  "event": {
    "type": "update",
    "params": { "currency": "USD" }
  }
}
```

#### 4. **Dynamic Validation**

```json
{
  "conditions": {
    "all": [{ "fact": "age", "operator": "lessThan", "value": 18 }]
  },
  "event": {
    "type": "uiAppend",
    "params": {
      "age": { "ui:options": { "error": "You must be at least 18 years old." } }
    }
  }
}
```

#### 5. **Execute Calculations with `mathjs`**

```json
{
  "conditions": {
    "all": [{ "fact": "billable_amount", "operator": "isNumber" }]
  },
  "event": {
    "type": "calculate",
    "params": {
      "__exp": "billable_amount * payment_mode / 100", // Uses mathjs syntax
      "__field": "bill",
      "billable_amount": { "fact": "billable_amount" },
      "payment_mode": { "fact": "payment_mode" }
    }
  }
}
```

#### 6. **Remove Fields**

```json
{
  "conditions": {
    "all": [{ "fact": "vendor_bill_type_purchase", "operator": "equal", "value": "cash" }]
  },
  "event": {
    "type": "remove",
    "params": { "field": ["bank_ledger", "vendor_bill_payment_cheque_number"] }
  }
}
```

#### 7. **Schema Overrides**

```json
{
  "conditions": {
    "all": [{ "fact": "advance_amount", "operator": "isNumber" }]
  },
  "event": {
    "type": "schemaOverride",
    "params": {
      "vendor_bill_advance_amount.maximum": { "fact": "advance_amount" }
    }
  }
}
```

#### 8. **Dynamic UI Attributes**

```json
{
  "conditions": {
    "all": [{ "fact": "vendor_bill_advance_amount", "operator": "greaterThan", "value": { "fact": "advance_amount" } }]
  },
  "event": {
    "type": "uiAppend",
    "params": {
      "vendor_bill_advance_amount": {
        "ui:options": { "error": "Amount exceeds adjustable balance." }
      }
    }
  }
}
```

#### 9. **UI Property Overrides**

```json
{
  "conditions": {
    "all": [{ "fact": "vendor_bill_type_purchase", "operator": "equal", "value": "cheque" }]
  },
  "event": {
    "type": "uiOverride",
    "params": {
      "vendor_bill_payment_cheque_number": { "ui:title": "Cheque Number" }
    }
  }
}
```

#### 10. **Path-Based Calculations**

```json
{
  "conditions": {
    "all": [{ "fact": "sequences", "operator": "notEqual", "value": null }]
  },
  "event": {
    "type": "calculate",
    "params": {
      "__exp": "sum(sequences)", // Uses jsonpath-plus path: $.data[*].due_amount
      "__field": "total_due",
      "sequences": { "fact": "sequences", "path": "$.data[*].due_amount" }
    }
  }
}
```

#### 11. **Tooltip Templating**

```json
{
  "conditions": {
    "all": [{ "fact": "cash_balance", "operator": "isNumber" }]
  },
  "event": {
    "type": "tooltip",
    "params": {
      "__title": "Balance: [[color:{{isSuccess}}]]{{cash_balance}}[[color]]",
      "__field": ["vendor_bill_type_purchase"],
      "cash_balance": { "fact": "cash_balance" },
      "isSuccess": { "fact": "cash_balance", "operator": "truthy" }
    }
  }
}
```

#### 12. **Require Fields Conditionally**

```json
{
  "conditions": {
    "all": [{ "fact": "show_writeoff", "operator": "equal", "value": true }]
  },
  "event": {
    "type": "require",
    "params": { "field": ["writeoff_amount"] }
  }
}
```

---

### 🔧 Technical Notes

- **Expressions**: Calculations use [`mathjs`](https://mathjs.org/) syntax (e.g., `sum()`, `count()`).
- **Paths**: Field paths in conditions/actions use [`jsonpath-plus`](https://github.com/JSONPath-Plus/JSONPath-plus) syntax (e.g., `$.data[*].due_amount`).

---

### 🔧 Theme Used and Theme Demos

[Materialize](https://demos.pixinvent.com/materialize-nextjs-admin-template/documentation/)

---

### 📂 Folder Structure and Important Files

```js
📦src
┣ 📂@core //! Do not touch // template theme files
┣ 📂@layouts
┃ ┣ 📂components
┃ ┃ ┗ 📂vertical
┃ ┃ ┃ ┣ 📜Footer.tsx
┃ ┃ ┃ ┣ 📜LayoutContent.tsx
┃ ┃ ┃ ┗ 📜Navbar.tsx
┃ ┣ 📂styles
┃ ┃ ┣ 📂shared
┃ ┃ ┃ ┗ 📜StyledMain.tsx
┃ ┃ ┗ 📂vertical
┃ ┃ ┃ ┣ 📜StyledContentWrapper.tsx
┃ ┃ ┃ ┣ 📜StyledFooter.tsx
┃ ┃ ┃ ┗ 📜StyledHeader.tsx
┃ ┣ 📂utils
┃ ┃ ┗ 📜layoutClasses.ts
┃ ┣ 📜BlankLayout.tsx // use if sidebar should be hidden
┃ ┣ 📜LayoutWrapper.tsx
┃ ┗ 📜VerticalLayout.tsx
┣ 📂@menu
┃ ┣ 📂components
┃ ┃ ┣ 📂vertical-menu
┃ ┃ ┃ ┣ 📜Menu.tsx
┃ ┃ ┃ ┣ 📜MenuButton.tsx
┃ ┃ ┃ ┣ 📜MenuItem.tsx
┃ ┃ ┃ ┣ 📜MenuSection.tsx
┃ ┃ ┃ ┣ 📜NavCollapseIcons.tsx
┃ ┃ ┃ ┣ 📜NavHeader.tsx
┃ ┃ ┃ ┣ 📜SubMenu.tsx
┃ ┃ ┃ ┣ 📜SubMenuContent.tsx
┃ ┃ ┃ ┗ 📜VerticalNav.tsx
┃ ┃ ┗ 📜RouterLink.tsx
┃ ┣ 📂contexts
┃ ┃ ┗ 📜verticalNavContext.tsx
┃ ┣ 📂hooks
┃ ┃ ┣ 📜useMediaQuery.tsx
┃ ┃ ┣ 📜useVerticalMenu.tsx
┃ ┃ ┗ 📜useVerticalNav.tsx
┃ ┣ 📂styles
┃ ┃ ┣ 📂vertical
┃ ┃ ┃ ┣ 📜StyledVerticalMenu.tsx
┃ ┃ ┃ ┣ 📜StyledVerticalMenuItem.tsx
┃ ┃ ┃ ┣ 📜StyledVerticalMenuSection.tsx
┃ ┃ ┃ ┣ 📜StyledVerticalNav.tsx
┃ ┃ ┃ ┣ 📜StyledVerticalNavBgColorContainer.tsx
┃ ┃ ┃ ┣ 📜StyledVerticalNavContainer.tsx
┃ ┃ ┃ ┣ 📜StyledVerticalNavExpandIcon.tsx
┃ ┃ ┃ ┗ 📜verticalNavBgImage.module.css
┃ ┃ ┣ 📜StyledBackdrop.tsx
┃ ┃ ┣ 📜StyledMenuIcon.tsx
┃ ┃ ┣ 📜StyledMenuLabel.tsx
┃ ┃ ┣ 📜StyledMenuPrefix.tsx
┃ ┃ ┣ 📜StyledMenuSectionLabel.tsx
┃ ┃ ┣ 📜StyledMenuSuffix.tsx
┃ ┃ ┣ 📜StyledSubMenuContent.tsx
┃ ┃ ┗ 📜styles.module.css
┃ ┣ 📂utils
┃ ┃ ┣ 📜menuClasses.ts
┃ ┃ ┣ 📜menuUtils.tsx
┃ ┃ ┗ 📜renderValue.tsx
┃ ┣ 📂vertical-menu
┃ ┃ ┗ 📜index.tsx
┃ ┣ 📜defaultConfigs.ts
┃ ┗ 📜types.ts
┣ 📂app
┃ ┣ 📂(blank-layout-pages)
┃ ┃ ┣ 📂(guest-only)
┃ ┃ ┃ ┣ 📂login
┃ ┃ ┃ ┃ ┗ 📜page.tsx
┃ ┃ ┃ ┗ 📜layout.tsx
┃ ┃ ┣ 📂(home)
┃ ┃ ┃ ┣ 📜layout.tsx
┃ ┃ ┃ ┗ 📜page.tsx
┃ ┃ ┣ 📂admin
┃ ┃ ┃ ┗ 📂select
┃ ┃ ┃ ┃ ┗ 📜page.tsx // page for selecting company
┃ ┃ ┣ 📂pages
┃ ┃ ┃ ┗ 📂misc
┃ ┃ ┃ ┃ ┣ 📂401-not-authorized
┃ ┃ ┃ ┃ ┃ ┗ 📜page.tsx
┃ ┃ ┃ ┃ ┣ 📂404-not-found
┃ ┃ ┃ ┃ ┃ ┗ 📜page.tsx
┃ ┃ ┃ ┃ ┣ 📂coming-soon
┃ ┃ ┃ ┃ ┃ ┗ 📜page.tsx
┃ ┃ ┃ ┃ ┗ 📂under-maintenance
┃ ┃ ┃ ┃ ┃ ┗ 📜page.tsx
┃ ┃ ┗ 📜layout.tsx
┃ ┣ 📂admin
┃ ┃ ┣ 📂@modal
┃ ┃ ┃ ┣ 📂(.)form
┃ ┃ ┃ ┃ ┗ 📂[...form]
┃ ┃ ┃ ┃ ┃ ┗ 📜page.tsx // file generating forms -> schema
┃ ┃ ┃ ┣ 📂(.)select
┃ ┃ ┃ ┃ ┗ 📜page.tsx // company select popup
┃ ┃ ┃ ┗ 📜default.tsx
┃ ┃ ┣ 📂[...slug]
┃ ┃ ┃ ┗ 📜page.tsx // includes pageTypeScreen, creating pages for form, table, cards and everything
┃ ┃ ┣ 📜default.tsx
┃ ┃ ┗ 📜layout.tsx
┃ ┣ 📂api
┃ ┃ ┣ 📂auth
┃ ┃ ┃ ┗ 📂[...nextauth]
┃ ┃ ┃ ┃ ┗ 📜route.ts // Next auth code inside
┃ ┃ ┗ 📂schema
┃ ┃ ┃ ┗ 📜route.ts
┃ ┣ 📂not-found
┃ ┃ ┗ 📜page.tsx
┃ ┣ 📂server
┃ ┃ ┣ 📜actions.ts
┃ ┃ ┣ 📜cookie.ts
┃ ┃ ┗ 📜societySelect.ts
┃ ┣ 📜globals.css
┃ ┗ 📜layout.tsx
┣ 📂components
┃ ┣ 📂DynamicPopover
┃ ┃ ┣ 📂Types
┃ ┃ ┃ ┗ 📜types.ts
┃ ┃ ┣ 📂data
┃ ┃ ┃ ┗ 📜popoverContent.ts
┃ ┃ ┣ 📜DispalyInfo.tsx
┃ ┃ ┗ 📜index.tsx
┃ ┣ 📂HtmlParse
┃ ┃ ┗ 📜index.tsx
┃ ┣ 📂SnackBar
┃ ┃ ┗ 📜CookieConsent.tsx
┃ ┣ 📂Tabs
┃ ┃ ┗ 📜index.tsx
┃ ┣ 📂card
┃ ┃ ┣ 📜DynamicCard.tsx
┃ ┃ ┣ 📜HelpNoteCard.tsx
┃ ┃ ┗ 📜PreviewRuleCard.tsx
┃ ┣ 📂chip
┃ ┃ ┗ 📜StatusChip.tsx
┃ ┣ 📂commonPageStructure
┃ ┃ ┣ 📜ButtonsClient.tsx
┃ ┃ ┣ 📜ComponentRenderer.tsx
┃ ┃ ┗ 📜index.tsx
┃ ┣ 📂customForm
┃ ┃ ┣ 📜BullUnitAdd.tsx
┃ ┃ ┣ 📜DemoForm.tsx
┃ ┃ ┣ 📜EditNewRule.tsx
┃ ┃ ┗ 📜NewRule.tsx
┃ ┣ 📂layout
┃ ┃ ┣ 📂front-pages
┃ ┃ ┃ ┣ 📜DashboardOrLogin.tsx
┃ ┃ ┃ ┣ 📜DropdownMenu.tsx
┃ ┃ ┃ ┣ 📜Footer.tsx
┃ ┃ ┃ ┣ 📜FrontMenu.tsx
┃ ┃ ┃ ┣ 📜Header.tsx
┃ ┃ ┃ ┣ 📜index.tsx
┃ ┃ ┃ ┗ 📜styles.module.css
┃ ┃ ┣ 📂horizontal
┃ ┃ ┃ ┣ 📜Footer.tsx
┃ ┃ ┃ ┣ 📜FooterContent.tsx
┃ ┃ ┃ ┣ 📜Header.tsx
┃ ┃ ┃ ┣ 📜HorizontalMenu.tsx
┃ ┃ ┃ ┣ 📜NavToggle.tsx
┃ ┃ ┃ ┣ 📜NavbarContent.tsx
┃ ┃ ┃ ┣ 📜Navigation.tsx
┃ ┃ ┃ ┗ 📜VerticalNavContent.tsx
┃ ┃ ┣ 📂shared
┃ ┃ ┃ ┣ 📂search
┃ ┃ ┃ ┃ ┣ 📜DefaultSuggestions.tsx
┃ ┃ ┃ ┃ ┣ 📜NoResult.tsx
┃ ┃ ┃ ┃ ┣ 📜index.tsx
┃ ┃ ┃ ┃ ┗ 📜styles.css
┃ ┃ ┃ ┣ 📜CompanyDropdown.tsx
┃ ┃ ┃ ┣ 📜CompanyToggle.tsx
┃ ┃ ┃ ┣ 📜Logo.tsx
┃ ┃ ┃ ┣ 📜ModeDropdown.tsx
┃ ┃ ┃ ┣ 📜NotificationsDropdown.tsx
┃ ┃ ┃ ┗ 📜UserDropdown.tsx
┃ ┃ ┗ 📂vertical
┃ ┃ ┃ ┣ 📜Footer.tsx
┃ ┃ ┃ ┣ 📜FooterContent.tsx
┃ ┃ ┃ ┣ 📜NavToggle.tsx
┃ ┃ ┃ ┣ 📜Navbar.tsx
┃ ┃ ┃ ┣ 📜NavbarContent.tsx
┃ ┃ ┃ ┣ 📜Navigation.tsx
┃ ┃ ┃ ┗ 📜VerticalMenu.tsx
┃ ┣ 📂login
┃ ┃ ┗ 📜index.tsx
┃ ┣ 📂logout
┃ ┃ ┗ 📜index.tsx
┃ ┣ 📂model
┃ ┃ ┗ 📜CustomModel.tsx
┃ ┣ 📂stepper-dot
┃ ┃ ┣ 📜index.tsx
┃ ┃ ┗ 📜styles.module.css
┃ ┣ 📂theme
┃ ┃ ┣ 📜ModeChanger.tsx
┃ ┃ ┣ 📜index.tsx
┃ ┃ ┣ 📜mergedTheme.ts
┃ ┃ ┣ 📜types.ts
┃ ┃ ┗ 📜userTheme.ts
┃ ┣ 📜ActiveLastBreadCrumb.tsx
┃ ┣ 📜AuthRedirect.tsx
┃ ┣ 📜DirectionIcon.tsx
┃ ┣ 📜ErrorBoundary.tsx
┃ ┣ 📜GenerateMenu.tsx
┃ ┣ 📜Link.tsx
┃ ┣ 📜Loader.tsx
┃ ┗ 📜Providers.tsx
┣ 📂configs
┃ ┣ 📜almanac.ts
┃ ┣ 📜apiEndpoints.ts
┃ ┣ 📜engine.ts
┃ ┣ 📜primaryColorConfig.ts
┃ ┗ 📜themeConfig.ts
┣ 📂contexts
┃ ┣ 📜intersectionContext.tsx
┃ ┣ 📜nextAuthProvider.tsx
┃ ┗ 📜queryClientProvider.tsx
┣ 📂data
┃ ┣ 📂navigation
┃ ┃ ┣ 📂modules // files containing navigation for each section
┃ ┃ ┃ ┣ 📜account.ts
┃ ┃ ┃ ┣ 📜dashboard.ts
┃ ┃ ┃ ┣ 📜dco.ts
┃ ┃ ┃ ┣ 📜expanse.ts
┃ ┃ ┃ ┣ 📜helpdesk.ts
┃ ┃ ┃ ┣ 📜income.ts
┃ ┃ ┃ ┣ 📜report.ts
┃ ┃ ┃ ┣ 📜settings.ts
┃ ┃ ┃ ┗ 📜vendor.ts
┃ ┃ ┗ 📜verticalMenuData.tsx
┃ ┣ 📂schema // form schemas inside this folder
┃ ┃ ┣ 📂roles
┃ ┃ ┃ ┗ 📂add
┃ ┃ ┃ ┃ ┣ 📜[id].json // for route: /admin/roles/add/1
┃ ┃ ┃ ┃ ┗ 📜index.json // for route: /admin/roles/add
┃ ┃ ┣ 📂vendorbill
┃ ┃ ┃ ┣ 📂addVendorAdvances
┃ ┃ ┃ ┃ ┣ 📜[id].json // for route: /admin/vendorbill/addVendorAdvances/1
┃ ┃ ┃ ┃ ┗ 📜index.json // for route: /admin/vendorbill/addVendorAdvances
┃ ┃ ┃ ┣ 📂add_billable
┃ ┃ ┃ ┃ ┣ 📂[vendor_id]
┃ ┃ ┃ ┃ ┃ ┗ 📜[voucher_id].json // for route: /admin/vendorbill/📂add_billable/4/1
┃ ┃ ┃ ┃ ┗ 📜[id].json // for route: /admin/vendorbill/📂add_billable/1
┃ ┣ 📜script.ts
┃ ┗ 📜searchData.ts
┣ 📂hocs
┃ ┣ 📜AuthGuard.tsx
┃ ┗ 📜GuestOnlyRoute.tsx
┣ 📂hooks
┃ ┗ 📜useIntersection.ts
┣ 📂libs
┃ ┣ 📂styles
┃ ┃ ┗ 📜AppKeenSlider.ts
┃ ┗ 📜auth.ts
┣ 📂types
┃ ┣ 📜cookies.ts
┃ ┣ 📜form.ts
┃ ┗ 📜menuTypes.ts
┣ 📂utils
┃ ┣ 📜calculate.tsx
┃ ┣ 📜convertJSONCtoJSON.js
┃ ┣ 📜evaluate.ts
┃ ┣ 📜generateEngine.ts // form engine generates here
┃ ┣ 📜generateSchema.ts // form generates here
┃ ┣ 📜getSchema.ts
┃ ┣ 📜injectFieldsInTitle.ts
┃ ┣ 📜object.ts
┃ ┣ 📜openPDF.tsx
┃ ┣ 📜replacePlaceholders.ts
┃ ┣ 📜string.ts
┃ ┗ 📜updateSchemaDefaults.ts
┣ 📂views
┃ ┣ 📂form
┃ ┃ ┣ 📂builder
┃ ┃ ┃ ┣ 📜server.tsx // get schema here
┃ ┃ ┃ ┗ 📜client.tsx // add user data inside schema here
┃ ┃ ┣ 📂components
┃ ┃ ┃ ┣ 📜Actions.tsx
┃ ┃ ┃ ┣ 📜Adornment.tsx
┃ ┃ ┃ ┣ 📜Modal.tsx
┃ ┃ ┃ ┣ 📜form.tsx
┃ ┃ ┃ ┗ 📜schemaOverride.tsx
┃ ┃ ┣ 📂fetchers
┃ ┃ ┃ ┗ 📜data.tsx
┃ ┃ ┣ 📂fields
┃ ┃ ┃ ┣ 📜AdornmentField.tsx
┃ ┃ ┃ ┗ 📜Typehead.tsx
┃ ┃ ┣ 📂layout
┃ ┃ ┃ ┣ 📜Accordion.tsx
┃ ┃ ┃ ┣ 📜Card.tsx
┃ ┃ ┃ ┣ 📜Group.tsx
┃ ┃ ┃ ┣ 📜Layout.tsx
┃ ┃ ┃ ┗ 📜Tabs.tsx
┃ ┃ ┣ 📂templates
┃ ┃ ┃ ┣ 📜FieldHelpTemplate.tsx
┃ ┃ ┃ ┣ 📜ObjectField.tsx
┃ ┃ ┃ ┗ 📜TitleField.tsx
┃ ┃ ┗ 📂widgets
┃ ┃ ┃ ┣ 📜Autocomplete.tsx
┃ ┃ ┃ ┣ 📜CameraWidget.tsx
┃ ┃ ┃ ┣ 📜ImageWidget.tsx
┃ ┃ ┃ ┣ 📜Table.tsx
┃ ┃ ┃ ┣ 📜TableWidget.tsx
┃ ┃ ┃ ┣ 📜TimeWidget.tsx
┃ ┃ ┃ ┗ 📜TreeselectWidget.tsx
┃ ┣ 📂landing
┃ ┃ ┣ 📜ContactUs.tsx
┃ ┃ ┣ 📜CustomerReviews.tsx
┃ ┃ ┣ 📜Faqs.tsx
┃ ┃ ┣ 📜GetStarted.tsx
┃ ┃ ┣ 📜HeroSection.tsx
┃ ┃ ┣ 📜Pricing.tsx
┃ ┃ ┣ 📜ProductStat.tsx
┃ ┃ ┣ 📜UsefulFeature.tsx
┃ ┃ ┣ 📜index.tsx
┃ ┃ ┗ 📜styles.module.css
┃ ┣ 📂pages
┃ ┃ ┗ 📂misc
┃ ┃ ┃ ┣ 📜ComingSoon.tsx
┃ ┃ ┃ ┗ 📜UnderMaintenance.tsx
┃ ┣ 📂screens
┃ ┃ ┣ 📜BulkUnitScreen.tsx
┃ ┃ ┣ 📜DashboardScreen.tsx
┃ ┃ ┣ 📜MixScreen.tsx
┃ ┃ ┣ 📜NewRuleScreen.tsx
┃ ┃ ┣ 📜PDFScreen.tsx
┃ ┃ ┣ 📜TableScreen.tsx
┃ ┃ ┗ 📜TimelineScreen.tsx
┃ ┣ 📂table
┃ ┃ ┣ 📂CustomFilters
┃ ┃ ┃ ┣ 📜CustomFilters.tsx
┃ ┃ ┃ ┗ 📜FinancialYearFilter.tsx
┃ ┃ ┣ 📜Actions.tsx
┃ ┃ ┣ 📜CalculateColumn.tsx
┃ ┃ ┣ 📜CustomSelect.tsx
┃ ┃ ┣ 📜EditableSelect.tsx
┃ ┃ ┣ 📜FilterBar.tsx
┃ ┃ ┣ 📜FilterBy.tsx
┃ ┃ ┣ 📜FilterByItem.tsx
┃ ┃ ┣ 📜GenerateColumns.tsx // table generates here
┃ ┃ ┣ 📜GenerateSelectFilters.tsx
┃ ┃ ┣ 📜HelpNote.tsx
┃ ┃ ┣ 📜NestedOrSelectColumn.tsx
┃ ┃ ┣ 📜OptionsMenu.tsx
┃ ┃ ┣ 📜RenderTable.tsx
┃ ┃ ┣ 📜RenderTableProps.tsx
┃ ┃ ┣ 📜RowActions.tsx
┃ ┃ ┣ 📜RowSelectionActions.tsx
┃ ┃ ┣ 📜StyledMenu.tsx
┃ ┃ ┣ 📜TableItem.tsx
┃ ┃ ┣ 📜TopToolbarCustomActions.tsx
┃ ┃ ┣ 📜tableInterceptor.tsx
┃ ┃ ┗ 📜tableSchemaState.ts
┃ ┣ 📜ForgotPassword.tsx
┃ ┣ 📜Login.tsx
┃ ┣ 📜NotAuthorized.tsx
┃ ┣ 📜NotFound.tsx
┃ ┗ 📜PageTypeScreen.tsx
┗ 📜middleware.ts // auth middleware
```
