"use client";

import { useState, useEffect } from 'react';

import axios from 'axios';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  CircularProgress,
  Paper,
  Grid
} from '@mui/material';

import DownloadButton from './DownloadButton';

interface NocForm {
  purpose: string;
  description: string;
}

const NocFormsPage = () => {
  const [loading, setLoading] = useState(true);
  const [nocForms, setNocForms] = useState<NocForm[]>([]);

  useEffect(() => {
    const fetchNocForms = async () => {
      try {
        // Using the specific API URL provided
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/societies/nocForms`, {
          withCredentials: true,
        });

        if (response.data.status === "success") {
          setNocForms(response.data.data || []);
        } else {
          console.warn("Failed to fetch NOC forms");
        }
      } catch (error) {
        console.warn("Error fetching NOC forms:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchNocForms();
  }, []);

  // Default NOC forms if API fails or returns empty
  const defaultNocForms: NocForm[] = [
    { purpose: "NOC Passport", description: "When you apply for the passport (new or renewal), letter from Housing Society certifying the period of your stay and your address is a MUST requirement. Secretary / Chairman should be careful in providing such letters as the default on their part may attract penalty & criminal proceedings under Passports Act, 1967." },
    { purpose: "NOC for Bank Loan", description: "In accordance with the society bye-laws no member of the Society shall assign, mortgage or create any charge on his occupancy right in the flat without the previous permission in writing of the Society / RWA. Therefore, it is important to obtain the society NOC for availing bank loan. Though each bank may have the different format of NOC which they want from the society / RWA to issue considering the type of loan you wish to avail, however some of the standard clauses may remain same." },
    { purpose: "NOC Domicile", description: "A domicile certificate enables its holder to prove that he/she is a resident of the State/ UT either by birth or by having lived in State/ UT for more than 15 years. The document enables the holder to avail of the domicile/resident quota extended by the state in education and jobs. District collectors are the competent authorities for issuing the domicile certificates. However to get such certificate, the Housing Societies / RWA are requires to certify that the applicant is staying within their society from the specific date." },
    { purpose: "NOC for Sale Property", description: "The model bye-laws framed by the state government under the Maharashtra Cooperative Societies Act hold that a person does not require a No Objection certificate (NOC) from the society at the time of transfer of a flat. Bye-law 38(a) of the model bye-laws of MHADA holds that a member is at liberty to sell of his/her flat to another person for his/her own reasons. But a lot of developers and housing societies / RWA insist on a transfer of a flat with an NOC from the society for their personal reasons. The society shall have to issue it within a month of receiving the application. It is important to note that NOC issued by the society / RWA does not absolve the responsibility of the purchaser to pay all the past dues (before the sale) to the society/ RWA." },
    { purpose: "NOC Name Change for Electric Meter", description: "On completion of the building construction all the electric meters stands either in the name of the builder or society / RWA since the member's names are not available at that point of time. As and when you purchase the flat / property / bungalow you should transfer the 'Electric Meter' in your name. This is one of the important document to prove the right of ownership of the property, besides this some time you may require to submit such document as an 'address proof'." },
    { purpose: "NOC for Sub-letting", description: "A member shall intimate to the society of subletting his flat or giving on leave and license basis or care taker basis or parting with its possession in any other manner, however the member shall submit a copy of leave and license agreement." }
  ];

  // Use default forms if API returns empty
  const displayForms = nocForms.length > 0 ? nocForms : defaultNocForms;

  // Download functionality is now handled by the DownloadButton component

  return (
    <Paper elevation={0} sx={{ p: 3, bgcolor: 'transparent' }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h1" sx={{ fontWeight: 600 }}>
          NOC
        </Typography>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Box>
          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" sx={{ mb: 1 }}>
              Member in a housing society needs various types of letters from their society office in order to make various applications, such as application of passport, application for domicile certificate, name change in electric meter, NOC for sale of property, bank loan etc. It is the responsibility of the Secretary / Chairman of the Housing Society / Resident Welfare Association (RWA) to promptly issue such letters / NOC. Objective Certificate with accuracy in details / data. Small mistake in data / draft can invite legal complication / trouble to members and the office bearer.
            </Typography>
            <Typography variant="body1" sx={{ mb: 1 }}>
              cubicle.in enables housing societies / Resident Welfare Association (RWA) in providing quick drafts of such letters along with the accurate data (since data is contributed) from the housing societies / RWA subscribe to such services.
            </Typography>
            <Typography variant="body1" sx={{ mb: 3 }}>
              Herein below we provide you a Letter Generation Module! First! Select the appropriate letter type to generate the letter.
            </Typography>
          </Box>

          <Grid container spacing={2}>
            {displayForms.map((form, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Card
                  sx={{
                    position: 'relative',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    boxShadow: 3,
                    borderRadius: 2,
                    overflow: 'hidden'
                  }}
                >
                  <CardHeader
                    title={form.purpose}
                    sx={{
                      backgroundColor: 'grey.100',
                      borderBottom: '1px solid #ddd',
                      '& .MuiCardHeader-title': {
                        fontSize: '1.1rem',
                        fontWeight: 600
                      }
                    }}
                  />
                  <CardContent sx={{ p: 3, flexGrow: 1 }}>
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      {form.description}
                    </Typography>
                  </CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 2, pt: 0 }}>
                    <DownloadButton formType={form.purpose} />
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Paper>
  );
};

export default NocFormsPage;
