import React from 'react';

import { <PERSON><PERSON>, CircularProgress } from '@mui/material';
import axios from 'axios';
import { toast } from 'react-toastify';
import { Icon } from '@iconify/react/dist/iconify.js';

interface DownloadButtonProps {
  formType: string;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({ formType }) => {
  const [loading, setLoading] = React.useState(false);

  const handleDownload = async () => {

    setLoading(true);

    try {
      // Make API call to get the document using the specific API URL
      const response = await axios.get(
        `https://apigw.cubeone.in/api/admin/societies/nocForms/download/${encodeURIComponent(formType)}?company_id=412`,
        {
          withCredentials: true,
          responseType: 'blob', // Important for file downloads
        }
      );

      // Create a blob from the response data
      const blob = new Blob([response.data], { type: 'application/pdf' });

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create a temporary link element

      const link = document.createElement('a');
      
      link.href = url;
      link.setAttribute('download', `${formType.replace(/\s+/g, '_')}.pdf`);

      // Append the link to the body
      document.body.appendChild(link);

      // Trigger the download
      link.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      toast.success(`${formType} downloaded successfully`);
    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error(`Failed to download ${formType}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      variant="contained"
      color="primary"
      onClick={handleDownload}
      disabled={loading}
      sx={{
        bgcolor: '#1976d2',
        minWidth: '40px',
        width: '40px',
        height: '40px',
        borderRadius: '50%',
        p: 0,
        '&:hover': {
          bgcolor: '#1565c0'
        }
      }}
    >
      {loading ?
        <CircularProgress size={20} color="inherit" /> :
        <Icon icon="ri-download-line" width={20} height={20} />
      }
    </Button>
  );
};

export default DownloadButton;
