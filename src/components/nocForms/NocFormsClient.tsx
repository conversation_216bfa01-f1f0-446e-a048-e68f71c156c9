"use client";

import React from 'react';

import dynamic from 'next/dynamic';

import { Box, Skeleton } from '@mui/material';

// Dynamically import the client component
const NocFormsPage = dynamic(() => import('@/components/nocForms/NocFormsPage'), {
  ssr: false,
  loading: () => (
    <Box sx={{ p: 3 }}>
      <Skeleton variant="text" width="200px" height={40} sx={{ mb: 2 }} />
      <Skeleton variant="text" width="100%" height={30} sx={{ mb: 1 }} />
      {[1, 2, 3].map((i) => (
        <Box key={i} sx={{ mb: 3 }}>
          <Skeleton variant="rectangular" width="100%" height={200} sx={{ borderRadius: 2 }} />
        </Box>
      ))}
    </Box>
  )
});

const NocFormsClient = () => {
  return <NocFormsPage />;
};

export default NocFormsClient;
