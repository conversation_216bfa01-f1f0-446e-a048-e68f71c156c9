'use client'

// React Imports
import type { ChangeEvent} from 'react';
import { useState, useEffect } from 'react'

// MUI Imports
import { Snackbar, Checkbox, Button, FormControlLabel, FormGroup, Typography, Box, IconButton, Fade } from '@mui/material'
import SettingsIcon from '@mui/icons-material/Settings'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'

// Import the server action
import { saveConsent, getConsent } from '@/app/server/cookie' // Ensure the path matches your project structure

// Define CookieConsent Type
type CookieConsent = {
  accepted: boolean
  required: boolean
  theme: boolean
  analytics: boolean
}

const defaultConsent: CookieConsent = {
  accepted: false,
  required: true,
  theme: true,
  analytics: true,
}

export const CookieConsentSnackbar = () => {
  const [consent, setConsent] = useState<CookieConsent>(defaultConsent)
  const [tempConsent, setTempConsent] = useState(consent)
  const [showPreferences, setShowPreferences] = useState(false)

  // Load existing consent on component mount
  useEffect(() => {
    const loadConsent = async () => {
      const existingConsent = await getConsent()

      setConsent(existingConsent)
      setTempConsent(existingConsent)
    }

    loadConsent()
  }, [])

  // Handles accepting all cookies
  const handleAcceptAll = async () => {
    const updatedConsent = { ...consent, theme: true, analytics: true, accepted: true } // Ensure required is included

    await saveConsent(updatedConsent) // Call server action to save cookies securely
    setConsent(updatedConsent) // Update local state
  }

  // Handles saving specific cookie preferences
  const handleAccept = async () => {
    const updatedConsent = { ...tempConsent, accepted: true } // Ensure all fields are included

    await saveConsent(updatedConsent) // Save using server action
    setConsent(updatedConsent) // Update local state
  }

  // Handles changing individual preferences
  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = event.target

    setTempConsent(prev => ({ ...prev, [name]: checked }))
  }

  // Toggles the preferences view
  const handlePreferencesClick = () => {
    setShowPreferences(true)
  }

  // Handles going back to the main view
  const handleBackClick = () => {
    setShowPreferences(false)
  }

  return (
    <Snackbar open={!consent.accepted} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
    >
      <Box
        sx={{
          backgroundColor: 'white',
          padding: '16px',
          borderRadius: '8px',
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
          width: '360px', // Fixed width to keep consistent sizing
          overflow: 'hidden',
          position: 'relative', // Helps with smooth content transitions
        }}
      >
        <Fade in={!showPreferences} timeout={300}>
          <Box
            sx={{
              position: showPreferences ? 'absolute' : 'relative',
              transition: 'opacity 0.3s, transform 0.3s',
              transform: showPreferences ? 'translateX(-100%)' : 'translateX(0)',
              opacity: showPreferences ? 0 : 1,
            }}
          >
            <Box display="flex" flexDirection="column" alignItems="center">
              <SettingsIcon fontSize="large" color="primary" style={{ marginBottom: '8px' }} />
              <Typography variant='h6' gutterBottom>We use cookies</Typography>
              <Typography variant="body2" color="textSecondary" style={{ textAlign: 'center', marginBottom: '16px' }}>
                We use cookies to enhance your experience. Please select your preferences.
              </Typography>
              <Box display="flex" gap="8px">
                <Button variant='contained' color='primary' onClick={handleAcceptAll}>
                  Accept All
                </Button>
                <Button variant='outlined' color='primary' onClick={handlePreferencesClick}>
                  Preferences
                </Button>
              </Box>
            </Box>
          </Box>
        </Fade>

        <Fade in={showPreferences} timeout={300}>
          <Box
            sx={{
              position: !showPreferences ? 'absolute' : 'relative',
              transition: 'opacity 0.3s, transform 0.3s',
              transform: !showPreferences ? 'translateX(100%)' : 'translateX(0)',
              opacity: !showPreferences ? 0 : 1,
            }}
          >
            <Box display="flex" alignItems="center" mb={2}>
              <IconButton onClick={handleBackClick} size="small">
                <ArrowBackIcon />
              </IconButton>
              <Typography variant='h6' gutterBottom style={{ marginLeft: '8px' }}>
                Cookie Preferences
              </Typography>
            </Box>
            <FormGroup>
              <Box mb={2}>
                <FormControlLabel
                  control={<Checkbox disabled checked />}
                  label="Required Cookies"
                />
                <Typography variant="body2" color="textSecondary">
                  These cookies are essential for the website to function properly and cannot be disabled.
                </Typography>
              </Box>
              <Box mb={2}>
                <FormControlLabel
                  control={<Checkbox checked={tempConsent.theme} onChange={handleChange} name="theme" />}
                  label="Theme-Related Cookies"
                />
                <Typography variant="body2" color="textSecondary">
                  These cookies are used to remember your theme preferences.
                </Typography>
              </Box>
              <Box mb={2}>
                <FormControlLabel
                  control={<Checkbox checked={tempConsent.analytics} onChange={handleChange} name="analytics" />}
                  label="Analytics Cookies"
                />
                <Typography variant="body2" color="textSecondary">
                  These cookies help us understand how our website is used by collecting and reporting information anonymously.
                </Typography>
              </Box>
            </FormGroup>
            <Button variant='contained' color='primary' onClick={handleAccept} style={{ marginTop: '16px' }}>
              Accept
            </Button>
          </Box>
        </Fade>
      </Box>
    </Snackbar>
  )
}
