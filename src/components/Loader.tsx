import { useEffect, useRef, useState } from 'react';

import { Box, CircularProgress, Fade } from '@mui/material';

// Constants
const LOADER_DELAY = 7000;
const DELAY_MESSAGE = 'Taking longer than usual...';

export function Loader(): JSX.Element {
  const [showMessage, setShowMessage] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    timerRef.current = setTimeout(() => {
      setShowMessage(true);
    }, LOADER_DELAY);

    return () => {
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, []);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        pt: 2,
        justifyContent: 'center',
        height: '500px',
        alignItems: 'center',
      }}
    >
      <CircularProgress color="primary" aria-label="Loading content" />
      <Fade in={showMessage}>
        <Box sx={{ mt: 2 }}>{DELAY_MESSAGE}</Box>
      </Fade>
    </Box>
  );
}
