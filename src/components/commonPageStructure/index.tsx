'use server';

import type { ReactNode } from "react";

import { Grid2 as Grid, Card, CardContent, Typography, Box, Divider } from "@mui/material";

import ButtonsClient from "./ButtonsClient";

interface ButtonConfig {
    label: string;
    route?: string;
    color?: "primary" | "secondary" | "default";
    icon?: string;
    iconClassName?: string;
}

interface CommonPageStructureProps {
    children: ReactNode;
    title: string;
    buttons?: ButtonConfig[];
}

const CommonPageStructure = ({ children, title, buttons = [] }: CommonPageStructureProps): JSX.Element => {
    return (
        <Grid container sx={{ mt: 5, height: "100%" }}>
            <Grid size={12}>
                <Card>
                    <CardContent>
                        <Grid container mb={2} sx={{ display: "flex", alignItems: "center" }}>
                            <Grid size={2}>
                                <Box sx={{ display: "flex", alignItems: "center" }}>
                                    <Typography variant="h6">{title}</Typography>
                                </Box>
                            </Grid>
                            <Grid size={10} sx={{ display: "flex", justifyContent: "flex-end" }}>
                                {/* Render the client-side buttons */}
                                <ButtonsClient buttons={buttons} />
                            </Grid>
                        </Grid>
                        <Divider />
                        <Box>
                            {children}
                        </Box>
                    </CardContent>
                </Card>
            </Grid>
        </Grid>
    );
};

export default CommonPageStructure;
