'use client';

import { useRouter } from 'next/navigation';

import { Button } from "@mui/material";

interface ButtonConfig {
    label: string;
    route?: string;
    goBack?: boolean;
    color?: "primary" | "secondary" | "default";
    icon?: string;
    iconClassName?: string;
    model?: boolean;
}

interface ButtonsClientProps {
    buttons: ButtonConfig[];
}

const ButtonsClient = ({ buttons }: ButtonsClientProps): JSX.Element => {
    const router = useRouter();

    const handleButtonClick = (route?: string, goBack?: boolean | undefined, model?: boolean | undefined) => {
        if (goBack) {
            router.back();
        } else if (route) {
            router.push(route);
        } else if (model) {
            router.push('/');
        }
    };

    return (
        <>
            {buttons.map((button, index) => (
                <Button
                    key={index}
                    variant="contained"
                    onClick={() => handleButtonClick(button.route, button.goBack, button.model)}
                    color={button.color || "primary"}
                    startIcon={button.icon ? <i className={`${button.icon}`} /> : null}
                    sx={{ ml: index > 0 ? 1 : 0 }}
                >
                    {button.label}
                </Button>
            ))}
        </>
    );
};

export default ButtonsClient;
