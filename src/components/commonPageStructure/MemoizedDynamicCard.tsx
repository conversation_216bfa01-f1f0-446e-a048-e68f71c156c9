'use client';

import { memo } from 'react';

import dynamic from 'next/dynamic';

// Dynamically import the DynamicCard component
const DynamicCard = dynamic(() => import('@/components/card/DynamicCard'), { ssr: false });

// Define the props interface
interface DynamicCardProps {
  headerKeyDisplay?: any[];
  keyDisplay: any[];
  customUrl: string;
  renderButtonGroup?: boolean;
  mainGrid: { sm: number; md: number; lg: number; xs: number };
  currentPage?: number;
  onDataFetch?: (data: any[]) => void;
}

// Define the props for the DynamicCard component
type DynamicCardComponentProps = any; // Use 'any' to bypass type checking for now

// Create a custom comparison function for the memo
const arePropsEqual = (prevProps: DynamicCardProps, nextProps: DynamicCardProps) => {
  // Only rerender if customUrl, currentPage, or mainGrid changes (deep comparison)
  if (prevProps.customUrl !== nextProps.customUrl) return false;
  if (prevProps.currentPage !== nextProps.currentPage) return false;

  // Special check for mainGrid since it's an object
  if (prevProps.mainGrid !== nextProps.mainGrid) {
    // If either is undefined and the other isn't, they're different
    if (!prevProps.mainGrid || !nextProps.mainGrid) {
      console.log('MemoizedDynamicCard: mainGrid changed (one is undefined)');

      return false;
    }

    // Compare individual properties
    if (prevProps.mainGrid.sm !== nextProps.mainGrid.sm ||
        prevProps.mainGrid.md !== nextProps.mainGrid.md ||
        prevProps.mainGrid.lg !== nextProps.mainGrid.lg ||
        prevProps.mainGrid.xs !== nextProps.mainGrid.xs) {
      console.log('MemoizedDynamicCard: mainGrid properties changed');

      return false;
    }
  }

  // Don't rerender for other prop changes
  return true;
};

// Create a memoized version of DynamicCard
const MemoizedDynamicCard = memo((props: DynamicCardProps) => {
  // Log props for debugging
  console.log('MemoizedDynamicCard props:', {
    mainGrid: props.mainGrid,
    customUrl: props.customUrl,
    currentPage: props.currentPage
  });

  // Cast props to any to bypass type checking
  return <DynamicCard {...(props as DynamicCardComponentProps)} />;
}, arePropsEqual);

export default MemoizedDynamicCard;
