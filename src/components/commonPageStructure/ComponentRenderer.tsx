

import { Fragment } from 'react';

import dynamic from 'next/dynamic';

import HtmlRenderer from '@/components/HtmlParse';
import HelpNote from '@/components/card/HelpNoteCard';
import TableScreen from '@/views/screens/TableScreen';
import FormBuilder from '@/views/form/builder/server';
import type { ComponentConfig } from '@/types/menuTypes';
import { replacePlaceholders } from '@/utils/replacePlaceholders';

// Dynamically import client-side components
const TimelineComponent = dynamic(() => import('@/views/screens/TimelineScreen'), { ssr: true });

// Import the DynamicCard component
import MemoizedDynamicCard from '@/components/commonPageStructure/MemoizedDynamicCard';

interface ComponentRendererProps {
    component: ComponentConfig;
    params: Record<string, any>;
    pathname: string;
    href: string;
}

export interface LocalComponentConfig {
    type: string;
    keyDisplay: {
        key: string;
        label: string;
        keyGrid: number;
        valueGrid: number;
    }[];
    mainGrid?: number;
    apiURL?: string;
    renderHtmlBelow?: boolean;
    htmlContent?: string;
    showTimeline?: boolean;
    headerKeyDisplay?: { key: string; label: string; keyGrid: number; valueGrid: number; }[];
    renderButtonGroup?: boolean;
}

// ✅ **Common function to generate customUrl**
const getCustomUrl = (apiURL: string | undefined, pathname: string, params: Record<string, any>) => {
    return replacePlaceholders(apiURL || pathname, params);
};

const ComponentRenderer = ({ component, params, pathname, href }: ComponentRendererProps & { component: LocalComponentConfig }) => {
    const customUrl = getCustomUrl(component.apiURL, pathname, params); // 🔥 Common logic used here

    switch (component.type) {
        case 'table': {
            return <TableScreen apiURL={customUrl} hideActions={true} />; // ✅ Using common customUrl
        }

        case 'card': {
            const { headerKeyDisplay, keyDisplay, renderHtmlBelow, htmlContent, showTimeline, mainGrid = processedMainGrid, renderButtonGroup = false } = component;

            // Create a properly typed mainGrid object with default values
            const processedMainGrid = {
                sm: 12,  // Default values
                md: 6,
                lg: 6,
                xs: 12
            };

            // // Log the original mainGrid for debugging
            // console.log('ComponentRenderer original mainGrid:', mainGrid);

            // // Log the processed mainGrid for debugging
            // console.log('ComponentRenderer processed mainGrid:', processedMainGrid);

            return (
                <Fragment>
                    <MemoizedDynamicCard
                        headerKeyDisplay={headerKeyDisplay}
                        keyDisplay={keyDisplay}
                        customUrl={customUrl} // ✅ Using common customUrl
                        renderButtonGroup={renderButtonGroup}
                        mainGrid={mainGrid} // ✅ Using mainGrid
                        currentPage={1} // Add currentPage as a critical prop
                    />
                    {renderHtmlBelow && htmlContent && (
                        <div style={{ marginTop: '20px', borderTop: '1px solid #ccc', paddingTop: '10px' }}>
                            <HtmlRenderer apiURL={htmlContent.replace('[id]', params.slug?.[params.slug.length - 1] || '')} />
                        </div>
                    )}
                    {showTimeline && <TimelineComponent />}
                </Fragment>
            );
        }

        case 'form': {
            return <FormBuilder href={href} params={params} screenFrom="mix" />;
        }

        case 'htmlContent': {
            return <HtmlRenderer apiURL={pathname} />;
        }

        case 'timeline': {
            return <TimelineComponent />;
        }

        case 'helpNote': {
            // Default to chargingTypes if contentType is not specified
            return <HelpNote contentType="chargingTypes" />;
        }

        case 'htmlContent': {
            return <HtmlRenderer apiURL={pathname} />;
        }

        default: {
            return <div>Unknown component type</div>;
        }
    }
};

export default ComponentRenderer;
