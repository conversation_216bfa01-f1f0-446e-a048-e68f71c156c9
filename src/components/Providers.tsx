import { ToastContainer } from 'react-toastify'

// Type Imports
import type { ChildrenType, Direction } from '@core/types'


// Context Imports
import { NextAuthProvider } from '@/contexts/nextAuthProvider'
import { VerticalNavProvider } from '@menu/contexts/verticalNavContext'
import { SettingsProvider } from '@core/contexts/settingsContext'
import ThemeProvider from '@components/theme'

// Styled Component Imports
// import AppReactToastify from '@/libs/styles/AppReactToastify'

import 'react-toastify/dist/ReactToastify.css'

// Util Imports
import { getMode, getSettingsFromCookie, getSystemMode } from '@core/utils/serverHelpers'
import QueryClientProviderWrapper from '@/contexts/queryClientProvider'

type Props = ChildrenType & {
  direction: Direction
}

const Providers = async (props: Props) => {
  // Props
  const { children, direction } = props

  // Vars
  const mode = await getMode()
  const settingsCookie = await getSettingsFromCookie()
  const systemMode = await getSystemMode()

  // 3600 seconds = 1 hour
  return (
    <NextAuthProvider refetchInterval={3600}>
      {/* <NextAuthProvider> */}
      <VerticalNavProvider>
        <SettingsProvider settingsCookie={settingsCookie} mode={mode}>
          <ThemeProvider direction={direction} systemMode={systemMode}>
            <QueryClientProviderWrapper>
              {children}
            </QueryClientProviderWrapper>
            <ToastContainer
              autoClose={1500}
              limit={5}
              theme={mode === "system" ? "colored": mode}
            />
          </ThemeProvider>
        </SettingsProvider>
      </VerticalNavProvider>
    </NextAuthProvider>
  )
}

export default Providers
