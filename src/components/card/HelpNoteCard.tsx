import React from "react";

import { Typography, Paper, List, ListItem, ListItemText } from "@mui/material";

const HelpNote = ({ contentType }) => {
    const getContent = () => {
        if (contentType === "chargingTypes") {
            return (
                <>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Help Note:
                    </Typography>
                    <Typography variant="h5" sx={{ marginBottom: "16px", fontWeight: "bold" }}>
                        Charging Types:
                    </Typography>
                    <List>
                        <ListItem alignItems="flex-start">
                            <ListItemText
                                primary="1) Onetime:"
                                secondary="Unit can be purchased only once/one-time by the member/non-member from the society."
                            />
                        </ListItem>
                        <ListItem alignItems="flex-start">
                            <ListItemText
                                primary="2) Recurring:"
                                secondary="Unit charge needs to pay on regular time intervals to the society."
                            />
                        </ListItem>
                        <ListItem alignItems="flex-start">
                            <ListItemText
                                primary="3) On Demand:"
                                secondary="Unit needs to pay when it is utilized/booked by member/non-member to the society."
                            />
                        </ListItem>
                        <ListItem alignItems="flex-start">
                            <ListItemText
                                primary="4) Free:"
                                secondary="Unit can be used free of cost by member/non-member."
                            />
                        </ListItem>
                    </List>
                </>
            );
        } else if (contentType === "sequenceChange") {
            return (
                <>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                        Help Note:
                    </Typography>
                    <List>
                        <ListItem>
                            <ListItemText primary="1. To change the sequence of particulars, select a particular row. Drag the selected particular row and place on another row to change." />
                        </ListItem>
                        <ListItem>
                            <ListItemText primary="2. Edit display name by changing the display name text." />
                        </ListItem>
                        <ListItem>
                            <ListItemText primary="3. To change the order of a column, click on the arrow beside the column header." />
                        </ListItem>
                    </List>
                </>
            );
        }

        
return null;
    };

    return (
        <Paper
            elevation={3}
            sx={{
                padding: "16px",
                borderRadius: "8px",
                maxWidth: "600px",
                margin: "auto",
                backgroundColor: "#ffffff",
                mt: 5
            }}
        >
            {getContent()}
        </Paper>
    );
};

export default HelpNote;
