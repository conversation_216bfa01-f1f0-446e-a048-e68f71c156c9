// @ts-nocheck

"use client";

import { useState, useEffect, Fragment, useCallback, useMemo, memo, useRef, Component, type FC } from 'react';

// FC type removed as it was unused

import { usePathname, useRouter } from 'next/navigation';

import {
  Card,
  CardContent,
  CardHeader,
  Grid,
  Typography,
  Box,
  Checkbox,
  Pagination,
  PaginationItem,
  Button,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';
import axios from 'axios';

import { toast } from 'react-toastify';

import { Icon } from '@iconify/react/dist/iconify.js';

import StatusChip from '../chip/StatusChip';
import { concatenateKeys, renderValue } from '../../@menu/utils/renderValue';
import { API_ENDPOINTS } from '../../configs/apiEndpoints';
import { replacePlaceholders } from '@/utils/replacePlaceholders';
import { stripAdminScopedPath } from '@/utils/string';


type KeyDisplay = {
  key: string | string[];
  label: string;
  keyGrid: number;
  valueGrid: number;
  showAsZero?: boolean;
  showAsYesNo?: boolean;
  showAsStatus?: boolean;
  displayMap?: Record<string | number, string>;
  showAsChip?: boolean;
  isLink: boolean;
};

type HeaderKeyDisplay = {
  key: string;
  label: string;
  isLink?: boolean;
  isCheckbox?: boolean;
  showChip?: boolean;
  showSubHeader?: boolean;
  displayMap?: Record<string | number, string>;
  showIcon?: boolean;
  redirectUrl?: string;
  IconClassName?: string;
};

type MainGridConfig = {
  xs?: number;
  sm: number;
  md?: number;
  lg?: number;
};

type DynamicCardProps = {
  keyDisplay: KeyDisplay[];
  mainGrid?: MainGridConfig;
  headerKeyDisplay?: HeaderKeyDisplay[];
  customUrl?: string;
  renderButtonGroup?: boolean;
  onDataFetch?: (data: any[]) => void;
};

type TableData = {
  item_name: string;
  item_cost: number;
  item_quantity: number;
  total_cost: number;
};

type ResponseData = {
  data: any[];
  meta: { pagination?: { total: number } };
};

// Custom hook to prevent unnecessary rerenders
function useStableProps<T>(props: T): T {
  const stablePropsRef = useRef<T>(props);

  // Only update the ref if critical props change
  useEffect(() => {
    const criticalProps = ['customUrl', 'currentPage', 'mainGrid'];
    let shouldUpdate = false;

    for (const prop of criticalProps) {
      if (props[prop] !== stablePropsRef.current[prop]) {
        shouldUpdate = true;
        break;
      }
    }

    if (shouldUpdate) {
      stablePropsRef.current = props;
    }
  }, [props]);

  return stablePropsRef.current;
}

// Define a custom comparison function for React.memo
const arePropsEqual = (prevProps: DynamicCardProps, nextProps: DynamicCardProps) => {
  // Only rerender if these specific props change
  const criticalProps = ['customUrl', 'currentPage', 'mainGrid'];

  // Check if any critical prop has changed
  for (const prop of criticalProps) {
    if (prevProps[prop] !== nextProps[prop]) {
      return false; // Props are not equal, should rerender
    }
  }

  // Special check for mainGrid since it's an object
  if (prevProps.mainGrid !== nextProps.mainGrid) {
    // If either is undefined and the other isn't, they're different
    if (!prevProps.mainGrid || !nextProps.mainGrid) {
      return false;
    }

    // Compare individual properties
    if (prevProps.mainGrid.sm !== nextProps.mainGrid.sm ||
      prevProps.mainGrid.md !== nextProps.mainGrid.md ||
      prevProps.mainGrid.lg !== nextProps.mainGrid.lg ||
      prevProps.mainGrid.xs !== nextProps.mainGrid.xs) {
      return false;
    }
  }

  // Props are equal, no need to rerender
  return true;
};

// Use React.memo with custom comparison function
const DynamicCard: FC<DynamicCardProps> = memo((props) => {
  // Log the original props
  // Use stable props to prevent unnecessary rerenders
  const {
    keyDisplay,
    mainGrid: rawMainGrid = { sm: 12, md: 6, lg: 6, xs: 12 }, // Default values
    headerKeyDisplay,
    customUrl,
    renderButtonGroup = false,
    onDataFetch
  } = useStableProps(props);

  // Ensure all grid properties are set - wrapped in useMemo to prevent unnecessary rerenders
  const mainGrid = useMemo(() => ({
    sm: rawMainGrid.sm || 12, // Ensure sm is always set
    md: rawMainGrid.md || 6,
    lg: rawMainGrid.lg || 6,
    xs: rawMainGrid.xs || 12
  }), [rawMainGrid.sm, rawMainGrid.md, rawMainGrid.lg, rawMainGrid.xs]);

  // Log the extracted mainGrid prop
  console.log('DynamicCard mainGrid after extraction:', mainGrid);

  const pathname = usePathname();
  const asPath = stripAdminScopedPath(pathname);
  const router = useRouter();

  // Debug counter to track rerenders
  const renderCountRef = useRef(0);

  renderCountRef.current += 1;


  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedIds, setSelectedIds] = useState<Set<number>>(new Set());
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);

  // Use this function instead of direct state updates to control rerenders
  const updateState = useCallback((updater) => {
    updater();
  }, []);

  // Use refs to store the latest props to prevent unnecessary rerenders
  const onDataFetchRef = useRef(onDataFetch);
  const keyDisplayRef = useRef(keyDisplay);
  const headerKeyDisplayRef = useRef(headerKeyDisplay);
  const customUrlRef = useRef(customUrl);

  // Update the refs whenever props change
  useEffect(() => {
    onDataFetchRef.current = onDataFetch;
    keyDisplayRef.current = keyDisplay;
    headerKeyDisplayRef.current = headerKeyDisplay;
    customUrlRef.current = customUrl;
  }, [onDataFetch, keyDisplay, headerKeyDisplay, customUrl]);

  const pageSize = 10;

  const normalizeData = useCallback((responseData: ResponseData) => {
    const { data = [], meta = {} } = responseData || {};
    const totalPages = Math.ceil((meta.pagination?.total || 0) / pageSize) || 1;

    return {
      items: Array.isArray(data) ? data : [data],
      totalPages
    };
  }, [pageSize]);

  const fetchData = useCallback(async () => {
    updateState(() => setLoading(true));

    try {
      const response = await axios.get(customUrlRef.current || asPath, {
        baseURL: process.env.NEXT_PUBLIC_API_URL,
        params: { page: currentPage, page_size: pageSize },
        withCredentials: true,
      });

      if (response.status === 200 && response.data?.data) {
        const normalizedData = normalizeData(response.data);

        // Use batch updates to prevent multiple rerenders
        updateState(() => {
          setData(normalizedData.items);
          setTotalPages(normalizedData.totalPages);
        });

        // Use the ref to access the latest onDataFetch callback
        // This prevents the fetchData function from being recreated when onDataFetch changes
        if (onDataFetchRef.current && normalizedData.items.length > 0) {
          // Only call onDataFetch if we have items to prevent unnecessary rerenders
          onDataFetchRef.current(normalizedData.items);
        }
      } else {
        toast.error('No Response: Provide data from backend side is null');
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Error fetching data');
    } finally {
      updateState(() => setLoading(false));
    }
  }, [currentPage, normalizeData, pageSize, updateState, asPath]);

  useEffect(() => {
    fetchData()
  }, [fetchData]);

  const handleCheckboxChange = useCallback((id: number) => {
    updateState(() => {
      setSelectedIds((prev) => {

        const newSet = new Set(prev);

        if (newSet.has(id)) {
          newSet.delete(id);
        } else {
          newSet.add(id);
        }

        return newSet;
      });
    });
  }, [updateState]);

  /**
   * Handle icon click with both scenarios:
   * 1. If item.id is defined, use that ID.
   * 2. Otherwise, fall back to the ID from the pathname.
   */
  const handleIconClick = useCallback((redirectUrl: string, item?: any) => {
    const url = replacePlaceholders(redirectUrl, item);

    router.push(url);
  }, [router]);

  const handlePageChange = useCallback((_: React.ChangeEvent<unknown>, value: number) => {
    updateState(() => setCurrentPage(value));
  }, [updateState]);


  const handleAction = useCallback(async (action: string) => {
    if (selectedIds.size === 0) {
      toast.warn('Please select at least one item');

      return;
    }

    try {
      const response = await axios.put(
        `${process.env.NEXT_PUBLIC_API_URL}/${API_ENDPOINTS[action]}`,
        { id: Array.from(selectedIds) },
        {
          withCredentials: true,
        }
      );

      response.data?.data?.forEach(({ status, message }: { status: string; message: string }) => {
        if (status === 'error') {
          toast.error(message);
        } else {
          toast.success(message);
        }
      });

      router.refresh();
    } catch (error) {
      console.error('Error performing action:', error);
      toast.error('Action failed');
    }
  }, [selectedIds, router]);

  const renderHeaderContent = useCallback((item: any) => (
    <Box display="flex" justifyContent="space-between" alignItems="center">
      <Box display="flex" alignItems="center">
        {headerKeyDisplayRef.current?.map(({ key, label, isLink, isCheckbox, showSubHeader, displayMap }) => (
          <Fragment key={`header-${key}`}>
            {isCheckbox && (
              <Checkbox
                onChange={() => handleCheckboxChange(item.id)}
                disabled={item.disable === 'true'}
                checked={selectedIds.has(item.id)}
              />
            )}
            <Typography variant="h6" component="div" sx={{ mr: 1 }}>
              <strong>{label}</strong>
              {isLink && item[key] && (
                <a
                  href={item[key]}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ textDecoration: 'none', color: 'blue', marginLeft: '5px' }}
                >
                  {item[key]}
                </a>
              )}
            </Typography>
            {showSubHeader && (
              <Typography variant="subtitle2" color="text.secondary">
                {displayMap?.[item[key]] ?? item[key]}
              </Typography>
            )}
          </Fragment>
        ))}
      </Box>
      <Box display="flex" alignItems="center">
        {headerKeyDisplayRef.current?.map(({ key, showChip }, index) => (
          <Fragment key={`chip-${key}-${index}`}>
            {showChip && (
              <StatusChip
                status={item[key]}
                fallbackLabel={item[key]}
                sx={{ ml: 2 }}
              />
            )}
          </Fragment>
        ))}
        {headerKeyDisplayRef.current?.map(({ showIcon, redirectUrl, IconClassName, key }, index) => {
          // Add console log for redirectUrl in the map function

          return (
            <Fragment key={`icon-${key || index}`}>
              {showIcon && (
                <Icon
                  icon={IconClassName}
                  width={20}
                  height={20}
                  style={{ cursor: 'pointer', marginLeft: '8px', color: '#0a4f92' }}
                  onClick={() => handleIconClick(redirectUrl || '', item)}
                />
              )}
            </Fragment>
          );
        })}
      </Box>
    </Box>
  ), [handleCheckboxChange, selectedIds, handleIconClick]);

  const renderTable = useCallback((tableData: TableData[]) => (
    <TableContainer component={Paper} sx={{ mt: 2 }}>
      <Table>
        <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
          <TableRow>
            <TableCell align="center" sx={{ fontWeight: 'bold' }}>
              Item Name
            </TableCell>
            <TableCell align="center" sx={{ fontWeight: 'bold' }}>
              Unit Cost (₹)
            </TableCell>
            <TableCell align="center" sx={{ fontWeight: 'bold' }}>
              Item Quantity
            </TableCell>
            <TableCell align="center" sx={{ fontWeight: 'bold' }}>
              Total Cost (₹)
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {tableData.length > 0 ? (
            tableData.map((row, index) => (
              <TableRow key={`table-row-${index}`}>
                <TableCell align="center">{row.item_name}</TableCell>
                <TableCell align="center">{row.item_cost}</TableCell>
                <TableCell align="center">{row.item_quantity}</TableCell>
                <TableCell align="center">{row.total_cost}</TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell align="center" colSpan={4}>
                No Record Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  ), []);

  // Using empty dependency array as concatenateKeys and renderValue are imported functions
  const renderCardContent = useCallback((item: any) => (
    <>
      <CardContent>
        <Grid container spacing={2}>
          {keyDisplayRef.current.map(({ key, label, keyGrid, valueGrid, showAsStatus, showAsYesNo, displayMap, isLink = false }) => {
            let value;

            if (Array.isArray(key)) {
              value = concatenateKeys(item, key);
            } else if (showAsStatus) {
              value = renderValue(item, key, { showAsStatus });
            } else if (showAsYesNo) {
              value = renderValue(item, key, { showAsYesNo });
            } else if (displayMap) {
              value = displayMap[item[key]] || item[key];
            } else {
              value = item[key];
            }

            return (
              <Fragment key={Array.isArray(key) ? key.join(',') : key}>
                <Grid item xs={keyGrid}>
                  <Typography variant="body2" sx={{ color: 'grey.700' }}>
                    <strong>{label}:</strong>
                  </Typography>
                </Grid>
                <Grid item xs={valueGrid}>
                  {isLink ? (
                    <a
                      href={value}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ textDecoration: 'none', color: 'blue' }}
                    >
                      {value}
                    </a>
                  ) : (
                    <Typography
                      variant="body2"
                      sx={{
                        color: 'grey.900',
                        wordWrap: 'break-word',
                        whiteSpace: 'normal',
                        overflowWrap: 'anywhere',
                        maxWidth: '100%',
                        display: 'block'
                      }}
                    >
                      {value || '-'}
                    </Typography>
                  )}
                </Grid>
              </Fragment>
            );
          })}
        </Grid>
      </CardContent>
    </>
  ), []); // Removed concatenateKeys and renderValue from dependencies as they are imported functions

  const ButtonGroup = memo(({
    isButton = false,
    handleAction
  }: {
    isButton: boolean;
    handleAction: (action: string) => void;
  }) => {
    if (!isButton) return null;

    return (
      <Box>
        {['approve', 'reject', 'review', 'refuse'].map((action) => (
          <Button
            key={`action-${action}`}
            variant="contained"
            color={action === 'approve' || action === 'review' ? 'success' : 'error'}
            sx={{ mr: 2 }}
            onClick={() => handleAction(action)}
          >
            {`${action.charAt(0).toUpperCase() + action.slice(1)} Selected`}
          </Button>
        ))}
      </Box>
    );
  });

  // Memoize the card content to prevent unnecessary recalculations
  const cardContent = useMemo(() => {
    // Debug mainGrid values
    console.log('DynamicCard mainGrid values:', {
      mainGrid,
      sm: mainGrid.sm,
      md: mainGrid.md,
      lg: mainGrid.lg,
      xs: mainGrid.xs
    });

    if (loading) {
      return (
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Skeleton variant="rectangular" width={530} height={118} />
          </Box>
        </Grid>
      );
    }

    if (data.length === 0) {
      return (
        <Grid item xs={12}>
          <Card sx={{ textAlign: "center", p: 2, boxShadow: 3 }}>
            <CardContent>
              <ErrorIcon color="error" fontSize="large" />
              <Typography variant="h6" color="error" mt={2}>
                No data found
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      );
    }

    return data.map((item, index) => {
      // Debug Grid props for each item
      console.log(`Grid props for item ${index}:`, {
        xs: mainGrid.xs,
        sm: mainGrid.sm,
        md: mainGrid.md,
        lg: mainGrid.lg
      });

      return (
        <Grid
          item
          xs={mainGrid.xs}
          sm={mainGrid.sm}
          md={mainGrid.md}
          lg={mainGrid.lg}
          key={`card-grid-${index}`}
          sx={{
            padding: 1,
            boxSizing: 'border-box',

            // Add explicit width for debugging

            width: {
              xs: mainGrid.xs === 12 ? '100%' : `${mainGrid.xs * 8.33}%`,
              sm: mainGrid.sm === 12 ? '100%' : `${mainGrid.sm * 8.33}%`,
              md: mainGrid.md === 12 ? '100%' : `${mainGrid.md * 8.33}%`,
              lg: mainGrid.lg === 12 ? '100%' : `${mainGrid.lg * 8.33}%`,
            }
          }} // Debug border and explicit width
        >
          <Card
            sx={{
              position: 'relative',
              mb: 3,
              boxShadow: 3,
              borderRadius: 2,
              ':hover': {
                boxShadow: 6,
                transform: 'scale(1.01)',
                transition: '0.3s ease-in-out'
              }
            }}
          >
            <CardHeader title={renderHeaderContent(item)} sx={{ backgroundColor: 'grey.100', borderBottom: '1px solid #ddd', mb: 2 }} />
            {renderCardContent(item)}
          </Card>

          <Box
            key={`table-box-${index}`}
            sx={{
              position: 'relative',
              mb: 3,
              boxShadow: 3,
              borderRadius: 2,
              ':hover': {
                boxShadow: 6,
                transform: 'scale(1.01)',
                transition: '0.3s ease-in-out'
              }
            }}
          >
            {item.purchase_form_items && renderTable(item.purchase_form_items)}
          </Box>
        </Grid>
      );
    });
  }, [data, loading, mainGrid.sm, mainGrid.md, mainGrid.lg, mainGrid.xs, renderTable, renderHeaderContent, renderCardContent]);

  // Memoize the pagination component
  const paginationComponent = useMemo(() => {
    if (totalPages <= 1) return null;

    return (
      <Pagination
        count={totalPages}
        page={currentPage}
        onChange={handlePageChange}
        shape="rounded"
        color="primary"
        renderItem={(item) => <PaginationItem {...item} />}
      />
    );
  }, [totalPages, currentPage, handlePageChange]);

  return (
    <>
      <Grid
        container
        spacing={3}
        sx={{
          mt: 2,
          width: '100%',
          display: 'flex',
          flexWrap: 'wrap',
          padding: 2,
          boxSizing: 'border-box'
        }}
      >
        {/* Add debug info */}
        {/* <div style={{ width: '100%', padding: '10px', backgroundColor: '#f0f0f0', marginBottom: '10px' }}>
          <h3>Debug Info:</h3>
          <p>mainGrid: {JSON.stringify(mainGrid)}</p>
          <p>Number of items: {data.length}</p>
        </div> */}
        {cardContent}
      </Grid>

      <Box display="flex" justifyContent="space-between" alignItems="center" mt={4}>
        {useMemo(() => (
          <ButtonGroup isButton={renderButtonGroup} handleAction={handleAction} />
        ), [renderButtonGroup, handleAction, ButtonGroup])}
        {paginationComponent}
      </Box>
    </>
  );
}, arePropsEqual);

// Create a class component wrapper to strictly control rerenders
class DynamicCardWrapper extends Component {
  // Add a custom shouldComponentUpdate to strictly control rerenders
  shouldComponentUpdate(nextProps) {
    // Only rerender if these specific props change
    const criticalProps = ['customUrl', 'currentPage', 'mainGrid'];

    // Check if any critical prop has changed
    for (const prop of criticalProps) {
      if (this.props[prop] !== nextProps[prop]) {
        console.log(`Rerendering because ${prop} changed`);

        return true;
      }
    }

    // Special check for mainGrid since it's an object
    if (this.props.mainGrid !== nextProps.mainGrid) {
      // If either is undefined and the other isn't, they're different
      if (!this.props.mainGrid || !nextProps.mainGrid) {
        console.log('Rerendering because mainGrid changed (one is undefined)');

        return true;
      }

      // Compare individual properties
      if (this.props.mainGrid.sm !== nextProps.mainGrid.sm ||
        this.props.mainGrid.md !== nextProps.mainGrid.md ||
        this.props.mainGrid.lg !== nextProps.mainGrid.lg ||
        this.props.mainGrid.xs !== nextProps.mainGrid.xs) {
        console.log('Rerendering because mainGrid properties changed');

        return true;
      }
    }

    // Don't rerender for other prop changes
    return false;
  }

  render() {
    return <DynamicCard {...this.props} />;
  }
}

// Export the wrapped component
export default DynamicCardWrapper;
