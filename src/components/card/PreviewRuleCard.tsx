// @ts-nocheck

'use client'
import { useEffect, useState } from 'react'

import { usePathname, useRouter } from 'next/navigation'

import axios from 'axios'
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button
} from '@mui/material'
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar'
import DescriptionIcon from '@mui/icons-material/Description'
import LayersIcon from '@mui/icons-material/Layers'
import InfoIcon from '@mui/icons-material/Info'
import ArrowBackIcon from '@mui/icons-material/ArrowBack';


const formatBoolean = value => (value ? 'Yes' : 'No')

const useFetchIncomeRuleDetails = (id) => {
  const [data, setData] = useState({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (!id) {
      setError('Invalid or missing ID')
      setLoading(false)

      return
    }

    const fetchData = async () => {
      try {
        const apiURL = `/admin/income-tracker-invoice-setting/preview_income/${id}`

        const response = await axios.get(apiURL, {
          baseURL: process.env.NEXT_PUBLIC_API_URL,
          withCredentials: true,
        })

        setData(response.data?.data || {})
      } catch (error) {
        setError(error.response ? error.response.data : error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [id])

  return { data, loading, error }
}

const ParkingRuleSection = ({ title, details }) => (
  <Box sx={{ mb: 4 }}>
    <Typography variant='h6' sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      {title}
    </Typography>
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>
              <Typography variant='subtitle2'>Parking Space</Typography>
            </TableCell>
            <TableCell>
              <Typography variant='subtitle2' fontWeight='bold'>
                2 Wheeler
              </Typography>
            </TableCell>
            <TableCell>
              <Typography variant='subtitle2'>4 Wheeler</Typography>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {details.map((item, idx) => (
            <TableRow key={idx}>
              <TableCell>{item.parking_type}</TableCell>
              <TableCell>₹ {item.fee_2_wheeler} /per month</TableCell>
              <TableCell>₹ {item.fee_4_wheeler} /per month</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  </Box>
)

const FloorBasedRuleSection = ({ title, details }) => (
  <Box sx={{ mb: 4 }}>
    <Typography variant='h6' sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      <LayersIcon sx={{ mr: 1 }} /> {title}
    </Typography>
    {details.map((item, idx) => (
      <Box key={idx} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <Typography variant='body1'>
          Flat Characteristics: No. of floor <strong>{item.floor_from}</strong> X{' '}
          <strong>₹{item.fee_carpet_area}</strong> ₹/Sqft Total Area /Month
        </Typography>
      </Box>
    ))}
  </Box>
)

const StandardNoneRuleSection = ({ details }) => (
  <Box sx={{ mb: 4 }}>
    <Typography variant='h6' sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      <InfoIcon sx={{ mr: 1, color: 'primary.main' }} /> {details.label}
    </Typography>
    <Box display='flex' alignItems='center' gap={2}>
      <Typography variant='body1' color='textSecondary'>
        {details?.data?.title}
      </Typography>
      <Typography variant='h6'>₹ {details?.data?.amount || '0.000'}</Typography>
      <Typography variant='body1' color='textSecondary'>
        {details?.data?.type || ''}
      </Typography>
    </Box>
  </Box>
)



const StandardFixedSection = ({ details }) => (
  <Box sx={{ mb: 4 }}>
    <Typography variant='h6' sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      <DescriptionIcon sx={{ mr: 1, color: 'primary.main' }} /> {details.label}
    </Typography>
    <Grid container>
      <Grid item xs={6}>
        <Typography variant='body1' color='textSecondary'>
          {details.data.title}
        </Typography>
        <Typography variant='h6'>
          ₹ {details.data.amount} / per month{' '}
          <Typography variant='body1' component='span'>
            {' '}
            {details.data.type}{' '}
          </Typography>{' '}
        </Typography>
      </Grid>
    </Grid>
  </Box>
)

const StandardNOCSection = ({ details }) => (
  <Box sx={{ mb: 4 }}>
    <Typography variant='h6' sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      <DescriptionIcon sx={{ mr: 1, color: 'primary.main' }} /> Non-Occupancy Charges
    </Typography>
    <Grid container spacing={2}>
      {details.data.map((item, idx) => (
        <Grid item xs={12} key={idx}>
          <Box>
            <Typography variant='body2' color='textSecondary'>
              Amount :
            </Typography>
            <Typography variant='h6'>{item.amount} %</Typography>
          </Box>
          <Typography variant='body1' textAlign='right'>
            {item.particular}
          </Typography>
        </Grid>
      ))}
    </Grid>
  </Box>
)

// response change code here 

const FlatFeeSection = ({ ruleData }) => (
  <Box sx={{ mb: 4 }}>
    <Typography variant="h6" sx={{ display: "flex", alignItems: "center", mb: 2 }}>
      <DescriptionIcon sx={{ mr: 1, color: "primary.main" }} /> {ruleData.label}
    </Typography>
    {Array.isArray(ruleData.details.charges) ? (
      ruleData.details.charges.map((charge, idx) => (
        <Box key={idx} sx={{ mb: 1 }}>
          <Typography variant="body1" color="textSecondary">
            {charge.label}
          </Typography>
          <Typography variant="h6">₹{charge.value} / {charge.unit}</Typography>
        </Box>
      ))
    ) : (
      <>
        <Typography variant="body1" color="textSecondary">
          Flat Fee / Month
        </Typography>
        <Typography variant="h6">₹{ruleData.details.charges.amount}</Typography>
      </>
    )}
  </Box>
)

const PerSqftFeeSection = ({ ruleData }) => (
  <Box sx={{ mb: 4, p: 2, }}>
    <Typography variant="h6" sx={{ display: "flex", alignItems: "center", mb: 2 }}>
      <DescriptionIcon sx={{ mr: 1, color: "primary.main" }} /> {ruleData.label}
    </Typography>
    <Typography variant="h5" sx={{ mb: 2 }}>
      {ruleData.type !== "persqft" ? "Per SqFt rate" : ""}
    </Typography>
    <Grid container spacing={2}>
      {Array.isArray(ruleData.details.charges) ? (
        ruleData.details.charges.map((charge, idx) => (
          <Grid key={idx} item xs={6}>
            <Typography variant="body1" color="textSecondary">
              {charge.label}
            </Typography>
            <Typography variant="h6">
              ₹{charge.value} / {charge.unit}
            </Typography>
          </Grid>
        ))
      ) : (
        <>
          <Grid item xs={6}>
            <Typography variant="body1" color="textSecondary">
              Carpet Area :
            </Typography>
            <Typography variant="h6">
              ₹{ruleData.details.charges.carpet_area} /per sqft /per month
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body1" color="textSecondary">
              Open Area :
            </Typography>
            <Typography variant="h6">
              ₹{ruleData.details.charges.open_area} /per sqft /per month
            </Typography>
          </Grid>
        </>
      )}
    </Grid>
  </Box>
);

const InletFeeSection = ({ ruleData }) => {

  return (
    <>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <InfoIcon sx={{ mr: 1, color: "primary.main" }} /> {ruleData.label}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Amount
        </Typography>
        {
          ruleData.details.charges.map((item, idx) => (
            <Typography variant="h6" key={idx}>
              ₹{item.value}{item.unit}
            </Typography>

          )
          )
        }

      </Box>

    </>



  )
}

const SimpleNOCSection = ({ ruleData }) => {
  return (
    <Box sx={{ mb: 4 }}>
      <Typography variant="h6" sx={{ display: "flex", alignItems: "center", mb: 2 }}>
        <DescriptionIcon sx={{ mr: 1, color: "primary.main" }} />{ruleData.label}
      </Typography>
      <Typography variant="h5" sx={{ mb: 2 }}>
        {ruleData.type !== "floor_based" ? "Simple NOC" : ""}
      </Typography>
      <Grid container spacing={2}>
        {Array.isArray(ruleData.details.charges)
          ? ruleData.details.charges.map((charge, idx) => (
            <Grid item xs={6} key={idx}>
              <Typography variant="body2" color="textSecondary">
                {charge.tenant_occupied} / per month
              </Typography>
              <Typography variant="h6">
                ₹{charge.vacant_flat} / per month
              </Typography>
            </Grid>
          ))
          : (
            <>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">
                  Tenant occupied flat:
                </Typography>
                <Typography variant="h6">
                  ₹{ruleData.details.charges.tenant_occupied} / per month
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="textSecondary">
                  Vacant flat:
                </Typography>
                <Typography variant="h6">
                  ₹{ruleData.details.charges.vacant_flat} / per month
                </Typography>
              </Grid>
            </>
          )}
      </Grid>
    </Box>
  )
}

const OtherChargesSection = ({ ruleData }) => {

  return (
    <Box sx={{ mb: 4 }}>
      <Typography variant="h6" sx={{ display: "flex", alignItems: "center", mb: 2 }}>
        <InfoIcon sx={{ mr: 1, color: "primary.main" }} /> {ruleData.label}
      </Typography>
      {Array.isArray(ruleData.details.charges) ? (
        ruleData.details.charges.map((charge, idx) => (
          <Grid container key={idx} sx={{ mb: 1 }}>
            <Grid item md={6}>
              <Typography variant="body1" color="textSecondary">
                Amount
              </Typography>
              <Typography variant="h6">
                ₹{charge.value === " " ? charge.value : " Error :- Recevied Empty String From BackEnd Side"} ( {charge.unit || ''} )
              </Typography>
            </Grid>
            <Grid item md={6}>
              <Typography variant="boday1" color="textSecondary">
                of {ruleData.details.charges.description || 'Maintenance Fee'}
              </Typography>
            </Grid>

          </Grid>
        ))
      ) : (
        <>
          <Typography variant="body1" color="textSecondary">
            Amount
          </Typography>
          <Typography variant="h6">
            ₹{ruleData.details.charges.amount} {ruleData.details.charges.unit || ''}
          </Typography>
          <Typography variant="body1" color="textSecondary">
            of {ruleData.details.charges.description || 'Maintenance Fee'}
          </Typography>
        </>
      )}
    </Box>
  )
}

const FixedChargesSection = ({ ruleData }) => {

  console.log(ruleData, "fixed rule")

  return (
    <Box sx={{ mb: 4 }}>
      <Typography variant="h6" sx={{ display: "flex", alignItems: "center", mb: 2 }}>
        <InfoIcon sx={{ mr: 1, color: "primary.main" }} /> {ruleData.label}
      </Typography>
      {Array.isArray(ruleData.details.charges) ? (
        ruleData.details.charges.map((charge, idx) => (
          <Grid container key={idx} sx={{ mb: 1 }}>
            <Grid item md={6}>
              <Typography variant="body1" color="textSecondary">
                Amount
              </Typography>
              <Typography variant="h6">
                ₹ {charge.value} fixed
              </Typography>
            </Grid>

          </Grid>
        ))
      ) : (
        <>
          <Typography variant="body1" color="textSecondary">
            Amount
          </Typography>
          <Typography variant="h6">
            ₹{ruleData.details.charges.amount} {ruleData.details.charges.unit || ''}
          </Typography>
          <Typography variant="body1" color="textSecondary">
            of {ruleData.details.charges.description || 'Maintenance Fee'}
          </Typography>
        </>
      )}
    </Box>
  )
}

const NoDataSection = () => (
  <Box sx={{ textAlign: 'center', py: 4 }}>
    <Typography variant='h6' color='textSecondary'>
      No specific rules to display.
    </Typography>
  </Box>
)

const PreviewRuleCard = () => {
  const pathname = usePathname()
  const router = useRouter()

  const id = (() => {
    const extractedId = pathname.split('/').pop()

    if (!extractedId || isNaN(Number(extractedId))) {
      console.warn('Invalid ID extracted from URL')

      return null
    }


    return extractedId
  })()

  const { data, loading, error } = useFetchIncomeRuleDetails(id)

  if (loading)
    return (
      <Box display='flex' justifyContent='center' alignItems='center' height='100vh'>
        <CircularProgress />
      </Box>
    )
  if (error)
    return (
      <Box display='flex' justifyContent='center' alignItems='center' height='100vh'>
        <Alert severity='error'>Error: {error}</Alert>
      </Box>
    )

  const {
    arrIncomeRuleDetails,
    parkingruledetails,
    rule,
    arrFloorBasedRule,
    standard_none,
    standard_fixed,
    standard_simple_noc,
    standard_noc,
    rule_data
  } = data

  const ownerDefaultVehicle =
    parkingruledetails?.filter(item => item.member_type === 'owner' && item.vehicle_number === 1) || []

  const tenantDefaultVehicle =
    parkingruledetails?.filter(item => item.member_type === 'tenant' && item.vehicle_number === 1) || []

  const ownerSecondVehicle =
    parkingruledetails?.filter(item => item.member_type === 'owner' && item.vehicle_number === 2) || []

  const tenantSecondVehicle =
    parkingruledetails?.filter(item => item.member_type === 'tenant' && item.vehicle_number === 2) || []

  return (
    <>
      <Card sx={{ maxWidth: 1000, margin: '20px auto', padding: '20px' }}>
        <Box display='flex' alignItems='center' justifyContent='space-between' marginBottom='16px'>
          <Box display='flex' alignItems='center'>
            <DescriptionIcon sx={{ fontSize: 40, color: 'blue', marginRight: '8px' }} />
            <Typography variant='h5'>Preview Rule</Typography>
          </Box>
          <Button
            variant='contained'
            color='secondary'
            startIcon={<ArrowBackIcon />}
            onClick={() => {
              router.back(); // Use Next.js router for navigation
            }}
          >
            Back
          </Button>
        </Box>
        <CardContent>
          <Grid container spacing={2} sx={{ mb: 4 }}>
            <Grid item xs={6}>
              <Typography variant='subtitle2' color='textSecondary'>
                Income Account
              </Typography>
              <Typography variant='body1'>{arrIncomeRuleDetails?.income_account_name || 'N/A'}</Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant='subtitle2' color='textSecondary'>
                Create Invoicing Rule for
              </Typography>
              <Typography variant='body1'>{arrIncomeRuleDetails?.unit_type || 'N/A'}</Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant='subtitle2' color='textSecondary'>
                Rate Effective From
              </Typography>
              <Typography variant='body1'>{arrIncomeRuleDetails?.effective_date || 'N/A'}</Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant='subtitle2' color='textSecondary'>
                Late Payment Interest Applicable?
              </Typography>
              <Typography variant='body1'>
                {formatBoolean(arrIncomeRuleDetails?.apply_late_payment_interest)}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Card sx={{ maxWidth: 1000, margin: '20px auto', padding: '20px' }}>
        <CardContent>
          {rule_data?.details?.type === 'flat_fee' || rule_data?.details?.type === 'unknown' ? (
            <FlatFeeSection ruleData={rule_data} />
          ) : rule_data?.details?.type === 'per_sqft' || rule_data?.details?.type === "persqft" ? (
            <PerSqftFeeSection ruleData={rule_data} />
          ) : rule_data?.details?.type === 'inlet' ? (
            <InletFeeSection ruleData={rule_data} />
          ) : rule_data?.details?.type === 'simple' ? (
            <SimpleNOCSection ruleData={rule_data} />
          ) :
            rule_data?.details?.type === 'percentage' ? (
              <OtherChargesSection ruleData={rule_data} />
            ) : rule_data?.details?.type === 'fixed' ? (
              <FixedChargesSection ruleData={rule_data} />
            ) :
              rule === 'floorbased' && Array.isArray(arrFloorBasedRule) && arrFloorBasedRule.length > 0 ? (
                <FloorBasedRuleSection title='Floor Based Rule' details={arrFloorBasedRule || []} />
              ) : rule === 'parking' ? (
                <Box>
                  <Typography variant='h5' sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <DirectionsCarIcon sx={{ mr: 1 }} /> Parking Rule
                  </Typography>
                  <Typography variant='h6'>Default Vehicle</Typography>
                  {ownerDefaultVehicle.length > 0 && (
                    <ParkingRuleSection title='Owner Parking' details={ownerDefaultVehicle} />
                  )}
                  {tenantDefaultVehicle.length > 0 && (
                    <ParkingRuleSection title='Tenant Parking' details={tenantDefaultVehicle} />
                  )}

                  <Typography variant='h6'>2nd Vehicle</Typography>
                  {ownerSecondVehicle.length > 0 && (
                    <ParkingRuleSection title='Owner Parking' details={ownerSecondVehicle} />
                  )}
                  {tenantSecondVehicle.length > 0 && (
                    <ParkingRuleSection title='Tenant Parking' details={tenantSecondVehicle} />
                  )}

                </Box>
              ) : rule === 'standard_none' && standard_none ? (
                <StandardNoneRuleSection details={standard_none} />
              ) : rule === 'standard_fixed' && standard_fixed ? (
                <StandardFixedSection details={standard_fixed} />
              ) : rule === 'standard_simple_noc' && standard_simple_noc ? (
                <OtherChargesSection ruleData={rule_data} />
              ) : rule === 'standard_noc' && standard_noc ? (
                <StandardNOCSection details={standard_noc} />
              ) : (
                <NoDataSection />
              )}
        </CardContent>
      </Card>
    </>
  )
}

export default PreviewRuleCard
