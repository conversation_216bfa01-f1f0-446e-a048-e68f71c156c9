import { isValidElement } from "react"

import { Dialog, DialogContentText, DialogContent } from "@mui/material"

type MyModelProps = {
  open: boolean
  onClose: () => void
  children?: React.ReactNode
  title?: string
}

const CustomModel: React.FC<MyModelProps> = ({ open, onClose, children }) => {
  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth='md'>
      <span
        style={{
          position: "absolute",
          top: "32px",
          right: "50px",
          cursor: "pointer",
          fontSize: "1.5rem",
          zIndex: 100
        }}
        onClick={onClose}
      >
        <i className='md-x' />
      </span>
      {isValidElement(children) ? (
        <DialogContent>{children}</DialogContent>
      ) : (
        <DialogContentText
          sx={{
            padding: "1rem"
          }}
        >
          {children}
        </DialogContentText>
      )}
    </Dialog>
  )
}

export default CustomModel
