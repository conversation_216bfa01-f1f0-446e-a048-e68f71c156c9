export const mockComplaintData = {
  id: '123',
  ticket_no: 'TKT-123',
  status: 'open',
  priority: 'high',
  subject: 'Water Leakage in Bathroom',
  help_topic: 'Plumbing complaints',
  created_at: '2023-06-15T10:30:00Z',
  member: {
    id: '456',
    name: '<PERSON><PERSON><PERSON>',
    email: '<PERSON><PERSON><PERSON>@example.com',
    phone: '9876543210',
    unit_no: 'A-101',
  },
  messages: [
    {
      id: '789',
      message: 'There is water leaking from the bathroom ceiling. It seems to be coming from the apartment above. Please send someone to check it urgently.',
      created_at: '2023-06-15T10:30:00Z',
      sender: {
        id: '456',
        name: '<PERSON><PERSON><PERSON>',
        role: 'member',
      },
    },
    {
      id: '790',
      message: 'Thank you for reporting this issue. We will send our maintenance team to check the problem today. Please provide a convenient time for the visit.',
      created_at: '2023-06-15T11:15:00Z',
      sender: {
        id: '001',
        name: 'Admin <PERSON>',
        role: 'admin',
      },
    },
    {
      id: '791',
      message: 'I will be available after 2 PM today. Please ask the maintenance team to call me before coming.',
      created_at: '2023-06-15T11:45:00Z',
      sender: {
        id: '456',
        name: '<PERSON><PERSON>j <PERSON>',
        role: 'member',
      },
    },
  ],
};
