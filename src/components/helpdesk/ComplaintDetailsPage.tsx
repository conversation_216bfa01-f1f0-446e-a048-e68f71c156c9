"use client";

import React, { useState, useEffect } from 'react';

import { useRouter, usePathname } from 'next/navigation';

import { useTheme } from '@mui/material/styles';

// import axios from 'axios'; // Uncomment when using real API
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  CircularProgress,
  Chip,
  Card,
  CardContent,
  CardHeader,
} from '@mui/material';

import { Icon } from '@iconify/react/dist/iconify.js';
import { toast } from 'react-toastify';

import MessageThread from './MessageThread';
import MemberDetails from './MemberDetails';
import ReplyForm from './ReplyForm';
import { mockComplaintData } from './mockData';

interface ComplaintDetails {

  id: string;
  ticket_no: string;
  status: string;
  priority: string;
  subject: string;
  help_topic: string;
  created_at: string;
  member: {
    id: string;
    name: string;
    email: string;
    phone: string;
    profile_pic?: string;
    unit_no?: string;
  };
  messages: Array<{
    id: string;
    message: string;
    created_at: string;
    sender: {
      id: string;
      name: string;
      role: string;
    };
    attachments?: Array<{
      id: string;
      name: string;
      url: string;
    }>;
  }>;
}

const ComplaintDetailsPage = () => {
  const router = useRouter();
  const pathname = usePathname();
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [complaintDetails, setComplaintDetails] = useState<ComplaintDetails | null>(null);
  const [replyStatus, setReplyStatus] = useState('open');

  // Extract ticket ID from URL
  const ticketId = pathname.split('/').pop();

  useEffect(() => {
    // For testing purposes, use mock data
    const fetchComplaintDetails = async () => {
      try {
        setLoading(true);

        // In a real app, uncomment this code to fetch from API
        // const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/helpdesk/view_issue/${ticketId}`, {
        //   withCredentials: true,
        // });
        //
        // if (response.data.status === 'success') {
        //   setComplaintDetails(response.data.data);
        // } else {
        //   toast.error('Failed to fetch complaint details');
        // }

        // Using mock data for testing
        setTimeout(() => {
          setComplaintDetails(mockComplaintData);
          setLoading(false);
        }, 1000); // Simulate network delay
      } catch (error) {
        console.error('Error fetching complaint details:', error);
        toast.error('Error fetching complaint details');
        setLoading(false);
      }
    };

    if (ticketId) {
      fetchComplaintDetails();
    }
  }, [ticketId]);

  const handlePostReply = async (replyData: { message: string; status: string }) => {
    try {
      // For testing purposes, simulate API call
      // In a real app, uncomment this code to post to API
      // const response = await axios.post(
      //   `${process.env.NEXT_PUBLIC_API_URL}/admin/helpdesk/reply/${ticketId}`,
      //   {
      //     message: replyData.message,
      //     status: replyData.status
      //   },
      //   {
      //     withCredentials: true,
      //   }
      // );
      //
      // if (response.data.status === 'success') {
      //   toast.success('Reply posted successfully');
      //   // Refresh complaint details
      //   const updatedResponse = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/helpdesk/view_issue/${ticketId}`, {
      //     withCredentials: true,
      //   });
      //
      //   if (updatedResponse.data.status === 'success') {
      //     setComplaintDetails(updatedResponse.data.data);
      //   }
      // } else {
      //   toast.error('Failed to post reply');
      // }

      // Simulate successful API response
      toast.success('Reply posted successfully');

      // Add the new message to the existing messages
      if (complaintDetails) {
        const newMessage = {
          id: `msg-${Date.now()}`,
          message: replyData.message,
          created_at: new Date().toISOString(),
          sender: {
            id: 'admin-1',
            name: 'Admin Staff',
            role: 'admin',
          },
        };

        setComplaintDetails({
          ...complaintDetails,
          status: replyData.status,
          messages: [...complaintDetails.messages, newMessage],
        });
      }
    } catch (error) {
      console.error('Error posting reply:', error);
      toast.error('Error posting reply');
    }
  };

  const handleChangeStatus = async (newStatus: string) => {
    try {
      // For testing purposes, simulate API call
      // In a real app, uncomment this code to post to API
      // const response = await axios.post(
      //   `${process.env.NEXT_PUBLIC_API_URL}/admin/helpdesk/change_status/${ticketId}`,
      //   {
      //     status: newStatus
      //   },
      //   {
      //     withCredentials: true,
      //   }
      // );
      //
      // if (response.data.status === 'success') {
      //   toast.success(`Status changed to ${newStatus}`);
      //   // Update local state
      //   if (complaintDetails) {
      //     setComplaintDetails({
      //       ...complaintDetails,
      //       status: newStatus
      //     });
      //   }
      // } else {
      //   toast.error('Failed to change status');
      // }

      // Simulate successful API response
      toast.success(`Status changed to ${newStatus}`);

      // Update local state
      if (complaintDetails) {
        setComplaintDetails({
          ...complaintDetails,
          status: newStatus
        });
      }
    } catch (error) {
      console.error('Error changing status:', error);
      toast.error('Error changing status');
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (!complaintDetails) {
    return (
      <Box p={3}>
        <Typography variant="h6" color="error">
          Complaint details not found
        </Typography>
        <Button
          variant="contained"
          color="primary"
          sx={{ mt: 2 }}
          onClick={() => router.push('/admin/society/helpdesk/issues')}
        >
          Back to Complaints
        </Button>
      </Box>
    );
  }

  return (
    <Paper elevation={0} sx={{
      p: 0,
      bgcolor: 'transparent',
      position: 'relative',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: theme => theme.palette.mode === 'dark'
          ? 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23ffffff\' fill-opacity=\'0.03\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")'
          : 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23000000\' fill-opacity=\'0.03\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")',
        backgroundSize: '180px 180px',
        opacity: 0.5,
        zIndex: 0,
        pointerEvents: 'none'
      }
    }}>
      {/* Header with breadcrumbs and actions */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
        px: 3,
        py: 2,
        position: 'relative',
        zIndex: 1,
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: '-10px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '60%',
          height: '1px',
          background: theme => `linear-gradient(90deg, ${theme.palette.primary.light}00 0%, ${theme.palette.primary.main}40 50%, ${theme.palette.primary.light}00 100%)`,
        }
      }}>
        <Box>
          <Typography variant="h6" component="h1" sx={{
            fontWeight: 700,
            background: theme => theme.palette.mode === 'dark'
              ? `linear-gradient(90deg, #fff 0%, rgba(255,255,255,0.7) 100%)`
              : `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: '-4px',
              left: '0',
              width: '40px',
              height: '2px',
              background: theme => `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light}80 100%)`,
              borderRadius: '2px',
            }
          }}>
            Complaint Details
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<Icon icon="ri-reply-line" />}
            onClick={() => document.getElementById('reply-form')?.scrollIntoView({ behavior: 'smooth' })}
          >
            Post Reply
          </Button>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<Icon icon="ri-settings-line" />}
            onClick={() => handleChangeStatus('resolved')}
          >
            Change Status
          </Button>
          <Button
            variant="outlined"
            color="secondary"
            startIcon={<Icon icon="ri-printer-line" />}
          >
            Print
          </Button>
        </Box>
      </Box>

      <Grid container spacing={2}>
        {/* Main content area */}
        <Grid item xs={12} md={8}>
          {/* Ticket info */}
          <Card sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: theme => theme.palette.mode === 'dark'
              ? '0 8px 32px 0 rgba(31, 38, 135, 0.37)'
              : '0 8px 32px 0 rgba(37, 99, 235, 0.1)',
            background: theme => theme.palette.mode === 'dark'
              ? 'rgba(17, 25, 40, 0.75)'
              : 'rgba(255, 255, 255, 0.85)',
            backdropFilter: 'blur(16px)',
            border: theme => theme.palette.mode === 'dark'
              ? '1px solid rgba(255, 255, 255, 0.125)'
              : '1px solid rgba(37, 99, 235, 0.1)',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: theme => theme.palette.mode === 'dark'
                ? `0 12px 28px 0 rgba(${theme.palette.primary.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.4)`
                : `0 12px 28px 0 rgba(${theme.palette.primary.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.2)`,
              border: theme => theme.palette.mode === 'dark'
                ? `1px solid rgba(${theme.palette.primary.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.3)`
                : `1px solid rgba(${theme.palette.primary.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.15)`
            }
          }}>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Icon icon="ri-ticket-line" style={{ marginRight: '8px', fontSize: '20px' }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    Ticket #{complaintDetails.ticket_no}
                  </Typography>
                </Box>
              }
              action={
                <Chip
                  label={complaintDetails.status.toUpperCase()}
                  color={
                    complaintDetails.status === 'open' ? 'info' :
                    complaintDetails.status === 'resolved' ? 'success' :
                    complaintDetails.status === 'on hold' ? 'warning' : 'default'
                  }
                  size="small"
                />
              }
              sx={{
                bgcolor: theme => theme.palette.mode === 'dark'
                  ? 'rgba(255,255,255,0.05)'
                  : 'rgba(37,99,235,0.05)',
                borderBottom: theme => theme.palette.mode === 'dark'
                  ? '1px solid rgba(255,255,255,0.1)'
                  : '1px solid rgba(37,99,235,0.1)',
                px: 3,
                py: 2,
                '& .MuiCardHeader-title': {
                  fontWeight: 600,
                  fontSize: '1rem',
                  color: theme => theme.palette.mode === 'dark'
                    ? theme.palette.primary.light
                    : theme.palette.primary.main
                }
              }}
            />
            <CardContent sx={{ p: 3 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body2" color="text.secondary">
                    Help Topic
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {complaintDetails.help_topic}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body2" color="text.secondary">
                    Priority
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {complaintDetails.priority}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body2" color="text.secondary">
                    Date/Time
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {new Date(complaintDetails.created_at).toLocaleString()}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Communication thread */}
          <Card sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: theme => theme.palette.mode === 'dark'
              ? '0 8px 32px 0 rgba(31, 38, 135, 0.37)'
              : '0 8px 32px 0 rgba(37, 99, 235, 0.1)',
            background: theme => theme.palette.mode === 'dark'
              ? 'rgba(17, 25, 40, 0.75)'
              : 'rgba(255, 255, 255, 0.85)',
            backdropFilter: 'blur(16px)',
            border: theme => theme.palette.mode === 'dark'
              ? '1px solid rgba(255, 255, 255, 0.125)'
              : '1px solid rgba(37, 99, 235, 0.1)',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: theme => theme.palette.mode === 'dark'
                ? `0 12px 28px 0 rgba(${theme.palette.info.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.4)`
                : `0 12px 28px 0 rgba(${theme.palette.info.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.2)`,
              border: theme => theme.palette.mode === 'dark'
                ? `1px solid rgba(${theme.palette.info.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.3)`
                : `1px solid rgba(${theme.palette.info.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.15)`
            }
          }}>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Icon icon="ri-chat-3-line" style={{ marginRight: '8px', fontSize: '20px' }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    Communication
                  </Typography>
                </Box>
              }
              sx={{
                bgcolor: theme => theme.palette.mode === 'dark'
                  ? 'rgba(255,255,255,0.05)'
                  : 'rgba(0,127,255,0.05)',
                borderBottom: theme => theme.palette.mode === 'dark'
                  ? '1px solid rgba(255,255,255,0.1)'
                  : '1px solid rgba(0,127,255,0.1)',
                px: 3,
                py: 2,
                '& .MuiCardHeader-title': {
                  fontWeight: 600,
                  fontSize: '1rem',
                  color: theme => theme.palette.mode === 'dark'
                    ? theme.palette.info.light
                    : theme.palette.info.main
                }
              }}
            />
            <CardContent sx={{ p: 0 }}>
              <MessageThread messages={complaintDetails.messages} />
            </CardContent>
          </Card>

          {/* Reply form */}
          <Card sx={{
            borderRadius: 2,
            boxShadow: theme => theme.palette.mode === 'dark'
              ? '0 8px 32px 0 rgba(31, 38, 135, 0.37)'
              : '0 8px 32px 0 rgba(37, 99, 235, 0.1)',
            background: theme => theme.palette.mode === 'dark'
              ? 'rgba(17, 25, 40, 0.75)'
              : 'rgba(255, 255, 255, 0.85)',
            backdropFilter: 'blur(16px)',
            border: theme => theme.palette.mode === 'dark'
              ? '1px solid rgba(255, 255, 255, 0.125)'
              : '1px solid rgba(37, 99, 235, 0.1)',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: theme => theme.palette.mode === 'dark'
                ? `0 12px 28px 0 rgba(${theme.palette.success.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.4)`
                : `0 12px 28px 0 rgba(${theme.palette.success.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.2)`,
              border: theme => theme.palette.mode === 'dark'
                ? `1px solid rgba(${theme.palette.success.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.3)`
                : `1px solid rgba(${theme.palette.success.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.15)`
            }
          }} id="reply-form">
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Icon icon="ri-reply-line" style={{ marginRight: '8px', fontSize: '20px' }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    Post Reply
                  </Typography>
                </Box>
              }
              sx={{
                bgcolor: theme => theme.palette.mode === 'dark'
                  ? 'rgba(255,255,255,0.05)'
                  : 'rgba(114,225,40,0.05)',
                borderBottom: theme => theme.palette.mode === 'dark'
                  ? '1px solid rgba(255,255,255,0.1)'
                  : '1px solid rgba(114,225,40,0.1)',
                px: 3,
                py: 2,
                '& .MuiCardHeader-title': {
                  fontWeight: 600,
                  fontSize: '1rem',
                  color: theme => theme.palette.mode === 'dark'
                    ? theme.palette.success.light
                    : theme.palette.success.main
                }
              }}
            />
            <CardContent sx={{ p: 3 }}>
              <ReplyForm
                onSubmit={handlePostReply}
                initialStatus={replyStatus}
                onStatusChange={setReplyStatus}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <MemberDetails
            member={complaintDetails.member}
            isDarkMode={theme.palette.mode === 'dark'}
          />

          {/* Job Cards section */}
          <Card sx={{
            mt: 3,
            borderRadius: 2,
            boxShadow: theme => theme.palette.mode === 'dark'
              ? '0 8px 32px 0 rgba(31, 38, 135, 0.37)'
              : '0 8px 32px 0 rgba(37, 99, 235, 0.1)',
            background: theme => theme.palette.mode === 'dark'
              ? 'rgba(17, 25, 40, 0.75)'
              : 'rgba(255, 255, 255, 0.85)',
            backdropFilter: 'blur(16px)',
            border: theme => theme.palette.mode === 'dark'
              ? '1px solid rgba(255, 255, 255, 0.125)'
              : '1px solid rgba(37, 99, 235, 0.1)',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: theme => theme.palette.mode === 'dark'
                ? `0 12px 28px 0 rgba(${theme.palette.warning.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.4)`
                : `0 12px 28px 0 rgba(${theme.palette.warning.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.2)`,
              border: theme => theme.palette.mode === 'dark'
                ? `1px solid rgba(${theme.palette.warning.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.3)`
                : `1px solid rgba(${theme.palette.warning.main.replace('#', '').match(/../g).map(hex => parseInt(hex, 16)).join(', ')}, 0.15)`
            }
          }}>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    Job Cards
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    size="small"
                  >
                    Add Job Card
                  </Button>
                </Box>
              }
              sx={{
                bgcolor: theme => theme.palette.mode === 'dark'
                  ? 'rgba(255,255,255,0.05)'
                  : 'rgba(255,180,0,0.05)',
                borderBottom: theme => theme.palette.mode === 'dark'
                  ? '1px solid rgba(255,255,255,0.1)'
                  : '1px solid rgba(255,180,0,0.1)',
                px: 3,
                py: 2,
                '& .MuiCardHeader-title': {
                  fontWeight: 600,
                  fontSize: '1rem',
                  color: theme => theme.palette.mode === 'dark'
                    ? theme.palette.warning.light
                    : theme.palette.warning.main
                }
              }}
            />
            <CardContent sx={{ p: 3 }}>
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                No job cards found for this complaint
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default ComplaintDetailsPage;
