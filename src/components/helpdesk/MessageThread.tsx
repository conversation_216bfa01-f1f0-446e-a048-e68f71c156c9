import React from 'react';

import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Avatar,
  Chip,
  Link,
  IconButton,
  Tooltip
} from '@mui/material';
import { Icon } from '@iconify/react/dist/iconify.js';

interface Attachment {
  id: string;
  name: string;
  url: string;
}

interface Sender {
  id: string;
  name: string;
  role: string;
}

interface Message {
  id: string;
  message: string;
  created_at: string;
  sender: Sender;
  attachments?: Attachment[];
}

interface MessageThreadProps {
  messages: Message[];
}

const MessageThread: React.FC<MessageThreadProps> = ({ messages }) => {
  if (!messages || messages.length === 0) {
    return (
      <Box sx={{ px: 3, py: 4, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          No messages found
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 0 }}>
      {messages.map((message, index) => (
        <Box key={message.id} sx={{
          px: 3,
          py: 3.5,
          borderBottom: index < messages.length - 1 ? '1px solid rgba(0,0,0,0.1)' : 'none',
          bgcolor: index % 2 === 0 ? 'rgba(0,0,0,0.01)' : 'transparent'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <Avatar
              sx={{
                width: 40,
                height: 40,
                mr: 2,
                bgcolor: message.sender.role === 'admin' ? 'primary.main' : 'secondary.main'
              }}
            >
              {message.sender.name.charAt(0).toUpperCase()}
            </Avatar>
            <Box sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  {message.sender.name}
                  {message.sender.role === 'admin' && (
                    <Chip
                      label="Staff"
                      size="small"
                      color="primary"
                      sx={{ ml: 1, height: 20, fontSize: '0.7rem' }}
                    />
                  )}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Icon icon="ri-time-line" style={{ fontSize: '14px', marginRight: '4px', opacity: 0.7 }} />
                  <Typography variant="caption" color="text.secondary">
                    {new Date(message.created_at).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                {message.message}
              </Typography>

              {/* Attachments if any */}
              {message.attachments && message.attachments.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                    Attachments:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {message.attachments.map(attachment => (
                      <Chip
                        key={attachment.id}
                        icon={<Icon icon="ri-file-line" />}
                        label={attachment.name}
                        component={Link}
                        href={attachment.url}
                        target="_blank"
                        clickable
                        size="small"
                        sx={{
                          bgcolor: 'background.paper',
                          border: '1px solid rgba(0,0,0,0.1)',
                          '&:hover': {
                            bgcolor: 'action.hover'
                          }
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
            <Tooltip title="Reply to this message">
              <IconButton size="small" sx={{ mr: 1 }}>
                <Icon icon="ri-reply-line" fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Forward this message">
              <IconButton size="small">
                <Icon icon="ri-share-forward-line" fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      ))}
    </Box>
  );
};

export default MessageThread;
