import React from 'react';

import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Avatar,
  Divider,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>
} from '@mui/material';
import { Icon } from '@iconify/react/dist/iconify.js';

interface Member {
  id: string;
  name: string;
  email: string;
  phone: string;
  profile_pic?: string;
  unit_no?: string;
}

interface MemberDetailsProps {
  member: Member;
  isDarkMode?: boolean;
}

const MemberDetails: React.FC<MemberDetailsProps> = ({ member, isDarkMode = false }) => {
  return (
    <Card sx={{
      borderRadius: 2,
      boxShadow: isDarkMode
        ? '0 8px 32px 0 rgba(31, 38, 135, 0.37)'
        : '0 8px 32px 0 rgba(37, 99, 235, 0.1)',
      background: isDarkMode
        ? 'rgba(17, 25, 40, 0.75)'
        : 'rgba(255, 255, 255, 0.85)',
      backdropFilter: 'blur(16px)',
      border: isDarkMode
        ? '1px solid rgba(255, 255, 255, 0.125)'
        : '1px solid rgba(37, 99, 235, 0.1)',
      transition: 'all 0.3s ease',
      '&:hover': {
        transform: 'translateY(-5px)',
        boxShadow: isDarkMode
          ? '0 12px 28px 0 rgba(102, 108, 255, 0.4)'
          : '0 12px 28px 0 rgba(102, 108, 255, 0.2)',
        border: isDarkMode
          ? '1px solid rgba(102, 108, 255, 0.3)'
          : '1px solid rgba(102, 108, 255, 0.15)'
      }
    }}>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Icon icon="ri-user-line" style={{ marginRight: '8px', fontSize: '20px' }} />
            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
              Member Details
            </Typography>
          </Box>
        }
        sx={{
          bgcolor: isDarkMode
            ? 'rgba(255,255,255,0.05)'
            : 'rgba(102,108,255,0.05)',
          borderBottom: isDarkMode
            ? '1px solid rgba(255,255,255,0.1)'
            : '1px solid rgba(102,108,255,0.1)',
          px: 3,
          py: 2,
          '& .MuiCardHeader-title': {
            fontWeight: 600,
            fontSize: '1rem',
            color: isDarkMode
              ? 'rgba(255,255,255,0.9)'
              : 'rgba(102,108,255,0.9)'
          }
        }}
      />
      <CardContent sx={{ p: 0 }}>
        <Box sx={{ px: 3, py: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Avatar
            src={member.profile_pic}
            alt={member.name}
            sx={{
              width: 80,
              height: 80,
              mb: 2,
              border: '3px solid #fff',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}
          >
            {member.name.charAt(0).toUpperCase()}
          </Avatar>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
            {member.name}
          </Typography>
          <Chip
            label="Primary"
            color="primary"
            size="small"
            sx={{ mb: 2 }}
          />
        </Box>

        <Divider />

        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Icon icon="ri-mail-line" style={{ marginRight: '8px', fontSize: '18px', opacity: 0.7 }} />
            <Typography variant="body2">
              <Link href={`mailto:${member.email}`} underline="hover" color="inherit">
                {member.email}
              </Link>
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Icon icon="ri-phone-line" style={{ marginRight: '8px', fontSize: '18px', opacity: 0.7 }} />
            <Typography variant="body2">
              <Link href={`tel:${member.phone}`} underline="hover" color="inherit">
                {member.phone}
              </Link>
            </Typography>
          </Box>

          {member.unit_no && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Icon icon="ri-home-4-line" style={{ marginRight: '8px', fontSize: '18px', opacity: 0.7 }} />
              <Typography variant="body2">
                Unit: {member.unit_no}
              </Typography>
            </Box>
          )}
        </Box>

        <Divider />

        <Box sx={{ p: 3 }}>
          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
            Attachments
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
            <Button
              variant="outlined"
              size="small"
              startIcon={<Icon icon="ri-file-upload-line" />}
              fullWidth
            >
              Add File
            </Button>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default MemberDetails;
