import React, { useState } from 'react';

import {
  Box,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Divider,
  IconButton,
  Tooltip,
  Paper
} from '@mui/material';
import { Icon } from '@iconify/react/dist/iconify.js';

interface ReplyFormProps {
  onSubmit: (data: { message: string; status: string }) => void;
  initialStatus?: string;
  onStatusChange?: (status: string) => void;
}

const ReplyForm: React.FC<ReplyFormProps> = ({
  onSubmit,
  initialStatus = 'open',
  onStatusChange
}) => {
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState(initialStatus);
  const [cannedResponse, setCannedResponse] = useState('');

  const handleStatusChange = (event: React.ChangeEvent<{ value: unknown }>) => {

    const newStatus = event.target.value as string;

    setStatus(newStatus);

    if (onStatusChange) {

      onStatusChange(newStatus);
    }
  };

  const handleCannedResponseChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    
    const selectedResponse = event.target.value as string;

    setCannedResponse(selectedResponse);

    // Add the canned response to the message
    if (selectedResponse) {
      // Predefined responses - in a real app, these would come from the API
      const responses: Record<string, string> = {
        'original_message': 'Thank you for your message. We are looking into this issue and will get back to you shortly.',
        'typing': 'We are currently working on resolving your issue. Please allow us some time to investigate further.',
      };

      setMessage(responses[selectedResponse] || '');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {

    e.preventDefault();
    
    if (message.trim()) {
      onSubmit({
        message,
        status
      });
      setMessage('');
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ px: 0.5 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="body2" sx={{ mb: 1 }}>
          To:
        </Typography>
        <TextField
          fullWidth
          disabled
          value="Neeraj Sharma (PR101)"
          size="small"
          InputProps={{
            startAdornment: <Icon icon="ri-user-line" style={{ marginRight: '8px', opacity: 0.7 }} />,
          }}
        />
      </Box>

      <Box sx={{ mb: 3 }}>
        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
          <InputLabel id="canned-response-label">Canned Response</InputLabel>
          <Select
            labelId="canned-response-label"
            id="canned-response"
            value={cannedResponse}
            label="Canned Response"
            onChange={handleCannedResponseChange}
          >
            <MenuItem value="">
              <em>Select a response</em>
            </MenuItem>
            <MenuItem value="original_message">Original Message</MenuItem>
            <MenuItem value="typing">Typing</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="body2" sx={{ mb: 1 }}>
          Message Text:
        </Typography>
        <Paper
          variant="outlined"
          sx={{
            p: 0.5,
            border: '1px solid rgba(0,0,0,0.2)',
            borderRadius: 1
          }}
        >
          <Box sx={{
            display: 'flex',
            flexWrap: 'wrap',
            borderBottom: '1px solid rgba(0,0,0,0.1)',
            p: 1
          }}>
            {['bold', 'italic', 'underline', 'strikethrough'].map((format) => (
              <Tooltip key={format} title={format.charAt(0).toUpperCase() + format.slice(1)}>
                <IconButton size="small">
                  <Icon icon={`ri-${format}-line`} fontSize="small" />
                </IconButton>
              </Tooltip>
            ))}
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            {['list-ordered', 'list-unordered', 'indent-increase', 'indent-decrease'].map((format) => (
              <Tooltip key={format} title={format.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}>
                <IconButton size="small">
                  <Icon icon={`ri-${format}-line`} fontSize="small" />
                </IconButton>
              </Tooltip>
            ))}
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            {['link', 'image-add', 'attachment-2'].map((format) => (
              <Tooltip key={format} title={format.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}>
                <IconButton size="small">
                  <Icon icon={`ri-${format}-line`} fontSize="small" />
                </IconButton>
              </Tooltip>
            ))}
          </Box>
          <TextField
            multiline
            fullWidth
            rows={6}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type your reply here..."
            variant="standard"
            InputProps={{
              disableUnderline: true,
              sx: { p: 1.5 }
            }}
          />
        </Paper>
      </Box>

      <Box sx={{ mb: 3 }}>
        <FormControl fullWidth size="small">
          <InputLabel id="status-label">Status</InputLabel>
          <Select
            labelId="status-label"
            id="status"
            value={status}
            label="Status"
            onChange={handleStatusChange}
          >
            <MenuItem value="open">Open</MenuItem>
            <MenuItem value="resolved">Resolved</MenuItem>
            <MenuItem value="closed">Closed</MenuItem>
            <MenuItem value="on hold">On Hold</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Button
          variant="outlined"
          color="inherit"
          startIcon={<Icon icon="ri-attachment-2-line" />}
        >
          Attach File
        </Button>
        <Box>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={<Icon icon="ri-send-plane-line" />}
            sx={{ ml: 1 }}
          >
            Post Reply
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default ReplyForm;
