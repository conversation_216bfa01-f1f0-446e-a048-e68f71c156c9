"use client";

import React from 'react';

import dynamic from 'next/dynamic';

import { Box, Skeleton } from '@mui/material';

// Dynamically import the client component
const ComplaintDetailsPage = dynamic(() => import('@/components/helpdesk/ComplaintDetailsPage'), {
  ssr: false,
  loading: () => (
    <Box sx={{ p: 3 }}>
      <Skeleton variant="text" width="200px" height={40} sx={{ mb: 2 }} />
      <Skeleton variant="rectangular" width="100%" height={100} sx={{ mb: 3, borderRadius: 2 }} />
      <Skeleton variant="rectangular" width="100%" height={300} sx={{ mb: 3, borderRadius: 2 }} />
      <Skeleton variant="rectangular" width="100%" height={200} sx={{ borderRadius: 2 }} />
    </Box>
  )
});

const ComplaintDetailsClient = () => {
  return <ComplaintDetailsPage />;
};

export default ComplaintDetailsClient;
