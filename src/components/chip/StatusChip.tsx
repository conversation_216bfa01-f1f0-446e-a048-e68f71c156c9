import { Chip } from '@mui/material';

type StatusChipProps = {
    status: number | string;
    fallbackLabel?: string; // Optional prop for customizing fallback label
};

// Define a type for allowed colors
type ChipColor = 'default' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';

const getStatusDetails = (status: number | string): { label: string; color: ChipColor } | null => {
    switch (status) {
        case "Approval Pending":
            return { label: 'Approval Pending', color: 'warning' };
        case 'Approved':
            return { label: 'Approved', color: 'success' };
        case 'Rejected':
            return { label: 'Rejected', color: 'error' };
        case 'In Progress':
            return { label: 'In Progress', color: 'primary' };
        case 'Reviewed':
            return { label: 'Reviewed', color: 'secondary' }
        case '1':
            return { label: 'Active', color: 'success' };
        case 'unpaid':
            return { label: 'Unpaid', color: 'error' };
        case 'paid':
            return { label: 'Paid', color: 'success' };
        case 'Partialpaid':
            return { label: 'Partial Paid', color: 'warning' };


        default:
            return null; // No match found, return null
    }
};

const StatusChip = ({ status, fallbackLabel = 'Default' }: StatusChipProps) => {
    const statusDetails = getStatusDetails(status);

    if (statusDetails) {
        // If status details are found, render the matched status chip
        const { label, color } = statusDetails;

        
return <Chip label={label} size="small" color={color} variant="outlined" sx={{ ml: 1 }} />;
    } else {
        // If no match, render the fallback chip
        return <Chip label={fallbackLabel} size="small" color="success" variant="outlined" sx={{ ml: 1 }} />;
    }
};

export default StatusChip;
