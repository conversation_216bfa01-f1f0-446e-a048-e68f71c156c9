import type { PopoverContent } from '../Types/types';

const popoverData: { [key: string]: PopoverContent[] } = {
    button1: [
        {
            id: 1,
            title: 'Dynamic Content Block 1',
            description: 'This is the first block of content for the popover.',
            additionalInfo: 'Additional information about block 1.',
        },
        {
            id: 2,
            title: 'Dynamic Content Block 2',
            description: 'Another block of dynamic content for the popover.',
            additionalInfo: 'Extra data for block 2.',
        },
    ],
    button2: [
        {
            id: 3,
            title: 'Dynamic Content Block 3',
            description: 'More content dynamically loaded here!',
            additionalInfo: 'Additional details about block 3.',
        },
        {
            id: 4,
            title: 'Dynamic Content Block 4',
            description: 'Yet another content block.',
        },
    ],
};

export default popoverData;
