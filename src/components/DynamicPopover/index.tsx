import React from 'react';

import { Popover, Box, Typography } from '@mui/material';

import type { PopoverContent } from './Types/types';

interface DynamicPopoverProps {
  isOpen: boolean;
  anchorEl: HTMLElement | null;
  onClose: () => void;
  contentItems: PopoverContent[];
  maxWidth?: number;
  anchorOrigin?: {
    vertical: 'top' | 'bottom' | 'center';
    horizontal: 'left' | 'right' | 'center';
  };
  transformOrigin?: {
    vertical: 'top' | 'bottom' | 'center';
    horizontal: 'left' | 'right' | 'center';
  };
}

const DynamicPopover: React.FC<DynamicPopoverProps> = ({
  isOpen,
  anchorEl,
  onClose,
  contentItems = [],
  maxWidth = 400,
  anchorOrigin = { vertical: 'bottom', horizontal: 'left' },
  transformOrigin = { vertical: 'top', horizontal: 'left' },
}) => {
  return (
    <Popover
      open={isOpen}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={anchorOrigin}
      transformOrigin={transformOrigin}
    >
      <Box p={2} maxWidth={maxWidth}>
        {contentItems.map((item) => (
          <Box key={item.id} mb={2}>
            {item.title && (
              <Typography variant="h6" color="primary">
                {item.title}
              </Typography>
            )}
            {item.description && (
              <Typography variant="body2" color="textSecondary">
                {item.description}
              </Typography>
            )}
            {item.additionalInfo && (
              <Typography variant="caption" color="textSecondary">
                {item.additionalInfo}
              </Typography>
            )}
          </Box>
        ))}
      </Box>
    </Popover>
  );
};

export default DynamicPopover;
