import { Popover, Box, Typography } from "@mui/material";





export const DynamicPopover = ({ isOpen, anchorEl, handleClose, popoverData }) => (
  <Popover
      open={isOpen}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
      }}
      transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
      }}
  >
      <Box p={2} maxWidth={400}>
          {
              popoverData.map((item) => (
                  <Box key={item.id} mb={2}>
                      {item.description && (
                          <Typography variant="body2" color="textSecondary">
                              {item.description}
                          </Typography>
                      )}
                  </Box>
              ))
          }
      </Box>
  </Popover>
);
