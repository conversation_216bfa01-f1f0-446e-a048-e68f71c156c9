import React from 'react';

import type { TypographyProps } from '@mui/material/Typography';
import { styled } from '@mui/material/styles';

const StyledKbd = styled('kbd')(({ theme }) => ({
  fontFamily: 'monospace',
  fontSize: '0.75rem',
  fontWeight: theme.typography.fontWeightBold,
  color: theme.palette.text.primary,
  border: `1px solid ${theme.palette.divider}`,
  borderBottomWidth: '3px', // thicker bottom border for a "key" look
  backgroundColor: theme.palette.background.paper,
  borderRadius: '0.35rem',
  padding: theme.spacing(0, 0.5), // 0 vertical, 0.5 spacing horizontal
  marginRight: theme.spacing(0.25),
}));

type KbdProps = TypographyProps;

const Kbd: React.FC<KbdProps> = ({ children, ...props }) => {
  return (
    <StyledKbd {...props}>
      {children}
    </StyledKbd>
  );
};

export default Kbd;
