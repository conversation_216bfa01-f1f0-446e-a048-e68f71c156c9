'use client';

import React, { useEffect, useState } from 'react';

import axios from 'axios';
import parse from 'html-react-parser';

const HtmlRendererDynamic = ({
  apiURL,
  requestParams = {},
  htmlKey = 'html'
}) => {
  const [error, setError] = useState(null);
  const [htmlContent, setHtmlContent] = useState('');
  const [cssContent, setCssContent] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!apiURL) {
      setError('API URL is missing');
      setLoading(false);
      
return;
    }

    const fetchHtml = async () => {
      try {
        const response = await axios.get(apiURL, {
          params: { ...requestParams },
          baseURL: process.env.NEXT_PUBLIC_API_URL,
          withCredentials: true,
        });

        console.log("API response:", response.data);

        let fullHtml = '';

        if (response.data?.data) {
          if (response.data.data[htmlKey]) {
            fullHtml = response.data.data[htmlKey];
          } else if (response.data.data.body) {
            fullHtml = response.data.data.body;
          }
        } else {
          fullHtml = response.data;
        }

        const styleTags = fullHtml.match(/<style[^>]*>([\s\S]*?)<\/style>/gi) || [];

        const extractedCss = styleTags
          .map((tag) => tag.replace(/<\/?style[^>]*>/g, ''))
          .join('\n');

        const bodyMatch = fullHtml.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
        const extractedBody = bodyMatch ? bodyMatch[1] : fullHtml;

        setHtmlContent(extractedBody);
        setCssContent(extractedCss);
        setLoading(false);
      } catch (err) {
        console.warn('Error fetching HTML:', err);
        setError(err.message || 'Error fetching content');
        setLoading(false);
      }
    };

    fetchHtml();
  }, [apiURL, requestParams, htmlKey]);

  const handleOpenInNewTab = () => {
    // This function is triggered by a user event (button click)
    const combinedHtml = `
      <html>
        <head>
          <meta charset="UTF-8" />
          <style>${cssContent}</style>
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `;

    const blob = new Blob([combinedHtml], { type: 'text/html' });
    const blobUrl = URL.createObjectURL(blob);

    // Open the new window
    const newWindow = window.open(blobUrl, '_blank');

    // Check if the window was successfully opened
    if (newWindow) {
      // Set window title after it is loaded
      newWindow.onload = () => {
        newWindow.document.title = 'Dynamic PDF Content';
      };
    } else {
      setError('Failed to open the new window. It may have been blocked.');
    }
  };

  const handlePrint = () => {
    // Print only the content, not the whole page
    const printWindow = window.open('', '', 'width=800,height=600');

    printWindow.document.write(`
      <html>
        <head>
          <style>
            @media print {
              body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
              }
              /* Hide any elements you don't want printed */
              header, footer, .no-print {
                display: none;
              }
            }
            ${cssContent}
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  // const handleDownload = () => {
  //   // Download the content as a PDF
  //   const doc = new jsPDF();

  //   doc.html(document.body, {
  //     callback: function (doc) {
  //       doc.save('content.pdf');
  //     },
  //     x: 10,
  //     y: 10,
  //   });
  // };

  if (loading) {
    return <p>Loading content...</p>;
  }

  if (error) {
    return <p style={{ color: 'red' }}>Error loading content: {error}</p>;
  }

  return (
    <div
      style={{
        padding: '20px',
        backgroundColor: '#f9f9f9',
        borderRadius: '8px',
        border: '1px solid #ddd',
        marginTop: '20px',
        overflow: 'auto',
        maxHeight: '500px',
      }}
    >
      {/* Header and buttons beside "Open in New Tab" */}
      <div style={{ marginBottom: '20px' }}>
        <button onClick={handlePrint} style={buttonStyle}>Print PDF</button>
        {/* <button onClick={handleDownload} style={buttonStyle}>Download PDF</button> */}
        <button onClick={handleOpenInNewTab} style={buttonStyle}>Open in New Tab</button>
      </div>

      {/* Dynamically injected CSS */}
      <style dangerouslySetInnerHTML={{ __html: cssContent }} />
      {htmlContent ? parse(htmlContent) : <p>Loading content...</p>}
    </div>
  );
};

// Styling for the buttons
const buttonStyle = {
  backgroundColor: '#007bff',
  color: 'white',
  padding: '10px 20px',
  border: 'none',
  borderRadius: '4px',
  margin: '0 10px',
  cursor: 'pointer',
  fontSize: '16px',
};

export default HtmlRendererDynamic;
