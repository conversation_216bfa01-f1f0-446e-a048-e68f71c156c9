// ** React Imports

import type { SyntheticEvent } from 'react'

// ** MUI Imports
import Tab from '@mui/material/Tab'

// import TabPanel from '@mui/lab/TabPanel'
import TabContext from '@mui/lab/TabContext'
import { styled } from '@mui/material/styles'

// import Typography from '@mui/material/Typography'
import MuiTabList from '@mui/lab/TabList'
import type { TabListProps } from '@mui/lab/TabList'

// Styled TabList component
const TabList = styled(MuiTabList)<TabListProps>(({ theme }) => ({
  '& .MuiTabs-indicator': {
    display: 'none'
  },
  '& .Mui-selected': {
    backgroundColor: theme.palette.primary.main,
    color: `${theme.palette.common.white} !important`
  },
  '& .MuiTab-root': {
    minHeight: 38,
    minWidth: 110,
    borderRadius: 8,
    paddingTop: theme.spacing(2),
    paddingBottom: theme.spacing(2)
  }
}))

type TabsCustomizedProps = {
  items: string[]
  onChange: (event: SyntheticEvent, value: string) => void
  value: string
}

const TabsCustomized = ({ items = [], onChange, value = '' }: TabsCustomizedProps): JSX.Element => {
  return (
    <TabContext value={value}>
      <TabList onChange={onChange} aria-label='customized tabs example'>
        {items.map((item, index) => {
          return <Tab key={index} value={item} label={item} />
        })}
      </TabList>
    </TabContext>
  )
}

export default TabsCustomized
