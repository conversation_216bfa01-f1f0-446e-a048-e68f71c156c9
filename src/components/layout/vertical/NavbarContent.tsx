// Third-party Imports
import classnames from 'classnames'

// Component Imports
import NavToggle from './NavToggle'
import NavSearch from '@components/layout/shared/search'
import UserDropdown from '@components/layout/shared/UserDropdown'

// Util Imports
import { verticalLayoutClasses } from '@layouts/utils/layoutClasses'
import CompanyToggle from '../shared/CompanyToggle'
import { getCompanyCookie, getToggleModeCookie } from '@/app/server/actions'

const NavbarContent = async () => {
  const company = await getCompanyCookie()
  const mode = await getToggleModeCookie()

  return (
    <div className={classnames(verticalLayoutClasses.navbarContent, 'flex items-center justify-between gap-4 is-full')}>
      <div className='flex items-center gap-[7px]'>
        <NavToggle />
        <NavSearch />
      </div>
      <div className='flex items-center gap-[7px]'>
        {/* <ModeDropdown /> */}
        <CompanyToggle company={company} />
        {/* <NotificationsDropdown notifications={notifications} /> */}
        <UserDropdown initialMode={mode}/>
      </div>
    </div>
  )
}

export default NavbarContent
