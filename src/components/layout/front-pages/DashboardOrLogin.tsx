"use client";

import { useRouter } from 'next/navigation'

import { signIn, useSession } from 'next-auth/react'
import LoadingButton from '@mui/lab/LoadingButton'

import CustomIconButton from '@core/components/mui/IconButton'

export function DashboardOrLogin({ showIconOnly = false }) {
  const { status } = useSession()
  const router = useRouter()

  const isAuthenticated = status === "authenticated"
  const icon = isAuthenticated ? 'ri-dashboard-line' : 'ri-login-circle-line'

  const handleClick = () => {
    if (isAuthenticated) {
      router.push('/admin/dashboard')
    } else {
      signIn("keycloak", { callbackUrl: '/admin/select', })
    }
  }

  return showIconOnly ? (
    <CustomIconButton
      variant='contained'
      color='primary'

      // loading={status === "loading"}
      onClick={handleClick}
    >
      <i className={icon} />
    </CustomIconButton>
  ) : (
    <LoadingButton
      variant='contained'
      startIcon={<i className={icon} />}
      className='whitespace-nowrap'
      loading={status === "loading"}
      onClick={handleClick}
    >
      {isAuthenticated ? "Go To Dashboard" : "Login"}
    </LoadingButton>
  )
}
