"use client";
import { usePathname, useRouter } from 'next/navigation';

import { Typography, useMediaQuery } from '@mui/material'

import type { Theme } from '@mui/material/styles' 

import { Icon } from '@iconify/react/dist/iconify.js';

import { getInitials } from '@/utils/string';

const CompanyToggle = ({ company }) => {
  const { push } = useRouter()
  const pathname = usePathname()
  const isSmallScreen = useMediaQuery((theme: Theme) => theme.breakpoints.down('sm'))

  const handleClick = () => {
    const param = new URLSearchParams({ redirectTo: pathname }).toString()
    
    push(`/admin/select?${param}`)
  }

  if (!company?.name) return <></>

  return (
    <Typography onClick={handleClick} sx={{ cursor: 'pointer' }}>
      {isSmallScreen ? "" : company?.name} ({getInitials(company?.name || "")}) <Icon icon='basil:exchange-solid' />
    </Typography>
  )
}

export default CompanyToggle
