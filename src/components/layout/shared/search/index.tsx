"use client";

import { useEffect, useState, useMemo, type ReactNode } from 'react';

import { useRouter, usePathname, useParams } from 'next/navigation';

import IconButton from '@mui/material/IconButton';
import classnames from 'classnames';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from 'cmdk';

import DefaultSuggestions from './DefaultSuggestions';
import NoResult from './NoResult';
import useVerticalNav from '@menu/hooks/useVerticalNav';
import { useSettings } from '@core/hooks/useSettings';
import './styles.css';
import data from '@/data/navigation/verticalMenuData';
import type { VerticalMenuItemDataType } from '@/types/menuTypes';
import Kbd from '@/components/custom/Kbd';

// --- Types ---
type Item = {
  id: string;
  name: string;
  url: string;
  excludeLang?: boolean;
  icon: string;
  shortcut?: string;
};

type Section = {
  title: string;
  items: Item[];
};

type SearchItemProps = {
  children: ReactNode;
  shortcut?: string;
  value: string;
  url: string;
  currentPath: string;
  onSelect?: () => void;
};

type SearchData = {
  id: string;
  name: string;
  url: string;
  icon: string;
  section: string;
};

// --- Helpers ---
// Flatten nested menu items, filter out id placeholders, and dedupe by URL
const flattenMenuData = (
  menu: VerticalMenuItemDataType[],
  section: string,
  excludeIds = true
): SearchData[] => {
  const items: SearchData[] = [];

  const extract = (
    ms: VerticalMenuItemDataType[],
    currentSection: string
  ) => {
    for (const it of ms) {
      if (excludeIds && it.href?.includes('[id]')) continue;

      const newItem: SearchData = {
        id: it.href || it.label,
        name: it.label,
        url: it.href || '',
        icon: it.icon || '',
        section: currentSection,
      };

      if (!items.some(e => e.url === newItem.url)) {
        items.push(newItem);
      }

      if (it.children) extract(it.children, currentSection);
    }
  };

  extract(menu, section);

  return items;
};

// Group flattened data into sections
const groupBySection = (data: SearchData[]): Section[] =>
  data.reduce((acc: Section[], item) => {
    const section = acc.find(s => s.title === item.section);
    
    const newItem: Item = {
      id: item.id,
      name: item.name,
      url: item.url,
      icon: item.icon,
    };

    if (section) section.items.push(newItem);
    else acc.push({ title: item.section, items: [newItem] });

    return acc;
  }, []);

// Render each item in the command palette
const SearchItem = ({
  children,
  shortcut,
  value,
  currentPath,
  url,
  onSelect = () => {},
}: SearchItemProps) => (
  <CommandItem
    onSelect={onSelect}
    value={value}
    className={classnames({ 'active-searchItem': currentPath === url })}
  >
    {children}
    {shortcut && (
      <div cmdk-vercel-shortcuts=''>
        {shortcut.split(' ').map(key => (
          <Kbd key={key}>{key}</Kbd>
        ))}
      </div>
    )}
  </CommandItem>
);

// Limit number of results per section
const getLimited = (sections: Section[]) => {
  const limit = sections.length > 1 ? 3 : 5;
  
  return sections.map(sec => ({
    ...sec,
    items: sec.items.slice(0, limit),
  }));
};

// Footer with navigation hints
const CommandFooter = () => (
  <div cmdk-footer=''>
    <div className='flex items-center gap-1'>
      <Kbd><i className='ri-arrow-up-line text-base' /></Kbd>
      <Kbd><i className='ri-arrow-down-line text-base' /></Kbd>
      <span>to navigate</span>
    </div>
    <div className='flex items-center gap-1'>
      <Kbd><i className='ri-corner-down-left-line text-base' /></Kbd>
      <span>to open</span>
    </div>
    <div className='flex items-center gap-1'>
      <Kbd>esc</Kbd>
      <span>to close</span>
    </div>
  </div>
);

// Main NavSearch component
export default function NavSearch() {
  const { mode } = useParams() as { mode?: 'society' | 'gate' };
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const router = useRouter();
  const pathName = usePathname();
  const { settings } = useSettings();
  const { isBreakpointReached } = useVerticalNav();

  // Memoize flattened & grouped data to avoid recomputing on each render
  const transformedData = useMemo(() => {
    if (!mode) return [];
    
    const flat = data(mode).flatMap(section =>
      flattenMenuData(section.children ?? [], section.label)
    );
    
    return groupBySection(flat);
  }, [mode]);

  // Filter sections based on search query
  const filtered = useMemo(() => {
    const query = searchValue.trim().toLowerCase();
    
    return transformedData
      .filter(sec => {
        const matchSection = sec.title.toLowerCase().includes(query);
        
        const matchItems = sec.items.some(
          it => it.name.toLowerCase().includes(query)
        );
        
        return matchSection || matchItems;
      })
      .map(sec => ({
        ...sec,
        items: sec.items.filter(
          it =>
            sec.title.toLowerCase().includes(query) ||
            it.name.toLowerCase().includes(query)
        ),
      }));
  }, [searchValue, transformedData]);

  // Limit results
  const limitedData = useMemo(() => getLimited(filtered), [filtered]);

  // Compute platform shortcut label
  const shortcutLabel = useMemo(() => {
    const ua = navigator.userAgent;
    
    if (/Mobi|Android/i.test(ua)) return null;
    
    if (ua.includes('Mac')) {
      return (
        <>
          <Kbd className='text-textDisabled px-2'>⌘</Kbd>+
          <Kbd className='text-textDisabled px-2'>K</Kbd>
        </>
      );
    }
    
    return (
      <>
        <Kbd className='text-textDisabled px-2'>Ctrl</Kbd>+
        <Kbd className='text-textDisabled px-2'>K</Kbd>
      </>
    );
  }, []);

  // Keyboard toggle (Ctrl/Cmd+K)
  useEffect(() => {
    const onKey = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen(o => !o);
      }
    };
    
    document.addEventListener('keydown', onKey);
    
    return () => document.removeEventListener('keydown', onKey);
  }, []);

  // Reset search on close
  useEffect(() => {
    if (!open && searchValue) setSearchValue('');
  }, [open, searchValue]);

  const onSelect = (url: string) => {
    if (url.startsWith('http')) window.open(url, '_blank');
    else router.push(url);
    setOpen(false);
  };

  return (
    <>
      {(isBreakpointReached || settings.layout === 'horizontal') ? (
        <IconButton className='text-textPrimary' onClick={() => setOpen(true)}>
          <i className='ri-search-line' />
        </IconButton>
      ) : (
        <div className='flex items-center gap-2 cursor-pointer' onClick={() => setOpen(true)}>
          <IconButton className='text-textPrimary'>
            <i className='ri-search-line' />
          </IconButton>
          <div className='whitespace-nowrap select-none text-textDisabled'>
            Search {shortcutLabel}
          </div>
        </div>
      )}

      <CommandDialog open={open} onOpenChange={setOpen} title='Search'>
        <div className='flex items-center justify-between border-be pli-4 plb-3 gap-2'>
          <i className='ri-search-line' />
          <CommandInput value={searchValue} onValueChange={setSearchValue} />
          <Kbd className='text-textDisabled px-2'>esc</Kbd>
          <i className='ri-close-line cursor-pointer' onClick={() => setOpen(false)} />
        </div>

        <CommandList>
          {searchValue ? (
            limitedData.length > 0 ? (
              limitedData.map((sec, i) => (
                <CommandGroup key={i} heading={sec.title.toUpperCase()} className='text-xs'>
                  {sec.items.map((it, j) => (
                    <SearchItem
                      key={j}
                      value={`${it.name} ${sec.title}`}
                      url={it.url}
                      currentPath={pathName}
                      shortcut={it.shortcut}
                      onSelect={() => onSelect(it.url)}
                    >
                      {it.icon && <div className='flex text-xl'><i className={it.icon} /></div>}
                      {it.name}
                    </SearchItem>
                  ))}
                </CommandGroup>
              ))
            ) : (
              <CommandEmpty>
                <NoResult searchValue={searchValue} setOpen={setOpen} />
              </CommandEmpty>
            )
          ) : (
            <DefaultSuggestions setOpen={setOpen} />
          )}
        </CommandList>

        <CommandFooter />
      </CommandDialog>
    </>
  );
}