'use client'

import React, { useState, useMemo } from 'react'
import type { ReactNode } from 'react'

// MUI Imports
import { useRouter, useSearchParams } from 'next/navigation'

import Box from '@mui/material/Box'
import Paper from '@mui/material/Paper'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import CircularProgress from '@mui/material/CircularProgress'
import { styled, alpha } from '@mui/material/styles'
import InputAdornment from '@mui/material/InputAdornment'
import SearchIcon from '@mui/icons-material/Search'
import Grid2 from '@mui/material/Grid2'

import axios from 'axios'

// Third Party Components
import PerfectScrollbar from 'react-perfect-scrollbar'
import classnames from 'classnames'

// Hook Imports
import { useSettings } from '@core/hooks/useSettings'
import { updateCompanyIdCookie, updateCompanyNameCookie, updateToggleModeCookie } from '@/app/server/actions'
import { queryClient } from '@/contexts/queryClientProvider'

// Import match and parse functions from your utilities
import { parse, match } from '@/utils/string'

// style sheet for perfect scrollbar
import 'react-perfect-scrollbar/dist/css/styles.css'

import { useIndexedDB } from '@/hooks/useIndexedDB'

// import { selectedCompany } from '@/data/db/tables/user'
import { tableNames } from '@/data/db'

// Define your company type
export type CompanyType = {
  company_id: number
  company_name: string
  user_roles: string[]
  toggleMode: 'society' | 'gate'
}

// Grouped companies by first letter
type GroupedCompanies = {
  [letter: string]: CompanyType[]
}

// Styled component for scroll container
const StyledScrollContainer = styled('div')(() => ({
  height: '100%',
  overflowX: 'hidden',
}))

const ScrollWrapper = ({
  children,
  hidden,
}: {
  children: ReactNode
  hidden: boolean
}) => {
  return hidden ? (
    <StyledScrollContainer>{children}</StyledScrollContainer>
  ) : (
    <PerfectScrollbar
      style={{ height: '100%' }}
      options={{ wheelPropagation: false, suppressScrollX: true }}
    >
      {children}
    </PerfectScrollbar>
  )
}

const CompanyDropdown = ({
  companies,
  isPopup = true,
}: {
  companies: CompanyType[]
  isPopup?: boolean
}) => {
  const { settings } = useSettings()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const tables = useMemo(() => [tableNames.financialYears, tableNames.user], []);
  const { isDBConnecting, putKeyValuePairs, deleteAll, putBulkValue } = useIndexedDB("soc-db", tables)

  // Search state and loading state
  const [search, setSearch] = useState('')
  const [loading, setLoading] = useState(false)

  // Group companies by first letter after filtering
  const groupedCompanies: GroupedCompanies = useMemo(() => {
    const filtered = companies.filter((company) =>
      company.company_name.toLowerCase().includes(search.toLowerCase())
    )

    filtered.sort((a, b) => a.company_name.localeCompare(b.company_name))
    const groups: GroupedCompanies = {}

    filtered.forEach((company) => {
      const letter = company.company_name.charAt(0).toUpperCase()

      if (!groups[letter]) groups[letter] = []
      groups[letter].push(company)
    })

    return groups
  }, [companies, search])

  const sortedLetters = useMemo(() => Object.keys(groupedCompanies).sort(), [
    groupedCompanies,
  ])

  const handleCompanySelect = async (company: CompanyType) => {
    if (!company || !company.company_id || isNaN(company.company_id)) return
    setLoading(true)

    try {
      const toggleMode = ["gate", "society"].includes(company?.toggleMode) ? company?.toggleMode : 'society';
      const isIdAdded = await updateCompanyIdCookie(company.company_id)
      const isNameAdded = await updateCompanyNameCookie(company.company_name)
      const isToggleModeAdded = await updateToggleModeCookie(toggleMode)

      if (!isIdAdded || !isNameAdded || !isToggleModeAdded) {
        setLoading(false)

        return
      }

      const { data } = await axios.get("/admin/accountsetting/accountset", {
        baseURL: process.env.NEXT_PUBLIC_API_URL,
        validateStatus: () => true,
        withCredentials: true,
      })

      if (data?.status_code === 200 && Array.isArray(data?.data)) {
        putKeyValuePairs(tableNames.user, {
          company_id: company.company_id,
          company_name: company.company_name,
          toggle_mode: toggleMode,
        })

        const financialYears = data?.data.map((financialYear: any) => {
          delete financialYear?.title;

          return financialYear;
        });

        deleteAll(tableNames.financialYears)
        putBulkValue(tableNames.financialYears, financialYears)
      }

      const redirectTo = searchParams.get('redirectTo') || `/admin/${toggleMode}/dashboard`
      const isRedirectToValid = redirectTo && redirectTo !== '/admin/select' && redirectTo !== '/admin/dashboard' && redirectTo?.includes(`/${toggleMode}/`)


      // replace(isPopup ? redirectTo : `/admin/${toggleMode}/dashboard`)
      replace(isRedirectToValid ? redirectTo : `/admin/${toggleMode}/dashboard`)

      if (isPopup) {
        setTimeout(() => queryClient?.refetchQueries?.(), 1500)
      }

    } catch (error) {
      console.warn('Error selecting company:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Paper
      className={classnames(
        settings.skin === 'bordered' ? 'border shadow-none' : 'shadow-lg'
      )}
      sx={{
        // Dynamic background for dark/light theme; no hard-coded border
        bgcolor: 'background.paper',
        boxShadow: (theme) => theme.shadows[1],
        border: 'none',
        borderRadius: 1,
        p: 2,
        m: 2,
        display: 'flex',
        flexDirection: 'column',
        height: isPopup ? '100vh' : 'auto',
        maxHeight: isPopup ? '100vh' : '80vh',
        overflow: 'hidden',
        position: 'relative',
      }}
    >
      {/* Top row: Title and Search Bar */}
      <Box
        sx={{   
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 2,
        }}
      >
        <TextField
          size="small"
          placeholder="Search..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          sx={{
            width: '100%',
            '& .MuiOutlinedInput-root': {
              borderRadius: 1,
              backgroundColor: 'transparent',
              '&:hover fieldset': {
                borderColor: 'text.secondary',
              },
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* Scrollable list */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        <ScrollWrapper hidden={false}>
          <Box sx={{ pt: 0, height: '100%' }}>
            {sortedLetters.length > 0 ? (
              sortedLetters.map((letter) => (
                <Box key={letter} sx={{ mb: 3 }}>
                  {/* Group header with theme primary color */}
                  <Typography
                    variant="subtitle2"
                    sx={{
                      mb: 1,
                      fontWeight: 'bold',
                      textTransform: 'uppercase',
                      color: 'primary.main',
                    }}
                  >
                    {letter}
                  </Typography>
                  <Grid2 container spacing={3}>
                    {groupedCompanies[letter].map((company) => (
                      <Grid2
                        size={{ xs: 12, sm: 6, md: 4, lg: 3 }}
                        key={company.company_id}
                        onClick={() => {
                          if (!loading) {
                            handleCompanySelect(company)
                          }
                        }}
                        sx={{
                          cursor: loading ? 'default' : 'pointer',
                        }}
                      >
                        <Paper
                          variant="outlined"
                          sx={{
                            p: 2,
                            textAlign: 'center',
                            borderRadius: 1,
                            transition: 'all 0.2s ease',
                            bgcolor: 'background.paper',
                            '&:hover': loading
                              ? {}
                              : {
                                transform: 'translateY(-2px)',
                                boxShadow: 3,
                              },
                          }}
                        >
                          <Typography variant="body2" sx={{ fontWeight: 500, color: 'text.primary' }}>
                            {parse(company.company_name, match(company.company_name, search)).map(
                              (segment, i) =>
                                segment.highlight ? (
                                  <span key={i} style={{ fontWeight: 700, textDecoration: 'underline' }}>
                                    {segment.text}
                                  </span>
                                ) : (
                                  <span key={i}>{segment.text}</span>
                                )
                            )}
                          </Typography>
                        </Paper>
                      </Grid2>
                    ))}
                  </Grid2>
                </Box>
              ))
            ) : (
              <Typography variant="body2" sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                No companies found.
              </Typography>
            )}
          </Box>
        </ScrollWrapper>
      </Box>

      {/* Loading overlay */}
      {(isDBConnecting || loading) && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            bgcolor: (theme) => alpha(theme.palette.background.paper, 0.7),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 999,
          }}
        >
          <CircularProgress />
        </Box>
      )}
    </Paper>
  )
}

export default CompanyDropdown
