import axios from 'axios';

import type {
  ComplaintsData,
  BalanceDuesData,
  AllotteesData,
  BankCashData,
  ExpenseItem,
  SurveyItem,
} from '../types';

export const fetchComplaints = () =>
  axios
    .get('/admin/admindashboard/index/helpdesk', { withCredentials: true, baseURL: process.env.NEXT_PUBLIC_API_URL })
    .then(res => res.data.data as ComplaintsData);

export const fetchBalanceDues = () =>
  axios
    .get('/admin/admindashboard/index/balance_dues', { withCredentials: true, baseURL: process.env.NEXT_PUBLIC_API_URL })
    .then(res => res.data.data as BalanceDuesData);

export const fetchAllottees = () =>
  axios
    .get('/admin/admindashboard/index/allottees', { withCredentials: true, baseURL: process.env.NEXT_PUBLIC_API_URL })
    .then(res => res.data.data as AllotteesData);

export const fetchBankCash = () =>
  axios
    .get('/admin/admindashboard/index/bank_cash_ledger', { withCredentials: true, baseURL: process.env.NEXT_PUBLIC_API_URL })
    .then(res => res.data.data as BankCashData);

export const fetchSurveys = () =>
  axios
    .get('/admin/admindashboard/index/surveys', { withCredentials: true, baseURL: process.env.NEXT_PUBLIC_API_URL })
    .then(res => res.data.data as SurveyItem[]);

export const fetchExpenses = () =>
  axios
    .get('/admin/admindashboard/index/expenses', { withCredentials: true, baseURL: process.env.NEXT_PUBLIC_API_URL })
    .then(res => res.data.data as ExpenseItem[]);
