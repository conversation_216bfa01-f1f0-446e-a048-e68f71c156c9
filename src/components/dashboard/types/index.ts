export interface ComplaintsData {
  new_open: number;
  on_hold: number;
  overdue: number;
  resolved: number;
}

export interface BalanceDuesData {
  non_member_list_total_due: number;
  incidental_bill_list_total_due	: number;
  maintenance_invoice_total_due	: number;
}

export interface AllotteesData {
  primary_members: number;
  tenants: number;
  associates: number;
  nominal: number;
}

export interface BankCashData {
  totalBankBalance: number;
  arrCashBalance: number;
}

export interface SurveyItem {
    soc_id?: string;
    survey_id?: number;
    notice_name?: string;
    count?: number;
}

export interface ExpenseItem {
  month: string;
  total_amount: number;
}