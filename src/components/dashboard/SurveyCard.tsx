import React from 'react';

import {
  <PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Typography,
  Button,
  Avatar,
  CircularProgress,
  LinearProgress
} from '@mui/material';

interface SurveyItem {
  title: string;
  description?: string;
  responses: number;
  total_responses?: number;
  survey_id?: number; // Added to store the survey_id from the API
}

interface SurveyData {

  // Array of survey items
  surveys: SurveyItem[];
}

interface SurveyCardProps {
  isDarkMode: boolean;
  dataLoading: boolean;
  surveyData?: SurveyData; // Optional for now
  handleViewAll: (route: string) => void;
}

const SurveyCard: React.FC<SurveyCardProps> = ({
  isDarkMode,
  dataLoading,
  surveyData = {
    surveys: [
      {
        title: 'Water Issue',
        description: 'Survey about water supply issues in the society',
        responses: 3,
        total_responses: 10
      },
      {
        title: 'test',
        description: 'Test survey',
        responses: 2,
        total_responses: 5
      }
    ]
  }, // Default values
  handleViewAll
}) => {
  return (
    <Box sx={{
      flex: '2 1 600px',
      minWidth: '300px',
      transition: 'transform 0.3s ease',
      '&:hover': {
        transform: 'translateY(-5px)'
      }
    }}>
      <Card sx={{
        height: '100%',
        background: isDarkMode
          ? 'rgba(17, 25, 40, 0.75)'
          : 'rgba(255, 255, 255, 0.85)',
        backdropFilter: 'blur(16px)',
        borderRadius: '16px',
        overflow: 'hidden',
        border: isDarkMode
          ? '1px solid rgba(255, 255, 255, 0.125)'
          : '1px solid rgba(37, 99, 235, 0.1)',
        transition: 'all 0.3s ease',
        boxShadow: isDarkMode
          ? '0 8px 32px 0 rgba(31, 38, 135, 0.37)'
          : '0 8px 32px 0 rgba(37, 99, 235, 0.1)',
        '&:hover': {
          animation: 'cardPop 0.5s forwards',
          boxShadow: isDarkMode
            ? '0 8px 32px 0 rgba(216,67,21,0.6)'
            : '0 8px 32px 0 rgba(216,67,21,0.2)',
          border: isDarkMode
            ? '1px solid rgba(216,67,21,0.4)'
            : '1px solid rgba(216,67,21,0.2)'
        },
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '1px',
          background: isDarkMode
            ? 'linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%)'
            : 'linear-gradient(90deg, rgba(37,99,235,0) 0%, rgba(37,99,235,0.2) 50%, rgba(37,99,235,0) 100%)',
        }
      }}>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar sx={{
                bgcolor: 'transparent',
                width: 40,
                height: 40,
                mr: 1.5,
                border: '2px solid rgba(216,67,21,0.5)',
                boxShadow: '0 0 15px rgba(216,67,21,0.5)',
                animation: 'glow 3s infinite ease-in-out',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: '-2px',
                  left: '-2px',
                  right: '-2px',
                  bottom: '-2px',
                  borderRadius: '50%',
                  border: '1px solid rgba(216,67,21,0.3)',
                  animation: 'pulse 2s infinite ease-in-out',
                }
              }}>
                <i className="ri-survey-line" style={{ fontSize: '20px', color: '#d84315' }} />
              </Avatar>
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 700,
                    letterSpacing: '1px',
                    color: isDarkMode
                      ? 'rgba(255,255,255,0.95)'
                      : 'rgba(30,64,175,0.95)',
                    textShadow: isDarkMode
                      ? '0 0 10px rgba(216,67,21,0.5)'
                      : '0 0 5px rgba(216,67,21,0.2)',
                    fontSize: '0.9rem',
                  }}
                >
                  SURVEY
                </Typography>
                <Box
                  sx={{
                    width: '30px',
                    height: '2px',
                    background: 'linear-gradient(90deg, #d84315 0%, rgba(216,67,21,0) 100%)',
                    mt: 0.5,
                    borderRadius: '1px',
                  }}
                />
              </Box>
            </Box>
          }
          action={
            <Button
              size="small"
              variant="contained"
              color="warning"
              disableElevation
              startIcon={<i className="ri-arrow-right-line" style={{ fontSize: '14px' }} />}
              sx={{
                fontSize: '11px',
                borderRadius: '8px',
                px: 2,
                py: 0.75,
                background: 'rgba(216,67,21,0.15)',
                color: '#d84315',
                border: '1px solid rgba(216,67,21,0.3)',
                backdropFilter: 'blur(4px)',
                boxShadow: '0 4px 8px rgba(216,67,21,0.2)',
                '&:hover': {
                  background: 'rgba(216,67,21,0.25)',
                  boxShadow: '0 6px 10px rgba(216,67,21,0.3)'
                }
              }}
              onClick={() => handleViewAll('/admin/surveys/list')}
            >
              View All
            </Button>
          }
          sx={{
            bgcolor: 'rgba(216,67,21,0.05)',
            borderBottom: '1px solid rgba(216,67,21,0.1)',
            py: 2.5,
            px: 3,
            '& .MuiCardHeader-title': { fontWeight: 'bold' },
            '& .MuiCardHeader-action': { alignSelf: 'center' }
          }}
        />
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {dataLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100px' }}>
                <CircularProgress color="warning" />
              </Box>
            ) : surveyData.surveys.length === 0 ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100px', flexDirection: 'column', gap: 1 }}>
                <i className="ri-survey-line" style={{ fontSize: '24px', color: '#d84315', opacity: 0.7 }} />
                <Typography variant="body2" sx={{ color: isDarkMode ? 'rgba(255,255,255,0.5)' : 'rgba(0,0,0,0.5)' }}>
                  No surveys available
                </Typography>
              </Box>
            ) : (
              surveyData.surveys.map((survey, index) => (
                <Box key={index} sx={{
                  p: 2.5,
                  bgcolor: isDarkMode
                    ? 'rgba(20, 30, 48, 0.5)'
                    : 'rgba(255, 255, 255, 0.7)',
                  borderRadius: '12px',
                  backdropFilter: 'blur(10px)',
                  boxShadow: isDarkMode
                    ? '0 4px 12px rgba(0,0,0,0.1)'
                    : '0 4px 12px rgba(0,0,0,0.03)',
                  border: isDarkMode
                    ? '1px solid rgba(255,255,255,0.1)'
                    : '1px solid rgba(216,67,21,0.1)',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    boxShadow: isDarkMode
                      ? '0 6px 16px rgba(216,67,21,0.2)'
                      : '0 6px 16px rgba(216,67,21,0.1)',
                    transform: 'translateY(-3px)'
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    background: 'linear-gradient(135deg, rgba(216,67,21,0.1) 0%, rgba(216,67,21,0) 100%)',
                    zIndex: -1,
                  }
                }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Typography variant="subtitle2" sx={{
                      color: isDarkMode ? 'rgba(255,255,255,0.7)' : 'rgba(30,64,175,0.8)',
                      mb: 1,
                      fontWeight: 500,
                      fontSize: '0.75rem',
                      letterSpacing: '0.5px'
                    }}>{survey.title.toUpperCase()}</Typography>
                    <Box sx={{
                      width: '24px',
                      height: '24px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: isDarkMode ? 'rgba(216,67,21,0.15)' : 'rgba(216,67,21,0.08)',
                      boxShadow: isDarkMode ? '0 0 10px rgba(216,67,21,0.3)' : '0 0 5px rgba(216,67,21,0.15)'
                    }}>
                      <i className="ri-survey-line" style={{ fontSize: '12px', color: '#d84315' }} />
                    </Box>
                  </Box>

                  {/* Progress bar */}
                  <Box sx={{ width: '100%', mb: 1 }}>
                    <LinearProgress
                      variant="determinate"
                      value={(survey.responses / (survey.total_responses || 1)) * 100}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4,
                          backgroundColor: '#d84315',
                        }
                      }}
                    />
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                    <Typography variant="caption" sx={{
                      color: isDarkMode ? 'rgba(255,255,255,0.5)' : 'rgba(0,0,0,0.5)',
                      fontWeight: 500
                    }}>
                      RESPONSES
                    </Typography>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 0.5,
                      color: '#d84315',
                      fontWeight: 'bold'
                    }}>
                      <i className="ri-user-voice-line" style={{ fontSize: '14px' }} />
                      {survey.responses} / {survey.total_responses}
                    </Box>
                  </Box>
                </Box>
              ))
            )}
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SurveyCard;
