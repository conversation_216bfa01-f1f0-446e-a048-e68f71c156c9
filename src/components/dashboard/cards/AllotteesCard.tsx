"use client";

import React from 'react';

import Link from 'next/link';

import {
  Card,
  CardHeader,
  CardContent,
  Typography,
  Box,
  Skeleton
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { useQuery } from '@tanstack/react-query';
import { pieArcLabelClasses, PieChart } from '@mui/x-charts/PieChart';

import { fetchAllottees } from '../service';
import type { AllotteesData } from '../types';
import primaryColorConfig from '@/configs/primaryColorConfig';

interface AllotteesCardProps {
  showInChart?: boolean;
}

interface Segment {
  label: string;
  value: number;
}

export default function AllotteesCard({ showInChart = false }: AllotteesCardProps) {
  const { data, isLoading, isError } = useQuery<AllotteesData>({
    queryKey: ['dashboard', 'allottees'],
    queryFn: fetchAllottees,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: true,
    placeholderData: (prev) => prev as AllotteesData,
  });

  // Loading skeleton
  if (isLoading) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <CardHeader
          title="Allottees"
          action={<Link href="/admin/member/list">View All</Link>}
        />
        <CardContent sx={{ flex: 1 }}>
          {[...Array(4)].map((_, idx) => (
            <Skeleton key={idx} height={24} sx={{ mb: 1 }} />
          ))}
        </CardContent>
      </Card>
    );
  }

  // Error fallback
  if (isError || !data) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <CardHeader
          title="Allottees"
          action={<Link href="/admin/member/list">See All</Link>}
        />
        <CardContent
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <ErrorOutlineIcon color="error" fontSize="large" />
          <Typography color="error" mt={1}>
            Couldn&apos;t load data.
          </Typography>
        </CardContent>
      </Card>
    );
  }

  // Destructure with defaults and ensure numeric
  const primary = Number(data.primary_members ?? 0);
  const tenant = Number(data.tenants ?? 0);
  const associate = Number(data.associates ?? 0);
  const nominal = Number(data.nominal ?? 0);

  const segments: Segment[] = [
    { label: 'Primary', value: primary },
    { label: 'Tenant', value: tenant },
    { label: 'Associates', value: associate },
    { label: 'Nominal', value: nominal },
  ];

  // Chart view
  if (showInChart) {
    return (
      <Card variant="outlined" elevation={1}>
        <CardHeader
          title="Allottees"
          action={<Link href="/admin/member/list">See All</Link>}
        />
        <CardContent>
          <PieChart
            colors={primaryColorConfig?.map((item) => item?.main)}
            height={300}
            width={400}
            sx={{
              [`& .${pieArcLabelClasses.root}`]: {
                fill: 'white',
                fontSize: 14,
              },
            }}
            series={[
              {
                data: segments,
                innerRadius: 30,
                outerRadius: 100,
                paddingAngle: 5,
                cornerRadius: 5,
                cx: 100,
                cy: 150,
                arcLabel: (d) => `${d.value}`,
                arcLabelMinAngle: 30,
                highlightScope: { fade: 'global', highlight: 'item' },
                faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
              },
            ]}
          />
        </CardContent>
      </Card>
    );
  }

  // Default list view
  return (
    <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
      <CardHeader
        title="Allottees"
        action={<Link href="/admin/member/list">View All</Link>}
      />
      <CardContent sx={{ flex: 1 }}>
        <Box display="grid" gridTemplateColumns="repeat(2, 1fr)" gap={2} mb={2}>
          {segments.slice(0, 2).map((seg) => (
            <Box key={seg.label}>
              <Typography variant="subtitle2">{seg.label}</Typography>
              <Typography variant="h5">{seg.value}</Typography>
            </Box>
          ))}
        </Box>
        <Box display="grid" gridTemplateColumns="repeat(2, 1fr)" gap={2}>
          {segments.slice(2).map((seg) => (
            <Box key={seg.label}>
              <Typography variant="subtitle2">{seg.label}</Typography>
              <Typography variant="h5">{seg.value}</Typography>
            </Box>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
}
