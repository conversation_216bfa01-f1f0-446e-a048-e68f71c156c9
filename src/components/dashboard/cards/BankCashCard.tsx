"use client";

import React from 'react';

import Link from 'next/link';

import {
  Card,
  CardHeader,
  CardContent,
  Typography,
  Box,
  Skeleton,
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { useQuery } from '@tanstack/react-query';
import { BarChart } from '@mui/x-charts/BarChart';

import { fetchBankCash } from '../service';
import type { BankCashData } from '../types';
import primaryColorConfig from '@/configs/primaryColorConfig';
import { formatCurrency } from '@/utils/string';

interface Segment {
  label: string;
  value: number;
}

interface BankCashCardProps {
  showInChart?: boolean;
}

export default function BankCashCard({ showInChart = true }: BankCashCardProps) {
  const { data, isLoading, isError } = useQuery<BankCashData>({
    queryKey: ['dashboard', 'bankCash'],
    queryFn: fetchBankCash,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: true,
    placeholderData: prev => prev as BankCashData,
  });

  // Consistent header
  const header = (
    <CardHeader
      title="Bank & Cash"
      action={
        <Link href="/bank-cash">
          See All
        </Link>
      }
    />
  );

  // Loading skeleton
  if (isLoading) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent sx={{ flex: 1 }}>
          {[...Array(2)].map((_, idx) => (
            <Skeleton key={idx} height={28} sx={{ mb: 1 }} />
          ))}
        </CardContent>
      </Card>
    );
  }

  // Error fallback
  if (isError || !data) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <ErrorOutlineIcon color="error" fontSize="large" />
          <Typography color="error" mt={1}>
            Couldn&apos;t load data.
          </Typography>
        </CardContent>
      </Card>
    );
  }

  // Ensure numeric values
  const bankBalance = data?.totalBankBalance ?? 0;
  const cashInHand = data?.arrCashBalance ?? 0;

  const segments: Segment[] = [
    { label: 'Bank Balance', value: bankBalance },
    { label: 'Cash In Hand', value: cashInHand },
  ];

  // Chart view
  if (showInChart) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent sx={{ flex: 1 }}>
          <Box height={250}>
            <BarChart
            yAxis={[{
                          // format every tick as INR with thousands separators
                          valueFormatter: (v) => formatCurrency(v, { notation: "compact"}),

                          // reserve enough left-margin so you don’t clip “ ₹ 1,23,456”
                          // right-align the text
                          tickLabelStyle: { textAnchor: 'end' },
                        }]}
            colors={primaryColorConfig?.map((item) => item?.main)}
              height={250}
              xAxis={[{ scaleType: 'band', data: segments.map(({ label }) => label), tickPlacement: 'middle' }]}
              series={[{ type: 'bar', id: 'base', data: segments.map(({ value }) => value), valueFormatter: (v) => formatCurrency(v) }]}
            />
          </Box>
        </CardContent>
      </Card>
    );
  }

  // List view
  return (
    <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
      {header}
      <CardContent sx={{ flex: 1 }}>
        <Box display="grid" gridTemplateColumns="repeat(2,1fr)" gap={2}>
          {segments.map(({ label, value }) => (
            <Box key={label} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexDirection: 'column' }}>
              <Typography variant="subtitle2">{label}</Typography>
              <Typography variant="h6">₹ {value}</Typography>
            </Box>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
}