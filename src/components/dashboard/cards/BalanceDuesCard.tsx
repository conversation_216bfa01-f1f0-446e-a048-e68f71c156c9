"use client";

import React from 'react';

import Link from 'next/link';

import {
  Card,
  CardHeader,
  CardContent,
  Typography,
  Box,
  Skeleton,
  Link as MuiLink
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { useQuery } from '@tanstack/react-query';
import { BarChart } from '@mui/x-charts/BarChart';

import { fetchBalanceDues } from '../service';
import type { BalanceDuesData } from '../types';
import primaryColorConfig from '@/configs/primaryColorConfig';
import { formatCurrency } from '@/utils/string';

interface BalanceDuesCardProps {
  showInChart?: boolean;
}

interface Segment {
  label: string;
  value: number;
  link?: string;
}

export default function BalanceDuesCard({ showInChart = true }: BalanceDuesCardProps) {
  const { data, isLoading, isError } = useQuery<BalanceDuesData>({
    queryKey: ['dashboard', 'balance_dues'],
    queryFn: fetchBalanceDues,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: true,
    placeholderData: (prev) => prev as BalanceDuesData,
  });

  // Loading state
  if (isLoading) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <CardHeader title="Balance Dues" action={<Link href="/admin/income-details/incomemember">See All</Link>} />
        <CardContent sx={{ flex: 1 }}>
          {[...Array(3)].map((_, idx) => (
            <Skeleton key={idx} height={28} sx={{ mb: 1 }} />
          ))}
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (isError || !data) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <CardHeader title="Balance Dues" action={<Link href="/admin/income-details/incomemember">See All</Link>} />
        <CardContent
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <ErrorOutlineIcon color="error" fontSize="large" />
          <Typography color="error" mt={1}>
            Couldn&apos;t load data.
          </Typography>
        </CardContent>
      </Card>
    );
  }

  // Ensure numeric values
  const maintenance = Number(data?.maintenance_invoice_total_due ?? 0);
  const incidental = Number(data?.incidental_bill_list_total_due ?? 0);
  const nonMember = Number(data?.non_member_list_total_due ?? 0);

  const segments: Segment[] = [
    { label: 'Maintenance', value: maintenance, link: null },
    { label: 'Incidental Bill', value: incidental, link: null },
    { label: 'Non Member', value: nonMember, link: null },
  ];

  // Chart view
  if (showInChart) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <CardHeader title="Balance Dues" action={<Link href="/admin/income-details/incomemember">See All</Link>} />
        <CardContent sx={{ flex: 1 }}>
          <BarChart
            yAxis={[{
              // format every tick as INR with thousands separators
              valueFormatter: (v) => formatCurrency(v, { notation: "compact" }),

              // reserve enough left-margin so you don’t clip “ ₹ 1,23,456”
              // right-align the text
              tickLabelStyle: { textAnchor: 'end' },
            }]}
            colors={primaryColorConfig?.map((item) => item?.main)}
            height={300}
            xAxis={[{ scaleType: 'band', data: segments.map(({ label }) => label), tickPlacement: 'middle' }]}
            series={[{
              type: 'bar',
              id: 'base', data: segments.map(({ value }) => value), valueFormatter: (v) => formatCurrency(v)
            }]}
          />
        </CardContent>
      </Card>
    );
  }

  // List view
  return (
    <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
      <CardHeader title="Balance Dues" action={<Link href="/admin/income-details/incomemember">See All</Link>} />
      <CardContent sx={{ flex: 1 }}>
        <Box display="grid" gridTemplateColumns="repeat(1, 1fr)" gap={2} mb={2}>
          {segments.map(({ label, value, link }) => (
            <Box key={label} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="subtitle2">{label}</Typography>
              {link ? (
                <MuiLink href={link} underline="none">
                  <Typography variant="h6">₹ {value.toLocaleString()}</Typography>
                </MuiLink>
              ) : (
                <Typography variant="h6">₹ {value.toLocaleString()}</Typography>
              )}
            </Box>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
}
