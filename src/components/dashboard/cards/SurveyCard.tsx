"use client";

import React from 'react';

import Link from 'next/link';

import {
  Card,
  CardHeader,
  CardContent,
  Typography,
  Box,
  Skeleton,
  LinearProgress
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { useQuery } from '@tanstack/react-query';
import { BarChart } from '@mui/x-charts/BarChart';

import { fetchSurveys } from '../service';
import type { SurveyItem } from '../types';

interface Segment {
  label: string;
  percent: number;
  count: number;
}

interface SurveyCardProps {
  showInChart?: boolean;
}

export default function SurveyCard({ showInChart = false }: SurveyCardProps) {
  // Extract only the data array; fallback to empty
  const { data: surveys = [], isLoading, isError } = useQuery<SurveyItem[], Error>({
    queryKey: ['dashboard', 'surveys'],
    queryFn: fetchSurveys,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: true,
  });

  // Shared header
  const header = (
    <CardHeader
      title="Survey"
      action={
        <Link href="/admin/surveys/list">
          See All
        </Link>
      }
    />
  );

  // Loading state
  if (isLoading) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent sx={{ flex: 1 }}>
          {[...Array(2)].map((_, idx) => (
            <Box key={idx} sx={{ mb: 2 }}>
              <Skeleton width="40%" height={24} sx={{ mb: 1 }} />
              <Skeleton variant="rectangular" height={8} />
            </Box>
          ))}
        </CardContent>
      </Card>
    );
  }

  // Error or no valid data
  if (isError || !Array.isArray(surveys)) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <ErrorOutlineIcon color="error" fontSize="large" />
          <Typography color="error" mt={1}>Couldn&apos;t load surveys.</Typography>
        </CardContent>
      </Card>
    );
  }

  // Map to segments; count out of 10 (can exceed)
  const segments: Segment[] = surveys.map(item => {
    const label = item.notice_name ?? 'Untitled';
    const count = typeof item.count === 'number' ? item.count : 0;
    const percent = (count / 10) * 100;

    
return { label, percent, count };
  });

  // Chart view
  if (showInChart) {
    const categories = segments.map(seg => seg.label);
    const values = segments.map(seg => seg.percent);

    
return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent sx={{ flex: 1 }}>
          <Box height={250}>
            <BarChart
              height={250}
              xAxis={[{ scaleType: 'band', data: categories, tickPlacement: 'middle' }]}
              series={[{ type: 'bar', id: 'progress', data: values }]}
            />
          </Box>
        </CardContent>
      </Card>
    );
  }

  // List view
  return (
    <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
      {header}
      <CardContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 2 }}>
        {segments.length === 0 ? (
          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Typography>No surveys available</Typography>
          </Box>
        ) : (
          segments.map((seg, idx) => (
            <Box key={surveys[idx]?.survey_id ?? idx} sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Typography>{seg.label}</Typography>
              <Box sx={{ width: '100%' }}>
                <LinearProgress variant="determinate" value={Math.min(seg.percent + 1, 100)} />
              </Box>
              <Typography variant="caption">{seg.count} / 10</Typography>
            </Box>
          ))
        )}
      </CardContent>
    </Card>
  );
}
