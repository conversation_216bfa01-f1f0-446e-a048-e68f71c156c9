"use client";

import React from 'react';

import Link from 'next/link';

import {
  Card,
  CardHeader,
  CardContent,
  Box,
  Typography,
  CircularProgress
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { BarChart } from '@mui/x-charts/BarChart';
import { useQuery } from '@tanstack/react-query';

import { fetchExpenses } from '../service';
import primaryColorConfig from '@/configs/primaryColorConfig';
import type { ExpenseItem } from '../types';
import { formatCurrency } from '@/utils/string';

export default function MonthlyExpensesChart() {

  // Fetch and transform data
  const { data: items = [], isLoading, isError } = useQuery<ExpenseItem[], Error>({
    queryKey: ['dashboard', 'monthlyExpenses'],
    queryFn: fetchExpenses,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: true,
    placeholderData: (prev) => prev,
  });

  // Shared header
  const header = (
    <CardHeader
      title="Monthly Expenses"
      action={
        <Link href="/admin/expenses/monthly">
          See All
        </Link>
      }
    />
  );

  // Loading state
  if (isLoading) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent sx={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <CircularProgress />
        </CardContent>
      </Card>
    );
  }

  // Error or no data
  if (isError || !Array.isArray(items) || items.length === 0) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent sx={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <ErrorOutlineIcon color="error" fontSize="large" />
          <Typography color="error" mt={1}>No expense data available.</Typography>
        </CardContent>
      </Card>
    );
  }

  const categories = items.map(item => item?.month);
  const values = items.map(item => item?.total_amount ?? 0);

  return (
    <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
      {header}
      <CardContent sx={{ flex: 1 }}>
        <Box height={300}>
          <BarChart
            colors={primaryColorConfig?.map((item) => item?.main)}
            height={300}
            yAxis={[{
              // format every tick as INR with thousands separators
              valueFormatter: (v) => formatCurrency(v, { notation: "compact"}),

              // reserve enough left-margin so you don’t clip “ ₹ 1,23,456”
              // right-align the text
              tickLabelStyle: { textAnchor: 'end' },
            }]}
            xAxis={[{ scaleType: 'band', data: categories, tickPlacement: 'middle'}]}
            series={[{
              type: 'bar', id: 'monthly', data: values,
              highlightScope: { fade: 'global', highlight: 'item' },
              valueFormatter: (v) => formatCurrency(v)
            }]}
          />
        </Box>
      </CardContent>
    </Card>
  );
}
