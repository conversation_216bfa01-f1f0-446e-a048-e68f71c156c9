"use client";

import React from 'react';

import Link from 'next/link';

import {
  Card,
  CardHeader,
  CardContent,
  Typography,
  Box,
  Skeleton,
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { useQuery } from '@tanstack/react-query';
import { BarChart } from '@mui/x-charts/BarChart';

import { fetchComplaints } from '../service';
import type { ComplaintsData } from '../types';
import primaryColorConfig from '@/configs/primaryColorConfig';

interface Segment {
  label: string;
  value: number;
}

interface ComplaintsCardProps {
  showInChart?: boolean;
}

export default function ComplaintsCard({ showInChart = true }: ComplaintsCardProps) {
  const { data, isLoading, isError } = useQuery<ComplaintsData>({
    queryKey: ['dashboard', 'complaints'],
    queryFn: fetchComplaints,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: true,
    placeholderData: prev => prev as ComplaintsData,
  });

  // Consistent header
  const header = (
    <CardHeader
      title="Complaints"
      action={
        <Link href="/admin/society/helpdesk/issues">
          See All
        </Link>
      }
    />
  );

  // Loading state
  if (isLoading) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent sx={{ flex: 1 }}>
          {[...Array(4)].map((_, idx) => (
            <Skeleton key={idx} height={32} sx={{ mb: 1 }} />
          ))}
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (isError || !data) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <ErrorOutlineIcon color="error" fontSize="large" />
          <Typography color="error" mt={1}>Couldn&apos;t load data.</Typography>
        </CardContent>
      </Card>
    );
  }

  // Prepare segments
  const segments: Segment[] = [
    { label: 'New/Open', value: Number(data.new_open ?? 0) },
    { label: 'On Hold', value: Number(data.on_hold ?? 0) },
    { label: 'Overdue', value: Number(data.overdue ?? 0) },
    { label: 'Resolved', value: Number(data.resolved ?? 0) },
  ];

  // Chart view
  if (showInChart) {
    return (
      <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {header}
        <CardContent sx={{ flex: 1 }}>
          <Box >
            <BarChart
              colors={primaryColorConfig?.map((item) => item?.main)}
              height={300}
              xAxis={[{ scaleType: 'band', data: segments.map(({ label }) => label), tickPlacement: 'middle' }]}
              series={[{ type: 'bar', id: 'base', data: segments.map(({ value }) => value) as number[] }]}
            />
          </Box>
        </CardContent>
      </Card>
    );
  }

  // List view
  return (
    <Card variant="outlined" elevation={1} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
      {header}
      <CardContent sx={{ flex: 1 }}>
        <Box display="grid" gridTemplateColumns="repeat(2,1fr)" gap={2}>
          {segments.map(({ label, value }) => (
            <Box key={label} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="subtitle2">{label}</Typography>
              <Typography variant="h6">{value}</Typography>
            </Box>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
}
