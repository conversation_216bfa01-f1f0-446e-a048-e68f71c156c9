// src/components/IncomeAccountSelector.jsx
"use client";
import React from "react";

import { Controller } from "react-hook-form";
import {
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
} from "@mui/material";

const IncomeAccountSelector = ({
  control,
  errors,
  loading,
  isCreatingIncomeAccount,
  incomeAccounts,
  setValue,
  onAccountChange,
}) => {
  return isCreatingIncomeAccount ? (
    <Controller
      name="new_incomeaccount"
      control={control}
      defaultValue=""
      render={({ field }) => (
        <TextField
          {...field}
          placeholder="Enter new income account name"
          fullWidth
          error={!!errors.new_incomeaccount}
          helperText={errors.new_incomeaccount?.message}
          onChange={(e) => {
            field.onChange(e.target.value);
            setValue("member_incomeaccount", e.target.value);
          }}
        />
      )}
    />
  ) : (
    <Controller
      name="incomeaccount"
      control={control}
      defaultValue=""
      render={({ field }) => (
        <FormControl fullWidth variant="outlined">
          <InputLabel id="income-account-label">Income Account</InputLabel>
          <Select
            {...field}
            labelId="income-account-label"
            label="Income Account"
            disabled={loading}
            value={
              field.value ||
              incomeAccounts.find((acc) => acc.account_name === "MaintenanceFee")?.id ||
              ""
            }
            onChange={(e) => {
              const selectedValue = e.target.value;

              field.onChange(selectedValue);

              if (onAccountChange) {
                onAccountChange(selectedValue);
              }
            }}
          >
            {loading ? (
              <MenuItem disabled>
                <CircularProgress size={20} />
              </MenuItem>
            ) : (
              incomeAccounts.map((account) => (
                <MenuItem key={account.id} value={account.id}>
                  {account.account_name}
                </MenuItem>
              ))
            )}
          </Select>
        </FormControl>
      )}
    />
  );
};

export default IncomeAccountSelector;
