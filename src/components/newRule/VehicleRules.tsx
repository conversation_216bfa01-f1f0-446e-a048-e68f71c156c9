// src/components/VehicleRules.jsx
"use client";
import React from "react";

import { Box, Grid, TextField, Typography } from "@mui/material";

const VehicleRules = ({ vehicleRules, setVehicleRules }) => {
  const handleOwnerChange = (index, field, value) => {
    const updated = [...vehicleRules];

    updated[index][field] = value === "" ? "" : Math.max(0, parseFloat(value));
    setVehicleRules(updated);
  };

  return (
    <Box>
      {vehicleRules.map((rule, index) => (
        <Box key={rule.id} sx={{ position: "relative" }}>
          <Box sx={{ p: 2, mt: 2, border: 1, borderColor: "#ddd", borderRadius: 1, mb: 4 }}>
            <Typography fontWeight="bold" sx={{ mb: 2, color: "text.primary" }}>
              {index === 0 ? "Owner Parking" : `Additional Vehicle Rule ${index}`}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Typography fontWeight="bold">Parking Space</Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography fontWeight="bold">2 Wheeler</Typography>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Typography fontWeight="bold">4 Wheeler</Typography>
              </Grid>
              <Grid item xs={12} sm={2} container alignItems="center">
                <Typography>Open parking</Typography>
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  type="number"
                  placeholder="Enter amount"
                  fullWidth
                  value={rule.ownerOpen2W || ""}
                  onChange={(e) => handleOwnerChange(index, "ownerOpen2W", e.target.value)}
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                    inputProps: { min: 0 },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  type="number"
                  placeholder="Enter amount"
                  fullWidth
                  value={rule.ownerOpen4W || ""}
                  onChange={(e) => handleOwnerChange(index, "ownerOpen4W", e.target.value)}
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                    inputProps: { min: 0 },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={2} container alignItems="center">
                <Typography>Shaded parking</Typography>
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  type="number"
                  placeholder="Enter amount"
                  fullWidth
                  value={rule.ownerShaded2W || ""}
                  onChange={(e) => handleOwnerChange(index, "ownerShaded2W", e.target.value)}
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                    inputProps: { min: 0 },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  type="number"
                  placeholder="Enter amount"
                  fullWidth
                  value={rule.ownerShaded4W || ""}
                  onChange={(e) => handleOwnerChange(index, "ownerShaded4W", e.target.value)}
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                    inputProps: { min: 0 },
                  }}
                />
              </Grid>
            </Grid>
          </Box>
          <Box sx={{ p: 2, border: 1, borderColor: "#ddd", borderRadius: 1 }}>
            <Typography fontWeight="bold" sx={{ mb: 2 }}>
              Tenant Parking
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={2} container alignItems="center">
                <Typography>Open parking</Typography>
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  type="number"
                  placeholder="Enter amount"
                  fullWidth
                  value={rule.tenantOpen2W || ""}
                  onChange={(e) => handleOwnerChange(index, "tenantOpen2W", e.target.value)}
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                    inputProps: { min: 0 },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  type="number"
                  placeholder="Enter amount"
                  fullWidth
                  value={rule.tenantOpen4W || ""}
                  onChange={(e) => handleOwnerChange(index, "tenantOpen4W", e.target.value)}
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                    inputProps: { min: 0 },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={2} container alignItems="center">
                <Typography>Shaded parking</Typography>
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  type="number"
                  placeholder="Enter amount"
                  fullWidth
                  value={rule.tenantShaded2W || ""}
                  onChange={(e) => handleOwnerChange(index, "tenantShaded2W", e.target.value)}
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                    inputProps: { min: 0 },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  type="number"
                  placeholder="Enter amount"
                  fullWidth
                  value={rule.tenantShaded4W || ""}
                  onChange={(e) => handleOwnerChange(index, "tenantShaded4W", e.target.value)}
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                    inputProps: { min: 0 },
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
      ))}
    </Box>
  );
};

export default VehicleRules;
