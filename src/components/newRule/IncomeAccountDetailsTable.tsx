// src/components/IncomeAccountDetailsTable.jsx
"use client";
import React from "react";

import { Box, Table, TableHead, TableRow, TableCell, TableBody, TableContainer, Paper, Typography, FormControl, Select, MenuItem, TextField } from "@mui/material";
import { Controller } from "react-hook-form";

const IncomeAccountDetailsTable = ({
  control,
  selectedBankAccount,
  bankAccounts,
  cashAccounts,
  setSelectedBankAccount,
  setValue,
}) => (
  <Box>
    <Typography fontWeight="bold" sx={{ mb: 2 }}>Income Account Details</Typography>
    <TableContainer component={Paper} sx={{ mb: 3 }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell><Typography fontWeight="bold">Income Account</Typography></TableCell>
            <TableCell><Typography fontWeight="bold">Bank Account Ledger</Typography></TableCell>
            <TableCell><Typography fontWeight="bold">Cash Account Ledger</Typography></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>
              <Controller
                name="member_incomeaccount"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <TextField {...field} placeholder="Enter account" fullWidth disabled />
                )}
              />
            </TableCell>
            <TableCell>
              <FormControl fullWidth>
                <Select
                  value={selectedBankAccount}
                  onChange={(e) => setSelectedBankAccount(e.target.value)}
                  displayEmpty
                >
                  <MenuItem value="">Select Bank</MenuItem>
                  {bankAccounts.map((account) => (
                    <MenuItem key={account.id} value={account.id}>
                      {account.ledger_account_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </TableCell>
            <TableCell>
              <FormControl fullWidth>
                <Controller
                  name="cash_account"
                  control={control}
                  defaultValue=""
                  render={({ field }) => (
                    <Select
                      {...field}
                      value={field.value || ""}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        setValue("member_incomeaccount", e.target.value);
                      }}
                    >
                      <MenuItem value="">Select Cash</MenuItem>
                      {cashAccounts.map((account) => (
                        <MenuItem key={account.id} value={account.ledger_account_name}>
                          {account.ledger_account_name}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
              </FormControl>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  </Box>
);

export default IncomeAccountDetailsTable;
