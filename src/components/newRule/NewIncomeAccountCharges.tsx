// src/components/NewIncomeAccountCharges.jsx
"use client";
import React from "react";

import { Box, Grid, Typography, RadioGroup, FormControlLabel, Radio, TextField, FormControl, Select, MenuItem } from "@mui/material";
import { Controller } from "react-hook-form";
import axios from "axios";

import IncomeAccountDetailsTable from "./IncomeAccountDetailsTable";

const NewIncomeAccountCharges = ({
  control,
  errors,
  chargesType,
  setChargesType,
  incomeAccounts,
  bankAccounts,
  cashAccounts,
  selectedBankAccount,
  setSelectedBankAccount,
  setValue,
}) => (
  <Box sx={{ mb: 3 }}>
    <Typography fontWeight="bold">Charges</Typography>
    <RadioGroup row value={chargesType} onChange={(e) => setChargesType(e.target.value)} sx={{ mt: 2 }}>
      <FormControlLabel value="Fixed" control={<Radio />} label="Fixed" />
      <FormControlLabel value="Percentage" control={<Radio />} label="Percentage" />
      <FormControlLabel value="persqft" control={<Radio />} label="Per Sqft Area" />
    </RadioGroup>
    {chargesType === "Fixed" && (
      <Grid container alignItems="center" sx={{ mt: 3, mb: 3 }}>
        <Grid item sm={4}>
          <Typography>Amount / Month</Typography>
        </Grid>
        <Grid item sm={8}>
          <Controller
            name="other_income_fee"
            control={control}
            defaultValue=""
            rules={{ required: "Amount is required", min: { value: 0, message: "Amount cannot be negative" } }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Amount / Month"
                placeholder="0.00"
                fullWidth
                error={!!errors.other_income_fee}
                helperText={errors.other_income_fee?.message}
                InputProps={{
                  startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                  inputProps: { min: 0, step: "any" },
                }}
                sx={{ mt: 2 }}
                onChange={(e) => {
                  const value = e.target.value;

                  if (value === "" || (!isNaN(value) && Number(value) >= 0)) {
                    field.onChange(value);
                  }
                }}
              />
            )}
          />
        </Grid>
      </Grid>
    )}
    {chargesType === "Percentage" && (
      <Grid container alignItems="center" sx={{ mt: 3, mb: 3 }}>
        <Grid item xs={12} sm={4} container alignItems="center">
          <Typography>Percentage</Typography>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Controller
            name="other_income_fee"
            control={control}
            defaultValue=""
            rules={{ required: "Amount is required", min: { value: 0, message: "Amount cannot be negative" } }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Amount / Month"
                placeholder="0.00"
                fullWidth
                error={!!errors.other_income_fee}
                helperText={errors.other_income_fee?.message}
                InputProps={{
                  startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                  inputProps: { min: 0, step: "any" },
                }}
                sx={{ mt: 2 }}
                onChange={(e) => {
                  const value = e.target.value;

                  if (value === "" || (!isNaN(value) && Number(value) >= 0)) {
                    field.onChange(value);
                  }
                }}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Controller
            name="percentageBase"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth>
                <Select
                  {...field}
                  displayEmpty
                  value={field.value || ""}
                  onChange={(e) => field.onChange(e.target.value)}
                  onOpen={async () => {
                    if (!incomeAccounts.length) {
                      try {
                        const response = await axios.get(
                          "https://societybackend.cubeone.in/api/admin/income-tracker-setting/readIncomeAccounts",
                          { withCredentials: true }
                        );

                        if (response.status === 200) {
                          const accounts = response.data.data;

                          setIncomeAccounts(accounts);

                          const defaultAccount = accounts.find(
                            (account) => account.account_name === "MaintenanceFee"
                          );

                          if (defaultAccount && !field.value) {
                            field.onChange(defaultAccount.id);
                          }
                        }
                      } catch (error) {
                        console.warn("Error fetching income accounts:", error);
                      }
                    }
                  }}
                >
                  <MenuItem value="">
                    <em>Select Base</em>
                  </MenuItem>
                  {incomeAccounts.map((account) => (
                    <MenuItem key={account.id} value={account.id}>
                      {account.account_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
        </Grid>
      </Grid>
    )}
    {chargesType === "persqft" && (
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={4} sm={4} container alignItems="center">
          <Typography>Fee / Sqft Area / Month</Typography>
        </Grid>
        <Grid item xs={8} sm={8}>
          <Controller
            name="other_income_fee"
            control={control}
            defaultValue=""
            rules={{ required: "Amount is required", min: { value: 0, message: "Amount cannot be negative" } }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Amount / Month"
                placeholder="0.00"
                fullWidth
                error={!!errors.other_income_fee}
                helperText={errors.other_income_fee?.message}
                InputProps={{
                  startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                  inputProps: { min: 0, step: "any" },
                }}
                sx={{ mt: 2 }}
                onChange={(e) => {
                  const value = e.target.value;

                  if (value === "" || (!isNaN(value) && Number(value) >= 0)) {
                    field.onChange(value);
                  }
                }}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={4} container alignItems="center">
          <Typography>Fee / Sqft Open Area / Month</Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <Controller
            name="other_open_income_fee"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                type="number"
                placeholder="0.00"
                fullWidth
                InputProps={{
                  startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                  inputProps: { min: 0 },
                }}
              />
            )}
          />
        </Grid>
      </Grid>
    )}
    <IncomeAccountDetailsTable
      control={control}
      selectedBankAccount={selectedBankAccount}
      bankAccounts={bankAccounts}
      cashAccounts={cashAccounts}
      setSelectedBankAccount={setSelectedBankAccount}
      setValue={setValue}
    />
  </Box>
);

export default NewIncomeAccountCharges;
