// src/components/NOCSection.jsx
"use client";
import React from "react";

import { Box, Grid, Typography, Divider, RadioGroup, FormControlLabel, Radio, TextField } from "@mui/material";
import { Controller } from "react-hook-form";

const NOCSection = ({ control, errors, feeType, setFeeType, incomeAccounts }) => (
  <Box sx={{ p: 2, mt: 2, border: 1, borderColor: "#ddd", borderRadius: 1 }}>
    <Typography fontWeight="bold" sx={{ mb: 2 }}>Non-Occupancy Charges</Typography>
    <Divider sx={{ mb: 2 }} />
    <RadioGroup row value={feeType} onChange={(e) => setFeeType(e.target.value)} sx={{ mb: 2 }}>
      <FormControlLabel value="Simple NOC" control={<Radio />} label="Simple NOC" />
      <FormControlLabel value="persqft" control={<Radio />} label="Per SqFt Area" />
      <FormControlLabel value="Advance NOC" control={<Radio />} label="Advance NOC" />
    </RadioGroup>
    {feeType === "Simple NOC" && (
      <>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4} container alignItems="center">
            <Typography>Tenant occupied flat :</Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <Controller
              name="noc_tenant"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <TextField
                  {...field}
                  type="number"
                  placeholder="0.00"
                  fullWidth
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                    inputProps: { min: 0 }
                  }}
                  error={!!errors.noc_tenant}
                  helperText={errors.noc_tenant?.message}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={4} container alignItems="center">
            <Typography>Vacant flat :</Typography>
          </Grid>
          <Grid item xs={12} sm={8}>
            <Controller
              name="noc_vacant"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <TextField
                  {...field}
                  type="number"
                  placeholder="0.00"
                  fullWidth
                  InputProps={{
                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                    inputProps: { min: 0 }
                  }}
                  error={!!errors.noc_vacant}
                  helperText={errors.noc_vacant?.message}
                />
              )}
            />
          </Grid>
          <Box sx={{ mt: 3, p: 2, border: 1, borderColor: "#ddd", borderRadius: 1, backgroundColor: "#f9f9f9", ml: 3 }}>
            <Typography fontWeight="bold">Notes:</Typography>
            <Typography>If you have NOC charges based on the Occupancy of a flat use this rule</Typography>
            <Typography>Example 1 : Tenant occupied flat : Charges Rs.500.00 per month</Typography>
            <Typography>Example 2 : Vacant flat : Charges Rs.2.00 per sq.ft.per month</Typography>
          </Box>
        </Grid>
      </>
    )}
    {feeType === "persqft" && (
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={4} sm={4} container alignItems="center">
          <Typography>Fee / Sqft Area / Month</Typography>
        </Grid>
        <Grid item xs={8} sm={8}>
          <Controller
            name="nocarea"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <TextField
                {...field}
                type="number"
                placeholder="0.00"
                fullWidth
                InputProps={{
                  startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                  inputProps: { min: 0 }
                }}
                error={!!errors.nocarea}
                helperText={errors.nocarea?.message}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={4} container alignItems="center">
          <Typography>Fee / Sqft Open Area / Month</Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <Controller
            name="open_nocarea"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <TextField
                {...field}
                type="number"
                placeholder="0.00"
                fullWidth
                InputProps={{
                  startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                  inputProps: { min: 0 }
                }}
                error={!!errors.open_nocarea}
                helperText={errors.open_nocarea?.message}
              />
            )}
          />
        </Grid>
      </Grid>
    )}
    {feeType === "Advance NOC" && (
      <Box>
        <Grid container spacing={2} alignItems="center">
          {incomeAccounts.map((account, idx) => (
            <React.Fragment key={account.id}>
              <Grid item xs={12} sm={4} container alignItems="center">
                <Typography>{account.account_name}:</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <Controller
                  name={`nocadvamount_${idx + 1}`}
                  control={control}
                  defaultValue={0}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      type="number"
                      placeholder="0"
                      fullWidth
                      error={!!error}
                      helperText={error ? error.message : ""}
                      InputProps={{
                        endAdornment: <Typography sx={{ ml: 1 }}>%</Typography>,
                        inputProps: { min: 0 }
                      }}
                    />
                  )}
                />
              </Grid>
            </React.Fragment>
          ))}
          <Box sx={{ mt: 3, p: 2, border: 1, borderColor: "#ddd", borderRadius: 1, backgroundColor: "#f9f9f9", ml: 3 }}>
            <Typography fontWeight="bold">Notes:</Typography>
            <Typography>This rule will be applied only to Flats having Occupancy set to Tenant.</Typography>
            <Typography>Example: NOC = 10% of Maintenance Fee + 10% of Electricity Charges + 10% of Water Charges.</Typography>
          </Box>
        </Grid>
      </Box>
    )}
  </Box>
);

export default NOCSection;
