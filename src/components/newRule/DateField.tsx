// src/components/DateField.jsx
"use client";
import React from "react";

import { Controller } from "react-hook-form";
import { Grid, TextField, IconButton } from "@mui/material";
import ClearIcon from "@mui/icons-material/Clear";

const DateField = ({ name, control, errors, onClear }) => (
  <Controller
    name={name}
    control={control}
    render={({ field }) => (
      <Grid container alignItems="center" spacing={1}>
        <Grid item xs>
          <TextField
            {...field}
            type="date"
            fullWidth
            placeholder="dd-mm-yyyy"
            error={!!errors[name]}
            helperText={errors[name]?.message}
          />
        </Grid>
        {onClear && (
          <Grid item>
            <IconButton color="error" onClick={onClear}>
              <ClearIcon />
            </IconButton>
          </Grid>
        )}
      </Grid>
    )}
  />
);

export default DateField;
