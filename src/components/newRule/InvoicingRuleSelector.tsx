// src/components/InvoicingRuleSelector.jsx
"use client";
import React from "react";

import { Controller } from "react-hook-form";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
} from "@mui/material";

const InvoicingRuleSelector = ({
  control,
  loading,
  unitTypes,
  isSelectOpen,
  setSelectOpen,
  setValue,
}) => {
  return (
    <Controller
      name="create_invoicing_rule"
      control={control}
      render={({ field }) => (
        <FormControl fullWidth variant="outlined">
          <InputLabel id="unit-type-select-label">Select Unit Types</InputLabel>
          <Select
            {...field}
            labelId="unit-type-select-label"
            label="Select Unit Types"
            multiple
            open={isSelectOpen}
            onOpen={() => setSelectOpen(true)}
            onClose={() => setSelectOpen(false)}
            value={field.value || []}
            onChange={(e) => {
              const selectedValues = e.target.value;

              field.onChange(selectedValues);
              setValue("create_invoicing_rule", selectedValues);
              setSelectOpen(false);
            }}
            disabled={loading}
          >
            {loading ? (
              <MenuItem disabled>
                <CircularProgress size={20} />
              </MenuItem>
            ) : (
              unitTypes.map((unit) => (
                <MenuItem key={unit.id} value={unit.id}>
                  {unit.type}
                </MenuItem>
              ))
            )}
          </Select>
        </FormControl>
      )}
    />
  );
};

export default InvoicingRuleSelector;
