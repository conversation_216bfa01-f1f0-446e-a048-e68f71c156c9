// @ts-nocheck

"use client";
import React, { useState, useEffect } from "react";

import { useRouter } from "next/navigation";

import axios from "axios";
import {
    Grid,
    MenuItem,
    FormControl,
    InputLabel,
    Select,
    Card,
    CardContent,
    CardHeader,
    Button,
    TextField,
    Divider,
    Typography,
    CircularProgress,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Box,
    Tabs,
    Tab,
    IconButton,
    FormControlLabel,
    RadioGroup,
    Radio,
    Checkbox
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { toast } from 'react-toastify';
import * as yup from "yup";
import { useEffectOnce } from "react-use";
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import ClearIcon from '@mui/icons-material/Clear';
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";

interface FormValues {
    incomeaccount: string;
    create_invoicing_rule: string[];
    effective_date: string;
    taxClass: string;
    latePaymentInterest: boolean;
    feeType: string;
    feePerSqft: number;
    feePerSqftOpen: number;
    flat_area: number;
    vehicleRules: {
        id: number;
        removable: boolean;
        openParking?: number | string;
        shadedParking?: number | string;
    }[];
}

const requiredStyle = { color: "red", marginLeft: "4px" };

const nocAdvAmountSchemaFields = Array.from({ length: 12 }).reduce((acc, _, i) => {
    const fieldName = `nocadvamount_${i + 1}`;

    acc[fieldName] = yup
        .number()
        .typeError("Please enter a valid number")
        .min(0, "Value must be at least 0");
    
return acc;
}, {});



const validationSchema = yup.object().shape({
    // incomeaccount: yup.string().required("Income Account is required"),
    effective_date: yup.string().required("Rate Effective From is required"),

    // noc_tenant: yup
    //     .number()
    //     .typeError("Please enter a valid amount")
    //     .min(0, "Amount cannot be negative"),
    // noc_vacant: yup
    //     .number()
    //     .typeError("Please enter a valid amount")
    //     .min(0, "Amount cannot be negative"),
    // nocarea: yup
    //     .number()
    //     .typeError("Please enter a valid amount")
    //     .min(0, "Amount cannot be negative"),
    // open_nocarea: yup
    //     .number()
    //     .typeError("Please enter a valid amount")
    //     .min(0, "Amount cannot be negative"),
    // other_income_fee: yup
    //     .number()
    //     .typeError("Please enter a valid amount")
    //     .min(0, "Amount cannot be negative"),
    other_open_income_fee: yup
        .number()
        .typeError("Please enter a valid amount")
        .min(0, "Amount cannot be negative"),
    area: yup
        .number()
        .typeError("Please enter a valid amount")
        .min(0, "Amount cannot be negative"),
    open_area: yup
        .number()
        .typeError("Please enter a valid amount")
        .min(0, "Amount cannot be negative"),
    flat_area: yup
        .number()
        .typeError("Please enter a valid amount"),
    ...nocAdvAmountSchemaFields,
});

const MyForm = () => {
    const [activeTab, setActiveTab] = useState(0); // Track active tab (0 = Standard Rule, 1 = Floor-Based Rule)
    const [feeType, setFeeType] = useState("persqft"); // Correctly initialize feeType with useState

    const [incomeAccounts, setIncomeAccounts] = useState<
        { id: string; account_name: string }[]
    >([]);

    const [loading, setLoading] = useState(false);
    const [showTabs, setShowTabs] = useState(false); // State to control tab visibility
    const [isSinkingFund, setIsSinkingFund] = useState(false);
    const [isParking, setIsParking] = useState(false); // State to control Parking UI

    // Instead of just openParking, shadedParking
    // let's rename them "ownerOpen2W, ownerOpen4W, tenantOpen2W, tenantOpen4W"
    // (or whichever structure you prefer)
    const [vehicleRules, setVehicleRules] = useState([
        {
            id: Date.now(),
            removable: false,

            // For Owner
            ownerOpen2W: 0,
            ownerOpen4W: 0,
            ownerShaded2W: 0,
            ownerShaded4W: 0,

            // For Tenant
            tenantOpen2W: 0,
            tenantOpen4W: 0,
            tenantShaded2W: 0,
            tenantShaded4W: 0,
        },
    ]);

    const [sinkingFundType, setSinkingFundType] = useState("fixed");
    const [floorRuleType, setFloorRuleType] = useState("Simple Multiplier Rule");
    const [dynamicHeader, setDynamicHeader] = useState("");
    const [isNOC, setIsNOC] = useState(false);
    const [unitTypes, setUnitTypes] = useState<{ id: string | number; type: string }[]>([]);
    const [isSelectOpen, setSelectOpen] = useState(false);
    const [taxClasses, setTaxClasses] = useState<{ id: string | number; tax_class_name: string }[]>([]);
    const [isCreatingIncomeAccount, setIsCreatingIncomeAccount] = useState(false);
    const [chargesType, setChargesType] = useState("fixed");
    const [deactivationFields, setDeactivationFields] = useState([]);


    // For Simple Multiplier
    const [simpleFloorRules, setSimpleFloorRules] = useState([
        { id: "", floor: "", rate: "" },

        // ...
    ]);

    // For Slab Based
    const [slabFloorRules, setSlabFloorRules] = useState([
        { id: "", fromFloor: "", toFloor: "", rate: "" },

        // ...
    ]);

    const [bankAccounts, setBankAccounts] = useState([]);
    const [cashAccounts, setCashAccounts] = useState([]);
    const [savedIncomeAccount, setSavedIncomeAccount] = useState({ id: "", name: "" });

    const router = useRouter();
    
    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
        reset,
        watch,
    } = useForm<FormValues>({
        resolver: yupResolver(validationSchema),
        defaultValues: {
            incomeaccount: "",
            effective_date: "",
            latePaymentInterest: false,
        },
    });

    const addDeactivationField = () => {
        if (deactivationFields.length === 0) {
            setDeactivationFields([{ id: Date.now(), value: "" }]);
        }
    };

    const removeDeactivationField = (id) => {
        console.log("Removing field with ID:", id);
        setDeactivationFields([]);
    };

    const DateField = ({ name, onClear }: { name: string; onClear?: () => void }) => (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <Grid container alignItems="center" spacing={1}>
                    <Grid item xs>
                        <TextField
                            {...field}
                            type="date"
                            fullWidth
                            placeholder="dd-mm-yyyy"
                            error={!!errors[name]}
                            helperText={errors[name]?.message}
                        />
                    </Grid>
                    {onClear && (
                        <Grid item>
                            <IconButton color="error" onClick={onClear}>
                                <ClearIcon />
                            </IconButton>
                        </Grid>
                    )}
                </Grid>
            )}
        />
    );

    useEffect(() => {
        const fetchIncomeAccounts = async () => {
            try {
                const response = await axios.get(
                    `${process.env.NEXT_PUBLIC_API_URL}/admin/income-tracker-setting/readIncomeAccounts`, {
                    withCredentials: true
                }
                );

                const accounts = response.data.data || [];

                setIncomeAccounts(accounts);

                // Log the account_name for each account
                accounts.forEach(account => {
                    console.log("Account Name:", account.account_name);
                });

                // Find "Maintenance Fee" and set it as default
                const maintenanceFeeAccount = accounts.find(
                    (account) => account.account_name === "MaintenanceFee"
                );

                if (maintenanceFeeAccount) {
                    setValue("incomeaccount", maintenanceFeeAccount.id.toString()); // Set default value
                }
            } catch (error) {
                console.warn("Error fetching income accounts:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchIncomeAccounts();
    }, [setValue]);

    useEffectOnce(() => {
        const fetchUnitTypes = async () => {
            try {
                setLoading(true);

                const response = await axios.get(
                    `${process.env.NEXT_PUBLIC_API_URL}/admin/societies/listUnitType`,
                    {
                        withCredentials: true,
                    }
                );

                if (response.status === 200) {
                    const units = response.data.data.filter((item) => item.type); // Filter valid `type` items

                    setUnitTypes(units.map((unit) => ({ id: unit.id, type: unit.type })));
                }
            } catch (error) {
                console.warn("Error fetching unit types:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchUnitTypes();
    });

    useEffect(() => {
        if (!loading && incomeAccounts.length > 0) {
            const defaultAccount = incomeAccounts.find((acc) => acc.account_name === "MaintenanceFee");


            if (defaultAccount) {
                setValue("incomeaccount", defaultAccount.id.toString());


                // Trigger dependent states for "Maintenance Fee"
                setShowTabs(true); // Show tabs tied to "Maintenance Fee"
                setIsSinkingFund(false); // Reset other flags
                setIsParking(false);
                setIsNOC(false);
                setDynamicHeader("");
            }

        }
    }, [incomeAccounts, loading, setValue]);

    // Fetch Tax Classes from API on component mount
    useEffect(() => {
        const fetchTaxClasses = async () => {
            try {
                setLoading(true);

                const response = await axios.get(
                    "https://societybackend.cubeone.in/api/admin/tax/viewTax",
                    {
                        withCredentials: true,
                    }
                );

                if (response.status === 200 && Array.isArray(response.data.data)) {
                    const units = response.data.data;

                    console.log("API Response Tax Classes:", units); // Debug API data

                    setTaxClasses(
                        units.map((unit) => ({
                            id: unit.id,
                            tax_class_name: unit.tax_class_name || "Unnamed Tax Class", // Fallback for missing name
                        }))
                    );
                } else {
                    console.warn("Unexpected response format or status code.");
                }
            } catch (error) {
                console.warn("Error fetching tax classes:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchTaxClasses(); // Call the async function
    }, []);

    // Fetch bank accounts from API on component mount
    useEffect(() => {
        const fetchBankAccounts = async () => {
            try {
                const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/accounts/viewBankAccounts`, {
                    withCredentials: true,
                });

                if (response.data.status === "success") {
                    const accounts = response.data.data[0]?.rows || [];

                    setBankAccounts(accounts);
                } else {
                    console.warn("Failed to fetch bank accounts");
                }
            } catch (error) {
                console.warn("Error fetching bank accounts:", error);
            }
        };

        fetchBankAccounts();
    }, []);

    useEffect(() => {
        const fetchCashAccounts = async () => {
            try {
                const response = await axios.get(
                    `${process.env.NEXT_PUBLIC_API_URL}/admin/accounts/getCashAccounts`,
                    { withCredentials: true }
                );

                if (response.data && response.data.id === 43) {
                    const rows = response.data.rows[0]?.rows || [];

                    // Collect ledger_account_name from both levels (rows inside rows)
                    const extractedAccounts = rows.flatMap((item) => {
                        const children = item.rows || [];

                        const nestedAccounts = children.map((child) => ({
                            id: child.id,
                            ledger_account_name: child.ledger_account_name,
                        }));

                        // Include the parent level if it has a ledger_account_name
                        const currentLevelAccount =
                            item.ledger_account_name && item.entity_type !== 'group'
                                ? [{ id: item.id, ledger_account_name: item.ledger_account_name }]
                                : [];

                        return [...currentLevelAccount, ...nestedAccounts];
                    });

                    setCashAccounts(extractedAccounts);
                }
            } catch (error) {
                console.warn('Error fetching cash accounts:', error);
            }
        };

        fetchCashAccounts();
    }, []);

    const handleSelectChange = (value) => {
        setValue("taxClass", value); // Update the form value
        console.log("Selected Tax Class:", value); // Debug the selected value
    };

    const convertDateToYYYYMMDD = (dateString: string) => {
        if (!dateString) return "";
        const [year, month, day] = dateString.split("-");

        
return `${year}-${month}-${day}`;
    };




    const onSubmit = async (data) => {
        // 1) Convert user-selected date
        const formattedEffectiveDate = convertDateToYYYYMMDD(data.effective_date);

        // 2) Handle optional deactivation date
        let effective_date_to = formattedEffectiveDate;

        if (deactivationFields.length > 0) {
            const deactivationDateField = Object.keys(data).find((key) =>
                key.startsWith("deactivation_date_")
            );

            if (deactivationDateField) {
                effective_date_to = data[deactivationDateField]
                    ? convertDateToYYYYMMDD(data[deactivationDateField])
                    : formattedEffectiveDate;
            }
        }

        const NewIncomeAccount = data.new_incomeaccount;

        const createCustomRule = data.new_incomeaccount ? "1" : "0";

        const saveSelectedAccount = {
            id: savedIncomeAccount.id,
            account_name: savedIncomeAccount.name,
        };

        const IsParking = isParking ? saveSelectedAccount.account_name : "";


        const selectedAccount = incomeAccounts.find(
            (acc) => data.incomeaccount ? acc.id.toString() === data.incomeaccount.toString() : false
        ) || {}; // Default to an empty object to avoid errors

        // 4) type_of_rule & incomeaccount
        const tabLabel = activeTab === 0 ? "Standard Rule" : "Floor Based Rule";
        const type_of_rule = `${selectedAccount.account_name || NewIncomeAccount} | ${tabLabel} | ${feeType}`;

        // Ensure we use selectedAccount if available, otherwise fallback to savedIncomeAccount
        const finalAccount = selectedAccount.id
            ? selectedAccount
            : saveSelectedAccount; // Fallback to saved account

        // Constructing the final incomeaccount string
        const incomeaccount = finalAccount.id
            ? `${finalAccount.id}_${finalAccount.account_name}`
            : ""; // Ensure a valid value


        console.log("Final Account:", incomeaccount); // Debug the final account object

        // 5) Start constructing the base payload
        let payload = {
            type_of_rule,
            IsParking,
            invoiceruleid: "",
            incomeaccount,
            create_custom_rule: createCustomRule,
            new_incomeaccount: data.new_incomeaccount || "",
            unittype: data.create_invoicing_rule || [],
            effective_date: formattedEffectiveDate,
            effective_date_to,
            selected_taxes: data.taxClass || "",
            applicableTaxes: "21",
            latepayment: data.latePaymentInterest ? "1" : "0",
            noc_tenant: data.noc_tenant || "0.00",
            noc_vacant: data.noc_vacant || "0.00",
            totaladvanceNOC: 12,

            // NOC extracharge_area block
            ...Array.from({ length: 12 }).reduce((acc, _, idx) => {
                acc[`noc_extracharge_area_${idx + 1}`] = [
                    "MaintenanceFee",
                    "Parking",
                    "PropertyTax",
                    "SinkingFund",
                    "CommonElectricityCharges",
                    "WaterCharges",
                    "ElectionFund",
                    "FireAndSafety",
                    "SolarProjectAdvance",
                    "TestForHSNSAC",
                    "Qwertyuio",
                    "NewIncomeAccount",
                ][idx] || "";
                
return acc;
            }, {}),

            nocarea: data.nocarea || "0.00",
            open_nocarea: data.open_nocarea || "0.00",
            other_income_fee_rate: sinkingFundType,
            other_income_fee: data.other_income_fee || 0,

            other_percent_of_income_account: data.percentageBase,
            other_open_income_fee: data.other_open_income_fee || "0.00",
            member_incomeaccount: data.member_incomeaccount || "",

            select: {
                member: {
                    cashaccount: {
                        memberIncomeAccount: data.cash_account,
                    },
                    bankaccount: {
                        memberIncomeAccount: data.bank_account,
                    }
                }
            },
            vehicle_count: vehicleRules.length.toString(),
            ...vehicleRules.reduce((acc, rule, idx) => {
                acc[`ownerparkingid_${rule.id}-${idx + 1}`] = "";
                acc[`ownertwowheelerparkingamount_${rule.id}-${idx + 1}`] =
                    rule.openParking || "0.00";
                acc[`ownerfourwheelerparkingamount_${rule.id}-${idx + 1}`] =
                    rule.shadedParking || "0.00";
                
return acc;
            }, {}),

            // Are we on Standard or Floor tab? 
            selected_tab: activeTab === 0 ? "standard" : "floorbased",
            standardrule: feeType === "Flat Fee" ? "flat" : "sqftarea",

            area: data.area || "0.00",
            open_area: data.open_area || "0.00",
            flat_area: data.flat_area || "0.00",

            // We'll fill in the floor-based rules below
            floorbaserule: "both",   // We do both sets at once
            totalsimplefloorbase: "0",
            totalslabbaserecord: "0",

            save: "",
        };

        // 6) Merge nocadvamount_ (nocadvamount_1..12) from the form
        const nocadvamountPayload = Array.from({ length: 12 }).reduce((acc, _, idx) => {
            const i = idx + 1;

            acc[`nocadvamount_${i}`] = Number(data[`nocadvamount_${i}`]) || 0;
            
return acc;
        }, {});

        payload = { ...payload, ...nocadvamountPayload };

        // 7) Add the "vehicles" array
        const vehiclesPayload = vehicleRules.map((rule) => ({
            owner: {
                open_parking: {
                    two_wheeler: Number(rule.ownerOpen2W) || 0,
                    four_wheeler: Number(rule.ownerOpen4W) || 0,
                },
                shaded_parking: {
                    two_wheeler: Number(rule.ownerShaded2W) || 0,
                    four_wheeler: Number(rule.ownerShaded4W) || 0,
                },
            },
            tenant: {
                open_parking: {
                    two_wheeler: Number(rule.tenantOpen2W) || 0,
                    four_wheeler: Number(rule.tenantOpen4W) || 0,
                },
                shaded_parking: {
                    two_wheeler: Number(rule.tenantShaded2W) || 0,
                    four_wheeler: Number(rule.tenantShaded4W) || 0,
                },
            },
        }));

        payload.vehicles = vehiclesPayload;

        // =========================
        // 8) BOTH RULE SETS
        // =========================

        // 8A) Simple Multiplier
        // If user used 'simpleFloorRules' array to store them
        payload.totalsimplefloorbase = simpleFloorRules.length.toString();

        simpleFloorRules.forEach((rule, idx) => {
            const i = idx + 1;

            payload[`floorid_${i}`] = "";
            payload[`floors_${i}`] = rule.floor?.toString() || "0";
            payload[`floors_amount_charge_${i}`] = rule.rate?.toString() || "0.00";
        });

        // 8B) Slab Based
        payload.totalslabbaserecord = slabFloorRules.length.toString();

        slabFloorRules.forEach((rule, idx) => {
            const i = idx + 1;

            payload[`slabbasedfloorid_${i}`] = "";
            payload[`floors_from_${i}`] = rule.fromFloor?.toString() || "0";
            payload[`floors_to_${i}`] = rule.toFloor?.toString() || "0";
            payload[`slab_based_amount_${i}`] = rule.rate?.toString() || "0.00";
        });

        // 9) POST
        try {
            const response = await axios.post(
                `${process.env.NEXT_PUBLIC_API_URL}/admin/income-tracker-invoice-setting/add_rule`,
                payload,
                {
                    withCredentials: true,
                    validateStatus: () => true,
                }
            );

            if (response.data.status === "success") {
                toast.success(response.data.message || "Form submitted successfully!");
                router.push("/admin/society/income-tracker-invoice-setting/invoicelisting");
            } else {
                // Handle API errors if status is not success
                const { message, meta } = response.data;

                if (meta?.errors) {
                    Object.entries(meta.errors).forEach(([field, messages]) => {
                        if (Array.isArray(messages)) {
                            messages.forEach((msg) => {
                                toast.error(`${field}: ${msg}`);
                            });
                        } else if (typeof messages === "string") {
                            toast.error(`${field}: ${messages}`);
                        } else {
                            toast.error(`Error in ${field}: ${JSON.stringify(messages)}`);
                        }
                    });
                } else {
                    toast.error(message || "An unexpected error occurred.");
                }
            }
        } catch (error) {
            if (error.response && error.response.data) {
                const { message } = error.response.data;

                toast.error(message || "Server error. Please try again.");
            } else {
                toast.error("Network error. Please try again.");
            }
        }

    };

    const handleAddVehicleRule = () => {
        setVehicleRules((prev) => [
            ...prev,
            {
                id: Date.now(),
                removable: true,

                ownerOpen2W: 0,
                ownerOpen4W: 0,
                ownerShaded2W: 0,
                ownerShaded4W: 0,
                tenantOpen2W: 0,
                tenantOpen4W: 0,
                tenantShaded2W: 0,
                tenantShaded4W: 0,
            },
        ]);
    };

    const handleRemoveVehicleRule = (id) => {
        setVehicleRules((prev) => prev.filter((rule) => rule.id !== id));
    };

    const renderFloorBasedRules = () => (
        <Box sx={{ mt: 3, p: 2, borderRadius: 1 }}>
            <RadioGroup
                row
                value={floorRuleType}
                onChange={(e) => setFloorRuleType(e.target.value)}
                sx={{ mb: 2 }}
            >
                <FormControlLabel
                    value="Simple Multiplier Rule"
                    control={<Radio />}
                    label="Simple Multiplier Rule"
                />
                <FormControlLabel
                    value="Slab Based Rule"
                    control={<Radio />}
                    label="Slab Based Rule"
                />
            </RadioGroup>

            {floorRuleType === "Simple Multiplier Rule" && (
                <Box>
                    {simpleFloorRules.map((rule, index) => (
                        <Grid container spacing={2} key={rule.id} alignItems="center">
                            {/* Floor No */}
                            <Grid item xs={2}>
                                <Typography>Floor No:</Typography>
                            </Grid>
                            <Grid item xs={3}>
                                <FormControl fullWidth>
                                    <InputLabel>Floor</InputLabel>
                                    <Select
                                        value={rule.floor}
                                        label="Floor"
                                        onChange={(e) => {
                                            const updated = [...simpleFloorRules];

                                            updated[index].floor = Number(e.target.value);
                                            setSimpleFloorRules(updated);
                                        }}
                                        fullWidth
                                    >
                                        {[...Array(20).keys()].map((f) => (
                                            <MenuItem key={f} value={f}>
                                                {f}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>

                            {/* Rate Field: Controller with min=0 validation */}
                            <Grid item xs={6}>
                                <Controller
                                    name={`rate_${rule.id}`}
                                    control={control}
                                    defaultValue={rule.rate}
                                    rules={{
                                        min: { value: 0, message: "No negative values allowed" },
                                    }}
                                    render={({ field, fieldState: { error } }) => (
                                        <TextField
                                            {...field}

                                            type="number"

                                            // Show the numeric value from RHF
                                            value={field.value ?? ""}
                                            onChange={(e) => {
                                                // parse to a real number
                                                const newRate = parseFloat(e.target.value) || 0;

                                                const updated = [...simpleFloorRules];

                                                updated[index].rate = newRate;
                                                setSimpleFloorRules(updated);


                                                field.onChange(newRate);
                                            }}
                                            InputProps={{
                                                startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                endAdornment: (
                                                    <Typography sx={{ ml: 1 }}>/ Sqft / Area / Month</Typography>
                                                ),
                                                inputProps: { min: 0 },
                                            }}
                                            error={!!error}
                                            helperText={error ? error.message : ""}
                                            fullWidth
                                        />
                                    )}
                                />
                            </Grid>

                            {/* Add/Remove Buttons */}
                            <Grid item xs={1}>
                                {index === simpleFloorRules.length - 1 ? (
                                    <IconButton
                                        onClick={() => {
                                            setSimpleFloorRules((prev) => [
                                                ...prev,
                                                { id: Date.now(), floor: 0, rate: 0 },
                                            ]);
                                        }}
                                    >
                                        <AddIcon />
                                    </IconButton>
                                ) : (
                                    <IconButton
                                        onClick={() =>
                                            setSimpleFloorRules((prev) => prev.filter((r) => r.id !== rule.id))
                                        }
                                        color="error"
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                )}
                            </Grid>
                        </Grid>
                    ))}



                </Box>
            )}

            {floorRuleType === "Slab Based Rule" && (
                <Box>
                    {slabFloorRules.map((rule, index) => (
                        <Grid container spacing={2} key={rule.id} alignItems="center">
                            {/* FROM Floor */}
                            <Grid item xs={1}>
                                <Typography>From Floor:</Typography>
                            </Grid>
                            <Grid item xs={3}>
                                <Controller

                                    // unique name for each row's "fromFloor"
                                    name={`fromFloor_${rule.id}`}
                                    control={control}
                                    defaultValue={rule.fromFloor} // use local state's initial
                                    // Optional rules for negative check, etc.
                                    // rules={{
                                    //   min: { value: 0, message: "Cannot be negative" },
                                    // }}
                                    render={({ field, fieldState: { error } }) => (
                                        <FormControl fullWidth>
                                            <Select
                                                {...field}

                                                // The field.value is your "fromFloor" numeric
                                                value={field.value}
                                                onChange={(e) => {
                                                    const newVal = Number(e.target.value) || 0;

                                                    // 1) local state update
                                                    const updated = [...slabFloorRules];

                                                    updated[index].fromFloor = newVal;
                                                    setSlabFloorRules(updated);

                                                    // 2) inform RHF
                                                    field.onChange(newVal);
                                                }}
                                                error={!!error}
                                            >
                                                {[...Array(20).keys()].map((f) => (
                                                    <MenuItem key={f} value={f}>
                                                        {f}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                            {error && (
                                                <Typography color="error" variant="caption">
                                                    {error.message}
                                                </Typography>
                                            )}
                                        </FormControl>
                                    )}
                                />
                            </Grid>

                            {/* TO Floor */}
                            <Grid item xs={1}>
                                <Typography>To:</Typography>
                            </Grid>
                            <Grid item xs={3}>
                                <Controller
                                    name={`toFloor_${rule.id}`}
                                    control={control}
                                    defaultValue={rule.toFloor} // from local state
                                    render={({ field, fieldState: { error } }) => (
                                        <FormControl fullWidth>
                                            <Select
                                                {...field}
                                                value={field.value}
                                                onChange={(e) => {
                                                    const newVal = Number(e.target.value) || 0;

                                                    // local state
                                                    const updated = [...slabFloorRules];

                                                    updated[index].toFloor = newVal;
                                                    setSlabFloorRules(updated);

                                                    // RHF
                                                    field.onChange(newVal);
                                                }}
                                                error={!!error}
                                            >
                                                {[...Array(20).keys()].map((f) => (
                                                    <MenuItem key={f} value={f}>
                                                        {f}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                            {error && (
                                                <Typography color="error" variant="caption">
                                                    {error.message}
                                                </Typography>
                                            )}
                                        </FormControl>
                                    )}
                                />
                            </Grid>

                            {/* Rate */}
                            <Grid item xs={3}>
                                <Controller
                                    name={`slabRate_${rule.id}`}
                                    control={control}
                                    defaultValue={rule.rate}
                                    rules={{
                                        // e.g. to block negative:
                                        min: { value: 0, message: "No negative values allowed" },
                                    }}
                                    render={({ field, fieldState: { error } }) => (
                                        <TextField
                                            {...field}
                                            type="number"
                                            value={field.value}
                                            onChange={(e) => {
                                                const newRate = parseFloat(e.target.value) || 0;

                                                // local state
                                                const updated = [...slabFloorRules];

                                                updated[index].rate = newRate;
                                                setSlabFloorRules(updated);

                                                // RHF
                                                field.onChange(newRate);
                                            }}
                                            InputProps={{
                                                startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                endAdornment: (
                                                    <Typography sx={{ ml: 1 }}>/ Sqft / Area / Month</Typography>
                                                ),
                                                inputProps: {
                                                    min: 0, // HTML-level min
                                                },
                                            }}
                                            error={!!error}
                                            helperText={error ? error.message : ""}
                                            fullWidth
                                        />
                                    )}
                                />
                            </Grid>

                            {/* Add / Remove Buttons */}
                            <Grid item xs={1}>
                                {index === slabFloorRules.length - 1 ? (
                                    <IconButton
                                        onClick={() => {
                                            setSlabFloorRules((prev) => [
                                                ...prev,
                                                { id: Date.now(), fromFloor: 0, toFloor: 0, rate: 0 },
                                            ]);
                                        }}
                                    >
                                        <AddIcon />
                                    </IconButton>
                                ) : (
                                    <IconButton
                                        onClick={() =>
                                            setSlabFloorRules((prev) => prev.filter((r) => r.id !== rule.id))
                                        }
                                        color="error"
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                )}
                            </Grid>
                        </Grid>
                    ))}

                    <Box sx={{ mt: 3, p: 2, borderRadius: 1 }}>
                        <Typography fontWeight="bold">Examples:</Typography>
                        <Typography>
                            Example 1: If Floor Number 0 To 5, Maintenance Fee = ₹2.50 / Sqft / Month
                        </Typography>
                        <Typography>
                            Example 2: If Floor Number 6 To 10, Maintenance Fee = ₹3.00 / Sqft / Month
                        </Typography>
                        <Typography>
                            Example 3: If Floor Number 11 To 15, Maintenance Fee = ₹3.50 / Sqft / Month
                        </Typography>
                        <Divider sx={{ my: 2 }} />
                        <Typography fontWeight="bold">Note:</Typography>
                        <Typography>Total Area = Area + Open Area</Typography>
                    </Box>
                </Box>
            )}



        </Box>
    );

    const renderNOCSection = () => (
        <Box sx={{ p: 2, mt: 2, borderRadius: 1 }}>
            <Typography fontWeight="bold" sx={{ mb: 2 }}>
                Non-Occupancy Charges
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <RadioGroup
                row
                value={feeType}
                onChange={(e) => setFeeType(e.target.value)}
                sx={{ mb: 2 }}
            >
                <FormControlLabel
                    value="Simple NOC"
                    control={<Radio />}
                    label="Simple NOC"
                />
                <FormControlLabel
                    value="persqft"
                    control={<Radio />}
                    label="Per SqFt Area"
                />
                <FormControlLabel
                    value="Advance NOC"
                    control={<Radio />}
                    label="Advance NOC"
                />
            </RadioGroup>

            {feeType === "Simple NOC" && (
                <>
                    <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>Tenant occupied flat :</Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Controller
                                name="noc_tenant"
                                control={control}
                                defaultValue="" // or "0.00"
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        type="number"
                                        placeholder="0.00"
                                        fullWidth
                                        InputProps={{
                                            startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                            endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,

                                            // This ensures the browser won't allow numbers < 0:
                                            inputProps: { min: 0 }
                                        }}
                                        error={!!errors.noc_tenant}
                                        helperText={errors.noc_tenant?.message}
                                    />
                                )}
                            />
                        </Grid>

                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>Vacant flat :</Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Controller
                                name="noc_vacant"
                                control={control}
                                defaultValue="" // or "0.00"
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        type="number"
                                        placeholder="0.00"
                                        fullWidth
                                        InputProps={{
                                            startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                            endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,

                                            // This ensures the browser won't allow numbers < 0:
                                            inputProps: { min: 0 }
                                        }}
                                        error={!!errors.noc_vacant}
                                        helperText={errors.noc_vacant?.message}
                                    />
                                )}
                            />
                        </Grid>
                        <Box sx={{ mt: 3, p: 2, borderRadius: 1, backgroundColor: "#f9f9f9", ml: 3 }}>
                            <Typography fontWeight="bold">Notes:</Typography>
                            <Typography>
                                If you have NOC charges based on the Occupancy of a flat use this rule
                            </Typography>
                            <Typography>
                                Example 1 : Tenant occupied flat : Charges Rs.500.00 per month
                            </Typography>
                            <Typography>
                                Example 2 : Vacant flat : Charges Rs.2.00 per sq.ft.per month
                            </Typography>
                        </Box>
                    </Grid>
                </>
            )}

            {feeType === "persqft" && (
                <Grid container spacing={2} alignItems="center">
                    <Grid item xs={4} sm={4} container alignItems="center">
                        <Typography>Fee / Sqft Area / Month</Typography>
                    </Grid>
                    <Grid item xs={8} sm={8}>
                        <Controller
                            name="nocarea"
                            control={control}
                            defaultValue="" // or "0.00"
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    type="number"
                                    placeholder="0.00"
                                    fullWidth
                                    InputProps={{
                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,

                                        // This ensures the browser won't allow numbers < 0:
                                        inputProps: { min: 0 }
                                    }}
                                    error={!!errors.nocarea}
                                    helperText={errors.nocarea?.message}
                                />
                            )}
                        />
                    </Grid>

                    <Grid item xs={12} sm={4} container alignItems="center">
                        <Typography>Fee / Sqft Open Area / Month</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                        <Controller
                            name="open_nocarea"
                            control={control}
                            defaultValue="" // or "0.00"
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    type="number"
                                    placeholder="0.00"
                                    fullWidth
                                    InputProps={{
                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,

                                        // This ensures the browser won't allow numbers < 0:
                                        inputProps: { min: 0 }
                                    }}
                                    error={!!errors.open_nocarea}
                                    helperText={errors.open_nocarea?.message}
                                />
                            )}
                        />
                    </Grid>
                </Grid>
            )}

            {feeType === "Advance NOC" && (
                <Box>
                    <Grid container spacing={2} alignItems="center">
                        {incomeAccounts.map((account, idx) => (
                            <React.Fragment key={account.id}>
                                <Grid item xs={12} sm={4} container alignItems="center">
                                    <Typography>{account.account_name}:</Typography>
                                </Grid>
                                <Grid item xs={12} sm={8}>
                                    <Controller
                                        name={`nocadvamount_${idx + 1}`} // e.g. nocadvamount_1..nocadvamount_12
                                        control={control}
                                        defaultValue={0} // default numeric value
                                        render={({ field, fieldState: { error } }) => (
                                            <TextField
                                                {...field}
                                                type="number"
                                                placeholder="0"
                                                fullWidth

                                                // If there's a validation error, show it in red
                                                error={!!error}
                                                helperText={error ? error.message : ""}
                                                InputProps={{
                                                    endAdornment: <Typography sx={{ ml: 1 }}>%</Typography>,
                                                    inputProps: { min: 0 }, // HTML-level min
                                                }}
                                            />
                                        )}
                                    />
                                </Grid>
                            </React.Fragment>
                        ))}

                        <Box
                            sx={{
                                mt: 3,
                                p: 2,
                                borderRadius: 1,
                                backgroundColor: "#f9f9f9",
                                ml: 3,
                            }}
                        >
                            <Typography fontWeight="bold">Notes:</Typography>
                            <Typography>
                                This rule will be applied only to Flats having Occupancy set to Tenant.
                            </Typography>
                            <Typography>
                                Example: NOC = 10% of Maintenance Fee + 10% of Electricity Charges + 10%
                                of Water Charges.
                            </Typography>
                        </Box>
                    </Grid>
                </Box>

            )}
        </Box>
    );

    const handleCancel = () => {
        router.back();
    }

    return (
        <Card>
            <CardHeader
                title="New Rule"
                action={
                    <Button
                        variant="contained"
                        color={isCreatingIncomeAccount ? "secondary" : "primary"}
                        onClick={() => {
                            setIsCreatingIncomeAccount(!isCreatingIncomeAccount);

                            if (!isCreatingIncomeAccount) {
                                // ✅ Switching to TextField mode: Save both ID and name
                                const selectedId = watch("incomeaccount");
                                const selectedAccount = incomeAccounts.find((acc) => acc.id.toString() === selectedId?.toString());

                                setSavedIncomeAccount({
                                    id: selectedId,
                                    name: selectedAccount?.account_name || "",
                                });

                                console.log("Saved Dropdown Value:", selectedId, selectedAccount?.account_name);

                                reset({
                                    new_incomeaccount: "",  // Clear TextField input
                                    member_incomeaccount: "", // Prevent ID sync issue
                                });
                            } else {
                                // ✅ Switching back to Dropdown mode: Restore both ID and Name
                                reset({
                                    incomeaccount: savedIncomeAccount.id, // Restore selected ID
                                    member_incomeaccount: savedIncomeAccount.name, // Restore selected Name
                                });

                                console.log("Restored Dropdown Value:", savedIncomeAccount.id, savedIncomeAccount.name);
                            }
                        }}
                    >
                        {isCreatingIncomeAccount ? "Back" : "New Income Account"}
                    </Button>
                }
            />
            <Divider />
            <CardContent>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Grid container spacing={3}>
                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>
                                Income Account <span style={requiredStyle}>*</span>
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            {isCreatingIncomeAccount ? (
                                <Controller
                                    name="new_incomeaccount"
                                    control={control}
                                    defaultValue=""
                                    render={({ field }) => (
                                        <TextField
                                            {...field}
                                            placeholder="Enter new income account name"
                                            fullWidth
                                            error={!!errors.new_incomeaccount}
                                            helperText={errors.new_incomeaccount?.message}
                                            onChange={(e) => {
                                                field.onChange(e.target.value);
                                                setValue("member_incomeaccount", e.target.value); // Sync with member_incomeaccount
                                            }}
                                        />
                                    )}
                                />
                            ) : (
                                <Controller
                                    name="incomeaccount"
                                    control={control}
                                    defaultValue={savedIncomeAccount.id} // Ensure previously selected ID is set
                                    render={({ field }) => (
                                        <FormControl fullWidth variant="outlined">
                                            <InputLabel id="income-account-label">Income Account</InputLabel>
                                            <Select
                                                {...field}
                                                labelId="income-account-label"
                                                label="Income Account"
                                                disabled={loading}
                                                value={field.value || savedIncomeAccount.id} // Restore saved value
                                                onChange={(e) => {
                                                    const selectedValue = e.target.value;

                                                    field.onChange(selectedValue); // Update form state

                                                    // Find the selected account
                                                    const selectedAccount = incomeAccounts.find(
                                                        (account) => account.id.toString() === selectedValue.toString()
                                                    );

                                                    console.log("Selected Account:", selectedAccount);

                                                    if (selectedAccount) {
                                                        // ✅ Store both ID and Name
                                                        setSavedIncomeAccount({
                                                            id: selectedAccount.id,
                                                            name: selectedAccount.account_name,
                                                        });

                                                        // ✅ Sync the selected account name with member_incomeaccount
                                                        setValue("member_incomeaccount", selectedAccount.account_name, {
                                                            shouldValidate: true,
                                                            shouldDirty: true,
                                                        });

                                                        setShowTabs(["MaintenanceFee", "PropertyTax"].includes(selectedAccount.account_name));
                                                        setIsParking(selectedAccount.account_name === "Parking");
                                                        setIsNOC(selectedAccount.account_name === "Noc");

                                                        const sinkingFundStatus = !["MaintenanceFee", "PropertyTax", "Parking", "Noc"].includes(selectedAccount.account_name);

                                                        setIsSinkingFund(sinkingFundStatus);
                                                        console.log(`isSinkingFund set to ${sinkingFundStatus} for account: ${selectedAccount.account_name}`);

                                                        if (sinkingFundStatus) {
                                                            const dynamicHeader = selectedAccount.account_name.replace(/([a-z])([A-Z])/g, '$1 $2') + " Charges";

                                                            setDynamicHeader(dynamicHeader);
                                                            console.log("Dynamic Header:", dynamicHeader);
                                                        }
                                                    }
                                                }}
                                            >
                                                {loading ? (
                                                    <MenuItem disabled>
                                                        <CircularProgress size={20} />
                                                    </MenuItem>
                                                ) : (
                                                    incomeAccounts.map((account) => (
                                                        <MenuItem key={account.id} value={account.id}>
                                                            {account.account_name}
                                                        </MenuItem>
                                                    ))
                                                )}
                                            </Select>
                                        </FormControl>
                                    )}
                                />
                            )}

                        </Grid>
                        {/* Invoicing Rule */}
                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>
                                Create Invoicing Rule for <span style={requiredStyle}>*</span>
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Controller
                                name="create_invoicing_rule" // Match the key in your payload
                                control={control}
                                render={({ field }) => (
                                    <FormControl fullWidth variant="outlined">
                                        <InputLabel id="unit-type-select-label">Select Unit Types</InputLabel>
                                        <Select
                                            {...field}
                                            labelId="unit-type-select-label"
                                            label="Select Unit Types"
                                            multiple
                                            open={isSelectOpen} // Control dropdown visibility with state
                                            onOpen={() => setSelectOpen(true)}
                                            onClose={() => setSelectOpen(false)}
                                            value={field.value || []} // Ensure it's an array
                                            onChange={(e) => {
                                                const selectedValues = e.target.value;

                                                field.onChange(selectedValues); // Update the React Hook Form state
                                                setValue("create_invoicing_rule", selectedValues); // Explicitly set value
                                                setSelectOpen(false); // Close the dropdown after selection
                                            }}
                                            disabled={loading}
                                        >
                                            {loading ? (
                                                <MenuItem disabled>
                                                    <CircularProgress size={20} />
                                                </MenuItem>
                                            ) : (
                                                unitTypes.map((unit) => (
                                                    <MenuItem key={unit.id} value={unit.id}>
                                                        {unit.type}
                                                    </MenuItem>
                                                ))
                                            )}
                                        </Select>
                                    </FormControl>
                                )}
                            />
                            <Typography color="error">{errors.create_invoicing_rule?.message}</Typography>
                        </Grid>

                        {/* Rate Effective From */}
                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>
                                Rate Effective From <span style={requiredStyle}>*</span>
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8} sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 2 }}>
                            <DateField
                                name="effective_date" />
                            {deactivationFields.length === 0 && (
                                <Button
                                    variant="contained"
                                    size="small"
                                    color="secondary"
                                    onClick={addDeactivationField}
                                    startIcon={<CalendarMonthIcon />}
                                >
                                    Deactivation Date
                                </Button>
                            )}

                        </Grid>

                        {deactivationFields.map((field) => (
                            <Grid container spacing={1} alignItems="center" key={field.id}>
                                <Grid item xs={12} sm={4} container alignItems="center">
                                    <Typography>
                                        Rule Deactivation Date
                                    </Typography>
                                </Grid>
                                <Grid item xs={12} sm={8}>
                                    <DateField
                                        name={`deactivation_date_${field.id}`}
                                        onClear={() => removeDeactivationField(field.id)}
                                    />
                                </Grid>
                            </Grid>
                        ))}

                        {/* Tax Class */}
                        <Grid container spacing={2} mt={2}>
                            <Grid item xs={12} sm={4} container alignItems="center">
                                <Typography sx={{ ml: 2 }}>
                                    Tax Class
                                </Typography>
                            </Grid>
                            <Grid item xs={12} sm={8}>
                                <Controller
                                    name="taxClass"
                                    control={control}
                                    render={({ field }) => (
                                        <FormControl fullWidth>
                                            <Select
                                                {...field}
                                                value={field.value || ""}
                                                displayEmpty
                                                onChange={(e) => {
                                                    field.onChange(e.target.value); // Update the form value
                                                    handleSelectChange(e.target.value); // Update the value explicitly
                                                }}
                                                onClose={() =>
                                                    console.log("Dropdown closed")
                                                }
                                                onOpen={() =>
                                                    console.log("Dropdown opened")
                                                }
                                            >
                                                {loading ? (
                                                    <MenuItem disabled>
                                                        <CircularProgress
                                                            size={24}
                                                        />
                                                    </MenuItem>
                                                ) :
                                                    taxClasses.length > 0 && [
                                                        <MenuItem
                                                            value=""
                                                            disabled
                                                            key="default"
                                                        >
                                                            Select Tax Class
                                                        </MenuItem>,
                                                        ...taxClasses.map(
                                                            (taxClass) => (
                                                                <MenuItem
                                                                    key={taxClass.id}
                                                                    value={
                                                                        taxClass.id
                                                                    }
                                                                >
                                                                    {
                                                                        taxClass.tax_class_name
                                                                    }
                                                                </MenuItem>
                                                            )
                                                        ),
                                                    ]}
                                            </Select>
                                        </FormControl>
                                    )}
                                />
                                {errors.taxClass && (
                                    <Typography color="error">
                                        {errors.taxClass.message}
                                    </Typography>
                                )}
                            </Grid>
                        </Grid>

                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>
                                Late Payment Interest Applicable?&nbsp;
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Controller
                                name="latePaymentInterest"
                                control={control}
                                render={({ field }) => (
                                    <FormControlLabel
                                        control={<Checkbox {...field} checked={field.value} />}
                                        label=""
                                    />
                                )}
                            />
                        </Grid>
                    </Grid>
                    <Divider sx={{ my: 3 }} />

                    {
                        !isCreatingIncomeAccount && (
                            <>

                                {isParking && (
                                    <>
                                        <Box>
                                            {vehicleRules.map((rule, index) => (
                                                <Box key={rule.id} sx={{ position: "relative" }}>
                                                    <Box
                                                        sx={{
                                                            p: 2,
                                                            mt: 2,
                                                            borderRadius: 1,
                                                            mb: 4,
                                                        }}
                                                    >
                                                        <Typography fontWeight="bold" sx={{ mb: 2, color: "text.primary" }}>
                                                            {index === 0 ? "Owner Parking" : `Additional Vehicle Rule ${index}`}
                                                        </Typography>
                                                        <Divider sx={{ mb: 2, mt: 2 }} />

                                                        <Grid container spacing={2}>
                                                            <Grid item xs={12} sm={4}>
                                                                <Typography fontWeight="bold">Parking Space</Typography>
                                                            </Grid>
                                                            <Grid item xs={12} sm={4}>
                                                                <Typography fontWeight="bold">2 Wheeler</Typography>
                                                            </Grid>
                                                            <Grid item xs={12} sm={4}>
                                                                <Typography fontWeight="bold">4 Wheeler</Typography>
                                                            </Grid>

                                                            {/* OWNER - OPEN PARKING */}
                                                            <Grid item xs={12} sm={2} container alignItems="center">
                                                                <Typography>Open parking</Typography>
                                                            </Grid>
                                                            <Grid item xs={12} sm={5}>
                                                                <TextField
                                                                    type="number"
                                                                    placeholder="Enter amount"
                                                                    fullWidth
                                                                    value={rule.ownerOpen2W || ""}
                                                                    onChange={(e) => {
                                                                        const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                        const updated = [...vehicleRules];

                                                                        updated[index].ownerOpen2W = val;
                                                                        setVehicleRules(updated);
                                                                    }}
                                                                    InputProps={{
                                                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                        endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                        inputProps: { min: 0 },
                                                                    }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={5}>
                                                                <TextField
                                                                    type="number"
                                                                    placeholder="Enter amount"
                                                                    fullWidth
                                                                    value={rule.ownerOpen4W || ""}
                                                                    onChange={(e) => {
                                                                        const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                        const updated = [...vehicleRules];

                                                                        updated[index].ownerOpen4W = val;
                                                                        setVehicleRules(updated);
                                                                    }}
                                                                    InputProps={{
                                                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                        endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                        inputProps: { min: 0 },
                                                                    }}
                                                                />
                                                            </Grid>

                                                            {/* OWNER - SHADED PARKING */}
                                                            <Grid item xs={12} sm={2} container alignItems="center">
                                                                <Typography>Shaded parking</Typography>
                                                            </Grid>
                                                            <Grid item xs={12} sm={5}>
                                                                <TextField
                                                                    type="number"
                                                                    placeholder="Enter amount"
                                                                    fullWidth
                                                                    value={rule.ownerShaded2W || ""}
                                                                    onChange={(e) => {
                                                                        const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                        const updated = [...vehicleRules];

                                                                        updated[index].ownerShaded2W = val;
                                                                        setVehicleRules(updated);
                                                                    }}
                                                                    InputProps={{
                                                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                        endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                        inputProps: { min: 0 },
                                                                    }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={5}>
                                                                <TextField
                                                                    type="number"
                                                                    placeholder="Enter amount"
                                                                    fullWidth
                                                                    value={rule.ownerShaded4W || ""}
                                                                    onChange={(e) => {
                                                                        const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                        const updated = [...vehicleRules];

                                                                        updated[index].ownerShaded4W = val;
                                                                        setVehicleRules(updated);
                                                                    }}
                                                                    InputProps={{
                                                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                        endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                        inputProps: { min: 0 },
                                                                    }}
                                                                />
                                                            </Grid>
                                                        </Grid>
                                                    </Box>

                                                    {/* TENANT PARKING - 4 Fields Fixed */}
                                                    <Box sx={{ p: 2, borderRadius: 1 }}>
                                                        <Typography fontWeight="bold" sx={{ mb: 2 }}>
                                                            Tenant Parking
                                                        </Typography>
                                                        <Divider sx={{ mb: 2, mt: 2 }} />

                                                        <Grid container spacing={2}>
                                                            <Grid item xs={12} sm={2} container alignItems="center">
                                                                <Typography>Open parking</Typography>
                                                            </Grid>
                                                            <Grid item xs={12} sm={5}>
                                                                <TextField
                                                                    type="number"
                                                                    placeholder="Enter amount"
                                                                    fullWidth
                                                                    value={rule.tenantOpen2W || ""}
                                                                    onChange={(e) => {
                                                                        const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                        const updated = [...vehicleRules];

                                                                        updated[index].tenantOpen2W = val;
                                                                        setVehicleRules(updated);
                                                                    }}
                                                                    InputProps={{
                                                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                        endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                        inputProps: { min: 0 },
                                                                    }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={5}>
                                                                <TextField
                                                                    type="number"
                                                                    placeholder="Enter amount"
                                                                    fullWidth
                                                                    value={rule.tenantOpen4W || ""}
                                                                    onChange={(e) => {
                                                                        const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                        const updated = [...vehicleRules];

                                                                        updated[index].tenantOpen4W = val;
                                                                        setVehicleRules(updated);
                                                                    }}
                                                                    InputProps={{
                                                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                        endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                        inputProps: { min: 0 },
                                                                    }}
                                                                />
                                                            </Grid>

                                                            <Grid item xs={12} sm={2} container alignItems="center">
                                                                <Typography>Shaded parking</Typography>
                                                            </Grid>
                                                            <Grid item xs={12} sm={5}>
                                                                <TextField
                                                                    type="number"
                                                                    placeholder="Enter amount"
                                                                    fullWidth
                                                                    value={rule.tenantShaded2W || ""}
                                                                    onChange={(e) => {
                                                                        const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                        const updated = [...vehicleRules];

                                                                        updated[index].tenantShaded2W = val;
                                                                        setVehicleRules(updated);
                                                                    }}
                                                                    InputProps={{
                                                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                        endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                        inputProps: { min: 0 },
                                                                    }}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={5}>
                                                                <TextField
                                                                    type="number"
                                                                    placeholder="Enter amount"
                                                                    fullWidth
                                                                    value={rule.tenantShaded4W || ""}
                                                                    onChange={(e) => {
                                                                        const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                        const updated = [...vehicleRules];

                                                                        updated[index].tenantShaded4W = val;
                                                                        setVehicleRules(updated);
                                                                    }}
                                                                    InputProps={{
                                                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                        endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                        inputProps: { min: 0 },
                                                                    }}
                                                                />
                                                            </Grid>
                                                        </Grid>
                                                    </Box>

                                                    {rule.removable && (
                                                        <IconButton
                                                            onClick={() => handleRemoveVehicleRule(rule.id)}
                                                            sx={{ position: "absolute", top: 10, right: 10 }}
                                                            color="error"
                                                        >
                                                            <DeleteIcon />
                                                        </IconButton>
                                                    )}
                                                </Box>
                                            ))}


                                        </Box>
                                        <Box display="flex" justifyContent="flex-end" sx={{ mt: 2 }}>
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                onClick={handleAddVehicleRule}
                                            >
                                                + Add More Vehicle Charges
                                            </Button>
                                        </Box>
                                    </>
                                )}

                                {dynamicHeader && (
                                    <Box sx={{ p: 2, mt: 2, borderRadius: 1 }}>
                                        <Typography fontWeight="bold" sx={{ mb: 2 }}>
                                            {dynamicHeader}
                                        </Typography>
                                        <Divider sx={{ mb: 2, mt: 2 }} />
                                        <Grid container spacing={2}>
                                            <Grid item xs={12} sm={4} container alignItems="center">
                                                <Typography>Type</Typography>
                                            </Grid>
                                            <Grid item xs={12} sm={8}>
                                                <RadioGroup
                                                    row
                                                    value={sinkingFundType}
                                                    onChange={(e) => setSinkingFundType(e.target.value)}
                                                >
                                                    <FormControlLabel value="fixed" control={<Radio />} label="Fixed" />
                                                    <FormControlLabel value="percentage" control={<Radio />} label="Percentage" />
                                                    <FormControlLabel value="persqft" control={<Radio />} label="Per Sqft Area" />
                                                </RadioGroup>
                                            </Grid>


                                            {sinkingFundType === "percentage" && (
                                                <>
                                                    <Grid item xs={12} sm={4} container alignItems="center">
                                                        <Typography>Percentage</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={4}>
                                                        <Controller
                                                            name="other_income_fee"
                                                            control={control}
                                                            defaultValue=""
                                                            render={({ field }) => (
                                                                <TextField
                                                                    {...field}
                                                                    type="number"
                                                                    placeholder="Enter percentage"
                                                                    fullWidth
                                                                    value={field.value === "" || field.value === 0 ? "" : field.value} // ✅ Show empty when 0 is removed
                                                                    onChange={(e) => {
                                                                        const value = e.target.value;

                                                                        field.onChange(value === "" ? "" : Number(value)); // ✅ Allow empty and convert to number
                                                                    }}
                                                                    InputProps={{
                                                                        endAdornment: (
                                                                            <Typography sx={{ ml: 1 }}>%</Typography>
                                                                        ),
                                                                        inputProps: {
                                                                            min: 0
                                                                        },
                                                                    }}
                                                                    error={!!errors.other_income_fee}
                                                                    helperText={errors.other_income_fee?.message}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={4}>
                                                        <Controller
                                                            name="percentageBase"
                                                            control={control}
                                                            render={({ field }) => (
                                                                <FormControl fullWidth>
                                                                    <Select
                                                                        {...field}
                                                                        displayEmpty
                                                                        value={field.value || "Maintenance Fee"} // Default to "Maintenance Fee"
                                                                        onOpen={async () => {
                                                                            if (!incomeAccounts.length) {
                                                                                try {
                                                                                    const response = await axios.get(
                                                                                        "https://societybackend.cubeone.in/api/admin/income-tracker-setting/readIncomeAccounts",
                                                                                        {
                                                                                            withCredentials: true,
                                                                                        }
                                                                                    );

                                                                                    if (response.status === 200) {
                                                                                        const accounts = response.data.data;

                                                                                        setIncomeAccounts(accounts);

                                                                                        // Ensure "Maintenance Fee" is defaulted
                                                                                        const defaultAccount = accounts.find(
                                                                                            (account) => account.account_name === "MaintenanceFee"
                                                                                        );

                                                                                        if (defaultAccount && !field.value) {
                                                                                            field.onChange(defaultAccount.id);
                                                                                        }
                                                                                    }
                                                                                } catch (error) {
                                                                                    console.warn("Error fetching income accounts:", error);
                                                                                }
                                                                            }
                                                                        }}
                                                                    >
                                                                        <MenuItem value="">
                                                                            <em>Select Base</em>
                                                                        </MenuItem>
                                                                        {incomeAccounts.map((account) => (
                                                                            <MenuItem key={account.id} value={account.id}>
                                                                                {account.account_name}
                                                                            </MenuItem>
                                                                        ))}
                                                                    </Select>
                                                                </FormControl>
                                                            )}
                                                        />
                                                    </Grid>
                                                </>
                                            )}


                                            {sinkingFundType === "fixed" && (
                                                <>
                                                    <Grid item xs={12} sm={4} container alignItems="center">
                                                        <Typography>Amount / Month</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={8}>
                                                        <Controller
                                                            name="other_income_fee"
                                                            control={control}
                                                            defaultValue="" // or "0.00"
                                                            render={({ field }) => (
                                                                <TextField
                                                                    {...field}
                                                                    type="number"
                                                                    placeholder="0.00"
                                                                    fullWidth
                                                                    InputProps={{
                                                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                        inputProps: { min: 0 }
                                                                    }}
                                                                    error={!!errors.other_income_fee}
                                                                    helperText={errors.other_income_fee?.message}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                </>
                                            )}
                                            {sinkingFundType === "persqft" && (
                                                <Grid container spacing={2}>
                                                    <Grid item xs={12} sm={6} container alignItems="center">
                                                        <Typography>Fee / Sqft Area / Month</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={6}>
                                                        <Controller
                                                            name="other_income_fee"
                                                            control={control}
                                                            render={({ field }) => (
                                                                <TextField
                                                                    {...field}
                                                                    type="number"
                                                                    placeholder="0.00"
                                                                    fullWidth
                                                                    InputProps={{
                                                                        startAdornment: (
                                                                            <Typography sx={{ mr: 1 }}>₹</Typography>
                                                                        ),
                                                                        inputProps: {
                                                                            min: 0
                                                                        }
                                                                    }}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={6} container alignItems="center">
                                                        <Typography>Fee / Sqft Open Area / Month</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={6}>
                                                        <Controller
                                                            name="other_open_income_fee"
                                                            control={control}
                                                            render={({ field }) => (
                                                                <TextField
                                                                    {...field}
                                                                    type="number"
                                                                    placeholder="0.00"
                                                                    fullWidth
                                                                    InputProps={{
                                                                        startAdornment: (
                                                                            <Typography sx={{ mr: 1 }}>₹</Typography>
                                                                        ),
                                                                        inputProps: {
                                                                            min: 0
                                                                        }
                                                                    }}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                </Grid>
                                            )}

                                        </Grid>
                                    </Box>
                                )}

                                {showTabs && !isSinkingFund && !isParking && (
                                    <>
                                        <Grid container spacing={2}>
                                            <Grid item xs={12} sm={8}>
                                                <Tabs
                                                    value={activeTab}
                                                    onChange={(_, newValue) => setActiveTab(newValue)}
                                                    sx={{
                                                        borderBottom: 1,
                                                        borderColor: "divider",
                                                        ".MuiTab-root": {
                                                            minWidth: 150,
                                                            textTransform: "none",
                                                            fontSize: "1rem",
                                                            fontWeight: "bold",
                                                        },
                                                        ".Mui-selected": {
                                                            color: "#1976d2",
                                                        },
                                                        ".MuiTabs-indicator": {
                                                            backgroundColor: "#1976d2",
                                                        },
                                                    }}
                                                >
                                                    <Tab label="Standard Rule" />
                                                    <Tab label="Floor Based Rule" />
                                                </Tabs>
                                            </Grid>
                                        </Grid>

                                        {activeTab === 0 && (
                                            <Box sx={{ p: 2, mt: 2,  borderRadius: 1 }}>
                                                <Grid container spacing={2}>
                                                    <Grid item xs={12} sm={8}>
                                                        <RadioGroup
                                                            row
                                                            value={feeType}
                                                            onChange={(e) => setFeeType(e.target.value)}
                                                            sx={{ mb: 2 }}
                                                        >
                                                            <FormControlLabel
                                                                value="persqft"
                                                                control={<Radio />}
                                                                label="Per Sqft Area"
                                                            />
                                                            <FormControlLabel
                                                                value="Flat Fee"
                                                                control={<Radio />}
                                                                label="Flat Fee"
                                                            />
                                                        </RadioGroup>
                                                    </Grid>
                                                </Grid>

                                                {feeType === "Flat Fee" && (
                                                    <Grid container spacing={2}>
                                                        <Grid item xs={4} sm={4} container alignItems="center">
                                                            <Typography>Flat Fee / Month</Typography>
                                                        </Grid>
                                                        <Grid item xs={8} sm={8}>
                                                            <Controller
                                                                name="flat_area"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <TextField
                                                                        {...field}
                                                                        type="number"
                                                                        placeholder="Enter flat fee"
                                                                        fullWidth
                                                                        InputProps={{
                                                                            startAdornment: (
                                                                                <Typography sx={{ mr: 1 }}>₹</Typography>
                                                                            ),
                                                                            inputProps: {
                                                                                min: 0
                                                                            }
                                                                        }}
                                                                        error={!!errors.flat_area}
                                                                        helperText={errors.flat_area?.message}
                                                                    />
                                                                )}
                                                            />
                                                        </Grid>
                                                    </Grid>
                                                )}

                                                {feeType === "persqft" && (
                                                    <Grid container spacing={2}>
                                                        <Grid item xs={4} sm={4} container alignItems="center">
                                                            <Typography>
                                                                Fee / Sqft Area / Month
                                                            </Typography>
                                                        </Grid>
                                                        <Grid item xs={8} sm={8}>
                                                            <Controller
                                                                name="area"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <TextField
                                                                        {...field}
                                                                        type="number"
                                                                        placeholder="Enter fee per sqft"
                                                                        fullWidth
                                                                        InputProps={{
                                                                            startAdornment: (
                                                                                <Typography sx={{ mr: 1 }}>₹</Typography>
                                                                            ),
                                                                            inputProps: {
                                                                                min: 0
                                                                            }
                                                                        }}

                                                                        error={!!errors.area}
                                                                        helperText={errors.area?.message}
                                                                    />
                                                                )}
                                                            />
                                                        </Grid>

                                                        <Grid item xs={4} sm={4} container alignItems="center">
                                                            <Typography>
                                                                Fee / Sqft Open Area / Month
                                                            </Typography>
                                                        </Grid>
                                                        <Grid item xs={8} sm={8}>
                                                            <Controller
                                                                name="open_area"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <TextField
                                                                        {...field}
                                                                        type="number"
                                                                        placeholder="Enter fee per sqft open area"
                                                                        fullWidth
                                                                        InputProps={{
                                                                            startAdornment: (
                                                                                <Typography sx={{ mr: 1 }}>₹</Typography>
                                                                            ),
                                                                            inputProps: {
                                                                                min: 0
                                                                            }
                                                                        }}
                                                                        error={!!errors.open_area}
                                                                        helperText={errors.open_area?.message}
                                                                    />
                                                                )}
                                                            />
                                                        </Grid>
                                                    </Grid>
                                                )}
                                            </Box>
                                        )}

                                        {activeTab === 1 && renderFloorBasedRules()}
                                    </>
                                )}

                                {isNOC && renderNOCSection()}

                            </>
                        )
                    }

                    {isCreatingIncomeAccount && (
                        <>
                            <Box sx={{ mb: 3 }}>
                                <Typography fontWeight="bold">Charges</Typography>
                                <RadioGroup
                                    row
                                    value={chargesType}
                                    onChange={(e) => setChargesType(e.target.value)}
                                    sx={{ mt: 2 }}
                                >
                                    <FormControlLabel
                                        value="fixed"
                                        control={<Radio />}
                                        label="Fixed"
                                    />
                                    <FormControlLabel
                                        value="percentage"
                                        control={<Radio />}
                                        label="Percentage"
                                    />
                                    <FormControlLabel
                                        value="persqft"
                                        control={<Radio />}
                                        label="Per Sqft Area"
                                    />
                                </RadioGroup>
                                {chargesType === "fixed" && (
                                    <Grid container alignItems='center' sx={{ mt: 3, mb: 3 }}>

                                        <Grid item sm={4}>
                                            <Typography>Amount / Month</Typography>
                                        </Grid>

                                        <Grid item sm={8}>
                                            <Controller
                                                name="other_income_fee"
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Amount is required', min: { value: 0, message: 'Amount cannot be negative' } }}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        label="Amount / Month"
                                                        placeholder="0.00"
                                                        fullWidth
                                                        error={!!errors.other_income_fee}
                                                        helperText={errors.other_income_fee?.message}
                                                        InputProps={{
                                                            startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                            inputProps: { min: 0, step: 'any' }, // HTML5 validation (optional, additional check)
                                                        }}
                                                        sx={{ mt: 2 }}
                                                        onChange={(e) => {
                                                            const value = e.target.value;

                                                            if (value === '' || (!isNaN(value) && Number(value) >= 0)) {
                                                                field.onChange(value);
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />


                                        </Grid>
                                    </Grid>

                                )}
                                {chargesType === "percentage" && (
                                    <Grid container alignItems='center' sx={{ mt: 3, mb: 3 }}>
                                        <Grid item xs={12} sm={4} container alignItems="center">
                                            <Typography>Percentage</Typography>
                                        </Grid>
                                        <Grid item xs={12} sm={4}>
                                            <Controller
                                                name="other_income_fee"
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Amount is required', min: { value: 0, message: 'Amount cannot be negative' } }}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        label="Amount / Month"
                                                        placeholder="0.00"
                                                        fullWidth
                                                        error={!!errors.other_income_fee}
                                                        helperText={errors.other_income_fee?.message}
                                                        InputProps={{
                                                            startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                            inputProps: { min: 0, step: 'any' }, // HTML5 validation (optional, additional check)
                                                        }}
                                                        sx={{ mt: 2 }}
                                                        onChange={(e) => {
                                                            const value = e.target.value;

                                                            if (value === '' || (!isNaN(value) && Number(value) >= 0)) {
                                                                field.onChange(value);
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={4}>
                                            <Controller
                                                name="percentageBase"
                                                control={control}
                                                render={({ field }) => (
                                                    <FormControl fullWidth>
                                                        <Select
                                                            {...field}
                                                            displayEmpty
                                                            value={field.value || ""} // Use empty value for unselected
                                                            onChange={(e) => field.onChange(e.target.value)} // Pass selected value to field.onChange
                                                            onOpen={async () => {
                                                                if (!incomeAccounts.length) {
                                                                    try {
                                                                        const response = await axios.get(
                                                                            "https://societybackend.cubeone.in/api/admin/income-tracker-setting/readIncomeAccounts",
                                                                            { withCredentials: true }
                                                                        );

                                                                        if (response.status === 200) {
                                                                            const accounts = response.data.data;

                                                                            setIncomeAccounts(accounts);

                                                                            // Ensure "Maintenance Fee" is defaulted
                                                                            const defaultAccount = accounts.find(
                                                                                (account) => account.account_name === "MaintenanceFee"
                                                                            );

                                                                            if (defaultAccount && !field.value) {
                                                                                field.onChange(defaultAccount.id); // Set default account
                                                                            }
                                                                        }
                                                                    } catch (error) {
                                                                        console.warn("Error fetching income accounts:", error);
                                                                    }
                                                                }
                                                            }}
                                                        >
                                                            <MenuItem value="">
                                                                <em>Select Base</em>
                                                            </MenuItem>
                                                            {incomeAccounts.map((account) => (
                                                                <MenuItem key={account.id} value={account.id}>
                                                                    {account.account_name}
                                                                </MenuItem>
                                                            ))}
                                                        </Select>
                                                    </FormControl>
                                                )}
                                            />
                                        </Grid>

                                    </Grid>
                                )}

                                {chargesType === "persqft" && (
                                    <Grid container spacing={2} alignItems="center">
                                        <Grid item xs={4} sm={4} container alignItems="center">
                                            <Typography>Fee / Sqft Area / Month</Typography>
                                        </Grid>
                                        <Grid item xs={8} sm={8}>
                                            <Controller
                                                name="other_income_fee"
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Amount is required', min: { value: 0, message: 'Amount cannot be negative' } }}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        label="Amount / Month"
                                                        placeholder="0.00"
                                                        fullWidth
                                                        error={!!errors.other_income_fee}
                                                        helperText={errors.other_income_fee?.message}
                                                        InputProps={{
                                                            startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                            inputProps: { min: 0, step: 'any' }, // HTML5 validation (optional, additional check)
                                                        }}
                                                        sx={{ mt: 2 }}
                                                        onChange={(e) => {
                                                            const value = e.target.value;

                                                            if (value === '' || (!isNaN(value) && Number(value) >= 0)) {
                                                                field.onChange(value);
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />
                                        </Grid>

                                        <Grid item xs={12} sm={4} container alignItems="center">
                                            <Typography>Fee / Sqft Open Area / Month</Typography>
                                        </Grid>
                                        <Grid item xs={12} sm={8}>
                                            <Controller
                                                name="other_open_income_fee"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        type="number"
                                                        placeholder="0.00"
                                                        fullWidth
                                                        InputProps={{
                                                            startAdornment: (
                                                                <Typography sx={{ mr: 1 }}>₹</Typography>
                                                            ),
                                                            inputProps: {
                                                                min: 0
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                    </Grid>
                                )}

                                <Box>
                                    <Typography fontWeight="bold" sx={{ mb: 2 }}>
                                        Income Account Details
                                    </Typography>
                                    <Divider />
                                    <TableContainer component={Paper} sx={{ mb: 3 }}>
                                        <Table>
                                            <TableHead>
                                                <TableRow>
                                                    <TableCell><Typography fontWeight="bold">Income Account</Typography></TableCell>
                                                    <TableCell><Typography fontWeight="bold">Bank Account Ledger</Typography></TableCell>
                                                    <TableCell><Typography fontWeight="bold">Cash Account Ledger</Typography></TableCell>
                                                </TableRow>
                                            </TableHead>
                                            <TableBody>
                                                <TableRow>
                                                    <TableCell>
                                                        <Controller
                                                            name="member_incomeaccount"
                                                            control={control}
                                                            defaultValue=""
                                                            render={({ field }) => (
                                                                <TextField
                                                                    {...field}
                                                                    placeholder="Enter account"
                                                                    fullWidth
                                                                    disabled
                                                                />
                                                            )}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Controller
                                                            name="bank_account"
                                                            control={control}
                                                            defaultValue=""
                                                            render={({ field }) => (
                                                                <FormControl fullWidth>
                                                                    <Select
                                                                        {...field}
                                                                        value={field.value || ''}
                                                                        onChange={(e) => {
                                                                            field.onChange(e.target.value);
                                                                        }}
                                                                        displayEmpty
                                                                    >
                                                                        <MenuItem value="">Select Bank</MenuItem>
                                                                        {bankAccounts.map((account) => (
                                                                            <MenuItem key={account.id} value={account.id}>
                                                                                {account.ledger_account_name}
                                                                            </MenuItem>
                                                                        ))}
                                                                    </Select>
                                                                </FormControl>
                                                            )}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <FormControl fullWidth>
                                                            <Controller
                                                                name="cash_account"
                                                                control={control}
                                                                defaultValue=""
                                                                render={({ field }) => (
                                                                    <Select
                                                                        {...field}
                                                                        value={field.value || ''}
                                                                        onChange={(e) => {
                                                                            field.onChange(e.target.value);
                                                                        }}
                                                                    >
                                                                        <MenuItem value="">Select Cash</MenuItem>
                                                                        {cashAccounts.map((account) => (
                                                                            <MenuItem key={account.id} value={account.ledger_account_name}>
                                                                                {account.ledger_account_name}
                                                                            </MenuItem>
                                                                        ))}
                                                                    </Select>
                                                                )}
                                                            />
                                                        </FormControl>
                                                    </TableCell>
                                                </TableRow>
                                            </TableBody>
                                        </Table>
                                    </TableContainer>
                                </Box>
                            </Box>
                        </>
                    )}
                    <Box sx={{ display: 'flex', justifyContent: 'end' }}>
                        <Grid item xs={4} sx={{ mt: 2, ml: 2, mr: 2 }}>
                            <Button
                                type="submit"
                                variant="contained"
                                color="success"
                                fullWidth
                            >
                                Save
                            </Button>
                        </Grid>
                        <Grid item xs={4} sx={{ mt: 2 }}>
                            <Button
                                variant="contained"
                                color="primary"
                                fullWidth
                                onClick={handleCancel}
                            >
                                Cancel
                            </Button>
                        </Grid>
                    </Box>
                </form>
            </CardContent>
        </Card>
    );
};

export default MyForm;
