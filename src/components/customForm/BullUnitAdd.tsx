// @ts-nocheck

"use client";
import React, { useState, useEffect, useMemo } from "react";

import { useRouter } from "next/navigation";

import axios from "axios";
import {
  Grid2 as Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Card,
  CardContent,
  CardHeader,
  Button,
  TextField,
  Divider,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Popover,
  Paper,
  IconButton,
  Box
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { toast } from 'react-toastify'
import * as yup from "yup";
import { useEffectOnce } from "react-use";
import InfoIcon from '@mui/icons-material/Info';

const requiredStyle = { color: "red", marginLeft: "4px" };

interface FormValues {
  fk_unit_category_id: string;
  soc_building_name: string;
  floorStart: string;
  floorEnd: string;
  occupancy_type: string;
  charge_type: string;
  unit_wef: string;
  area: number;
  open_space_area: number;
  unit_total_water_inlets: number;
  status: string;
  soc_building_floor: number[];
  unit_flat_number: string[];
  unit_category: string;
}

interface RowData {
  id: number;
  floorNumber: string;
  unit_flat_number: string[];
}

const validationSchema = yup.object().shape({
  fk_unit_category_id: yup.string().required("Category is required"),
  soc_building_name: yup.string().required("Building is required"),
  floorStart: yup.string().required("Floor Start is required"),
  floorEnd: yup.string().required("Floor End is required"),
  occupancy_type: yup.string().required("Occupancy Type is required"),
  charge_type: yup.string().required("Charge Type is required"),
  soc_building_floor: yup.array().of(yup.number()).required(),
  unit_flat_number: yup.array().of(yup.string()).required(),
  unit_wef: yup.string()
    .required("Unit w.e.f is required")
    .matches(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  unit_total_water_inlets: yup
    .number()
    .min(0, "Water Inlet cannot be negative"),
  area: yup.number().min(0, "Area cannot be negative"),
  status: yup.string().required("Status is required"),
  open_space_area: yup.number().min(0, "Open Area cannot be negative"),
});

const FormComponent: React.FC = () => {
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      fk_unit_category_id: "",
      unit_category: "",
      soc_building_name: "",
      floorStart: "",
      floorEnd: "",
      occupancy_type: "",
      charge_type: "",
      unit_wef: "",
      area: 0,
      open_space_area: 0,
      unit_total_water_inlets: 0,
      status: "",
      soc_building_floor: [],
      unit_flat_number: [],
    },
  });

  const [categories, setCategories] = useState<string[]>([]);
  const [categoryDataMap, setCategoryDataMap] = useState<any>({});
  const [rows, setRows] = useState<RowData[]>([]);
  const [buildings, setBuildings] = useState<any[]>([]);
  const router = useRouter();
  const fk_unit_category_id = watch("fk_unit_category_id");
  const soc_building_name = watch("soc_building_name");
  const floorStart = watch("floorStart");
  const floorEnd = watch("floorEnd");
  const occupancy_type = watch("occupancy_type");
  const [popoverAnchor, setPopoverAnchor] = useState(null); // Stores the anchor element
  const [popoverId, setPopoverId] = useState(null); // Identifies which popover is open

  useEffectOnce(() => {
    const fetchCategories = async () => {
      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/societies/listUnitType`, {
          withCredentials: true,
        });

        const rawCategories = response.data.data;

        setCategories(rawCategories);

        const dataMap = rawCategories.reduce((map: any, item: any) => {
          if (item.type && item.id) {
            map[item.type] = item;
          }

          return map;
        }, {});

        setCategoryDataMap(dataMap);
      } catch (error) {
        console.warn("Error fetching categories:", error);
      }
    };

    fetchCategories();
  });

  useEffectOnce(() => {
    const fetchBuildings = async () => {
      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/building/list`, {
          withCredentials: true,
        });

        setBuildings(response.data.data);
      } catch (error) {
        console.warn("Error fetching buildings:", error);
      }
    };

    fetchBuildings();
  });

  useEffect(() => {
    if (fk_unit_category_id) {
      const selectedCategoryData = categoryDataMap[fk_unit_category_id] || {};

      setValue("area", selectedCategoryData.area_sq_ft ?? "0");
      setValue("open_space_area", selectedCategoryData.open_space_area_sq_ft ?? "0");
      setValue("unit_category", selectedCategoryData.type || "");
    }
  }, [fk_unit_category_id, categoryDataMap, setValue]);


  const floorArray = useMemo(() => {
    const selectedBuilding = buildings.find((building) => building.soc_building_name === soc_building_name);


    return selectedBuilding ? selectedBuilding.floor_array : [];
  }, [soc_building_name, buildings]);

  useEffect(() => {
    const start = parseInt(floorStart, 10);
    const end = parseInt(floorEnd, 10);

    if (!isNaN(start) && !isNaN(end) && start <= end) {
      const newRows = Array.from({ length: end - start + 1 }, (_, i) => start + i).map((soc_building_floor) => ({
        id: soc_building_floor,
        floorNumber: `Floor no.${soc_building_floor}`,
        unit_flat_number: [""],
      }));

      setRows(newRows);
      setValue(
        "soc_building_floor",
        newRows.map((row) => row.id)
      );
    } else {
      setRows([]);
      setValue("soc_building_floor", []);
    }
  }, [floorStart, floorEnd, setValue]);

  const handleAddTextField = (rowId: number) => {
    setRows((prevRows) =>
      prevRows.map((row) => (row.id === rowId ? { ...row, unit_flat_number: [...row.unit_flat_number, ""] } : row))
    );
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>, rowId: number, index: number) => {
    const { value } = event.target;

    setRows((prevRows) =>
      prevRows.map((row) =>
        row.id === rowId
          ? {
            ...row,
            unit_flat_number: row.unit_flat_number.map((item, idx) => (idx === index ? value : item)),
          }
          : row
      )
    );

    const updatedIdentifiedAs = rows
      .map((row) =>
        row.id === rowId
          ? {
            ...row,
            unit_flat_number: row.unit_flat_number.map((item, idx) => (idx === index ? value : item)),
          }
          : row
      )
      .flatMap((row) => row.unit_flat_number);

    setValue("unit_flat_number", updatedIdentifiedAs);
  };

  const handleSave = async (data: FormValues) => {
    try {
      const selectedBuilding = buildings.find((building) => building.soc_building_name === data.soc_building_name);
      const selectedCategory = categories.find((category) => category.type === data.fk_unit_category_id);

      const payload = {
        ...data,
        soc_building_name: selectedBuilding ? selectedBuilding.id : data.soc_building_name,
        fk_unit_category_id: selectedCategory ? selectedCategory.id : "",
        unit_category: selectedCategory ? selectedCategory.type : "",
      };

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/admin/units/bulkUnitAdd`,
        payload,
        {
          withCredentials: true
        }
      );

      if (response.status === 200) {
        const { status, message } = response.data;

        if (status === "success") {
          toast.success(message || "Data saved successfully!");
          reset();
          router.push("/admin/society/units/list");
          setRows([]);
        } else {
          toast.error(message || "An error occurred during the save operation.");
        }
      } else {
        toast.error("Unexpected server response. Please try again.");
      }
    } catch (error: any) {
      const apiError = error.response?.data;

      if (apiError) {
        const { message, status_code } = apiError;

        toast.error(`Error ${status_code || ""}: ${message || "An unknown error occurred."}`);
      } else {
        toast.error("Failed to save data. Please check your connection and try again.");
      }
    }
  };


  const handleCancel = () => {
    router.push("/admin/society/units/list/");
  };

  const chargeTypeOptions = useMemo(
    () => [
      { value: "onetime", label: "Onetime" },
      { value: "recurring", label: "Recurring" },
      { value: "rental", label: "On Demand", disabled: occupancy_type === "common" },
      { value: "free", label: "Free", disabled: occupancy_type === "reserved" },
    ],
    [occupancy_type]
  );

  const onBackClick = () => {
    router.back()
  }

  const isOpen = Boolean(popoverAnchor); // Determines if a popover is open

  const handleOpen = (event, id) => {
    setPopoverAnchor(event.currentTarget);
    setPopoverId(id); // Set the specific popover ID
  };

  const handleClose = () => {
    setPopoverAnchor(null);
    setPopoverId(null); // Reset the popover ID
  };


  const popoverDataMap = {
    categoriesName: [
      { id: 1, description: "It define type of unit structure based on their size like 1BHK for 1 bedroom hall kitchen" },
    ],
    chargeType: [
      { id: 1, description: "Please see help section below." }
    ],
    unitWEF: [
      { id: 1, description: "It define from what date is unit effective for allocation." },
    ],
  };

  const DynamicPopover = ({ isOpen, anchorEl, handleClose, popoverData }) => (
    <Popover
      open={isOpen}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
    >
      <Box p={2} maxWidth={400}>
        {
          popoverData.map((item) => (
            <Box key={item.id} mb={2}>
              {item.description && (
                <Typography variant="body2" color="textSecondary">
                  {item.description}
                </Typography>
              )}
            </Box>
          ))
        }
      </Box>
    </Popover>
  );





  return (
    <Grid container>
      <Grid item xs={12}>
        <Card>
          <CardHeader title="New Bulk Unit Identification"

            action={
              <Button onClick={onBackClick} variant="contained" color="secondary">
                Back
              </Button>
            }

          />
          <Divider />
          <form onSubmit={handleSubmit(handleSave)}>
            <Grid container sx={{ mt: 5, ml: 5, mb: 5 }} spacing={3}>
              <Grid size={8} direction="column">
                <Card>
                  <CardHeader title="Unit Identification Details" />
                  <CardContent>
                    <Grid container>
                      {/* Category Name */}
                      <Grid size={4} sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography>Category Name <span style={requiredStyle}>*</span></Typography>

                        <IconButton
                          onClick={(event) => handleOpen(event, 'categoriesName')}
                          aria-label="Info"
                        >
                          <InfoIcon color="primary" fontSize="small" />
                        </IconButton>


                      </Grid>
                      <Grid size={8} sx={{ mb: 5 }}>
                        <FormControl fullWidth>
                          <InputLabel id="category_name-label">
                            Category Name
                          </InputLabel>
                          <Controller
                            name="fk_unit_category_id"
                            control={control}
                            render={({ field }) => (
                              <Select {...field} labelId="category_name-label" label="Category Name">
                                {categories.map((cat, idx) => (
                                  <MenuItem key={idx} value={cat.type}>
                                    {cat.type}
                                  </MenuItem>
                                ))}
                              </Select>
                            )}
                          />
                        </FormControl>
                        <Typography color="red" >{errors.fk_unit_category_id?.message}</Typography>
                      </Grid>

                      {/* Building Name */}
                      <Grid size={4}>
                        <Typography>Building Name <span style={requiredStyle}>*</span></Typography>
                      </Grid>
                      <Grid size={8} sx={{ mb: 5 }}>
                        <FormControl fullWidth>
                          <InputLabel id="building_name-label">
                            Building Name
                          </InputLabel>
                          <Controller
                            name="soc_building_name"
                            control={control}
                            render={({ field }) => (
                              <Select {...field} labelId="building_name-label" label="Building Name">
                                {buildings.map((building, idx) => (
                                  <MenuItem key={idx} value={building.soc_building_name}>
                                    {building.soc_building_name}
                                  </MenuItem>
                                ))}
                              </Select>
                            )}
                          />
                        </FormControl>
                        <Typography color="red">{errors.soc_building_name?.message}</Typography>
                      </Grid>

                      {/* Floor Range */}
                      <Grid size={4}>
                        <Typography>Floor Range<span style={requiredStyle}>*</span></Typography>
                      </Grid>
                      <Grid size={4} sx={{ mb: 5 }}>
                        <Controller
                          name="floorStart"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth>
                              <InputLabel id="floorStart-label">
                                Floor Start
                              </InputLabel>
                              <Select {...field} labelId="floorStart-label" label="Floor Start">
                                {floorArray.map((soc_building_floor) => (
                                  <MenuItem key={soc_building_floor} value={soc_building_floor}>
                                    {soc_building_floor}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        />
                        <Typography color="red">{errors.floorStart?.message}</Typography>
                      </Grid>

                      <Grid size={4} sx={{ mb: 5 }}>
                        <Controller
                          name="floorEnd"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth>
                              <InputLabel id="floorEnd-label">
                                Floor End
                              </InputLabel>
                              <Select {...field} labelId="floorEnd-label" label="Floor End">
                                {floorArray.map((soc_building_floor) => (
                                  <MenuItem key={soc_building_floor} value={soc_building_floor}>
                                    {soc_building_floor}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        />
                        <Typography color="red">{errors.floorEnd?.message}</Typography>
                      </Grid>

                      {/* Rows Table */}
                      {rows.length > 0 && (
                        <Grid size={12} sx={{ mb: 5 }}>
                          <Card>
                            <TableContainer component={Paper}>
                              <Table>
                                <TableHead>
                                  <TableRow>
                                    <TableCell>Floor Number</TableCell>
                                    <TableCell>Identified As</TableCell>
                                    <TableCell>Action</TableCell>
                                  </TableRow>
                                </TableHead>
                                <TableBody>
                                  {rows.map((row) => (
                                    <TableRow key={row.id}>
                                      <TableCell>{row.floorNumber}</TableCell>
                                      <TableCell>
                                        {row.unit_flat_number.map((value, index) => (
                                          <TextField
                                            key={index}
                                            value={value}
                                            onChange={(event) => handleInputChange(event, row.id, index)}
                                            size="small"
                                            style={{
                                              width: "80px",
                                              height: "40px",
                                              marginRight: "8px",
                                            }}
                                          />
                                        ))}
                                      </TableCell>
                                      <TableCell>
                                        <Button variant="contained" onClick={() => handleAddTextField(row.id)}>
                                          Add
                                        </Button>
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </Card>
                        </Grid>
                      )}

                      {/* Occupancy Type */}
                      <Grid size={4}>
                        <Typography>Occupancy Type <span style={requiredStyle}>*</span></Typography>
                      </Grid>
                      <Grid size={8} sx={{ mb: 5 }}>
                        <Controller
                          name="occupancy_type"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth>
                              <InputLabel id="occupancy_type-label">
                                Occupancy Type
                              </InputLabel>
                              <Select {...field} labelId="occupancy_type-label" label="Occupancy Type">
                                <MenuItem value="reserved">Reserved to Member</MenuItem>
                                <MenuItem value="common">Common</MenuItem>
                              </Select>
                            </FormControl>
                          )}
                        />
                        <Typography color="red">{errors.occupancy_type?.message}</Typography>
                      </Grid>

                      {/* Charge Type */}
                      <Grid size={4} sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography>Charge Type<span style={requiredStyle}>*</span></Typography>

                        <IconButton
                          onClick={(event) => handleOpen(event, 'chargeType')}
                          aria-label="Info"
                        >
                          <InfoIcon color="primary" fontSize="small" />
                        </IconButton>
                      </Grid>
                      <Grid size={8} sx={{ mb: 5 }}>
                        <Controller
                          name="charge_type"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth>
                              <InputLabel id="charging_type-label">
                                Charge Type
                              </InputLabel>
                              <Select {...field} labelId="charging_type-label" label="Charge Type">
                                {chargeTypeOptions.map((option) => (
                                  <MenuItem key={option.value} value={option.value} disabled={option.disabled}>
                                    {option.label}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        />
                        <Typography color="red">{errors.charge_type?.message}</Typography>
                      </Grid>

                      {/* Charge Date */}
                      <Grid size={4} sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography>
                          Unit w.e.f<span style={requiredStyle}>*</span>
                          <IconButton
                            onClick={(event) => handleOpen(event, 'unitWEF')}
                            aria-label="Info"
                          >
                            <InfoIcon color="primary" fontSize="small" />
                          </IconButton>
                        </Typography>
                      </Grid>
                      <Grid size={8} sx={{ mb: 5 }}>
                        <Controller
                          name="unit_wef"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              type="date"
                              fullWidth
                              variant="outlined"
                              InputLabelProps={{ shrink: true }}
                              error={!!errors.unit_wef} // Show red border if validation fails
                            // helperText={errors.unit_wef?.message} // Show error message
                            />
                          )}
                        />
                        <Typography color="red">{errors.unit_wef?.message}</Typography>
                      </Grid>

                      {/* Area Fields */}
                      {fk_unit_category_id && (
                        <>
                          <Grid size={4}>
                            <Typography>Area</Typography>
                          </Grid>
                          <Grid size={8} sx={{ mb: 5 }}>
                            <Controller
                              name="area"
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  fullWidth
                                  variant="outlined"
                                  label="Area"
                                  type="number"
                                  InputProps={{
                                    endAdornment: (
                                      <Typography sx={{ mr: 1 }}>
                                        sqft {field.value || ''}
                                      </Typography>
                                    ),
                                  }}
                                />
                              )}
                            />

                            <Typography color="red">{errors.area?.message}</Typography>
                          </Grid>

                          <Grid size={4}>
                            <Typography>Open Area</Typography>
                          </Grid>
                          <Grid size={8} sx={{ mb: 5 }}>
                            <Controller
                              name="open_space_area"
                              control={control}
                              render={({ field }) => (
                                <TextField {...field} fullWidth variant="outlined" label="Open Area" type="number" InputProps={{
                                  endAdornment: (
                                    <Typography sx={{ mr: 1 }}>
                                      sqft {field.value || ''}
                                    </Typography>
                                  ),
                                }} />
                              )}
                            />
                            <Typography color="red">{errors.open_space_area?.message}</Typography>
                          </Grid>

                          <Grid size={4}>
                            <Typography>Water Inlet</Typography>
                          </Grid>
                          <Grid size={8} sx={{ mb: 5 }}>
                            <Controller
                              name="unit_total_water_inlets"
                              control={control}
                              render={({ field }) => (
                                <TextField {...field} fullWidth variant="outlined" label="Water Inlet" type="number" />
                              )}
                            />
                            <Typography color="red">{errors.unit_total_water_inlets?.message}</Typography>
                          </Grid>

                          <Grid size={4}>
                            <Typography>Status<span style={requiredStyle}>*</span></Typography>
                          </Grid>
                          <Grid size={8} sx={{ mb: 5 }}>
                            <Controller
                              name="status"
                              control={control}
                              render={({ field }) => (
                                <FormControl fullWidth>
                                  <InputLabel id="status-label">
                                    Status
                                  </InputLabel>
                                  <Select {...field} labelId="status-label" label="Status">
                                    <MenuItem value="Active">Active</MenuItem>
                                    <MenuItem value="Inactive">Inactive</MenuItem>
                                  </Select>
                                </FormControl>
                              )}
                            />
                            <Typography color="red">{errors.status?.message}</Typography>
                          </Grid>
                        </>
                      )}
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
              <Grid size={4}>
                {fk_unit_category_id === "open parking" && (

                  <Card>
                    <CardHeader title="Specify these details" />
                    <Divider />
                    <CardContent>
                      <Grid size={12}>
                        <FormControl fullWidth>
                          <InputLabel id="vehicle_type-label">Vehicle Type</InputLabel>
                          <Controller
                            name="vehicle_type"
                            control={control}
                            render={({ field }) => (
                              <Select {...field} labelId="vehicle_type-label" label="Vehicle Type">
                                <MenuItem value="Two Wheeler">Two Wheeler</MenuItem>
                                <MenuItem value="Four Wheeler">Four Wheeler</MenuItem>
                              </Select>
                            )}
                          />
                        </FormControl>
                        <Typography color="red">{errors.vehicle_type?.message}</Typography>
                      </Grid>

                    </CardContent>
                  </Card>



                )}
              </Grid>


              {/* Action Buttons */}
              <Grid size={12} sx={{ mt: 5 }}>
                <Button type="submit" variant="contained" color="primary" sx={{ mr: 2 }}>
                  Save
                </Button>
                <Button type="button" variant="outlined" color="error" onClick={reset} sx={{ mr: 2 }}>
                  Reset
                </Button>
                <Button type="button" variant="outlined" color="secondary" onClick={handleCancel}>
                  Cancel
                </Button>
              </Grid>
            </Grid>
          </form>

          <DynamicPopover
            isOpen={isOpen}
            anchorEl={popoverAnchor}
            handleClose={handleClose}
            popoverData={popoverDataMap[popoverId] || []}
          />


          {/* <Popover
            open={isOpen}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
          >
            <Box p={2} maxWidth={400}>
              {
                PopoverData.map((item) => (
                  <Box key={item.id} mb={2}>
                    {item.description && (
                      <Typography variant="body2" color="textSecondary">
                        {item.description}
                      </Typography>
                    )}
                  </Box>
                ))
              }
            </Box>
          </Popover> */}
        </Card>
      </Grid>
    </Grid>
  );
};

export default FormComponent;
