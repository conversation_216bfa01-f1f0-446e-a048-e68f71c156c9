// @ts-nocheck

"use client";
import React, { useState, useEffect } from "react";

import { useRouter, useParams } from "next/navigation";

import axios from "axios";
import {
    Grid,
    MenuItem,
    FormControl,
    InputLabel,
    Select,
    Card,
    CardContent,
    CardHeader,
    Button,
    TextField,
    Divider,
    Typography,
    CircularProgress,
    Box,
    Tabs,
    Tab,
    IconButton,
    FormControlLabel,
    RadioGroup,
    Radio,
    Checkbox
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { toast } from 'react-toastify';
import * as yup from "yup";
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import ClearIcon from '@mui/icons-material/Clear';

interface FormValues {
    incomeaccount: string;
    create_invoicing_rule: string[];
    effective_date: string;
    taxClass: string;
    latePaymentInterest: boolean;
    feeType: string;
    feePerSqft: number;
    feePerSqftOpen: number;
    flat_area: number;
    vehicleRules: {
        id: number;
        removable: boolean;
        openParking?: number | string;
        shadedParking?: number | string;
    }[];
}

const requiredStyle = { color: "red", marginLeft: "4px" };

const validationSchema = yup.object().shape({
    effective_date: yup.string().required("Rate Effective From is required")
});

const MyForm = () => {
    const [activeTab, setActiveTab] = useState(0);
    const [feeType, setFeeType] = useState("persqft");

    const [incomeAccounts, setIncomeAccounts] = useState<
        { id: string; account_name: string }[]
    >([]);

    const [loading, setLoading] = useState(false);
    const [showTabs, setShowTabs] = useState(false);
    const [isSinkingFund, setIsSinkingFund] = useState(false);
    const [isParking, setIsParking] = useState(false);

    const [vehicleRules, setVehicleRules] = useState([
        {
            id: Date.now(),
            removable: false,

            // For Owner
            ownerOpen2W: 0,
            ownerOpen4W: 0,
            ownerShaded2W: 0,
            ownerShaded4W: 0,

            // For Tenant
            tenantOpen2W: 0,
            tenantOpen4W: 0,
            tenantShaded2W: 0,
            tenantShaded4W: 0,
        },
    ]);

    const [sinkingFundType, setSinkingFundType] = useState("");
    const [floorRuleType, setFloorRuleType] = useState("Simple Multiplier Rule");
    const [dynamicHeader, setDynamicHeader] = useState("");
    const [isNOC, setIsNOC] = useState(false);
    const [taxClasses, setTaxClasses] = useState<{ id: string | number; tax_class_name: string }[]>([]);
    const [isCreatingIncomeAccount, setIsCreatingIncomeAccount] = useState(false);
    const [chargesType, setChargesType] = useState("fixed");


    // For Simple Multiplier
    const [simpleFloorRules, setSimpleFloorRules] = useState([
        // optional initial
        { id: Date.now(), floor: 0, rate: 0 }
    ]);

    const [slabFloorRules, setSlabFloorRules] = useState([
        { id: "", fromFloor: "", toFloor: "", rate: "" },
    ]);

    const router = useRouter();
    const { slug } = useParams();
    const id = slug?.[2];

    console.log( setIsCreatingIncomeAccount)

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
        reset,
    } = useForm<FormValues>({
        resolver: yupResolver(validationSchema),
        defaultValues: {
            incomeaccount: "",
            effective_date: "",
            latePaymentInterest: false,
        },
    });

    const DateField = ({ name, onClear }: { name: string; onClear?: () => void }) => (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <Grid container alignItems="center" spacing={1}>
                    <Grid item xs>
                        <TextField
                            {...field}
                            type="date"
                            fullWidth
                            placeholder="dd-mm-yyyy"
                            error={!!errors[name]}
                            helperText={errors[name]?.message}
                        />
                    </Grid>
                    {onClear && (
                        <Grid item>
                            <IconButton color="error" onClick={onClear}>
                                <ClearIcon />
                            </IconButton>
                        </Grid>
                    )}
                </Grid>
            )}
        />
    );

    useEffect(() => {
        if (!id) return;

        const fetchExisting = async () => {
            setLoading(true);

            try {
                const response = await axios.get(
                    `${process.env.NEXT_PUBLIC_API_URL}/admin/income-tracker-invoice-setting/add_rule/${id}`,
                    { withCredentials: true }
                );

                if (response.data.status === "success" && response.data.data.length > 0) {
                    const existing = response.data.data[0];

                    // Log the entire response object
                    console.log("=== Full API Response ===", response.data);

                    // Log the existing data object
                    console.log("=== Existing Data Object ===", existing);

                    // Log specific fields from the response
                    console.log("=== Key Fields from Response ===", {
                        account_name: existing?.account_name,
                        effective_date: existing?.effective_date,
                        unit_type: existing?.unit_type,
                        apply_late_payment_interest: existing?.apply_late_payment_interest,
                        area: existing?.area,
                        open_area: existing?.open_area,
                        flat_area: existing?.flat_area,
                        selection: existing?.selection,
                        noc_tenant: existing?.noc_tenant,
                        account_id: existing?.account_id,
                        other_income_fee: existing?.other_income_fee,
                        noc_vacant: existing?.noc_vacant,
                        open_nocarea: existing?.open_nocarea,
                        nocarea: existing?.nocarea,
                        other_percent_of_income_account: existing?.other_percent_of_income_account,
                        other_open_income_fee: existing?.other_open_income_fee,
                        selected_taxes: existing?.selected_taxes,
                        parking_details: existing?.parking_details,
                        totalsimplefloorbase: existing?.totalsimplefloorbase
                    });

                    // 1) Log the entire object from the server
                    console.log("=== Full server response for this rule ===", existing);

                    // 2) Parking logic
                    //    - We'll check if account_name === 'Parking' and handle parking_details
                    if (existing?.account_name === "Parking") {
                        setIsParking(true);
                        setShowTabs(false);
                        setIsNOC(false);
                        setIsSinkingFund(false);

                        // Parking data
                        const parkingDetails = existing.parking_details || [];

                        console.log("parking_details =>", parkingDetails);

                        // Group by vehicle_number
                        const detailsGroupedByVehicle = parkingDetails.reduce((acc, item) => {
                            const vNum = item.vehicle_number || 1;

                            if (!acc[vNum]) {
                                acc[vNum] = [];
                            }

                            acc[vNum].push(item);

                            return acc;
                        }, {});

                        // Map each group to a single rule object
                        const newVehicleRules = Object.keys(detailsGroupedByVehicle).map((vehicleNum) => {
                            const newRule = {
                                id: `vehicle-${vehicleNum}`,
                                removable: true,
                                ownerOpen2W: 0,
                                ownerOpen4W: 0,
                                ownerShaded2W: 0,
                                ownerShaded4W: 0,
                                tenantOpen2W: 0,
                                tenantOpen4W: 0,
                                tenantShaded2W: 0,
                                tenantShaded4W: 0,
                            };

                            detailsGroupedByVehicle[vehicleNum].map((pd) => {
                                const { member_type, parking_type, two_wheeler, four_wheeler } = pd;

                                if (member_type === "owner" && parking_type === "open parking") {
                                    newRule.ownerOpen2W = two_wheeler;
                                    newRule.ownerOpen4W = four_wheeler;
                                } else if (member_type === "owner" && parking_type === "shaded parking") {
                                    newRule.ownerShaded2W = two_wheeler;
                                    newRule.ownerShaded4W = four_wheeler;
                                } else if (member_type === "tenant" && parking_type === "open parking") {
                                    newRule.tenantOpen2W = two_wheeler;
                                    newRule.tenantOpen4W = four_wheeler;
                                } else if (member_type === "tenant" && parking_type === "shaded parking") {
                                    newRule.tenantShaded2W = two_wheeler;
                                    newRule.tenantShaded4W = four_wheeler;
                                }


                                return null;
                            });

                            return newRule;
                        });

                        setVehicleRules(newVehicleRules);
                    } else {
                        // If not Parking, handle other account names:
                        if (existing?.account_name === "MaintenanceFee") {
                            setShowTabs(true);
                            setIsSinkingFund(false);
                            setIsParking(false);
                            setIsNOC(false);
                        } else if (existing?.account_name === "SinkingFund") {
                            setIsSinkingFund(true);
                            setShowTabs(false);
                            setIsParking(false);
                            setIsNOC(false);
                        } else if (existing?.account_name === "Noc") {
                            setIsNOC(true);
                            setShowTabs(false);
                            setIsParking(false);
                            setIsSinkingFund(false);
                        } else if (existing?.account_name === "CommonElectricityCharges") {
                            setDynamicHeader("Common Electricity Charges");
                            setShowTabs(false);
                            setIsNOC(false);
                            setIsParking(false);
                            setIsSinkingFund(true);
                        } else if (existing?.account_name === "WaterCharges") {
                            setDynamicHeader("Water Charges");
                            setShowTabs(false);
                            setIsNOC(false);
                            setIsParking(false);
                            setIsSinkingFund(true);
                        } else if (existing?.account_name === "ElectionFund") {
                            setDynamicHeader("Election Fund");
                            setShowTabs(false);
                            setIsNOC(false);
                            setIsParking(false);
                            setIsSinkingFund(true);
                        } else if (existing?.account_name === "FireAndSafety") {
                            setDynamicHeader("Fire And Safety");
                            setShowTabs(false);
                            setIsNOC(false);
                            setIsParking(false);
                            setIsSinkingFund(true);
                        } else if (existing?.account_name === "PropertyTax") {
                            setShowTabs(true);
                            setIsNOC(false);
                            setIsParking(false);
                            setIsSinkingFund(false);
                        } else {
                            // default => sinking fund
                            setDynamicHeader(existing?.account_name);
                            setShowTabs(false);
                            setIsNOC(false);
                            setIsParking(false);
                            setIsSinkingFund(true);
                        }
                    }

                    // 3) Floor-based logic => Just logging for now
                    console.log("totalsimplefloorbase =>", existing.totalsimplefloorbase);
                    const totalFloors = Number(existing?.totalsimplefloorbase) || 0;

                    if (totalFloors > 0) {
                        for (let i = 1; i <= totalFloors; i++) {
                            const floorKey = `floors_${i}`;
                            const rateKey = `floors_amount_charge_${i}`;

                            console.log(`Index [${i}] =>`, {
                                [floorKey]: existing[floorKey],
                                [rateKey]: existing[rateKey],
                            });
                        }
                    } else {
                        console.log("No floors found, or totalsimplefloorbase is 0/missing.");
                    }

                    // 4) Sinking Fund Type (if needed)
                    const feeType = existing?.other_income_fee_rate;

                    if (feeType) {
                        setSinkingFundType(feeType);
                    }

                    // 5) Reset form with other fields
                    reset({
                        incomeaccount: existing?.account_id ?? "",
                        effective_date: existing?.effective_date ?? "",
                        unittype: existing?.unit_type ?? "",
                        latePaymentInterest: existing?.apply_late_payment_interest === 1,
                        area: existing?.area ?? "",
                        open_area: existing?.open_area ?? "",
                        flat_area: existing?.flat_area ?? "",
                        feeType: existing?.selection?.includes("Flat Fee") ? "Flat Fee" : "persqft",
                        noc_tenant: existing?.noc_tenant ?? "",
                        accountId: existing?.account_id ?? "",
                        other_income_fee: existing?.other_income_fee ?? "",
                        noc_vacant: existing?.noc_vacant ?? "",
                        open_nocarea: existing?.open_nocarea ?? "",
                        nocarea: existing?.nocarea ?? "",
                        percentage: existing?.other_percent_of_income_account || "",
                        other_open_income_fee: existing?.other_open_income_fee ?? "",
                        taxClass: existing?.selected_taxes || "",

                        // Set the account name in a separate field for display
                        accountName: existing?.account_name ?? "",
                    });

                    // Set the fee type based on selection
                    if (existing?.selection) {
                        setFeeType(existing.selection.includes("Flat Fee") ? "Flat Fee" : "persqft");
                    }

                    // Set the sinking fund type if it exists
                    if (existing?.other_income_fee_rate) {
                        setSinkingFundType(existing.other_income_fee_rate);
                    }

                    // Handle floor-based rules if they exist
                    if (existing?.totalsimplefloorbase) {
                        const totalFloors = Number(existing.totalsimplefloorbase) || 0;
                        const floorRules = [];

                        for (let i = 1; i <= totalFloors; i++) {
                            const floorKey = `floors_${i}`;
                            const rateKey = `floors_amount_charge_${i}`;

                            if (existing[floorKey] && existing[rateKey]) {
                                floorRules.push({
                                    id: Date.now() + i,
                                    floor: Number(existing[floorKey]),
                                    rate: Number(existing[rateKey])
                                });
                            }
                        }

                        if (floorRules.length > 0) {
                            setSimpleFloorRules(floorRules);
                        }
                    }

                    // Handle parking details if they exist
                    if (existing?.parking_details) {
                        const parkingDetails = existing.parking_details;

                        console.log("Processing parking details:", parkingDetails);

                        // Group by vehicle_number
                        const detailsGroupedByVehicle = parkingDetails.reduce((acc, item) => {
                            const vNum = item.vehicle_number || 1;

                            if (!acc[vNum]) {
                                acc[vNum] = [];
                            }

                            acc[vNum].push(item);

                            return acc;
                        }, {});

                        // Map each group to a vehicle rule
                        const newVehicleRules = Object.keys(detailsGroupedByVehicle).map((vehicleNum) => {
                            const newRule = {
                                id: Date.now() + Number(vehicleNum),
                                removable: true,
                                ownerOpen2W: 0,
                                ownerOpen4W: 0,
                                ownerShaded2W: 0,
                                ownerShaded4W: 0,
                                tenantOpen2W: 0,
                                tenantOpen4W: 0,
                                tenantShaded2W: 0,
                                tenantShaded4W: 0,
                            };

                            detailsGroupedByVehicle[vehicleNum].forEach((pd) => {
                                const { member_type, parking_type, two_wheeler, four_wheeler } = pd;

                                if (member_type === "owner" && parking_type === "open parking") {
                                    newRule.ownerOpen2W = Number(two_wheeler);
                                    newRule.ownerOpen4W = Number(four_wheeler);
                                } else if (member_type === "owner" && parking_type === "shaded parking") {
                                    newRule.ownerShaded2W = Number(two_wheeler);
                                    newRule.ownerShaded4W = Number(four_wheeler);
                                } else if (member_type === "tenant" && parking_type === "open parking") {
                                    newRule.tenantOpen2W = Number(two_wheeler);
                                    newRule.tenantOpen4W = Number(four_wheeler);
                                } else if (member_type === "tenant" && parking_type === "shaded parking") {
                                    newRule.tenantShaded2W = Number(two_wheeler);
                                    newRule.tenantShaded4W = Number(four_wheeler);
                                }
                            });

                            return newRule;
                        });

                        setVehicleRules(newVehicleRules);
                    }
                } else {
                    toast.error(response.data.message || "Failed to load rule.");
                }
            } catch (error) {
                console.warn("Error fetching existing rule:", error);
                toast.error("Could not load existing rule data.");
            } finally {
                setLoading(false);
            }
        };

        fetchExisting();
    }, [id, reset]);


    useEffect(() => {
        const fetchTaxClasses = async () => {
            try {
                setLoading(true);

                const response = await axios.get(
                    `${process.env.NEXT_PUBLIC_API_URL}/admin/tax/viewTax`,
                    {
                        withCredentials: true,
                    }
                );

                if (response.status === 200 && Array.isArray(response.data.data)) {
                    const units = response.data.data;

                    setTaxClasses(
                        units.map((unit) => ({
                            id: unit.id,
                            tax_class_name: unit.tax_class_name || "Unnamed Tax Class", // Fallback for missing name
                        }))
                    );
                } else {
                    console.warn("Unexpected response format or status code.");
                }
            } catch (error) {
                console.warn("Error fetching tax classes:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchTaxClasses(); // Call the async function
    }, []);

    useEffect(() => {
        const fetchIncomeAccounts = async () => {
            try {
                const response = await axios.get(
                    `${process.env.NEXT_PUBLIC_API_URL}/admin/income-tracker-setting/readIncomeAccounts`, {
                    withCredentials: true
                }
                );

                const accounts = response.data.data || [];

                setIncomeAccounts(accounts);

                // Find "Maintenance Fee" and set it as default
                const maintenanceFeeAccount = accounts.find(
                    (account) => account.account_name === "MaintenanceFee"
                );

                if (maintenanceFeeAccount) {
                    setValue("incomeaccount", maintenanceFeeAccount.id.toString()); // Set default value
                }
            } catch (error) {
                console.warn("Error fetching income accounts:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchIncomeAccounts();
    }, [setValue]);

    const handleSelectChange = (value) => {
        setValue("taxClass", value); // Update the form value
    };

    const convertDateToYYYYMMDD = (dateString: string) => {
        if (!dateString) return "";
        const [year, month, day] = dateString.split("-");


        return `${year}-${month}-${day}`;
    };

    const handleAddVehicleRule = () => {
        setVehicleRules((prev) => [
            ...prev,
            {
                id: Date.now(),
                removable: true,
                ownerOpen2W: 0,
                ownerOpen4W: 0,
                ownerShaded2W: 0,
                ownerShaded4W: 0,
                tenantOpen2W: 0,
                tenantOpen4W: 0,
                tenantShaded2W: 0,
                tenantShaded4W: 0,
            },
        ]);
    };

    const handleRemoveVehicleRule = (id) => {
        setVehicleRules((prev) => prev.filter((rule) => rule.id !== id));
    };

    const renderFloorBasedRules = () => (
        <Box sx={{ mt: 3, p: 2, borderRadius: 1 }}>
            <RadioGroup
                row
                value={floorRuleType}
                onChange={(e) => setFloorRuleType(e.target.value)}
                sx={{ mb: 2 }}
            >
                <FormControlLabel
                    value="Simple Multiplier Rule"
                    control={<Radio />}
                    label="Simple Multiplier Rule"
                />
                <FormControlLabel
                    value="Slab Based Rule"
                    control={<Radio />}
                    label="Slab Based Rule"
                />
            </RadioGroup>

            {floorRuleType === "Simple Multiplier Rule" && (
                <Box>
                    {simpleFloorRules.map((rule, index) => (
                        <Grid container spacing={2} key={rule.id} alignItems="center">
                            {/* Floor No */}
                            <Grid item xs={2}>
                                <Typography>Floor No:</Typography>
                            </Grid>
                            <Grid item xs={3}>
                                <FormControl fullWidth>
                                    <InputLabel>Floor</InputLabel>
                                    <Select
                                        value={rule.floor}
                                        onChange={(e) => {
                                            const updated = [...simpleFloorRules];

                                            updated[index].floor = Number(e.target.value);
                                            setSimpleFloorRules(updated);
                                        }}
                                    >
                                        {[...Array(20).keys()].map((f) => (
                                            <MenuItem key={f} value={f}>
                                                {f}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>

                            {/* Rate Field */}
                            <Grid item xs={6}>
                                <Controller
                                    name={`rate_${rule.id}`}
                                    control={control}
                                    defaultValue={rule.rate}

                                    // ...
                                    render={({ field, fieldState: { } }) => (
                                        <TextField
                                            {...field}
                                            type="number"
                                            value={field.value ?? ""}
                                            onChange={(e) => {
                                                const newRate = parseFloat(e.target.value) || 0;

                                                // 1) local state
                                                const updated = [...simpleFloorRules];

                                                updated[index].rate = newRate;
                                                setSimpleFloorRules(updated);

                                                // 2) inform RHF
                                                field.onChange(newRate);
                                            }}

                                        // ...
                                        />
                                    )}
                                />
                            </Grid>

                            {/* Add/Remove Buttons */}
                            <Grid item xs={1}>
                                {index === simpleFloorRules.length - 1 ? (
                                    <IconButton
                                        onClick={() => {
                                            setSimpleFloorRules((prev) => [
                                                ...prev,
                                                { id: Date.now(), floor: 0, rate: 0 },
                                            ]);
                                        }}
                                    >
                                        <AddIcon />
                                    </IconButton>
                                ) : (
                                    <IconButton
                                        onClick={() =>
                                            setSimpleFloorRules((prev) => prev.filter((r) => r.id !== rule.id))
                                        }
                                        color="error"
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                )}
                            </Grid>
                        </Grid>
                    ))}




                </Box>
            )}

            {floorRuleType === "Slab Based Rule" && (
                <Box>
                    {slabFloorRules.map((rule, index) => (
                        <Grid container spacing={2} key={rule.id} alignItems="center">
                            {/* FROM Floor */}
                            <Grid item xs={1}>
                                <Typography>From Floor:</Typography>
                            </Grid>
                            <Grid item xs={3}>
                                <Controller

                                    // unique name for each row's "fromFloor"
                                    name={`fromFloor_${rule.id}`}
                                    control={control}
                                    defaultValue={rule.fromFloor} // use local state's initial
                                    // Optional rules for negative check, etc.
                                    // rules={{
                                    //   min: { value: 0, message: "Cannot be negative" },
                                    // }}
                                    render={({ field, fieldState: { error } }) => (
                                        <FormControl fullWidth>
                                            <Select
                                                {...field}

                                                // The field.value is your "fromFloor" numeric
                                                value={field.value}
                                                onChange={(e) => {
                                                    const newVal = Number(e.target.value) || 0;

                                                    // 1) local state update
                                                    const updated = [...slabFloorRules];

                                                    updated[index].fromFloor = newVal;
                                                    setSlabFloorRules(updated);

                                                    // 2) inform RHF
                                                    field.onChange(newVal);
                                                }}
                                                error={!!error}
                                            >
                                                {[...Array(20).keys()].map((f) => (
                                                    <MenuItem key={f} value={f}>
                                                        {f}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                            {error && (
                                                <Typography color="error" variant="caption">
                                                    {error.message}
                                                </Typography>
                                            )}
                                        </FormControl>
                                    )}
                                />
                            </Grid>

                            {/* TO Floor */}
                            <Grid item xs={1}>
                                <Typography>To:</Typography>
                            </Grid>
                            <Grid item xs={3}>
                                <Controller
                                    name={`toFloor_${rule.id}`}
                                    control={control}
                                    defaultValue={rule.toFloor} // from local state
                                    render={({ field, fieldState: { error } }) => (
                                        <FormControl fullWidth>
                                            <Select
                                                {...field}
                                                value={field.value}
                                                onChange={(e) => {
                                                    const newVal = Number(e.target.value) || 0;

                                                    // local state
                                                    const updated = [...slabFloorRules];

                                                    updated[index].toFloor = newVal;
                                                    setSlabFloorRules(updated);

                                                    // RHF
                                                    field.onChange(newVal);
                                                }}
                                                error={!!error}
                                            >
                                                {[...Array(20).keys()].map((f) => (
                                                    <MenuItem key={f} value={f}>
                                                        {f}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                            {error && (
                                                <Typography color="error" variant="caption">
                                                    {error.message}
                                                </Typography>
                                            )}
                                        </FormControl>
                                    )}
                                />
                            </Grid>

                            {/* Rate */}
                            <Grid item xs={3}>
                                <Controller
                                    name={`slabRate_${rule.id}`}
                                    control={control}
                                    defaultValue={rule.rate}
                                    rules={{
                                        // e.g. to block negative:
                                        min: { value: 0, message: "No negative values allowed" },
                                    }}
                                    render={({ field, fieldState: { error } }) => (
                                        <TextField
                                            {...field}
                                            type="number"
                                            value={field.value}
                                            onChange={(e) => {
                                                const newRate = parseFloat(e.target.value) || 0;

                                                // local state
                                                const updated = [...slabFloorRules];

                                                updated[index].rate = newRate;
                                                setSlabFloorRules(updated);

                                                // RHF
                                                field.onChange(newRate);
                                            }}
                                            InputProps={{
                                                startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                endAdornment: (
                                                    <Typography sx={{ ml: 1 }}>/ Sqft / Area / Month</Typography>
                                                ),
                                                inputProps: {
                                                    min: 0, // HTML-level min
                                                },
                                            }}
                                            error={!!error}
                                            helperText={error ? error.message : ""}
                                            fullWidth
                                        />
                                    )}
                                />
                            </Grid>

                            {/* Add / Remove Buttons */}
                            <Grid item xs={1}>
                                {index === slabFloorRules.length - 1 ? (
                                    <IconButton
                                        onClick={() => {
                                            setSlabFloorRules((prev) => [
                                                ...prev,
                                                { id: Date.now(), fromFloor: 0, toFloor: 0, rate: 0 },
                                            ]);
                                        }}
                                    >
                                        <AddIcon />
                                    </IconButton>
                                ) : (
                                    <IconButton
                                        onClick={() =>
                                            setSlabFloorRules((prev) => prev.filter((r) => r.id !== rule.id))
                                        }
                                        color="error"
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                )}
                            </Grid>
                        </Grid>
                    ))}

                    <Box sx={{ mt: 3, p: 2, borderRadius: 1 }}>
                        <Typography fontWeight="bold">Examples:</Typography>
                        <Typography>
                            Example 1: If Floor Number 0 To 5, Maintenance Fee = ₹2.50 / Sqft / Month
                        </Typography>
                        <Typography>
                            Example 2: If Floor Number 6 To 10, Maintenance Fee = ₹3.00 / Sqft / Month
                        </Typography>
                        <Typography>
                            Example 3: If Floor Number 11 To 15, Maintenance Fee = ₹3.50 / Sqft / Month
                        </Typography>
                        <Divider sx={{ my: 2 }} />
                        <Typography fontWeight="bold">Note:</Typography>
                        <Typography>Total Area = Area + Open Area</Typography>
                    </Box>
                </Box>
            )}
        </Box>
    );

    const onSubmit = async (data) => {
        // 1) Convert user-selected date
        const formattedEffectiveDate = convertDateToYYYYMMDD(data.effective_date);

        const tabLabel = activeTab === 0 ? "Standard Rule" : "Floor Based Rule";
        const incomeaccount = `${data?.accountId}_${data.accountName}`;
        const type_of_rule = `${incomeaccount} | ${tabLabel} | ${feeType}`;

        // Ensure unittype is an array
        const unittype = Array.isArray(data?.unittype) ? data.unittype : [data?.unittype];

        // 5) Start constructing the base payload
        const payload = {
            IsParking: data.accountName === "Parking" ? "Parking" : "",
            invoiceruleid: "",
            type_of_rule,
            incomeaccount,
            create_custom_rule: "0",
            new_incomeaccount: data.new_incomeaccount || "",
            unittype, // Ensure this is always an array
            effective_date: formattedEffectiveDate,
            selected_taxes: data.taxClass || "",
            applicableTaxes: "21",
            latepayment: data.latePaymentInterest ? "1" : "0",
            noc_tenant: Number(data.noc_tenant) || 0,
            noc_vacant: Number(data.noc_vacant) || 0,
            totaladvanceNOC: 12,

            // NOC extracharge_area block
            ...Array.from({ length: 12 }).reduce((acc, _, idx) => {
                acc[`noc_extracharge_area_${idx + 1}`] = [
                    "MaintenanceFee",
                    "Parking",
                    "PropertyTax",
                    "SinkingFund",
                    "CommonElectricityCharges",
                    "WaterCharges",
                    "ElectionFund",
                    "FireAndSafety",
                    "SolarProjectAdvance",
                    "TestForHSNSAC",
                    "Qwertyuio",
                    "NewIncomeAccount",
                ][idx] || "";

                return acc;
            }, {}),

            nocarea: Number(data.nocarea) || 0,
            open_nocarea: Number(data.open_nocarea) || 0,
            other_income_fee_rate: sinkingFundType,
            other_income_fee: Number(data.other_income_fee) || 0,
            other_percent_of_income_account: data.other_percent_of_income_account,
            other_open_income_fee: Number(data.other_open_income_fee) || 0,
            member_incomeaccount: data.member_incomeaccount || "",

            vehicle_count: vehicleRules.length.toString(),
            ...vehicleRules.reduce((acc, rule, idx) => {
                acc[`ownerparkingid_${rule.id}-${idx + 1}`] = "";
                acc[`ownertwowheelerparkingamount_${rule.id}-${idx + 1}`] = rule.openParking || "0.00";
                acc[`ownerfourwheelerparkingamount_${rule.id}-${idx + 1}`] = rule.shadedParking || "0.00";

                return acc;
            }, {}),

            // Are we on Standard or Floor tab? 
            selected_tab: activeTab === 0 ? "standard" : "floorbased",
            standardrule: feeType === "Flat Fee" ? "flat" : "sqftarea",

            area: data.area || "0.00",
            open_area: data.open_area || "0.00",
            flat_area: data.flat_area || "0.00",

            // We'll fill in the floor-based rules below
            floorbaserule: "both",   // We do both sets at once
            totalsimplefloorbase: "0",
            totalslabbaserecord: "0",

            save: "",
        };

        // 6) Merge nocadvamount_ (nocadvamount_1..12) from the form
        const nocadvamountPayload = Array.from({ length: 12 }).reduce((acc, _, idx) => {
            const i = idx + 1;

            acc[`nocadvamount_${i}`] = Number(data[`nocadvamount_${i}`]) || 0;

            return acc;
        }, {});

        Object.assign(payload, nocadvamountPayload);

        // 7) Add the "vehicles" array
        const vehiclesPayload = vehicleRules.map((rule) => ({
            owner: {
                open_parking: {
                    two_wheeler: Number(rule.ownerOpen2W) || 0,
                    four_wheeler: Number(rule.ownerOpen4W) || 0,
                },
                shaded_parking: {
                    two_wheeler: Number(rule.ownerShaded2W) || 0,
                    four_wheeler: Number(rule.ownerShaded4W) || 0,
                },
            },
            tenant: {
                open_parking: {
                    two_wheeler: Number(rule.tenantOpen2W) || 0,
                    four_wheeler: Number(rule.tenantOpen4W) || 0,
                },
                shaded_parking: {
                    two_wheeler: Number(rule.tenantShaded2W) || 0,
                    four_wheeler: Number(rule.tenantShaded4W) || 0,
                },
            },
        }));

        payload.vehicles = vehiclesPayload;

        // 8) BOTH RULE SETS
        // 8A) Simple Multiplier
        payload.totalsimplefloorbase = simpleFloorRules.length.toString();

        simpleFloorRules.forEach((rule, idx) => {
            const i = idx + 1;

            payload[`floorid_${i}`] = "";
            payload[`floors_${i}`] = rule.floor?.toString() || "0";
            payload[`floors_amount_charge_${i}`] = rule.rate?.toString() || "0.00";
        });

        // 8B) Slab Based
        payload.totalslabbaserecord = slabFloorRules.length.toString();

        slabFloorRules.forEach((rule, idx) => {
            const i = idx + 1;

            payload[`slabbasedfloorid_${i}`] = "";
            payload[`floors_from_${i}`] = rule.fromFloor?.toString() || "0";
            payload[`floors_to_${i}`] = rule.toFloor?.toString() || "0";
            payload[`slab_based_amount_${i}`] = rule.rate?.toString() || "0.00";
        });

        // 9) PUT API Call to update the rule
        try {
            const response = await axios.put(
                `${process.env.NEXT_PUBLIC_API_URL}/admin/income-tracker-invoice-setting/add_rule/${id}`,
                payload,
                {
                    withCredentials: true,
                    validateStatus: () => true,
                }
            );

            if (response.data.status === "success") {
                toast.success(response.data.message || "Form submitted successfully!");
                router.push("/admin/society/income-tracker-invoice-setting/invoicelisting");
            } else {
                const { message, meta } = response.data;

                if (meta?.errors) {
                    Object.entries(meta.errors).forEach(([field, messages]) => {
                        if (Array.isArray(messages)) {
                            messages.forEach((msg) => toast.error(`${field}: ${msg}`));
                        } else if (typeof messages === "string") {
                            toast.error(`${field}: ${messages}`);
                        } else {
                            toast.error(`Error in ${field}: ${JSON.stringify(messages)}`);
                        }
                    });
                } else {
                    toast.error(message || "An unexpected error occurred.");
                }
            }
        } catch (error) {
            if (error.response && error.response.data) {
                const { message } = error.response.data;

                toast.error(message || "Server error. Please try again.");
            } else {
                toast.error("Network error. Please try again.");
            }
        }
    };

    const renderNOCSection = () => (
        <Box sx={{ p: 2, mt: 2, borderRadius: 1 }}>
            <Typography fontWeight="bold" sx={{ mb: 2 }}>
                Non-Occupancy Charges
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <RadioGroup
                row
                value={feeType}
                onChange={(e) => setFeeType(e.target.value)}
                sx={{ mb: 2 }}
            >
                <FormControlLabel
                    value="Simple NOC"
                    control={<Radio />}
                    label="Simple NOC"
                />
                <FormControlLabel
                    value="persqft"
                    control={<Radio />}
                    label="Per SqFt Area"
                />
                <FormControlLabel
                    value="Advance NOC"
                    control={<Radio />}
                    label="Advance NOC"
                />
            </RadioGroup>

            {feeType === "Simple NOC" && (
                <>
                    <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>Tenant occupied flat :</Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Controller
                                name="noc_tenant"
                                control={control}
                                defaultValue="" // Set this to "0.00" or other initial values if needed
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        type="number"
                                        placeholder="0.00"
                                        fullWidth
                                        InputProps={{
                                            startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                            endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                            inputProps: { min: 0 }
                                        }}
                                        error={!!errors.noc_tenant}
                                        helperText={errors.noc_tenant?.message}
                                    />
                                )}
                            />

                        </Grid>

                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>Vacant flat :</Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Controller
                                name="noc_vacant"
                                control={control}
                                defaultValue="" // or "0.00"
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        type="number"
                                        placeholder="0.00"
                                        fullWidth
                                        InputProps={{
                                            startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                            endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,

                                            // This ensures the browser won't allow numbers < 0:
                                            inputProps: { min: 0 }
                                        }}
                                        error={!!errors.noc_vacant}
                                        helperText={errors.noc_vacant?.message}
                                    />
                                )}
                            />
                        </Grid>
                        <Box sx={{ mt: 3, p: 2, borderRadius: 1, backgroundColor: "#f9f9f9", ml: 3 }}>
                            <Typography fontWeight="bold">Notes:</Typography>
                            <Typography>
                                If you have NOC charges based on the Occupancy of a flat use this rule
                            </Typography>
                            <Typography>
                                Example 1 : Tenant occupied flat : Charges Rs.500.00 per month
                            </Typography>
                            <Typography>
                                Example 2 : Vacant flat : Charges Rs.2.00 per sq.ft.per month
                            </Typography>
                        </Box>
                    </Grid>
                </>
            )}

            {feeType === "persqft" && (
                <Grid container spacing={2} alignItems="center">
                    <Grid item xs={4} sm={4} container alignItems="center">
                        <Typography>Fee / Sqft Area / Month</Typography>
                    </Grid>
                    <Grid item xs={8} sm={8}>
                        <Controller
                            name="nocarea"
                            control={control}
                            defaultValue="" // or "0.00"
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    type="number"
                                    placeholder="0.00"
                                    fullWidth
                                    InputProps={{
                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,

                                        // This ensures the browser won't allow numbers < 0:
                                        inputProps: { min: 0 }
                                    }}
                                    error={!!errors.nocarea}
                                    helperText={errors.nocarea?.message}
                                />
                            )}
                        />
                    </Grid>

                    <Grid item xs={12} sm={4} container alignItems="center">
                        <Typography>Fee / Sqft Open Area / Month</Typography>
                    </Grid>
                    <Grid item xs={12} sm={8}>
                        <Controller
                            name="open_nocarea"
                            control={control}
                            defaultValue="" // or "0.00"
                            render={({ field }) => (
                                <TextField
                                    {...field}
                                    type="number"
                                    placeholder="0.00"
                                    fullWidth
                                    InputProps={{
                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,

                                        // This ensures the browser won't allow numbers < 0:
                                        inputProps: { min: 0 }
                                    }}
                                    error={!!errors.open_nocarea}
                                    helperText={errors.open_nocarea?.message}
                                />
                            )}
                        />
                    </Grid>
                </Grid>
            )}

            {feeType === "Advance NOC" && (
                <Box>
                    <Grid container spacing={2} alignItems="center">
                        {incomeAccounts.map((account, idx) => (
                            <React.Fragment key={account.id}>
                                <Grid item xs={12} sm={4} container alignItems="center">
                                    <Typography>{account.account_name}:</Typography>
                                </Grid>
                                <Grid item xs={12} sm={8}>
                                    <Controller
                                        name={`nocadvamount_${idx + 1}`} // e.g. nocadvamount_1..nocadvamount_12
                                        control={control}
                                        defaultValue={0} // default numeric value
                                        render={({ field, fieldState: { error } }) => (
                                            <TextField
                                                {...field}
                                                type="number"
                                                placeholder="0"
                                                fullWidth

                                                // If there's a validation error, show it in red
                                                error={!!error}
                                                helperText={error ? error.message : ""}
                                                InputProps={{
                                                    endAdornment: <Typography sx={{ ml: 1 }}>%</Typography>,
                                                    inputProps: { min: 0 }, // HTML-level min
                                                }}
                                            />
                                        )}
                                    />
                                </Grid>
                            </React.Fragment>
                        ))}

                        <Box
                            sx={{
                                mt: 3,
                                p: 2,

                                borderRadius: 1,
                                backgroundColor: "#f9f9f9",
                                ml: 3,
                            }}
                        >
                            <Typography fontWeight="bold">Notes:</Typography>
                            <Typography>
                                This rule will be applied only to Flats having Occupancy set to Tenant.
                            </Typography>
                            <Typography>
                                Example: NOC = 10% of Maintenance Fee + 10% of Electricity Charges + 10%
                                of Water Charges.
                            </Typography>
                        </Box>
                    </Grid>
                </Box>

            )}
        </Box>
    );

    const handleCancel = () => {
        router.back();
    }

    return (
        <Card>
            <CardHeader title="Edit Rule"
                action={
                    <Button
                        variant="contained"
                        color="secondary"
                        onClick={() => {
                            router.back();
                        }}
                    >
                        Back
                    </Button>
                }
            />
            <Divider />
            <CardContent>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Grid container spacing={3}>
                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>
                                Income Account <span style={requiredStyle}>*</span>
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>

                            <Controller
                                name="accountName"
                                control={control}
                                defaultValue=""
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        fullWidth
                                        label="Income Account"
                                        variant="outlined"
                                        disabled
                                    />
                                )}
                            />

                        </Grid>
                        {/* Invoicing Rule */}
                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>
                                Create Invoicing Rule for <span style={requiredStyle}>*</span>
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Controller
                                name="unittype"
                                control={control}
                                defaultValue="" // Default value can be set directly here
                                render={({ field }) => (
                                    <TextField
                                        {...field}
                                        fullWidth
                                        variant="outlined"
                                        label="Unit Type"
                                        value={field.value || ""} // Ensure the field value is set to the unit type
                                        disabled // read-only
                                    />
                                )}
                            />


                            <Typography color="error">{errors.create_invoicing_rule?.message}</Typography>
                        </Grid>

                        {/* Rate Effective From */}
                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>
                                Rate Effective From <span style={requiredStyle}>*</span>
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8} sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 2 }}>
                            <DateField
                                name="effective_date" />
                        </Grid>

                        {/* Tax Class */}
                        <Grid container spacing={2} mt={2}>
                            <Grid item xs={12} sm={4} container alignItems="center">
                                <Typography sx={{ ml: 2 }}>
                                    Tax Class
                                </Typography>
                            </Grid>
                            <Grid item xs={12} sm={8}>
                                <Controller
                                    name="taxClass"
                                    control={control}
                                    render={({ field }) => (
                                        <FormControl fullWidth>
                                            <Select
                                                {...field}
                                                value={field.value || ""}
                                                displayEmpty
                                                onChange={(e) => {
                                                    field.onChange(e.target.value); // Update the form value
                                                    handleSelectChange(e.target.value); // Update the value explicitly
                                                }}
                                                onClose={() => console.log("Dropdown closed")}
                                                onOpen={() => console.log("Dropdown opened")}
                                            >
                                                {loading ? (
                                                    <MenuItem disabled>
                                                        <CircularProgress size={24} />
                                                    </MenuItem>
                                                ) : (
                                                    taxClasses.length > 0 && [
                                                        <MenuItem value="" disabled key="default">
                                                            Select Tax Class
                                                        </MenuItem>,
                                                        ...taxClasses.map((taxClass) => (
                                                            <MenuItem
                                                                key={taxClass.id}
                                                                value={taxClass.id}
                                                            >
                                                                {taxClass.tax_class_name}
                                                            </MenuItem>
                                                        )),
                                                    ]
                                                )}
                                            </Select>
                                        </FormControl>
                                    )}
                                />

                            </Grid>



                        </Grid>

                        <Grid item xs={12} sm={4} container alignItems="center">
                            <Typography>
                                Late Payment Interest Applicable?&nbsp;
                            </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                            <Controller
                                name="latePaymentInterest"
                                control={control}
                                render={({ field }) => (
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                {...field}
                                                checked={field.value} // This will be true if apply_late_payment_interest is 1, otherwise false
                                            />
                                        }
                                        label=""
                                    />
                                )}
                            />
                        </Grid>

                    </Grid>
                    <Divider sx={{ my: 3 }} />

                    {
                        !isCreatingIncomeAccount && (
                            <>
                                {isParking && (
                                    <Box>
                                        {vehicleRules.map((rule, index) => (
                                            <Box key={rule.id} sx={{ position: "relative" }}>
                                                <Box
                                                    sx={{
                                                        p: 2,
                                                        mt: 2,

                                                        borderRadius: 1,
                                                        mb: 4,
                                                    }}
                                                >
                                                    <Typography fontWeight="bold" sx={{ mb: 2, color: "text.primary" }}>
                                                        {index === 0 ? "Owner Parking" : `Additional Vehicle Rule ${index}`}
                                                    </Typography>
                                                    <Divider sx={{ mb: 2, mt: 2 }} />

                                                    <Grid container spacing={2}>
                                                        <Grid item xs={12} sm={4}>
                                                            <Typography fontWeight="bold">Parking Space</Typography>
                                                        </Grid>
                                                        <Grid item xs={12} sm={4}>
                                                            <Typography fontWeight="bold">2 Wheeler</Typography>
                                                        </Grid>
                                                        <Grid item xs={12} sm={4}>
                                                            <Typography fontWeight="bold">4 Wheeler</Typography>
                                                        </Grid>

                                                        {/* OWNER - OPEN PARKING */}
                                                        <Grid item xs={12} sm={2} container alignItems="center">
                                                            <Typography>Open parking</Typography>
                                                        </Grid>
                                                        <Grid item xs={12} sm={5}>
                                                            <TextField
                                                                type="number"
                                                                placeholder="Enter amount"
                                                                fullWidth
                                                                value={rule.ownerOpen2W || ""}
                                                                onChange={(e) => {
                                                                    const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                    const updated = [...vehicleRules];

                                                                    updated[index].ownerOpen2W = val;
                                                                    setVehicleRules(updated);
                                                                }}
                                                                InputProps={{
                                                                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                    inputProps: { min: 0 },
                                                                }}
                                                            />
                                                        </Grid>
                                                        <Grid item xs={12} sm={5}>
                                                            <TextField
                                                                type="number"
                                                                placeholder="Enter amount"
                                                                fullWidth
                                                                value={rule.ownerOpen4W || ""}
                                                                onChange={(e) => {
                                                                    const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                    const updated = [...vehicleRules];

                                                                    updated[index].ownerOpen4W = val;
                                                                    setVehicleRules(updated);
                                                                }}
                                                                InputProps={{
                                                                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                    inputProps: { min: 0 },
                                                                }}
                                                            />
                                                        </Grid>

                                                        {/* OWNER - SHADED PARKING */}
                                                        <Grid item xs={12} sm={2} container alignItems="center">
                                                            <Typography>Shaded parking</Typography>
                                                        </Grid>
                                                        <Grid item xs={12} sm={5}>
                                                            <TextField
                                                                type="number"
                                                                placeholder="Enter amount"
                                                                fullWidth
                                                                value={rule.ownerShaded2W || ""}
                                                                onChange={(e) => {
                                                                    const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                    const updated = [...vehicleRules];

                                                                    updated[index].ownerShaded2W = val;
                                                                    setVehicleRules(updated);
                                                                }}
                                                                InputProps={{
                                                                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                    inputProps: { min: 0 },
                                                                }}
                                                            />
                                                        </Grid>
                                                        <Grid item xs={12} sm={5}>
                                                            <TextField
                                                                type="number"
                                                                placeholder="Enter amount"
                                                                fullWidth
                                                                value={rule.ownerShaded4W || ""}
                                                                onChange={(e) => {
                                                                    const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                    const updated = [...vehicleRules];

                                                                    updated[index].ownerShaded4W = val;
                                                                    setVehicleRules(updated);
                                                                }}
                                                                InputProps={{
                                                                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                    inputProps: { min: 0 },
                                                                }}
                                                            />
                                                        </Grid>
                                                    </Grid>
                                                </Box>

                                                {/* TENANT PARKING - 4 Fields Fixed */}
                                                <Box sx={{ p: 2, borderRadius: 1 }}>
                                                    <Typography fontWeight="bold" sx={{ mb: 2 }}>
                                                        Tenant Parking
                                                    </Typography>
                                                    <Divider sx={{ mb: 2, mt: 2 }} />

                                                    <Grid container spacing={2}>
                                                        <Grid item xs={12} sm={2} container alignItems="center">
                                                            <Typography>Open parking</Typography>
                                                        </Grid>
                                                        <Grid item xs={12} sm={5}>
                                                            <TextField
                                                                type="number"
                                                                placeholder="Enter amount"
                                                                fullWidth
                                                                value={rule.tenantOpen2W || ""}
                                                                onChange={(e) => {
                                                                    const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                    const updated = [...vehicleRules];

                                                                    updated[index].tenantOpen2W = val;
                                                                    setVehicleRules(updated);
                                                                }}
                                                                InputProps={{
                                                                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                    inputProps: { min: 0 },
                                                                }}
                                                            />
                                                        </Grid>
                                                        <Grid item xs={12} sm={5}>
                                                            <TextField
                                                                type="number"
                                                                placeholder="Enter amount"
                                                                fullWidth
                                                                value={rule.tenantOpen4W || ""}
                                                                onChange={(e) => {
                                                                    const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                    const updated = [...vehicleRules];

                                                                    updated[index].tenantOpen4W = val;
                                                                    setVehicleRules(updated);
                                                                }}
                                                                InputProps={{
                                                                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                    inputProps: { min: 0 },
                                                                }}
                                                            />
                                                        </Grid>

                                                        <Grid item xs={12} sm={2} container alignItems="center">
                                                            <Typography>Shaded parking</Typography>
                                                        </Grid>
                                                        <Grid item xs={12} sm={5}>
                                                            <TextField
                                                                type="number"
                                                                placeholder="Enter amount"
                                                                fullWidth
                                                                value={rule.tenantShaded2W || ""}
                                                                onChange={(e) => {
                                                                    const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                    const updated = [...vehicleRules];

                                                                    updated[index].tenantShaded2W = val;
                                                                    setVehicleRules(updated);
                                                                }}
                                                                InputProps={{
                                                                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                    inputProps: { min: 0 },
                                                                }}
                                                            />
                                                        </Grid>
                                                        <Grid item xs={12} sm={5}>
                                                            <TextField
                                                                type="number"
                                                                placeholder="Enter amount"
                                                                fullWidth
                                                                value={rule.tenantShaded4W || ""}
                                                                onChange={(e) => {
                                                                    const val = e.target.value === "" ? "" : Math.max(0, parseFloat(e.target.value));
                                                                    const updated = [...vehicleRules];

                                                                    updated[index].tenantShaded4W = val;
                                                                    setVehicleRules(updated);
                                                                }}
                                                                InputProps={{
                                                                    startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                                    endAdornment: <Typography sx={{ ml: 1 }}>/per month</Typography>,
                                                                    inputProps: { min: 0 },
                                                                }}
                                                            />
                                                        </Grid>
                                                    </Grid>
                                                </Box>



                                                {rule.removable && (
                                                    <IconButton
                                                        onClick={() => handleRemoveVehicleRule(rule.id)}
                                                        sx={{ position: "absolute", top: 10, right: 10 }}
                                                        color="error"
                                                    >
                                                        <DeleteIcon />
                                                    </IconButton>
                                                )}

                                            </Box>


                                        ))}

                                        <Box mt={4} textAlign="end">
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                onClick={handleAddVehicleRule}
                                            >
                                                Add More Vehicle Charges
                                            </Button>
                                        </Box>
                                    </Box>
                                )}


                                {dynamicHeader && (
                                    <Box sx={{ p: 2, mt: 2, borderRadius: 1 }}>
                                        <Typography fontWeight="bold" sx={{ mb: 2 }}>
                                            {dynamicHeader}
                                        </Typography>
                                        <Divider sx={{ mb: 2, mt: 2 }} />
                                        <Grid container spacing={2}>
                                            <Grid item xs={12} sm={4} container alignItems="center">
                                                <Typography>Type</Typography>
                                            </Grid>
                                            <Grid item xs={12} sm={8}>
                                                <Grid item xs={12} sm={8}>
                                                    <RadioGroup
                                                        row
                                                        value={sinkingFundType}  // Bind the value of RadioGroup to sinkingFundType state
                                                        onChange={(e) => setSinkingFundType(e.target.value)}  // Update the state on change
                                                    >
                                                        <FormControlLabel value="fixed" control={<Radio />} label="Fixed" />
                                                        <FormControlLabel value="percentage" control={<Radio />} label="Percentage" />
                                                        <FormControlLabel value="persqft" control={<Radio />} label="Per Sqft Area" />
                                                    </RadioGroup>
                                                </Grid>

                                            </Grid>

                                            {sinkingFundType === "percentage" && (
                                                <>
                                                    <Grid item xs={12} sm={4} container alignItems="center">
                                                        <Typography>Percentage</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={4}>
                                                        <Controller
                                                            name="other_income_fee"
                                                            defaultValue=""
                                                            control={control}
                                                            render={({ field }) => (
                                                                <TextField
                                                                    {...field}
                                                                    type="number"
                                                                    placeholder="Enter percentage"
                                                                    fullWidth
                                                                    InputProps={{
                                                                        endAdornment: (
                                                                            <Typography sx={{ ml: 1 }}>%</Typography>
                                                                        ),
                                                                        inputProps: {
                                                                            min: 0
                                                                        },

                                                                    }}
                                                                    error={!!errors.percentageValue}
                                                                    helperText={errors.percentageValue?.message}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={4}>
                                                        <Controller
                                                            name="percentage"
                                                            control={control}
                                                            render={({ field }) => {
                                                                console.log("field.value:", field.value);  // Debugging field value

                                                                return (
                                                                    <FormControl fullWidth>
                                                                        <Select
                                                                            {...field}
                                                                            displayEmpty
                                                                            value={field.value || 1} // Default to 1 if no value is selected
                                                                            onChange={(e) => {
                                                                                console.log("Selected value:", e.target.value);  // Debugging onChange
                                                                                field.onChange(e.target.value); // Update the form field when a selection is made
                                                                            }}
                                                                            onOpen={async () => {
                                                                                if (!incomeAccounts.length) {
                                                                                    try {
                                                                                        const response = await axios.get(
                                                                                            `${process.env.NEXT_PUBLIC_API_URL}/admin/income-tracker-setting/readIncomeAccounts`,
                                                                                            {
                                                                                                withCredentials: true,
                                                                                            }
                                                                                        );

                                                                                        if (response.status === 200) {
                                                                                            const accounts = response.data.data;

                                                                                            setIncomeAccounts(accounts);
                                                                                            console.log("Income Accounts fetched:", accounts);  // Debugging income accounts

                                                                                            // Set the default value when accounts are fetched
                                                                                            const defaultAccount = accounts.find(
                                                                                                (account) => account.account_name === "MaintenanceFee"
                                                                                            );

                                                                                            if (defaultAccount && !field.value) {
                                                                                                console.log("Setting default account ID:", defaultAccount.id);
                                                                                                field.onChange(1); // Set the default value to `id 1`
                                                                                            }
                                                                                        }
                                                                                    } catch (error) {
                                                                                        console.warn("Error fetching income accounts:", error);
                                                                                    }
                                                                                }
                                                                            }}
                                                                        >
                                                                            <MenuItem value="">
                                                                                <em>Select Base</em>
                                                                            </MenuItem>
                                                                            {incomeAccounts.map((account) => (
                                                                                <MenuItem key={account.id} value={account.id}>
                                                                                    {account.account_name}
                                                                                </MenuItem>
                                                                            ))}
                                                                        </Select>
                                                                    </FormControl>
                                                                );
                                                            }}
                                                        />
                                                    </Grid>

                                                </>
                                            )}


                                            {sinkingFundType === "fixed" && (
                                                <>
                                                    <Grid item xs={12} sm={4} container alignItems="center">
                                                        <Typography>Amount / Month</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={8}>
                                                        <Controller
                                                            name="other_income_fee"
                                                            control={control}
                                                            defaultValue="" // If it's empty, you can set the default value as 0.00 or an empty string
                                                            render={({ field }) => (
                                                                <TextField
                                                                    {...field}
                                                                    type="number"
                                                                    placeholder="0.00"
                                                                    fullWidth
                                                                    InputProps={{
                                                                        startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,

                                                                        // This ensures the browser won't allow numbers < 0:
                                                                        inputProps: { min: 0 }
                                                                    }}
                                                                    error={!!errors.other_income_fee}
                                                                    helperText={errors.other_income_fee?.message}
                                                                />
                                                            )}
                                                        />

                                                    </Grid>
                                                </>
                                            )}
                                            {sinkingFundType === "persqft" && (
                                                <Grid container spacing={2}>
                                                    <Grid item xs={12} sm={6} container alignItems="center">
                                                        <Typography>Fee / Sqft Area / Month</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={6}>
                                                        <Controller
                                                            name="other_income_fee"
                                                            control={control}
                                                            defaultValue=""
                                                            render={({ field }) => (
                                                                <TextField
                                                                    {...field}
                                                                    type="number"
                                                                    placeholder="0.00"
                                                                    fullWidth
                                                                    InputProps={{
                                                                        startAdornment: (
                                                                            <Typography sx={{ mr: 1 }}>₹</Typography>
                                                                        ),
                                                                        inputProps: {
                                                                            min: 0
                                                                        }
                                                                    }}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                    <Grid item xs={12} sm={6} container alignItems="center">
                                                        <Typography>Fee / Sqft Open Area / Month</Typography>
                                                    </Grid>
                                                    <Grid item xs={12} sm={6}>
                                                        <Controller
                                                            name="other_open_income_fee"
                                                            control={control}
                                                            render={({ field }) => (
                                                                <TextField
                                                                    {...field}
                                                                    type="number"
                                                                    placeholder="0.00"
                                                                    fullWidth
                                                                    InputProps={{
                                                                        startAdornment: (
                                                                            <Typography sx={{ mr: 1 }}>₹</Typography>
                                                                        ),
                                                                        inputProps: {
                                                                            min: 0
                                                                        }
                                                                    }}
                                                                />
                                                            )}
                                                        />
                                                    </Grid>
                                                </Grid>
                                            )}

                                        </Grid>
                                    </Box>
                                )}

                                {showTabs && !isSinkingFund && !isParking && (
                                    <>
                                        <Grid container spacing={2}>
                                            <Grid item xs={12} sm={8}>
                                                <Tabs
                                                    value={activeTab}
                                                    onChange={(_, newValue) => setActiveTab(newValue)}
                                                    sx={{
                                                        borderBottom: 1,
                                                        borderColor: "divider",
                                                        ".MuiTab-root": {
                                                            minWidth: 150,
                                                            textTransform: "none",
                                                            fontSize: "1rem",
                                                            fontWeight: "bold",
                                                        },
                                                        ".Mui-selected": {
                                                            color: "#1976d2",
                                                        },
                                                        ".MuiTabs-indicator": {
                                                            backgroundColor: "#1976d2",
                                                        },
                                                    }}
                                                >
                                                    <Tab label="Standard Rule" />
                                                    <Tab label="Floor Based Rule" />
                                                </Tabs>
                                            </Grid>
                                        </Grid>

                                        {activeTab === 0 && (
                                            <Box sx={{ p: 2, mt: 2, borderRadius: 1 }}>
                                                <Grid container spacing={2}>
                                                    <Grid item xs={12} sm={8}>
                                                        <RadioGroup
                                                            row
                                                            value={feeType}
                                                            onChange={(e) => setFeeType(e.target.value)}
                                                            sx={{ mb: 2 }}
                                                        >
                                                            <FormControlLabel
                                                                value="persqft"
                                                                control={<Radio />}
                                                                label="Per Sqft Area"
                                                            />
                                                            <FormControlLabel
                                                                value="Flat Fee"
                                                                control={<Radio />}
                                                                label="Flat Fee"
                                                            />
                                                        </RadioGroup>
                                                    </Grid>
                                                </Grid>

                                                {feeType === "Flat Fee" && (
                                                    <Grid container spacing={2}>
                                                        <Grid item xs={4} sm={4} container alignItems="center">
                                                            <Typography>Flat Fee / Month</Typography>
                                                        </Grid>
                                                        <Grid item xs={8} sm={8}>
                                                            <Controller
                                                                name="flat_area"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <TextField
                                                                        {...field}
                                                                        type="number"
                                                                        placeholder="Enter flat fee"
                                                                        fullWidth
                                                                        InputProps={{
                                                                            startAdornment: (
                                                                                <Typography sx={{ mr: 1 }}>₹</Typography>
                                                                            ),
                                                                            inputProps: {
                                                                                min: 0
                                                                            }
                                                                        }}
                                                                        error={!!errors.flat_area}
                                                                        helperText={errors.flat_area?.message}
                                                                    />
                                                                )}
                                                            />
                                                        </Grid>
                                                    </Grid>
                                                )}

                                                {feeType === "persqft" && (
                                                    <Grid container spacing={2}>
                                                        <Grid item xs={4} sm={4} container alignItems="center">
                                                            <Typography>
                                                                Fee / Sqft Area / Month
                                                            </Typography>
                                                        </Grid>
                                                        <Grid item xs={8} sm={8}>
                                                            <Controller
                                                                name="area"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <TextField
                                                                        {...field}
                                                                        type="number"
                                                                        placeholder="Enter fee per sqft"
                                                                        fullWidth
                                                                        InputProps={{
                                                                            startAdornment: (
                                                                                <Typography sx={{ mr: 1 }}>₹</Typography>
                                                                            ),
                                                                            inputProps: {
                                                                                min: 0
                                                                            }
                                                                        }}
                                                                        error={!!errors.area}
                                                                        helperText={errors.area?.message}
                                                                    />
                                                                )}
                                                            />

                                                        </Grid>

                                                        <Grid item xs={4} sm={4} container alignItems="center">
                                                            <Typography>
                                                                Fee / Sqft Open Area / Month
                                                            </Typography>
                                                        </Grid>
                                                        <Grid item xs={8} sm={8}>
                                                            <Controller
                                                                name="open_area"
                                                                control={control}
                                                                render={({ field }) => (
                                                                    <TextField
                                                                        {...field}
                                                                        type="number"
                                                                        placeholder="Enter fee per sqft open area"
                                                                        fullWidth
                                                                        InputProps={{
                                                                            startAdornment: (
                                                                                <Typography sx={{ mr: 1 }}>₹</Typography>
                                                                            ),
                                                                            inputProps: {
                                                                                min: 0
                                                                            }
                                                                        }}
                                                                        error={!!errors.open_area}
                                                                        helperText={errors.open_area?.message}
                                                                    />
                                                                )}
                                                            />
                                                        </Grid>
                                                    </Grid>
                                                )}
                                            </Box>
                                        )}

                                        {activeTab === 1 && renderFloorBasedRules()}
                                    </>
                                )}

                                {isNOC && renderNOCSection()}

                            </>
                        )
                    }

                    {isCreatingIncomeAccount && (
                        <>
                            <Box sx={{ mb: 3 }}>
                                <Typography fontWeight="bold">Charges</Typography>
                                <RadioGroup
                                    row
                                    value={chargesType}
                                    onChange={(e) => setChargesType(e.target.value)}
                                    sx={{ mt: 2 }}
                                >
                                    <FormControlLabel
                                        value="fixed"
                                        control={<Radio />}
                                        label="Fixed"
                                    />
                                    <FormControlLabel
                                        value="percentage"
                                        control={<Radio />}
                                        label="Percentage"
                                    />
                                    <FormControlLabel
                                        value="persqft"
                                        control={<Radio />}
                                        label="Per Sqft Area"
                                    />
                                </RadioGroup>
                                {chargesType === "fixed" && (
                                    <Grid container alignItems='center' sx={{ mt: 3, mb: 3 }}>

                                        <Grid item sm={4}>
                                            <Typography>Amount / Month</Typography>
                                        </Grid>

                                        <Grid item sm={8}>
                                            <Controller
                                                name="other_income_fee"
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Amount is required', min: { value: 0, message: 'Amount cannot be negative' } }}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        label="Amount / Month"
                                                        placeholder="0.00"
                                                        fullWidth
                                                        error={!!errors.other_income_fee}
                                                        helperText={errors.other_income_fee?.message}
                                                        InputProps={{
                                                            startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                            inputProps: { min: 0, step: 'any' }, // HTML5 validation (optional, additional check)
                                                        }}
                                                        sx={{ mt: 2 }}
                                                        onChange={(e) => {
                                                            const value = e.target.value;

                                                            if (value === '' || (!isNaN(value) && Number(value) >= 0)) {
                                                                field.onChange(value);
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />


                                        </Grid>
                                    </Grid>

                                )}
                                {chargesType === "percentage" && (
                                    <Grid container alignItems='center' sx={{ mt: 3, mb: 3 }}>
                                        <Grid item xs={12} sm={4} container alignItems="center">
                                            <Typography>Percentage</Typography>
                                        </Grid>
                                        <Grid item xs={12} sm={4}>
                                            <Controller
                                                name="other_income_fee"
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Amount is required', min: { value: 0, message: 'Amount cannot be negative' } }}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        label="Amount / Month"
                                                        placeholder="0.00"
                                                        fullWidth
                                                        error={!!errors.other_income_fee}
                                                        helperText={errors.other_income_fee?.message}
                                                        InputProps={{
                                                            startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                            inputProps: { min: 0, step: 'any' }, // HTML5 validation (optional, additional check)
                                                        }}
                                                        sx={{ mt: 2 }}
                                                        onChange={(e) => {
                                                            const value = e.target.value;

                                                            if (value === '' || (!isNaN(value) && Number(value) >= 0)) {
                                                                field.onChange(value);
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                        <Grid item xs={12} sm={4}>
                                            <Controller
                                                name="percentageBase"
                                                control={control}
                                                defaultValue="" // Ensure defaultValue is set
                                                render={({ field }) => (
                                                    <FormControl fullWidth>
                                                        <Select
                                                            {...field}
                                                            displayEmpty
                                                            value={field.value || ""}
                                                            onOpen={async () => {
                                                                if (!incomeAccounts.length) {
                                                                    try {
                                                                        const response = await axios.get(
                                                                            "https://societybackend.cubeone.in/api/admin/income-tracker-setting/readIncomeAccounts",
                                                                            {
                                                                                withCredentials: true,
                                                                            }
                                                                        );

                                                                        if (response.status === 200) {
                                                                            const accounts = response.data.data;

                                                                            setIncomeAccounts(accounts);

                                                                            // Automatically select "Maintenance Fee" if available
                                                                            const defaultAccount = accounts.find(
                                                                                (account) => account.account_name === "Maintenance Fee"
                                                                            );

                                                                            if (defaultAccount) {
                                                                                setValue("percentageBase", defaultAccount.id, { shouldValidate: true });
                                                                            }
                                                                        }
                                                                    } catch (error) {
                                                                        console.warn("Error fetching income accounts:", error);
                                                                    }
                                                                }
                                                            }}
                                                        >
                                                            <MenuItem value="">
                                                                <em>Select Base</em>
                                                            </MenuItem>
                                                            {incomeAccounts.map((account) => (
                                                                <MenuItem key={account.id} value={account.id}>
                                                                    {account.account_name}
                                                                </MenuItem>
                                                            ))}
                                                        </Select>
                                                    </FormControl>
                                                )}
                                            />

                                        </Grid>
                                    </Grid>
                                )}

                                {chargesType === "persqft" && (
                                    <Grid container spacing={2} alignItems="center">
                                        <Grid item xs={4} sm={4} container alignItems="center">
                                            <Typography>Fee / Sqft Area / Month</Typography>
                                        </Grid>
                                        <Grid item xs={8} sm={8}>
                                            <Controller
                                                name="other_income_fee"
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Amount is required', min: { value: 0, message: 'Amount cannot be negative' } }}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        label="Amount / Month"
                                                        placeholder="0.00"
                                                        fullWidth
                                                        error={!!errors.other_income_fee}
                                                        helperText={errors.other_income_fee?.message}
                                                        InputProps={{
                                                            startAdornment: <Typography sx={{ mr: 1 }}>₹</Typography>,
                                                            inputProps: { min: 0, step: 'any' }, // HTML5 validation (optional, additional check)
                                                        }}
                                                        sx={{ mt: 2 }}
                                                        onChange={(e) => {
                                                            const value = e.target.value;

                                                            if (value === '' || (!isNaN(value) && Number(value) >= 0)) {
                                                                field.onChange(value);
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />
                                        </Grid>

                                        <Grid item xs={12} sm={4} container alignItems="center">
                                            <Typography>Fee / Sqft Open Area / Month</Typography>
                                        </Grid>
                                        <Grid item xs={12} sm={8}>
                                            <Controller
                                                name="other_open_income_fee"
                                                control={control}
                                                render={({ field }) => (
                                                    <TextField
                                                        {...field}
                                                        type="number"
                                                        placeholder="0.00"
                                                        fullWidth
                                                        InputProps={{
                                                            startAdornment: (
                                                                <Typography sx={{ mr: 1 }}>₹</Typography>
                                                            ),
                                                            inputProps: {
                                                                min: 0
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />
                                        </Grid>
                                    </Grid>
                                )}
                            </Box>
                        </>
                    )}
                    <Box sx={{ display: 'flex', justifyContent: 'end' }}>
                        <Grid item xs={4} sx={{ mt: 2, ml: 2, mr: 2 }}>
                            <Button
                                type="submit"
                                variant="contained"
                                color="success"
                                fullWidth
                            >
                                Save
                            </Button>
                        </Grid>
                        <Grid item xs={4} sx={{ mt: 2 }}>
                            <Button
                                variant="contained"
                                color="primary"
                                fullWidth
                                onClick={handleCancel}
                            >
                                Cancel
                            </Button>
                        </Grid>
                    </Box>
                </form>
            </CardContent>
        </Card>
    );
};

export default MyForm;
