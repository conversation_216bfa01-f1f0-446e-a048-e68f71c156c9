"use client";

import React, { useState, useEffect } from 'react';

import { Box, Typography, Tooltip, useTheme, CircularProgress } from '@mui/material';

interface MonthlyExpense {
  month: string;
  amount: number;
}

interface MonthlyExpensesChartProps {
  data?: MonthlyExpense[];
  loading?: boolean;
  isDarkMode?: boolean;
}

// Sample data for the monthly expenses - used as fallback when no data is provided
const monthlyData = [
  { month: 'January', amount: 60000 },
  { month: 'February', amount: 10000 },
  { month: 'March', amount: 90000 },
  { month: 'April', amount: 50000 },
  { month: 'May', amount: 500000 },
  { month: 'July', amount: 20000 },
  { month: 'August', amount: 70000 },
  { month: 'September', amount: 120000 },
  { month: 'October', amount: 30000 },
  { month: 'November', amount: 80000 },
  { month: 'December', amount: 40000 },
];

// Y-axis labels - matching the image
const yAxisLabels = ['0', '50k', '100k', '150k', '200k', '250k', '300k', '350k', '400k', '450k', '500k', '550k'];
const maxValue = 550000; // Maximum value on y-axis

const MonthlyExpensesChart: React.FC<MonthlyExpensesChartProps> = ({
  data = [], // Default to empty array
  loading = false,
  isDarkMode: propIsDarkMode
}) => {
  const theme = useTheme();
  const isDarkMode = propIsDarkMode !== undefined ? propIsDarkMode : theme.palette.mode === 'dark';

  // Use sample data if no data is provided
  const displayData = data.length > 0 ? data : monthlyData;

  const [animatedData, setAnimatedData] = useState(displayData.map(item => ({ ...item, animatedAmount: 0 })));
  const [hoveredBar, setHoveredBar] = useState<number | null>(null);

  // Update animated data when data changes
  useEffect(() => {
    // Use sample data if no data is provided
    const currentData = data.length > 0 ? data : monthlyData;

    setAnimatedData(currentData.map(item => ({ ...item, animatedAmount: 0 })));

    // Animation effect for bars
    const timer = setTimeout(() => {
      setAnimatedData(currentData.map(item => ({ ...item, animatedAmount: item.amount })));
    }, 300);

    return () => clearTimeout(timer);
  }, [data]);

  // Calculate the height of each bar based on the amount
  const getBarHeight = (amount: number) => {
    return (amount / maxValue) * 300; // 300px is the max height of our chart
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Generate gradient for bars
  const getBarGradient = (index: number, isHovered: boolean) => {
    const darkGradients = [
      'linear-gradient(180deg, rgba(66,153,225,0.9) 0%, rgba(49,130,206,0.7) 100%)',
      'linear-gradient(180deg, rgba(72,187,120,0.9) 0%, rgba(56,161,105,0.7) 100%)',
      'linear-gradient(180deg, rgba(237,100,166,0.9) 0%, rgba(213,63,140,0.7) 100%)',
      'linear-gradient(180deg, rgba(246,173,85,0.9) 0%, rgba(237,137,54,0.7) 100%)',
      'linear-gradient(180deg, rgba(144,205,244,0.9) 0%, rgba(86,171,225,0.7) 100%)'
    ];

    const lightGradients = [
      'linear-gradient(180deg, rgba(49,130,206,0.85) 0%, rgba(66,153,225,0.65) 100%)',
      'linear-gradient(180deg, rgba(56,161,105,0.85) 0%, rgba(72,187,120,0.65) 100%)',
      'linear-gradient(180deg, rgba(213,63,140,0.85) 0%, rgba(237,100,166,0.65) 100%)',
      'linear-gradient(180deg, rgba(237,137,54,0.85) 0%, rgba(246,173,85,0.65) 100%)',
      'linear-gradient(180deg, rgba(86,171,225,0.85) 0%, rgba(144,205,244,0.65) 100%)'
    ];

    if (isHovered) {
      return isDarkMode
        ? 'linear-gradient(180deg, rgba(66,153,225,1) 0%, rgba(49,130,206,0.8) 100%)'
        : 'linear-gradient(180deg, rgba(49,130,206,0.95) 0%, rgba(66,153,225,0.75) 100%)';
    } else {
      const gradients = isDarkMode ? darkGradients : lightGradients;

      return gradients[index % gradients.length];
    }
  };

  return (
    <Box sx={{
      width: '100%',
      height: '350px',
      position: 'relative',
      p: 3,
      pt: 5,
      fontFamily: 'system-ui, -apple-system, sans-serif',
      background: isDarkMode
        ? 'linear-gradient(180deg, rgba(25,118,210,0.05) 0%, rgba(25,118,210,0) 100%)'
        : 'linear-gradient(180deg, rgba(37,99,235,0.03) 0%, rgba(37,99,235,0) 100%)',
      borderRadius: '12px',
      color: isDarkMode ? 'white' : theme.palette.text.primary,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: loading ? 'center' : 'flex-start',
      alignItems: loading ? 'center' : 'stretch'
    }}>
      {loading ? (
        <CircularProgress color="primary" size={40} />
      ) : (
        <>
          {/* Hidden title - actual title is in the parent component */}
          <Box sx={{ position: 'absolute', top: -1000, left: -1000, opacity: 0 }}>
        <Typography variant="h6">
          Monthly Expenses
        </Typography>
      </Box>

      {/* Y-axis labels */}
      <Box sx={{
        position: 'absolute',
        left: 0,
        top: 60,
        height: 'calc(100% - 80px)',
        width: '50px',
        display: 'flex',
        flexDirection: 'column-reverse',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        pr: 1,
        fontSize: '0.7rem',
        color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(30,64,175,0.6)',
        fontWeight: 500,
        fontFamily: '"Roboto Mono", monospace',
        letterSpacing: '0.5px'
      }}>
        {yAxisLabels.map((label, index) => (
          <Box key={index} sx={{
            transition: 'all 0.2s ease',
            color: index === 0
              ? (isDarkMode ? 'rgba(255,255,255,0.9)' : 'rgba(30,64,175,0.9)')
              : (isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(30,64,175,0.6)'),
            fontWeight: index === 0 ? 600 : 400,
            textShadow: index === 0
              ? (isDarkMode ? '0 0 5px rgba(66,153,225,0.5)' : '0 0 3px rgba(66,153,225,0.3)')
              : 'none',
            position: 'relative',
            '&::after': index % 2 === 0 ? {
              content: '""',
              position: 'absolute',
              right: '-12px',
              top: '50%',
              width: '8px',
              height: '1px',
              background: isDarkMode ? 'rgba(255,255,255,0.2)' : 'rgba(30,64,175,0.2)',
              transform: 'translateY(-50%)'
            } : {}
          }}>
            {label}
          </Box>
        ))}
      </Box>

      {/* Chart area */}
      <Box sx={{
        ml: '50px',
        height: 'calc(100% - 80px)',
        mt: '60px',
        display: 'flex',
        alignItems: 'flex-end',
        borderLeft: isDarkMode
          ? '1px solid rgba(255,255,255,0.15)'
          : '1px solid rgba(30,64,175,0.15)',
        borderBottom: isDarkMode
          ? '1px solid rgba(255,255,255,0.15)'
          : '1px solid rgba(30,64,175,0.15)',
        position: 'relative',
        pr: 2,
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: '-6px',
          left: '-6px',
          width: '12px',
          height: '12px',
          borderRadius: '50%',
          background: 'rgba(66,153,225,0.5)',
          boxShadow: isDarkMode
            ? '0 0 10px rgba(66,153,225,0.7)'
            : '0 0 8px rgba(66,153,225,0.5)'
        }
      }}>
        {/* Horizontal grid lines */}
        <Box sx={{
          position: 'absolute',
          left: 0,
          right: 0,
          top: 0,
          height: '100%',
          pointerEvents: 'none'
        }}>
          {yAxisLabels.map((_, index) => (
            <Box
              key={index}
              sx={{
                position: 'absolute',
                bottom: `${(index / (yAxisLabels.length - 1)) * 100}%`,
                left: 0,
                right: 0,
                borderTop: index % 2 === 0
                  ? (isDarkMode ? '1px dashed rgba(255,255,255,0.1)' : '1px dashed rgba(30,64,175,0.1)')
                  : (isDarkMode ? '1px dashed rgba(255,255,255,0.05)' : '1px dashed rgba(30,64,175,0.05)'),
                height: 1,
                '&::after': index % 4 === 0 && index !== 0 ? {
                  content: '""',
                  position: 'absolute',
                  left: '-4px',
                  top: '-4px',
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  background: isDarkMode ? 'rgba(66,153,225,0.3)' : 'rgba(66,153,225,0.2)',
                  boxShadow: isDarkMode ? '0 0 5px rgba(66,153,225,0.5)' : '0 0 4px rgba(66,153,225,0.3)'
                } : {}
              }}
            />
          ))}
        </Box>

        {/* Bars */}
        {animatedData.map((item, index) => (
          <Tooltip
            key={index}
            title={formatCurrency(item.amount)}
            placement="top"
            arrow
          >
            <Box
              onMouseEnter={() => setHoveredBar(index)}
              onMouseLeave={() => setHoveredBar(null)}
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                height: '100%',
                justifyContent: 'flex-end',
                position: 'relative',
                cursor: 'pointer',
                transition: 'transform 0.2s ease',
                transform: hoveredBar === index ? 'translateY(-5px)' : 'none',
                '&:hover': {
                  zIndex: 10
                }
              }}
            >
              {/* Value label above bar */}
              <Typography
                variant="caption"
                sx={{
                  position: 'absolute',
                  bottom: `calc(${getBarHeight(item.animatedAmount)}px + 8px)`,
                  color: isDarkMode ? 'white' : 'rgba(30,64,175,0.95)',
                  fontWeight: 600,
                  opacity: hoveredBar === index ? 1 : 0,
                  transition: 'all 0.3s ease',
                  backgroundColor: isDarkMode
                    ? 'rgba(17, 25, 40, 0.75)'
                    : 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(4px)',
                  border: isDarkMode
                    ? '1px solid rgba(66,153,225,0.3)'
                    : '1px solid rgba(37,99,235,0.2)',
                  px: 1.5,
                  py: 0.7,
                  borderRadius: '6px',
                  boxShadow: hoveredBar === index
                    ? (isDarkMode ? '0 0 15px rgba(66,153,225,0.5)' : '0 0 10px rgba(37,99,235,0.3)')
                    : 'none',
                  fontFamily: '"Roboto Mono", monospace',
                  letterSpacing: '0.5px',
                  fontSize: '0.7rem',
                  transform: hoveredBar === index ? 'translateY(-5px) scale(1)' : 'translateY(0) scale(0.9)',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: '-4px',
                    left: '50%',
                    transform: 'translateX(-50%) rotate(45deg)',
                    width: '8px',
                    height: '8px',
                    backgroundColor: isDarkMode
                      ? 'rgba(17, 25, 40, 0.75)'
                      : 'rgba(255, 255, 255, 0.9)',
                    border: isDarkMode
                      ? '1px solid rgba(66,153,225,0.3)'
                      : '1px solid rgba(37,99,235,0.2)',
                    borderTop: 'none',
                    borderLeft: 'none',
                  }
                }}
              >
                {formatCurrency(item.amount).replace('₹', '₹ ')}
              </Typography>

              {/* Bar */}
              <Box
                sx={{
                  width: '60%',
                  height: `${getBarHeight(item.animatedAmount)}px`,
                  background: getBarGradient(index, hoveredBar === index),
                  borderTopLeftRadius: 6,
                  borderTopRightRadius: 6,
                  minHeight: item.amount > 0 ? 4 : 0,
                  transition: 'all 1s cubic-bezier(0.4, 0, 0.2, 1)',
                  boxShadow: hoveredBar === index
                    ? (isDarkMode ? '0 0 20px rgba(66, 153, 225, 0.7)' : '0 0 15px rgba(66, 153, 225, 0.5)')
                    : (isDarkMode ? '0 0 10px rgba(66, 153, 225, 0.3)' : '0 0 8px rgba(66, 153, 225, 0.2)'),
                  position: 'relative',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '30%',
                    background: 'linear-gradient(180deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 100%)',
                    borderTopLeftRadius: 6,
                    borderTopRightRadius: 6,
                  },
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: 0,
                    left: '10%',
                    right: '10%',
                    height: '3px',
                    background: hoveredBar === index
                      ? (isDarkMode ? 'rgba(255,255,255,0.8)' : 'rgba(255,255,255,0.9)')
                      : (isDarkMode ? 'rgba(255,255,255,0.3)' : 'rgba(255,255,255,0.5)'),
                    borderRadius: '3px',
                    boxShadow: hoveredBar === index
                      ? (isDarkMode ? '0 0 10px rgba(255,255,255,0.8)' : '0 0 8px rgba(255,255,255,0.6)')
                      : (isDarkMode ? '0 0 5px rgba(255,255,255,0.3)' : '0 0 4px rgba(255,255,255,0.2)'),
                    transition: 'all 0.3s ease'
                  }
                }}
              />

              {/* Month label */}
              <Box
                sx={{
                  position: 'absolute',
                  bottom: -25,
                  fontSize: '0.7rem',
                  color: hoveredBar === index
                    ? (isDarkMode ? 'rgba(255,255,255,0.95)' : 'rgba(30,64,175,0.95)')
                    : (isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(30,64,175,0.6)'),
                  textAlign: 'center',
                  width: '100%',
                  fontWeight: hoveredBar === index ? 600 : 400,
                  transition: 'all 0.2s ease',
                  textShadow: hoveredBar === index
                    ? (isDarkMode ? '0 0 5px rgba(66,153,225,0.7)' : '0 0 3px rgba(66,153,225,0.4)')
                    : 'none',
                  letterSpacing: '0.5px',
                  fontFamily: '"Roboto Mono", monospace',
                  '&::before': hoveredBar === index ? {
                    content: '""',
                    position: 'absolute',
                    bottom: '-5px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: '20px',
                    height: '2px',
                    background: 'rgba(66,153,225,0.7)',
                    borderRadius: '1px'
                  } : {}
                }}
              >
                {item.month}
              </Box>
            </Box>
          </Tooltip>
        ))}
      </Box>
        </>
      )}
    </Box>
  );
};

export default MonthlyExpensesChart;
