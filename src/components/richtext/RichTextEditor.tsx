'use client';

import React, { useState, useRef } from 'react';

import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Divider,
  Select,
  MenuItem,
  FormControl,
} from '@mui/material';

// Icons
import FormatBoldIcon from '@mui/icons-material/FormatBold';
import FormatItalicIcon from '@mui/icons-material/FormatItalic';
import FormatUnderlinedIcon from '@mui/icons-material/FormatUnderlined';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import FormatAlignLeftIcon from '@mui/icons-material/FormatAlignLeft';
import FormatAlignCenterIcon from '@mui/icons-material/FormatAlignCenter';
import FormatAlignRightIcon from '@mui/icons-material/FormatAlignRight';
import FormatAlignJustifyIcon from '@mui/icons-material/FormatAlignJustify';
import LinkIcon from '@mui/icons-material/Link';
import LinkOffIcon from '@mui/icons-material/LinkOff';
import ImageIcon from '@mui/icons-material/Image';
import CodeIcon from '@mui/icons-material/Code';
import SaveIcon from '@mui/icons-material/Save';
import FormatQuoteIcon from '@mui/icons-material/FormatQuote';
import HorizontalRuleIcon from '@mui/icons-material/HorizontalRule';
import UndoIcon from '@mui/icons-material/Undo';
import RedoIcon from '@mui/icons-material/Redo';
import FormatColorTextIcon from '@mui/icons-material/FormatColorText';
import FormatColorFillIcon from '@mui/icons-material/FormatColorFill';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  rows?: number;
  placeholder?: string;
  label?: string;
  onSave?: () => void;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  rows = 10,
  label,
  onSave
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [headingLevel, setHeadingLevel] = useState<string>('p');
  
  // Initialize the editor
  React.useEffect(() => {
    if (editorRef.current) {
      editorRef.current.innerHTML = value;
      
      // Make the div editable
      editorRef.current.setAttribute('contentEditable', 'true');
      
      // Add event listener for input
      const handleInput = () => {
        if (editorRef.current) {
          onChange(editorRef.current.innerHTML);
        }
      };
      
      editorRef.current.addEventListener('input', handleInput);
      
      // Cleanup
      return () => {
        if (editorRef.current) {
          editorRef.current.removeEventListener('input', handleInput);
        }
      };
    }
  }, []);
  
  // Update the editor content when value changes externally
  React.useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);
  
  // Execute a command on the document
  const execCommand = (command: string, value: string = '') => {
    document.execCommand(command, false, value);
    
    // Focus back on the editor
    if (editorRef.current) {
      editorRef.current.focus();
      
      // Update the value
      onChange(editorRef.current.innerHTML);
    }
  };
  
  // Handle heading level change
  const handleHeadingChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const newHeadingLevel = event.target.value as string;

    setHeadingLevel(newHeadingLevel);
    
    if (newHeadingLevel === 'p') {
      execCommand('formatBlock', '<p>');
    } 
    else {
      execCommand('formatBlock', `<${newHeadingLevel}>`);
    }
  };
  
  // Insert link
  const insertLink = () => {

    const url = prompt('Enter URL:');

    if (url) {
      execCommand('createLink', url);
    }
  };
  
  // Remove link
  const removeLink = () => {
    execCommand('unlink');
  };
  
  // Insert image
  const insertImage = () => {

    const url = prompt('Enter image URL:');
    
    if (url) {
      execCommand('insertImage', url);
    }
  };
  
  // Change text color
  const changeTextColor = () => {
    const color = prompt('Enter color (hex, rgb, or name):', '#000000');

    if (color) {
      execCommand('foreColor', color);
    }
  };
  
  // Change background color
  const changeBackgroundColor = () => {
    const color = prompt('Enter background color (hex, rgb, or name):', '#ffffff');

    if (color) {
      execCommand('hiliteColor', color);
    }
  };
  
  return (
    <Box sx={{ width: '100%' }}>
      {label && (
        <Typography variant="subtitle1" sx={{ mb: 1 }}>
          {label}
        </Typography>
      )}
      
      <Paper variant="outlined" sx={{ mb: 2 }}>
        <Box sx={{ p: 1, borderBottom: '1px solid', borderColor: 'divider' }}>
          {/* Formatting Toolbar */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, alignItems: 'center' }}>
            {/* Heading Level */}
            <FormControl size="small" sx={{ minWidth: 100 }}>
              <Select
                value={headingLevel}
                onChange={handleHeadingChange}
                displayEmpty
                variant="outlined"
                sx={{ height: '36px' }}
              >
                <MenuItem value="p">Paragraph</MenuItem>
                <MenuItem value="h1">Heading 1</MenuItem>
                <MenuItem value="h2">Heading 2</MenuItem>
                <MenuItem value="h3">Heading 3</MenuItem>
                <MenuItem value="h4">Heading 4</MenuItem>
              </Select>
            </FormControl>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Text Formatting */}
            <IconButton 
              size="small" 
              onClick={() => execCommand('bold')}
              title="Bold"
            >
              <FormatBoldIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={() => execCommand('italic')}
              title="Italic"
            >
              <FormatItalicIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={() => execCommand('underline')}
              title="Underline"
            >
              <FormatUnderlinedIcon fontSize="small" />
            </IconButton>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Text Alignment */}
            <IconButton 
              size="small" 
              onClick={() => execCommand('justifyLeft')}
              title="Align Left"
            >
              <FormatAlignLeftIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={() => execCommand('justifyCenter')}
              title="Align Center"
            >
              <FormatAlignCenterIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={() => execCommand('justifyRight')}
              title="Align Right"
            >
              <FormatAlignRightIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={() => execCommand('justifyFull')}
              title="Justify"
            >
              <FormatAlignJustifyIcon fontSize="small" />
            </IconButton>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Lists */}
            <IconButton 
              size="small" 
              onClick={() => execCommand('insertUnorderedList')}
              title="Bullet List"
            >
              <FormatListBulletedIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={() => execCommand('insertOrderedList')}
              title="Numbered List"
            >
              <FormatListNumberedIcon fontSize="small" />
            </IconButton>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Links and Media */}
            <IconButton 
              size="small" 
              onClick={insertLink}
              title="Insert Link"
            >
              <LinkIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={removeLink}
              title="Remove Link"
            >
              <LinkOffIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={insertImage}
              title="Insert Image"
            >
              <ImageIcon fontSize="small" />
            </IconButton>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Additional Formatting */}
            <IconButton 
              size="small" 
              onClick={() => execCommand('formatBlock', '<blockquote>')}
              title="Quote"
            >
              <FormatQuoteIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={() => execCommand('insertHorizontalRule')}
              title="Horizontal Line"
            >
              <HorizontalRuleIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={() => execCommand('formatBlock', '<pre>')}
              title="Code Block"
            >
              <CodeIcon fontSize="small" />
            </IconButton>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Colors */}
            <IconButton 
              size="small" 
              onClick={changeTextColor}
              title="Text Color"
            >
              <FormatColorTextIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={changeBackgroundColor}
              title="Background Color"
            >
              <FormatColorFillIcon fontSize="small" />
            </IconButton>
            
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            
            {/* Undo/Redo */}
            <IconButton 
              size="small" 
              onClick={() => execCommand('undo')}
              title="Undo"
            >
              <UndoIcon fontSize="small" />
            </IconButton>
            
            <IconButton 
              size="small" 
              onClick={() => execCommand('redo')}
              title="Redo"
            >
              <RedoIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>
        
        {/* Editor Area */}
        <Box
          ref={editorRef}
          sx={{
            p: 2,
            minHeight: `${rows * 24}px`,
            outline: 'none',
            fontFamily: 'inherit',
            fontSize: '1rem',
            lineHeight: 1.5,
            '&:focus': {
              outline: 'none',
            },
            '& img': {
              maxWidth: '100%',
            },
            '& blockquote': {
              borderLeft: '4px solid #ccc',
              margin: '1.5em 10px',
              padding: '0.5em 10px',
              fontStyle: 'italic',
            },
            '& pre': {
              backgroundColor: 'rgba(0,0,0,0.05)',
              padding: '10px',
              borderRadius: '4px',
              fontFamily: 'monospace',
              whiteSpace: 'pre-wrap',
            },
            '& hr': {
              border: 'none',
              borderTop: '1px solid #ccc',
              margin: '1em 0',
            },
          }}
          dangerouslySetInnerHTML={{ __html: value }}
        />
      </Paper>
      
      {onSave && (
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            color="success"
            startIcon={<SaveIcon />}
            onClick={onSave}
            sx={{
              borderRadius: '4px',
              px: 3,
              py: 1,
              bgcolor: '#8bc34a',
              '&:hover': {
                bgcolor: '#7cb342',
              }
            }}
          >
            Save
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default RichTextEditor;
