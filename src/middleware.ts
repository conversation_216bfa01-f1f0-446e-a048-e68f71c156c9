// src/middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

import { getAdminModeFromPath } from './utils/string';
import { cookieConfig } from './data/cookies';

export function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;
  const cookieMode = request.cookies.get('toggle_mode')?.value;

  // console.log('🧭 MIDDLEWARE pathname:', pathname, cookieMode);

  // 0. Handle /logout
  if (pathname === '/logout') {
    const logoutRedirectUri = encodeURIComponent(process.env.NEXTAUTH_URL!);
    const logoutUrl = `${process.env.KEYCLOAK_ISSUER}/protocol/openid-connect/logout?post_logout_redirect_uri=${logoutRedirectUri}`;

    return NextResponse.redirect(logoutUrl);
  }

  // 1. Always allow the selection page
  if (pathname === '/admin/select') {
    return NextResponse.next();
  }

  // 2. Collapse clean dashboard URL
  if (pathname === '/admin/dashboard') {
    if (cookieMode === 'society' || cookieMode === 'gate') {
      const url = request.nextUrl.clone();

      url.pathname = `/admin/${cookieMode}/dashboard`;

      return NextResponse.rewrite(url);
    }


    // no mode yet? only once
    if (!searchParams.has('noredirect')) {
      const url = new URL('/admin/select', request.url);

      url.searchParams.set('noredirect', '1');

      return NextResponse.redirect(url);
    }


    return NextResponse.next();
  }

  // 3. For all other /admin routes:
  if (pathname.startsWith('/admin')) {
    // 3a. Unauthorized‐path guard
    const pathMode = getAdminModeFromPath(pathname); // 'society' | 'gate' | null

    if (pathMode) {
      if(!["gate", "society"].includes(pathMode)) {
        return NextResponse.rewrite(new URL('/admin/select', request.url));
      }

      if(cookieMode !== pathMode) {
        const response = NextResponse.next()

        response.cookies.set('toggle_mode', pathMode, cookieConfig);
        
        return response
      }

      // mismatch → send back to select (once)
      // if (cookieMode !== pathMode && !searchParams.has('noredirect')) {
      //   const url = request.nextUrl.clone();

      //   url.pathname = '/admin/select';
      //   url.searchParams.set('noredirect', '1');

      //   return NextResponse.redirect(url);
      // }
    }

    // 3b. company_id guard
    const companyId = request.cookies.get('company_id')?.value;

    if (!companyId && !searchParams.has('noredirect')) {
      const url = request.nextUrl.clone();

      url.pathname = '/admin/select';
      url.searchParams.set('noredirect', '1');

      return NextResponse.redirect(url);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/logout', '/admin/:path*'],
};
