{"meta": {"formId": "New Role", "title": "New Role", "api_path": "/api/schema", "edit": {"allowFor": ["admin"]}, "add": {"allowFor": ["admin"]}, "delete": {"allowFor": ["admin", "complaints"]}}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/api/roles", "redirect": "/roles"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/roles/listSocRoles"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "/api/roles"}], "flow": {"title": "Credit Note", "order": ["account", "name", "bill_Type", "Invoice Number", "rectification_date", "Narration"], "required": [], "children": {"account": {"title": "Account", "type": "radio", "enum": ["Member", "Non Member"], "children": {"Member": {"bill_Type": {"title": "Bill <PERSON>s", "type": "select", "enum": ["Select Bill Type", "Incidental Bill", "Maintenance Bill"]}}}}, "name": {"title": "Name"}, "Invoice Number": {"title": "Invoice Number", "type": "select", "enum": ["1", "2", "3"]}, "rectification_date": {"title": "Rectification Date", "type": "date"}, "Narration": {"title": "Narration", "type": "textarea"}}}}}