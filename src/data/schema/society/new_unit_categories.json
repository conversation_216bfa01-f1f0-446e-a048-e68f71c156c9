{"meta": {"formId": "New Role", "title": "New Role", "api_path": "/api/schema"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "societies/defineUnitType"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/roles/listSocRoles"}], "flow": {"title": "New Unit Category", "children": {"type": {"title": "Unit Category"}, "soc_unit_type": {"title": "Unit Type", "type": "dropdown", "apiPath": "admin/societies/listUnitType", "keys": ["soc_unit_type"]}, "use_category": {"title": "Use Category", "type": "select", "enum": ["Residential", "Commercial", "common"]}, "quantity": {"title": "Quantity", "type": "number"}, "area/unit": {"title": "Area/Unit(sqft)", "type": "number"}, "open/area_unit": {"title": "Open Area/Unit(sqft)", "type": "number"}, "chargeable": {"title": "Chargeable", "type": "checkbox", "enum": ["Hello"]}, "water_inlets": {"title": "Water Inlets", "type": "number"}}}}}