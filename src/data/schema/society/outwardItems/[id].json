{"meta": {"title": "Outward Inventory", "data_source": "/inventory/listInventory/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "inventory/outward", "redirect": "/admin/society/inventory/listInventory"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/inventory/listInventory"}], "flow": {"children": {"inven_items_name": {"title": "Product Name", "type": "string", "readonly": true, "disabled": true}, "inventory_txns_date": {"title": "Date", "type": "date", "required": true}, "inventory_txns_quantity": {"title": "Quantity", "type": "number", "required": true, "endAdornment": "KG"}, "location": {"title": "Location", "type": "string", "required": true}, "transaction_by": {"title": "Outward By", "type": "string", "required": true}, "inventory_txns_comments": {"title": "Comments", "type": "textarea", "required": true}}}}}