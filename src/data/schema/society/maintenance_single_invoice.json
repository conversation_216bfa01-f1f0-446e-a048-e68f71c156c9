{"meta": {"title": "Generate Invoice"}, "schema": {"actions": [{"title": "Genrate", "type": "submit", "api_path": "/api/roles", "redirect": "/roles"}, {"title": "Preview", "api_path": "/api/roles", "redirect": "/roles"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/roles/listSocRoles"}], "rules": [{"conditions": {"any": [{"fact": "member_unit", "operator": "truthy", "value": true}]}, "event": [{"type": "update", "params": {"genrate_invoice_for": {"fact": "member_unit", "path": "unit_name"}}}]}, {"conditions": {"any": [{"fact": "member_unit", "operator": "falsy", "value": true}]}, "event": [{"type": "remove", "params": {"field": ["genrate_invoice_for", "invoice_date", "invoice_due_date", "invoice_period_from", "invoice_periode_to"]}}]}, {"conditions": {"all": [{"fact": "invoice_due_date", "operator": "lessThan", "value": {"fact": "invoice_date"}}]}, "event": [{"type": "validation", "params": {"field": "invoice_due_date", "message": "Invoice due date must be after the invoice date"}}]}, {"conditions": {"all": [{"fact": "invoice_period_from", "operator": "greaterThan", "value": {"fact": "invoice_periode_to"}}]}, "event": [{"type": "validation", "params": {"field": "invoice_period_from", "message": "Invoice period from date must be before the invoice period to date"}}]}], "flow": {"children": {"member_unit": {"title": "Enter a Member or Unit", "type": "dropdown", "apiPath": "admin/income-details/incomemember", "labelKeys": ["member_name"], "dependent": [{"field": "unit_name"}]}, "genrate_invoice_for": {"title": "Generate Invoice For", "type": "string", "disabled": true}, "invoice_date": {"title": "Invoice Date", "type": "date", "required": true, "description": "Must be after the last invoice date", "ui:options": {"helperText": "The date on which the invoice is generated"}}, "invoice_due_date": {"title": "Invoice Due Date", "type": "date", "required": true, "description": "Must be after the invoice date", "ui:options": {"helperText": "The date by which the payment must be made"}}, "invoice_period_from": {"title": "Invoice period from", "type": "date", "required": true, "description": "Start date of the invoice period", "ui:options": {"helperText": "The start date of the period for which the invoice is generated"}}, "invoice_periode_to": {"title": "Invoice period to", "type": "date", "required": true, "description": "End date of the invoice period (must be after the start date)", "ui:options": {"helperText": "The end date of the period for which the invoice is generated"}}}}}}