{"meta": {"title": "New / Edit Vehicles Registration"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "unit_number", "path": "id", "operator": "falsy"}]}, "event": [{"type": "require", "params": {"field": ["soc_building_name", "soc_building_floor", "flat"]}}]}, {"conditions": {"any": [{"fact": "soc_building_name", "path": "id", "operator": "falsy"}, {"fact": "soc_building_floor", "operator": "falsy"}, {"fact": "flat", "path": "id", "operator": "falsy"}]}, "event": [{"type": "require", "params": {"field": ["unit_number"]}}]}, {"conditions": {"any": [{"fact": "unit_number", "path": "id", "operator": "truthy", "value": true}]}, "event": [{"type": "update", "params": {"soc_building_name": {"fact": "unit_number", "path": "soc_building_id"}}}]}, {"conditions": {"any": [{"fact": "soc_building_name", "path": "floor_array", "operator": "truthy", "value": true}]}, "event": [{"type": "schemaOverride", "params": {"soc_building_floor.enum": {"fact": "soc_building_name", "path": "floor_array"}}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "parking/registervehicle", "redirect": "/admin/society/parking/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/parking/list"}], "flow": {"children": {"unit_number": {"title": "Unit Number", "type": "dropdown", "apiPath": "/admin/units/list", "labelKeys": ["building_unit"], "placeholder": "Select Unit Number", "dependent": [{"field": "soc_building_id"}]}, "or_divider": {"title": "OR", "type": "null", "hideLabel": false}, "soc_building_name": {"title": "Building(s)", "type": "dropdown", "apiPath": "/admin/building/list", "labelKeys": ["soc_building_name"], "placeholder": "Select Building", "onMount": true, "dependent": [{"field": "floor_array"}]}, "soc_building_floor": {"title": "Floor(s)", "type": "select", "placeholder": "Select Floor"}, "flat": {"title": "Flat(s)", "type": "dropdown", "apiPath": "/admin/units/list", "labelKeys": ["unit_flat_number"], "placeholder": "Select Units"}}}}}