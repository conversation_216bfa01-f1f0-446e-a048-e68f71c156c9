{"meta": {"formId": "create_group", "title": "App Preferences", "api_path": "/api/schema", "edit": {"allowFor": ["admin"]}, "add": {"allowFor": ["admin"]}, "delete": {"allowFor": ["admin", "complaints"]}}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/api/roles", "redirect": "/roles"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/roles/listSocRoles"}], "flow": {"title": "Tax Exemption Details", "required": ["tax_classes", "lower_limit", "upper_limit", "rate", "percentage", "effective_date", "description"], "children": {"tax_classes": {"title": "Tax Classes", "type": "select", "enum": ["Select"]}, "lower_limit": {"title": "Lower Limit", "type": "number", "default": "0.00"}, "upper_limit": {"title": "Upper Limit", "type": "number", "default": "0.00"}, "gender": {"title": "Gender", "type": "checkbox", "enum": ["Male", "Female"]}, "rate": {"title": "Rate", "type": "number", "default": "0.00"}, "percentage": {"title": "percentage", "type": "select", "enum": ["Select", "Percentage", "Fixed"]}, "effective_date": {"title": "Effective Date", "type": "date"}, "description": {"title": "Description", "type": "textarea"}}}}}