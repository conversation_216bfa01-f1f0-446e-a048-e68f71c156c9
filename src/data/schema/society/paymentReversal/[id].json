{"meta": {"title": "Payment Reversal", "data_source": "/income-details/fetchDataIncomePaymentTrackerConfirmationReversal/:id"}, "schema": {"actions": [{"title": "submit", "type": "submit", "api_path": "/income-details/incomePaymentTrackerConfirmation/4", "method": "put"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendorbill/expensePaymentTrackerList/"}], "rules": [], "flow": {"children": {"sdsd": {"title": " ", "type": "null", "enableMD": true, "description": "## **Are you sure you want to reverse the payment ?**"}, "receipt_number": {"title": "Payment Number", "disabled": true}, "invoice_number": {"title": "Voucher Number", "default": "", "type": "string", "disabled": true}, "payment_mode": {"title": "Payment Mode", "disabled": true}, "payment_amount": {"title": "Payment Amount", "type": "number", "disabled": true, "startAdornment": "₹"}, "reversal_note": {"title": "Reversal Comment", "type": "textarea", "required": true}}}}}