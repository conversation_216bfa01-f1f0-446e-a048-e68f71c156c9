{"meta": {"title": "Refund Money (Refundable Balance: {{total_refundable}} Rs)", "data_source": "credit-accounts/refundMoneyAmount/:id"}, "schema": {"actions": [{"title": "Confirm", "type": "submit", "api_path": "credit-accounts/refundMoney", "redirect": "/admin/society/vendorbill/expensePaymentTrackerList", "default_params": [{"account_context": "vendor"}]}], "rules": [{"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account"]}}]}], "flow": {"children": {"sdsd": {"title": "", "type": "null", "enableMD": true, "description": "## **Are you sure about refund the money ?**"}, "payment_amount": {"title": "Refund Amount", "placeholder": "Enter Refund Amount", "type": "number", "default": 0, "min": 0}, "payment_mode": {"title": "Refund Option", "type": "select", "required": true, "enum": [{"const": "cash", "title": "Cash"}, {"const": "bank", "title": "Bank"}], "default": "cash"}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "/admin/accounts/getBankAccounts", "required": true, "onMount": true, "labelKeys": ["ledger_account_name"]}, "total_refundable": {"title": "", "type": "number"}}}}}