{"meta": {"title": "Add Item"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "inventory/addInventoryItem", "redirect": "/admin/society/inventory/listInventory"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "inventory/addInventoryItem", "redirect": "/admin/society/inventory/addInventoryItem"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/inventory/listInventory/"}], "flow": {"children": {"inven_items_category_id": {"title": "Select Category", "type": "dropdown", "apiPath": "admin/inventory/settings", "labelKeys": ["inventory_categories_name"]}, "inven_items_name": {"title": "Product Name", "type": "string"}, "inven_items_unit": {"title": "Measure Units", "type": "string"}, "inven_led_id": {"title": "Expense <PERSON>'s", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers?current_tab=expense", "labelKeys": ["ledger_account_name"]}}}}}