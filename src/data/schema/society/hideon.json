{"meta": {"title": "Hidden Input"}, "schema": {"actions": [{"title": "Save", "type": "submit"}], "flow": {"title": "Hidden Input", "required": [], "children": {"hidden_input_parent": {"type": "radio", "hidden": true, "children": {"true": {"hidden_input": {"title": "Hidden Input", "description": "On Clicking Hide, Input will be again hidden", "endAdornment": {"title": "Hide (optional)", "update": {"key": "hidden_input_parent", "value": false}}}}}}, "bye": {"title": "sample field 3", "description": "On Clicking Show, Hidden Input will be shown", "endAdornment": {"title": "Show", "update": {"key": "hidden_input_parent", "value": true}}}}}}}