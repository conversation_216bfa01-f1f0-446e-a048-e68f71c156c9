Auto Invoicing Rules are sets of rule which help to generate invoice automatically on regular basis as society billing cycle. . No doubt it is a tedious task to be done, but only for once, and the whole invoicing process becomes just a matter of clicks subsequently. The rules are set and classified as per the unit categories present in the complex. Unit categories are nothing but classification of units done based on their type and area in sqft.

    
###### Features :

    
This section lists all the rules set for auto-invoicing system and allows their edition. Also it allows to set new invoicing rules and late payment charges (interest on invoice). The listed rules are reflected unit category wise in the page.

    
Prerequisite for Auto-Invoicing Rule Creation is that the unit categories should be created.

    
Note : Parking Rule and Late Payment Rules are common and applicable for all unit categories equally.

###### How it Works

    
###### To Add a new Invoicing Rule

    
      
- Click “Add Rule” button at the top right corner. It will open the Add Rule form.
      
- Fill the form with required details. Click ? sign beside the field to know more.
      
- Click Save Changes button at the bottom to save the data and finish the process.
    
    
Note that there are four different formats of forms for several different Income Accounts. Refer the ? marks beside the fields in each form to know more on them. The form types types are:

    
      
- Maintenance Charges / Property Tax - Which can be configured flat fee / per Sqft / floor rise basis
      
- Parking Charges - Can configure for 2/4 wheeler , Tenant, Owner, No of vehicle etc
      
- Non Occupancy Charges (NOC) - Can be configured fixed, percent of other particular and per Sqft
      
- Other Charges / Custom Rules - Can be configured fixed, percent of other particular and per Sqft
    

    
###### To Edit/Change an Invoicing Rule

    
Note that a rule can be edited on till the time no invoicing has been made using it. Once an invoice has been generated using the rule, it can no more be “edited” but “changed”. The application will show Edit or Change Button at the end of the listed rule accordingly.

    
      
- Click “Edit/ Rule” button given at the end of the listed rule. It will open the Edit Rule form. Allow to edit existing rules before they use by any invoice
      
- Click “Copy Rule” button given at the end of the listed rule. It will make the copy of existing rule which you can modify and create the new rule.
      
- Make changes as per requirement.
      
- Click Save Changes button at the bottom to save the data and finish the process.
    

    
###### To Edit interest of an existing Late Payment Charges rule

    
      
- Click “Edit Interest” button given at the end of the listed rule. It will open the Edit Interest form.
      
- Make changes as per requirement.
      
- Click Save Changes button at the bottom to save the data and finish the process.
    

    
###### To Add Late Payment Charges

    
      
- Click “Add Late Payment Charges Rule” button. It will open the form to add interest rate and other details related to late payment charges.
      
- Fill the form with required details. Click ? sign beside the field to know more.
      
- Click Save button at the bottom to save the data and finish the process.
    

    
Charges Type : 
    Choose whether the late charge is fixed amount or percentage based.

    
**If Fixed, mentioned the Amount.

    
**If Percentage, mention the rate of interest per year and also set whether the interest is calculated in simple interest or compound interest.

    
Grace Period : 
    It is the time allotted for the members to make the payment after the bill generation. After this period, the late payment charge get levied.
   

    
Effective Date : 
    It is the date from when this new late payment charge rule becomes applicable.
   

    
Interest is calculated from : 
    In case the late payment charge is charged on percentage basis, you have the option to choose from when the interest is to be calculated, 
    Bill Date (date of bill generation) or Due Date (Last date of the grace period).
   
    
Choose Bill Period End if the end date of billing period should to be considered end date to calculate  number of days delays

    
Choose Per Day when Receipt Date should be considered end date to calculate  number of days delays.

    
###### FAQ

    
        
            
                
                   How late days calculation work at CHSONE?
                
            
            
                
                    
Consider the scenario of quarterly invoicing with 15 days Grace Period and invoice date on first date of quarter. Member has done late payment on 20th day from invoice date. Five day late to make payment. In above scenario CHSONE one will consider below number of late days to calculate late payment charges

                    
                      
                        
                          
                            
Invoice Date

                            
Invoice Period End

                            
                            90 Days

                            
(Duration Between Invoice Date and  Invoice Period End Date)

                            
                          
                          
                            
Due Date

                            
Invoice Period End

                            
                            75 Days

                            
(Duration Between Invoice Date and  Invoice Period End Date)

                            
                          
                          
                            
Invoice Date

                            
Per Day

                            
                            20 Days

                            
(Duration Between Invoice Date and  Invoice Period End Date)

                            
                          
                          
                            
Due Date

                            
Per Day

                            
                            5 Days

                            
(Duration Between Invoice Date and  Invoice Period End Date)

                            
                          
                        
                      
                    
                
            
        
    

###### Check below auto invoice engine