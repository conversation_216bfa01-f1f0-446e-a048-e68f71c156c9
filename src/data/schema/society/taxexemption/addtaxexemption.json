{"meta": {"title": "Tax Exemption Details"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "accounts/addtaxexemption", "redirect": "/admin/society/taxexemption/viewTaxExemption"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/taxexemption/viewTaxExemption"}], "flow": {"children": {"tax_class": {"title": "Tax Classes", "type": "dropdown", "apiPath": "/admin/tax/viewTax", "labelKeys": ["tax_class_name"], "onMount": true}, "lower_limit": {"title": "Lower Limit", "type": "number", "default": 0.0, "startAdornment": "₹", "required": true}, "upper_limit": {"title": "Upper Limit", "type": "number", "default": 0.0, "startAdornment": "₹", "required": true}, "gender": {"title": "Gender", "type": "radio", "enum": [{"const": "male", "title": "Male"}, {"const": "female", "title": "Female"}]}, "rate": {"title": "Rate", "type": "number", "default": "0.00", "startAdornment": "₹", "required": true}, "type": {"title": "percentage", "type": "select", "enum": [{"const": "percentage", "title": "Parcentage"}, {"const": "fixed", "title": "Fixed"}], "default": "percentage", "required": true}, "effective_date": {"title": "Effective Date", "type": "date", "required": true}, "tax_exemption_description": {"title": "Description", "type": "textarea", "required": true}}}}}