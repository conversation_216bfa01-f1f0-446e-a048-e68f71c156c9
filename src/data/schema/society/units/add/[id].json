{"meta": {"title": "New/Edit Unit", "data_source": "units/edit/:id"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "soc_building_id", "path": "floor_array", "operator": "truthy", "value": true}]}, "event": [{"type": "schemaOverride", "params": {"soc_building_floor.enum": {"fact": "soc_building_id", "path": "floor_array"}}}]}, {"conditions": {"any": [{"fact": "floor_array", "operator": "truthy", "value": true}]}, "event": [{"type": "schemaOverride", "params": {"soc_building_floor.enum": {"fact": "floor_array"}}}]}, {"conditions": {"any": [{"fact": "fk_unit_category_id", "path": "area", "operator": "truthy", "value": true}, {"fact": "fk_unit_category_id", "path": "open_space_area", "operator": "truthy", "value": true}]}, "event": [{"type": "update", "params": {"unit_area": {"fact": "fk_unit_category_id", "path": "area"}, "unit_open_area": {"fact": "fk_unit_category_id", "path": "open_space_area"}}}]}, {"conditions": {"any": [{"fact": "occupancy_type", "operator": "equal", "value": "Reserved to Member"}]}, "event": [{"type": "uiOverride", "params": {"charging_type": {"ui:enumDisabled": ["free"]}}}]}, {"conditions": {"any": [{"fact": "occupancy_type", "operator": "equal", "value": "common"}]}, "event": [{"type": "uiOverride", "params": {"charging_type": {"ui:enumDisabled": ["on_demand"]}}}]}, {"conditions": {"any": [{"fact": "occupancy_type", "operator": "equal", "value": "select_occupancy_type"}]}, "event": [{"type": "uiOverride", "params": {"charging_type": {"ui:disabled": true}}}]}, {"conditions": {"any": [{"fact": "category_name", "path": "id", "operator": "truthy", "value": true}]}, "event": [{"type": "update", "params": {"area": {"fact": "category_name", "path": "area"}, "open_space_area": {"fact": "category_name", "path": "open_space_area"}}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "units/edit", "redirect": "/admin/society/units/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/units/list"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "units/edit", "redirect": "/admin/society/units/add"}], "flow": {"title": "Unit Details", "children": {"fk_unit_category_id": {"title": "Category Name", "type": "dropdown", "placeholder": "select unit type...", "apiPath": "/admin/societies/listUnitType", "onMount": true, "labelKeys": ["unit_category", {"key": "type", "field": "unit_category"}], "required": true, "dependent": [{"field": "area", "key": "area_sq_ft"}, {"field": "open_space_area", "key": "open_space_area_sq_ft"}]}, "soc_building_id": {"title": "Building Name", "required": true, "placeholder": "select building name...", "type": "dropdown", "apiPath": "/admin/building/list", "labelKeys": ["soc_building_name"], "onMount": true, "dependent": [{"field": "floor_array"}]}, "soc_building_floor": {"title": "Floor", "required": true, "type": "select", "placeholder": "select building floor..."}, "unit_flat_number": {"title": "Identified As", "type": "string", "required": true, "placeholder": "Flat/Shop/Office no"}, "occupancy_type": {"title": "Occupancy Type", "type": "select", "required": true, "placeholder": "select occupancy Type ...", "enum": [{"const": "select_occupancy_type", "title": "select occupancy Type ..."}, {"const": "reserved", "title": "Reserved to Member"}, {"const": "common", "title": "Common"}], "default": "select_occupancy_type"}, "charge_type": {"title": "Charging Type", "type": "select", "required": true, "placeholder": "select charging Type ...", "order": 1, "enum": [{"const": "select charging Type...", "title": "select charging Type"}, {"const": "onetime", "title": "One Time"}, {"const": "recurring", "title": "Recurring"}, {"const": "free", "title": "Free"}, {"const": "rental", "title": "on Demand"}], "default": "select charging Type..."}, "effective_date": {"title": "Unit w.e.f", "type": "date", "required": true}, "unit_area": {"title": "Area", "type": "number", "disabled": true, "endAdornment": {"title": "sqft"}}, "unit_open_area": {"title": "Open Space Area", "type": "number", "disabled": true, "endAdornment": {"title": "sqft"}}, "unit_total_water_inlets": {"title": "Water Inlet", "type": "number", "description": "Total Water Inlets"}, "income_late_charge_id": {"title": "Delay Payment Charges", "required": true, "placeholder": "As per invoicing rule", "type": "dropdown", "apiPath": "/admin/units/fetchUnitLatePaymentCharges", "labelKeys": ["late_charge"], "onMount": true}, "floor_array": {"title": "Floor", "type": "select", "placeholder": "select floor...", "hidden": true}}}}}