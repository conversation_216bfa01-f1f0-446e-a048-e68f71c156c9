{"meta": {"title": "New Unit"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "soc_building_name", "path": "id", "operator": "truthy", "value": true}]}, "event": [{"type": "schemaOverride", "params": {"soc_building_floor.enum": {"fact": "soc_building_name", "path": "floor_array"}}}]}, {"conditions": {"any": [{"fact": "occupancy_type", "operator": "equal", "value": "Reserved to Member"}]}, "event": [{"type": "uiOverride", "params": {"charge_type": {"ui:enumDisabled": ["free"]}}}]}, {"conditions": {"any": [{"fact": "occupancy_type", "operator": "equal", "value": "common"}]}, "event": [{"type": "uiOverride", "params": {"charge_type": {"ui:enumDisabled": ["on_demand"]}}}]}, {"conditions": {"any": [{"fact": "occupancy_type", "operator": "falsy", "value": true}]}, "event": [{"type": "uiOverride", "params": {"charge_type": {"ui:disabled": true}}}]}, {"conditions": {"any": [{"fact": "fk_unit_category_id", "path": "id", "operator": "truthy", "value": true}]}, "event": [{"type": "update", "params": {"area": {"fact": "fk_unit_category_id", "path": "area"}, "open_space_area": {"fact": "fk_unit_category_id", "path": "open_space_area"}}}]}, {"conditions": {"any": [{"fact": "fk_unit_category_id", "operator": "falsy", "value": true}]}, "event": [{"type": "remove", "params": {"field": ["area", "open_space_area", "income_late_charge_id"]}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "units/add", "redirect": "/admin/society/units/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/units/list"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "units/add", "redirect": "/admin/society/units/add"}], "flow": {"title": "Unit Details", "children": {"fk_unit_category_id": {"title": "Category Name", "type": "dropdown", "required": true, "placeholder": "select unit type...", "apiPath": "/admin/societies/listUnitType", "onMount": true, "labelKeys": ["type"], "dependent": [{"field": "area", "key": "area_sq_ft"}, {"field": "open_space_area", "key": "open_space_area_sq_ft"}]}, "soc_building_name": {"title": "Building Name", "required": true, "placeholder": "select building name...", "type": "dropdown", "apiPath": "/admin/building/list", "description": "(If unit is not inside building, specify name of nearby building)", "labelKeys": ["soc_building_name"], "onMount": true, "dependent": [{"field": "floor_array"}]}, "soc_building_floor": {"title": "Floor", "required": true, "type": "select", "placeholder": "select building floor..."}, "unit_flat_number": {"title": "Identified As", "required": true, "type": "string", "placeholder": "Flat/Shop/Office no"}, "occupancy_type": {"title": "Occupancy Type", "type": "select", "required": true, "placeholder": "select occupancy Type ...", "enum": [{"const": "reserved", "title": "Reserved to Member"}, {"const": "common", "title": "Common"}]}, "charge_type": {"title": "Charging Type", "type": "select", "required": true, "placeholder": "select charging Type ...", "order": 1, "enum": [{"const": "onetime", "title": "One Time"}, {"const": "recurring", "title": "Recurring"}, {"const": "free", "title": "Free"}, {"const": "rental", "title": "on Demand"}]}, "unit_wef": {"title": "unit W.e.f", "type": "date", "required": true}, "area": {"title": "Area", "type": "number", "disabled": true, "min": 0, "endAdornment": {"title": "sqft"}}, "open_space_area": {"title": "Open Space Area", "type": "number", "disabled": true, "min": 0, "endAdornment": {"title": "sqft"}}, "income_late_charge_id": {"title": "Total Water Inlets", "type": "number", "min": 0}}}}}