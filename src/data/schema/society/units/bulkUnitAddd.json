{"meta": {"title": "New Bulk Unit Identification"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/api/roles", "redirect": "/roles"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/roles/listSocRoles"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "/api/roles"}], "flow": {"title": "Unit Identification Details", "required": ["category_name", "Role Display Name"], "children": {"category_name": {"title": "Category Name", "type": "select", "enum": ["Select Unit Type", "1 BHK", "2 BHK", "3 BHK", "4 BHK", "5 BHK", "6 BHK", "7 BHK", "8 BHK", "9 BHK", "10 BHK"]}, "building_name": {"title": "Building Name", "type": "select", "enum": ["Select Building name", "Tower A", "Tower B", "Tower C", "Tower D", "Tower E", "Tower F", "Tower G"]}, "floor": {"title": "Floor Range", "type": "select", "enum": ["select building floor ..."]}, "occupancy_type": {"title": "Occupancy Type", "type": "select", "enum": ["select occupancy Type ...", "Reserved to Member", "common"]}, "charging_type": {"title": "Charging Type", "type": "select", "enum": ["select charging type ...", "Fixed", "Variable"]}, "unit W.e.f": {"title": "Unit W.e.f", "type": "date"}}}}}