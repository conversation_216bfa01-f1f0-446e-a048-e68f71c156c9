{"meta": {"title": "Edit Staff", "data_source": "staffs/staffDetails/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "staffs/editStaff", "redirect": "/admin/society/staffs/staffLists"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/staffs/staffLists"}, {"title": "Reset", "type": "reset"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "staffs/editStaff", "redirect": "/admin/society/staffs/addStaff"}], "flow": {"title": "Staff Details", "required": ["staff_type_id", "staff_first_name", "staff_contact_number", "staff_dob", "staff_address_1", "staff_proof"], "children": {"staff_type_id": {"title": "Staff Category", "type": "dropdown", "apiPath": "/admin/staffs/staffCategory", "labelKeys": ["category"], "onMount": true}, "staff_first_name": {"title": "Name", "type": "string", "placeholder": "Enter Name"}, "staff_badge_number": {"title": "Badge No", "type": "string", "placeholder": "Enter Badge No", "required": true}, "staff_contact_number": {"title": "Mobile Number", "type": "number", "placeholder": "Enter Mobile Number"}, "staff_email_id": {"title": "Email", "type": "string", "placeholder": "<PERSON><PERSON>"}, "staff_address_1": {"title": "Address", "type": "textarea", "placeholder": "Enter Address"}, "staff_gender": {"title": "Gender", "type": "radio", "enum": [{"const": "M", "title": "Male"}, {"const": "F", "title": "Female"}], "default": "M"}, "staff_dob": {"title": "Date of Birth", "type": "date"}, "staff_proof": {"title": "Proof of Residence", "type": "file"}, "staff_image": {"title": "Capture Image", "type": "camera"}, "staff_qualification": {"title": "Qualification", "type": "string", "placeholder": "Enter Qualification"}, "staff_skill": {"title": "Skill", "type": "string", "placeholder": "<PERSON><PERSON>"}, "staff_lang_iso_639_3": {"title": "Language Spoken", "type": "string", "placeholder": "Enter Language Spoken"}, "staff_rfid": {"title": "RFID / Biometric ID", "type": "string", "placeholder": "Enter RFID / Biometric ID"}, "staff_note": {"title": "Note", "type": "textarea", "placeholder": "Enter Note"}}}}}