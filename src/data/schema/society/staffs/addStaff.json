{"meta": {"title": "New Staff"}, "schema": {"actions": [{"title": "Save & New", "type": "submit_and_new", "api_path": "staffs/addStaff"}, {"title": "Save", "type": "submit", "api_path": "staffs/addStaff", "redirect": "/admin/society/staffs/staffLists"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/staffs/staffLists"}], "flow": {"title": "Staff Details", "children": {"staff_type_id": {"title": "Staff Category", "type": "dropdown", "placeholder": "Please select category", "apiPath": "/admin/staffs/settings", "required": true, "onMount": true, "labelKeys": ["category"]}, "staff_first_name": {"title": "Name", "type": "string", "placeholder": "Enter Name", "required": true}, "staff_badge_number": {"title": "Badge No", "type": "string", "placeholder": "Enter Badge No", "required": true}, "staff_contact_number": {"title": "Mobile Number", "type": "number", "pattern": "[0-9]{10}", "placeholder": "Enter Mobile Number", "required": true}, "staff_email_id": {"title": "Email", "type": "string", "placeholder": "<PERSON><PERSON>"}, "staff_address_1": {"title": "Address", "type": "textarea", "placeholder": "Enter Address", "required": true}, "staff_gender": {"title": "Gender", "type": "radio", "enum": [{"title": "Male", "const": "M"}, {"title": "Female", "const": "F"}], "default": "M", "required": true}, "staff_dob": {"title": "Date of Birth", "type": "date", "required": true}, "staff_proof": {"title": "Proof of Residence", "type": "file", "required": true, "description": "Allowed file types - pdf( pdf ) ,image( png,jpeg,jpg,gif,bmp,tif )", "accept": ".pdf,image/png,image/jpeg,image/jpg,image/gif,image/bmp,image/tif"}, "staff_image": {"title": "Capture Image", "type": "camera"}, "staff_qualification": {"title": "Qualification", "type": "string", "placeholder": "Enter Qualification"}, "staff_skill": {"title": "Skill", "type": "string", "placeholder": "<PERSON><PERSON>"}, "staff_lang_iso_639_3": {"title": "Language Spoken", "type": "string", "placeholder": "Enter Language Spoken"}, "staff_rfid": {"title": "RFID / Biometric ID", "type": "string", "placeholder": "Enter RFID / Biometric ID"}, "staff_note": {"title": "Note", "type": "textarea", "placeholder": "Enter Note"}}}}}