{"meta": {"title": "Asset Categories"}, "schema": {"facts": [{"fact": "sequences", "path": "/admin/membertype/details", "method": "GET", "returns": "all", "onMount": true}], "rules": [{"conditions": {"all": [{"fact": "sequences", "operator": "truthy"}]}, "event": [{"type": "uiSchemaReplace", "params": {"sequence.ui:options.tableSchema": {"fact": "sequences", "path": "meta"}}}, {"type": "update", "params": {"sequence": {"fact": "sequences", "path": "data"}}}]}], "actions": [{"title": "Save", "type": "submit_and_new", "refetch": true, "api_path": "assets/settings"}], "flow": {"children": {"params": {"title": "Allottee Type", "type": "array", "children": {"member_type_name": {"title": "Asset Categories", "type": "string", "required": true, "placeholder": "Enter Asset Category"}}}, "sequence": {"title": "Asset Categories", "type": "table", "selectable": false, "default": []}}}}}