{"meta": {"title": "New Communication"}, "schema": {"rules": [{"when": "type", "is": "survey", "then": [{"show": ["survey_purpose", "survey_question", "survey_note"]}]}, {"when": "type", "isNot": "survey", "then": [{"hide": ["survey_purpose", "survey_question", "survey_note"]}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "notices/add", "redirect": "/admin/society/notices/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/notices/list"}], "flow": {"children": {"type": {"title": "Type", "type": "radio", "required": true, "enum": [{"const": "notice", "title": "Notice"}, {"const": "circular", "title": "Circular"}, {"const": "mom", "title": "MoM"}, {"const": "announcement", "title": "Announcement"}, {"const": "survey", "title": "Survey"}], "default": "notice"}, "template": {"title": "Template", "type": "select", "placeholder": "Select template", "apiPath": "admin/notices/templates", "labelKeys": ["template_name"]}, "subject": {"title": "Subject", "type": "string", "required": true, "placeholder": "Enter subject"}, "body": {"title": "Body", "type": "richtext", "required": true, "placeholder": "Your members are requested to attend meeting tomorrow."}, "effective_from": {"title": "Effective From", "type": "date", "required": true}, "published_on": {"title": "Published On", "type": "date", "required": true}, "notify_via": {"title": "Notify Via", "type": "radio", "enum": [{"const": "app", "title": "App"}, {"const": "sms", "title": "SMS"}], "default": "app"}, "send_to": {"title": "Send To", "type": "radio", "required": true, "enum": [{"const": "committee_members", "title": "Committee Members"}, {"const": "all_members", "title": "All Members"}, {"const": "individual_members", "title": "Individual Members"}, {"const": "checklist_members", "title": "Checklist Members"}], "default": "all_members"}, "members": {"title": "Members", "type": "multiselect", "placeholder": "Select Some Options", "apiPath": "admin/members/list", "labelKeys": ["member_name"], "dependsOn": {"field": "send_to", "value": "individual_members"}}, "attachment": {"title": "Attachment", "type": "file", "accept": ".pdf,.doc,.docx,.jpg,.jpeg,.png"}, "survey_purpose": {"title": "Survey Purpose", "type": "string", "placeholder": "Enter Survey Purpose"}, "survey_question": {"title": "Survey Question", "type": "string", "placeholder": "Enter Survey Question"}, "survey_note": {"title": "Survey Note", "type": "textarea", "placeholder": "Enter Survey Note"}}}}}