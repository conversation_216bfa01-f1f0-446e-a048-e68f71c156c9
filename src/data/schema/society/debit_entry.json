{"meta": {"title": "App Preferences"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "transaction/addDebitEntry", "redirect": "/"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/"}], "flow": {"title": "Debit Note Details", "required": [], "children": {"salesType": {"title": "Type", "type": "radio", "enum": ["Income", "Expense", "Adjustment"]}, "from_ledger_debit_id": {"title": "From ledger/Debit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"]}, "to_ledger_credit_id": {"title": "To ledger/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"]}, "to_ledger_credit_id1": {"title": "To ledger/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"]}, "amount": {"title": "Amount", "type": "string"}, "receipt_date": {"title": "Receipt Date", "type": "date"}, "reference_number": {"title": "Reference Number", "type": "string"}, "memo_description": {"title": "Memo Description", "format": "textarea"}}}}}