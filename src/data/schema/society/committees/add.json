{"meta": {"title": "Form a Committee"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "members", "path": "member_name", "operator": "truthy"}]}, "event": {"type": "uiSchemaReplace", "params": {"office.items.office_member_name.ui:options.loadedOptions": {"fact": "members", "path": "member_name"}}}}], "actions": [{"title": "Save", "type": "submit_and_new", "refetch": true, "api_path": "staffs/settings"}], "flow": {"children": {"Committee": {"title": "Committee", "layout": "group", "children": {"committee_type": {"title": "Comittee Type", "type": "radio", "required": true, "enum": [{"title": "Provisional", "const": "provisional"}, {"title": "Elected", "const": "elected"}], "default": "provisional"}, "effective_date": {"title": "Committee Effective Date", "type": "date", "required": true}}}, "members": {"title": "Committee Members", "layout": "group", "children": {"member_name": {"title": "Member Name", "type": "dropdown", "required": true, "multiple": true, "minItems": 5, "limitTags": -1, "apiPath": "admin/member/unitWiseMembersList", "onMount": true, "labelKeys": ["member_display_name", "building_unit"], "placeholder": "Select Committee Members"}}}, "office": {"title": "Office Assignments", "type": "array", "minItems": 1, "required": true, "children": {"responsibility": {"title": "Responsibility", "type": "string", "required": true, "placeholder": "Enter responsibility (e.g., President, Secretary)"}, "office_member_name": {"title": "Member Name", "type": "dropdown", "required": true, "placeholder": "Select a member for this responsibility"}}}}}}}