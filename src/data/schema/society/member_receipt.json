{"meta": {"title": "New Receipt"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "income-details/createReceipt", "redirect": "admin/income-details/incomemember"}], "rules": [{"conditions": {"all": [{"fact": "amount", "operator": "lessThan", "value": 0}]}, "event": [{"type": "validation", "params": {"field": "amount", "message": "Amount must be a positive number"}}]}, {"conditions": {"all": [{"fact": "writeoff_amount", "operator": "lessThan", "value": 0}, {"fact": "show_writeoff", "operator": "equal", "value": true}]}, "event": [{"type": "validation", "params": {"field": "writeoff_amount", "message": "Write-off amount must be a positive number"}}]}, {"conditions": {"all": [{"fact": "amount", "operator": "truthy"}, {"fact": "writeoff_amount", "operator": "truthy"}, {"fact": "show_writeoff", "operator": "equal", "value": true}, {"fact": "amount", "operator": "add", "value": {"fact": "writeoff_amount"}, "path": "$", "operator2": "greaterThan", "value2": {"fact": "total_due"}}]}, "event": [{"type": "validation", "params": {"field": "amount", "message": "Payment amount + Write-off amount cannot exceed the total due amount"}}]}, {"conditions": {"all": [{"fact": "cheque_date", "operator": "greaterThan", "value": "{{today}}"}]}, "event": [{"type": "validation", "params": {"field": "cheque_date", "message": "Cheque date cannot be in the future"}}]}, {"conditions": {"all": [{"fact": "payment_date", "operator": "greaterThan", "value": "{{today}}"}]}, "event": [{"type": "validation", "params": {"field": "payment_date", "message": "Payment date cannot be in the future"}}]}, {"conditions": {"all": [{"fact": "amount", "operator": "truthy"}, {"fact": "writeoff_amount", "operator": "truthy"}, {"fact": "total_due", "operator": "truthy"}]}, "event": [{"type": "calculate", "params": {"amount": {"fact": "amount"}, "writeoff_amount": {"fact": "writeoff_amount"}, "total_due": {"fact": "total_due"}, "__field": "remaining_amount", "__exp": "total_due - amount - writeoff_amount"}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "truthy", "value": true}]}, "event": [{"type": "schemaOverride", "params": {"soc_building_floor.enum": {"fact": "soc_building_name", "path": "floor_array"}}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "falsy", "value": true}]}, "event": [{"type": "remove", "params": {"field": ["sequence"]}}]}, {"conditions": {"any": [{"fact": "suspense_receipt", "operator": "truthy", "value": true}]}, "event": [{"type": "remove", "params": {"field": ["unit_key", "unit_details", "total_due"]}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"unit_details": {"fact": "unit_key", "path": "$..display_member_name"}}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"unit_details": ""}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"received_from": {"fact": "unit_key", "path": "$..member_name"}}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"received_from": ""}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"total_due": {"fact": "unit_key", "path": "$..due_amount"}}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"total_due": ""}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"amount": {"fact": "unit_key", "path": "$..due_amount"}}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"amount": ""}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": false}]}, "event": [{"type": "update", "params": {"writeoff_amount": 0}}, {"type": "remove", "params": {"field": ["writeoff_amount"]}}, {"type": "uiOverride", "params": {"vendor_bill_advance_amount": {"ui:disabled": false}}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": true}]}, "event": [{"type": "update", "params": {"vendor_bill_advance_amount": 0}}, {"type": "require", "params": {"field": ["writeoff_amount"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "cheque_number", "bank_name", "cheque_date"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "select_a_mode_of_payment"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "cheque_number", "bank_name", "cheque_date", "payment_ref"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "cheque_number", "bank_name", "cheque_date", "payment_ref"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "etf"}]}, "event": [{"type": "remove", "params": {"field": ["cheque_number", "bank_name", "cheque_date"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "cheque_number", "bank_name", "cheque_date", "payment_ref"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cheque"}]}, "event": [{"type": "remove", "params": {"field": ["payment_ref"]}}]}], "flow": {"title": "Maintenance Receipt", "children": {"suspense_receipt": {"title": "Is Suspense Receipt ?", "type": "boolean", "enum": [{"const": true, "title": "Yes"}, {"const": false, "title": "No"}], "default": false}, "unit_key": {"title": "Enter a Member or Unit", "type": "dropdown", "apiPath": "admin/income-details/incomemember", "labelKeys": ["member_name"], "dependent": [{"field": "display_member_name"}, {"field": "due_amount"}, {"field": "member_name"}]}, "unit_details": {"title": "Unit Details", "type": "string", "disabled": true}, "received_from": {"title": "Received From", "type": "string"}, "total_due": {"title": "Total Due", "type": "number", "default": 0.0, "disabled": true, "startAdornment": "₹", "description": "The total amount due for this member", "ui:options": {"backgroundColor": "#fff8f0", "fontWeight": "bold"}}, "amount": {"title": "Amount", "type": "number", "default": 0.0, "startAdornment": "₹", "description": "Enter a positive amount (cannot exceed the total due)", "ui:options": {"inputProps": {"min": 0}}, "endAdornment": [{"title": "TDS", "icon": "ri-add-line", "showButton": true, "update": {"key": "show_writeoff", "value": true}}]}, "writeoff_amount": {"title": "TDS Amount", "type": "number", "startAdornment": "₹", "description": "Enter a positive amount (cannot exceed the total due)", "ui:options": {"inputProps": {"min": 0}, "backgroundColor": "#f0fff0"}, "endAdornment": [{"title": "Remove Write Off", "icon": "ri-close-line", "update": {"key": "show_writeoff", "value": false}}]}, "show_writeoff": {"type": "radio", "hidden": true, "default": false}, "remaining_amount": {"title": "Remaining Amount", "type": "number", "disabled": true, "startAdornment": "₹", "description": "Total Due - Amount - Write-off Amount", "ui:widget": "highlighted-field", "ui:options": {"backgroundColor": "#f0f8ff", "fontWeight": "bold"}}, "payment_mode": {"title": "Mode of Payment", "type": "select", "enum": [{"const": "select_a_mode_of_payment", "title": "Select a mode of payment"}, {"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "etf", "title": "Electronic Fund Transfer"}], "default": "select_a_mode_of_payment", "required": true}, "bank_account": {"title": "Bank Account", "type": "dropdown", "required": true, "apiPath": "admin/income-details/getBankAccountList", "labelKeys": ["bank_account_name"]}, "payment_ref": {"title": "Payment Reference", "type": "string", "required": true}, "cheque_number": {"title": "Cheque Number", "type": "string", "description": "", "required": true}, "bank_name": {"title": "Bank & Branch Name", "type": "string", "description": "", "required": true}, "cheque_date": {"title": "Cheque Date", "type": "date", "maxDateTime": "now", "required": true}, "payment_date": {"title": "Date of Receipt", "type": "date", "maxDateTime": "now", "required": true}, "receipt_note": {"title": "Receipt Note", "type": "textarea"}, "sequence": {"title": "Table", "type": "table", "apiPath": "/admin/vendorbill/fetchDataVendorPaymentList/:unit_key.id", "changeOn": "unit_key"}}}}}