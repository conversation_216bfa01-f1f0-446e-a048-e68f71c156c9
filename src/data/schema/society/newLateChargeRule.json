{"meta": {"title": "New Late Charges Rule"}, "schema": {"actions": [{"title": "Save", "type": "submit", "default_params": [{"type": "maintenance"}], "api_path": "income-tracker-invoice-setting/add_late_payment_charge_rule"}], "rules": [{"conditions": {"any": [{"fact": "interest_amount_type", "operator": "equal", "value": "percentage"}]}, "event": [{"type": "uiOverride", "params": {"simple_interest": {"ui:title": "Interest Per Year"}}}, {"type": "schemaOverride", "params": {"simple_interest.maximum": 100}}]}, {"conditions": {"any": [{"fact": "interest_amount_type", "operator": "equal", "value": "fixed"}]}, "event": [{"type": "remove", "params": {"field": ["interest_type"]}}]}], "flow": {"required": [], "children": {"interest_amount_type": {"title": "Charges Type", "type": "select", "enum": [{"const": "fixed", "title": "Fixed"}, {"const": "percentage", "title": "Percentage"}], "default": "fixed"}, "simple_interest": {"title": "Amount Monthly", "type": "number", "min": 0, "max": 999999, "required": true}, "interest_type": {"title": "Interest Type", "type": "select", "enum": [{"const": "simple_interest", "title": "Simple Interest"}, {"const": "Monthly", "title": "Compound Interest"}], "default": "simple_interest"}, "grace_period": {"title": "<PERSON> Period", "type": "string", "required": true}, "effective_date": {"title": "Effective Date", "type": "date", "required": true}, "calculate_from": {"title": "Interest is calculated from", "type": "select", "enum": [{"const": "billdate", "title": "<PERSON>"}, {"const": "duedate", "title": "Due Date"}, {"const": "both", "title": "Both (Bill Date and Due Date)"}], "default": "billdate"}, "calculate_for": {"title": "Interest will calculate for", "type": "select", "enum": [{"const": "perday", "title": "Per Day"}, {"const": "billperiod", "title": "<PERSON> End"}], "default": "billperiod"}, "applicable_taxes": {"title": "Tax Class ", "type": "dropdown", "placeholder": "Select Tax Class", "apiPath": "/admin/tax/viewTax", "labelKeys": ["tax_class_name"], "onMount": true}}}}}