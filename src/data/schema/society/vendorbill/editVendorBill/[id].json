{"meta": {"title": "<PERSON>", "data_source": "vendorbill/editVendorBill/:id"}, "schema": {"facts": [{"fact": "purchaseformlist", "path": "/admin/vendorbill/purchaseformlist/:id", "xpath": "$.id", "method": "GET", "dependsOn": "vendor_id", "dependent": ["PO_WO"]}], "actions": [{"title": "Save", "type": "submit", "api_path": "vendorbill/editVendorBill/:id", "redirect": "/admin/society/vendorbill/vendorBill?current_tab=[status]"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendorbill/vendorBill/"}], "rules": [{"conditions": {"all": [{"fact": "vendor_id", "path": "rate", "operator": "exists"}]}, "event": [{"type": "update", "params": {"vendor_bill_tds_rate": {"fact": "vendor_id", "path": "rate"}}}, {"type": "uiSchemaReplace", "params": {"bill_amount_tds.ui:disabled": {"fact": "vendor_id", "path": "rate", "operator": "truthy"}}}]}, {"conditions": {"any": [{"fact": "apply_rcm", "operator": "truthy"}]}, "event": [{"type": "nested<PERSON><PERSON><PERSON>", "params": {"fields": ["particulars.*.particular_gst", "particulars.*.particular_sgst", "particulars.*.particular_cgst", "particulars.*.particular_igst"]}}]}, {"conditions": {"any": [{"fact": "particulars", "operator": "truthy"}]}, "event": {"type": "calculate", "params": {"cgst": {"fact": "particulars", "path": "$..particular_cgst"}, "sgst": {"fact": "particulars", "path": "$..particular_sgst"}, "igst": {"fact": "particulars", "path": "$..particular_igst"}, "amount": {"fact": "particulars", "path": "$..amount"}, "rcm": {"fact": "vendor_id", "path": "vendor_rcm"}, "__field": "particulars.*.total_amount", "__exp": "round(amount .* (1 + (cgst + sgst + igst + rcm) ./ 100), 2)"}}}, {"conditions": {"all": [{"fact": "vendor_id", "path": "id", "operator": "truthy"}, {"fact": "bill_date", "operator": "truthy"}]}, "event": [{"type": "updateDate", "params": {"credit_period": {"fact": "vendor_id", "path": "vendor_credit_period"}, "bill_date": {"fact": "bill_date"}, "__exp": "{{bill_date}} + {{credit_period}} days", "__field": "vendor_bill_due_date"}}]}, {"conditions": {"any": [{"fact": "show_writeoff", "operator": "falsy"}, {"fact": "purchase_mode", "operator": "equal", "value": "credit"}]}, "event": [{"type": "remove", "params": {"field": ["writeoff_amount"]}}, {"type": "update", "params": {"writeoff_amount": 0}}]}, {"conditions": {"all": [{"fact": "vendor_id", "path": "id", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"vendor_gstin": {"fact": "vendor_id", "path": "vendor_gstin"}, "apply_rcm": {"fact": "vendor_id", "path": "vendor_rcm", "operator": "truthy"}}}, {"type": "uiSchemaReplace", "params": {"PO_WO.ui:options.loadedOptions": {"fact": "purchaseformlist"}}}]}, {"conditions": {"any": [{"fact": "bill_amount_tds", "operator": "truthy"}, {"fact": "particulars", "operator": "truthy"}, {"fact": "purchase_order_amount", "operator": "truthy"}]}, "event": [{"type": "calculate", "params": {"vendor_bill_tds_rate": {"fact": "vendor_id", "path": "rate"}, "bill_amount_tds": {"fact": "bill_amount_tds"}, "purchase_order_amount": {"fact": "purchase_order_amount"}, "total_amount": {"fact": "particulars", "path": "$..amount"}, "writeoff_amount": {"fact": "writeoff_amount"}, "__field": "bill_amount_tds", "__exp": "vendor_bill_tds_rate > 0 ? round((purchase_order_amount + sum(total_amount) - writeoff_amount) * (vendor_bill_tds_rate / 100), 2) : bill_amount_tds"}}, {"type": "calculate", "params": {"apply_round_off": {"fact": "apply_round_off"}, "bill_amount_tds": {"fact": "bill_amount_tds"}, "purchase_order_amount": {"fact": "purchase_order_amount"}, "total_amount": {"fact": "particulars", "path": "$..total_amount"}, "writeoff_amount": {"fact": "writeoff_amount"}, "__field": "bill_amount", "__exp": "round((purchase_order_amount + sum(total_amount) - writeoff_amount) - bill_amount_tds, apply_round_off ? 0 : 2)"}}, {"type": "calculate", "params": {"bill_amount_tds": {"fact": "bill_amount_tds"}, "bill_amount": {"fact": "bill_amount"}, "purchase_order_amount": {"fact": "purchase_order_amount"}, "total_amount": {"fact": "particulars", "path": "$..total_amount"}, "writeoff_amount": {"fact": "writeoff_amount"}, "__field": "vendor_bill_roundoff", "__exp": "min(max(bill_amount - ((purchase_order_amount + sum(total_amount) - writeoff_amount) - bill_amount_tds), 0), 1)"}}]}, {"conditions": {"all": [{"fact": "vendor_id", "path": "id", "operator": "truthy"}, {"fact": "bill_date", "operator": "truthy"}]}, "event": [{"type": "updateDate", "params": {"credit_period": {"fact": "vendor_id", "path": "vendor_credit_period"}, "bill_date": {"fact": "bill_date"}, "__exp": "{{bill_date}} + {{credit_period}} days", "__field": "vendor_bill_due_date"}}]}, {"conditions": {"all": [{"fact": "vendor_id", "path": "id", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"vendor_gstin": "", "apply_rcm": false, "PO_WO": {}}}, {"type": "uiSchemaReplace", "params": {"PO_WO.ui:options.loadedOptions": []}}]}, {"conditions": {"all": [{"fact": "PO_WO", "path": "purchase_form_amount", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"purchase_order_amount": {"fact": "PO_WO", "path": "purchase_form_amount"}}}]}, {"conditions": {"any": [{"fact": "purchase_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["status"]}}, {"type": "uiOverride", "params": {"bill_amount": {"ui:options": {"endAdornment": [{"icon": "ri:increase-decrease-line", "title": "Write Off", "showButton": true, "update": {"key": "show_writeoff", "value": true}}]}}}}]}, {"conditions": {"any": [{"fact": "bill_amount_tds", "operator": "truthy"}, {"fact": "vendor_id", "path": "id", "operator": "truthy"}, {"fact": "vendor_id", "path": "rate", "operator": "truthy"}]}, "event": {"type": "tooltip", "params": {"bill_amount_tds": {"fact": "bill_amount_tds"}, "vendor_bill_tds_rate": {"fact": "vendor_bill_tds_rate"}, "__field": ["bill_amount_tds"], "__title": "Vendor TDS Rate- {{vendor_bill_tds_rate}}% TDS Amount as per Vendor TDS Rate- {{bill_amount_tds}}"}}}], "flow": {"title": "<PERSON>", "required": ["bill_number", "bill_date", "particulars", "bill_amount"], "order": ["vendor_id", "vendor_gstin", "PO_WO", "bill_number", "bill_date", "vendor_bill_due_date", "purchase_mode", "particulars", "purchase_order_amount", "bill_amount", "*"], "children": {"vendor_id": {"title": "Vendor Name", "type": "dropdown", "placeholder": "Please Enter Vendor Name", "onMount": true, "required": true, "apiPath": "/admin/vendor/viewVendorDropdown", "labelKeys": ["vendor_name"], "dependent": [{"field": "vendor_gstin", "key": "vendor_service_regn"}, {"field": "vendor_rcm"}, {"field": "vendor_credit_period"}, {"field": "rate"}], "endAdornment": [{"icon": "mdi:plus", "title": "Add <PERSON>", "form": "vendor/addVendor"}]}, "vendor_gstin": {"title": "GST", "type": "string", "placeholder": "Enter GST", "disabled": true}, "PO_WO": {"title": "PO/WO (Optional)", "type": "dropdown", "placeholder": "Select Purchase Order", "onMount": true, "labelKeys": ["purchase_form_po_number", "purchase_form_title"], "dependent": [{"field": "purchase_form_amount"}]}, "bill_number": {"title": "<PERSON>", "placeholder": "Enter Bill Number", "type": "number"}, "bill_date": {"title": "<PERSON>", "type": "date", "maxDateTime": "now", "minDateTime": "-94 year"}, "vendor_bill_due_date": {"title": "Payment Due Date", "type": "date"}, "purchase_mode": {"title": "Purchase Type", "type": "radio", "enum": [{"title": "Credit", "const": "credit"}, {"title": "Cash", "const": "cash"}], "default": "credit"}, "particulars": {"title": "<PERSON>", "type": "array", "layout": "tabular", "default": [], "children": {"particular": {"title": "Particular", "type": "textarea"}, "et_id": {"title": "Expense Account", "type": "dropdown", "onMount": true, "apiPath": "/admin/expensetracker/settingsApply/", "labelKeys": ["et_type_name"], "defaultParams": ["vendor_type"], "required": true}, "amount": {"title": "Amount", "type": "number", "default": 0}, "hsn_sac": {"title": "HSN/SAC", "type": "string"}, "particular_gst": {"title": "GST", "type": "dropdown", "apiPath": "/admin/expensetracker/expenseTaxSetupDropDown/", "labelKeys": ["ledger_account_name"], "onMount": true, "required": true, "dependent": [{"field": "sgst"}, {"field": "cgst"}, {"field": "igst"}], "update": {"particular_sgst": "sgst", "particular_cgst": "cgst", "particular_igst": "igst"}}, "particular_sgst": {"title": "SGST", "type": "number", "disabled": true, "default": 0}, "particular_cgst": {"title": "CGST", "type": "number", "disabled": true, "default": 0}, "particular_igst": {"title": "IGST", "type": "number", "disabled": true, "default": 0}, "total_amount": {"title": "Total", "type": "number", "disabled": true, "default": 0}}}, "bill_amount": {"title": "Total Bill Amount", "type": "number", "disabled": true, "description": "Total Bill Amount = Purchase Order Amount + GST Amount + TDS Amount + Round Off Amount", "endAdornment": []}, "show_writeoff": {"title": "Write Off", "hidden": true, "type": "radio", "enum": [{"title": "Yes", "const": true}, {"title": "No", "const": false}]}, "writeoff_amount": {"title": "Write Off Amount", "type": "number", "placeholder": "Enter Write Off Amount", "required": true, "endAdornment": [{"icon": "ri:close-line", "title": "Remove Write Off", "update": {"key": "show_writeoff", "value": false}}]}, "purchase_order_amount": {"title": "Purchase Order Amount", "type": "number", "disabled": true, "placeholder": "Please Purchase Order Amount", "default": 0}, "vendor_bill_tds_rate": {"title": "TDS Rate", "type": "number", "disabled": true, "hidden": true, "default": 0, "min": 0, "max": 100}, "bill_amount_tds": {"title": "Deduct TDS", "type": "number", "placeholder": "Enter TDS Amount", "help": "Vendor TDS Rate- 0.00% TDS Amount as per Vendor TDS Rate- 0.00"}, "apply_round_off": {"title": "Apply Round Off", "type": "boolean", "default": true}, "vendor_bill_roundoff": {"title": "Adjustment", "type": "number", "disabled": true, "min": -1, "max": 1}, "apply_rcm": {"title": "Apply RCM", "type": "boolean", "disabled": true}, "vendor_bill_soft_copy_path": {"title": "Scanned Bill", "type": "file"}, "status": {"title": "Status", "type": "select", "enum": [{"title": "Approved", "const": "approved"}, {"title": "Unapproved", "const": "unapproved"}], "default": "unapproved"}, "is_billable": {"title": "Is billable to all members?", "type": "radio", "enum": [{"title": "Yes", "const": 1}, {"title": "No", "const": 0}], "default": 0}, "details": {"title": "Bill <PERSON>", "placeholder": "Enter Bill <PERSON>", "type": "textarea"}}}}}