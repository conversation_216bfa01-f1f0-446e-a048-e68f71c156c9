{"meta": {"title": "Edit Receipt", "data_source": "vendorbill/updatePaymentTracker/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "vendorbill/updatePaymentTracker", "redirect": "/admin/society/vendorbill/expensePaymentTrackerList"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendorbill/expensePaymentTrackerList"}, {"title": "Reset", "type": "reset"}], "rules": [{"conditions": {"all": [{"fact": "roles", "operator": "truthy", "value": true, "params": {"id": "const"}}]}, "event": [{"type": "update", "params": {"role_ids": {"fact": "roles", "path": "$.data[?(@.checked == true)].id"}}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": false}]}, "event": [{"type": "remove", "params": {"field": ["writeoff_amount"]}}]}], "flow": {"title": "Edit Receipt", "children": {"payment_date": {"title": "Payment Date", "type": "date", "maxDateTime": "now"}, "payment_number": {"title": "Payment Number", "type": "string"}, "advance_amount": {"title": "Use Advance Amount", "type": "number", "disabled": true, "endAdornment": [{"title": "Writeoff", "update": {"key": "hidden_input_parent", "value": true}}]}, "payment_amount": {"title": "Payment Amount", "type": "number", "endAdornment": [{"title": "Writeoff", "icon": "ri-add-line", "showButton": true, "update": {"key": "show_writeoff", "value": true}}]}, "writeoff_amount": {"title": "Write Off Amount", "type": "number", "description": "", "endAdornment": [{"title": "Remove Write Off", "icon": "ri-close-line", "update": {"key": "show_writeoff", "value": false}}]}, "show_writeoff": {"type": "radio", "hidden": true, "default": false}, "payment_mode": {"title": "Payment Mode", "type": "select", "disabled": true, "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}]}, "bank_id": {"title": "Bank Account (Complex)", "type": "dropdown", "apiPath": "/admin/accounts/getBankAccounts", "labelKeys": ["ledger_account_name"], "onMount": true, "required": true, "tooltip": {"title": "Your {{label}} bank balance is currency({{ledger_amount}})", "url": "/admin/society/vendorbill/fetchDataAddVendorPayment/:bank_id", "onMount": true}, "dependant": [{"field": "ledger_amount", "key": "ledger_amount"}]}, "transaction_reference": {"title": "Cheque Number", "type": "number", "pattern": "^\\d{6}$", "required": true}, "comments": {"title": "Comments", "type": "textarea"}}}}}