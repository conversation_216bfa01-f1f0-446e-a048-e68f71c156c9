{"meta": {"title": "Payment Reversal", "data_source": "vendorbill/fetchDataPaymentTrackerConfirmationReversal/:id"}, "schema": {"actions": [{"title": "submit", "type": "submit", "api_path": "vendorbill/paymentTrackerConfirmation/4", "method": "put"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendorbill/expensePaymentTrackerList/"}], "rules": [{"conditions": {"any": [{"fact": "bill_to", "operator": "equal", "value": "all_members"}]}, "event": [{"type": "remove", "params": {"field": ["member_name", "unit_number"]}}]}], "flow": {"title": "Payment Reversal", "children": {"payment_number": {"title": "Particulars", "type": "dropdown", "apiPath": "admin/income-tracker-setting/readcommonbilling/activebilling", "required": true, "onMount": true, "labelKeys": ["particular"], "endAdornment": [{"title": "Add Particular", "icon": "mdi:plus", "redirect": "/admin/society/income-tracker-setting/readcommonbilling/activebilling"}]}, "bill_to": {"title": "Bill <PERSON>", "type": "radio", "enum": [{"const": "all_members", "title": "All Members"}, {"const": "some_members", "title": "Some Members"}], "default": "all_members", "required": true}, "unit_number": {"title": "Unit Number", "type": "number", "required": true}, "member_name": {"title": "Member Name", "type": "string"}, "invoice_number": {"title": "Billable Amount", "default": 105, "type": "number", "disabled": true}, "payment_mode": {"title": "Bill To Member", "type": "number", "min": 0, "description": " % ( 105.00 divided equally to members)"}, "payment_amount": {"title": "Each Member Charges", "type": "number", "disabled": true, "startAdornment": "₹", "required": true}, "reversal_comment": {"title": "Billing Period", "type": "dropdown", "apiPath": "admin/income-tracker-setting/readcommonbilling/activebilling", "required": true, "onMount": true}, "note": {"title": "Note", "type": "textarea"}}}}}