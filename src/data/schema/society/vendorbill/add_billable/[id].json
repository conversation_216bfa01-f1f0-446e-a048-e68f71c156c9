{"meta": {"title": "Payment Reversal"}, "schema": {"facts": [{"fact": "billable", "path": "/admin/vendorbill/add_billable/:id", "method": "GET", "dependsOn": "id"}], "actions": [{"title": "submit", "type": "submit", "api_path": "vendorbill/add_billable/:id", "method": "post", "redirect": "/admin/society/vendorbill/billableVendorBills"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendorbill/expensePaymentTrackerList/"}], "rules": [{"conditions": {"any": [{"fact": "billable", "path": "billable_amount", "operator": "isNumber"}]}, "event": [{"type": "update", "params": {"billable_amount": {"fact": "billable", "path": "billable_amount"}}}, {"type": "calculate", "params": {"billable_amount": {"fact": "billable", "path": "billable_amount"}, "payment_mode": {"fact": "payment_mode"}, "__field": "bill", "__exp": "billable_amount * payment_mode / 100"}}, {"type": "tooltip", "params": {"__title": "currency({{bill}}) divided equally to members", "__field": ["payment_mode"], "bill": {"fact": "bill"}}}]}, {"conditions": {"any": [{"fact": "billable_item_for", "operator": "equal", "value": "all"}]}, "event": [{"type": "remove", "params": {"field": ["member_name", "unit_id"]}}, {"type": "calculate", "params": {"total_unit": {"fact": "billable", "path": "total_unit"}, "bill": {"fact": "bill"}, "__field": "member_amount", "__exp": "total_unit ? bill / total_unit : 0"}}]}, {"conditions": {"any": [{"fact": "billable_item_for", "operator": "equal", "value": "some"}]}, "event": [{"type": "calculate", "params": {"selected_units": {"fact": "unit_id"}, "bill": {"fact": "bill"}, "__field": "member_amount", "__exp": "count(selected_units) ? bill / count(selected_units) : 0"}}]}, {"conditions": {"any": [{"fact": "unit_id", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"member_name": {"fact": "unit_id", "path": "$..member_display_name"}}}]}, {"conditions": {"any": [{"fact": "unit_id", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"member_name": ""}}]}], "flow": {"title": "Expense Bill To Member", "children": {"billing_type": {"title": "Particulars", "type": "dropdown", "apiPath": "admin/income-tracker-setting/readcommonbilling/activebilling", "required": true, "onMount": true, "labelKeys": ["particular"], "endAdornment": [{"title": "Add Particular", "icon": "mdi:plus", "redirect": "/admin/society/income-tracker-setting/readcommonbilling/activebilling"}]}, "billable_item_for": {"title": "Bill <PERSON>", "type": "radio", "enum": [{"const": "all", "title": "All Members"}, {"const": "some", "title": "Some Members"}], "default": "all", "required": true}, "unit_id": {"title": "Unit Number", "type": "dropdown", "required": true, "apiPath": "admin/member/unitWiseMembersList", "onMount": true, "multiple": true, "labelKeys": ["building_unit"], "dependent": [{"field": "member_display_name"}]}, "member_name": {"title": "Member Name", "type": "string", "disabled": true}, "billable_amount": {"title": "Billable Amount", "type": "number", "disabled": true, "default": 0}, "bill": {"title": "Bill", "type": "number", "disabled": true, "hidden": true}, "payment_mode": {"title": "Bill To Member", "type": "number", "min": 0, "max": 100, "default": 100, "enableMD": true, "endAdornment": "%"}, "total_unit": {"title": "Total Unit", "type": "number", "disabled": true, "default": 0, "hidden": true}, "member_amount": {"title": "Each Member Charges", "type": "number", "disabled": true, "startAdornment": "₹", "required": true}, "invoice_frequency": {"title": "Billing Period", "type": "dropdown", "apiPath": "admin/accountsetting/financialYearList", "labelKeys": ["name"], "required": true, "onMount": true, "multiple": true}, "note": {"title": "Note", "type": "textarea"}}}}}