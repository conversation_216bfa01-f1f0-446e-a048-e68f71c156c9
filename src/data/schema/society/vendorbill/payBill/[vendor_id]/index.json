{"meta": {"title": "New Payment", "data_source": "vendorbill/payBill/:vendor_id"}, "schema": {"actions": [{"title": "Pay", "type": "submit", "api_path": "vendorbill/newBillPayment", "redirect": "/admin/society/vendorbill/expensePaymentTrackerList"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendor/viewVendor/"}], "facts": [{"fact": "advance_amount", "path": "/admin/vendorbill/fetchDataVendorAdvance/:id", "xpath": "$.id", "method": "GET", "dependsOn": "vendor_id", "returns": "total_advances"}, {"fact": "cash_balance", "path": "/admin/vendorbill/fetchDataAddVendorPayment/cash", "method": "GET", "returns": "ledger_amount"}, {"fact": "bank_balance", "path": "/admin/vendorbill/fetchDataAddVendorPayment/:id", "method": "GET", "dependsOn": "bank_ledger", "xpath": "$.id"}, {"fact": "sequences", "path": "/admin/vendorbill/fetchDataVendorPaymentList/:id", "xpath": "$.id", "method": "GET", "dependsOn": "vendor_id", "returns": "all", "defaultParams": [{"path": "data", "params": {"selected": true}}]}], "rules": [{"conditions": {"any": [{"fact": "vendor_bill_type_purchase", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["bank_ledger", "vendor_bill_payment_cheque_number"]}}]}, {"conditions": {"any": [{"fact": "advance_amount", "operator": "isNumber"}]}, "event": [{"type": "schemaOverride", "params": {"vendor_bill_advance_amount.maximum": {"fact": "advance_amount"}}}, {"type": "tooltip", "params": {"__title": "[[color:{{isSuccess}}]] Your Adjustable Advance Balance is currency({{advance_amount}}) [[color]]", "__field": ["vendor_bill_advance_amount"], "advance_amount": {"fact": "advance_amount"}, "isSuccess": {"fact": "advance_amount", "operator": "truthy"}}}]}, {"conditions": {"any": [{"fact": "vendor_bill_advance_amount", "operator": "greaterThan", "value": {"fact": "advance_amount"}}]}, "event": [{"type": "uiAppend", "params": {"vendor_bill_advance_amount": {"ui:options": {"error": "Advanced amount should be less than Adjustable Advance Balance."}}}}]}, {"conditions": {"any": [{"fact": "vendor_bill_type_purchase", "operator": "equal", "value": "cheque"}]}, "event": [{"type": "uiOverride", "params": {"vendor_bill_payment_cheque_number": {"ui:title": "Cheque Number"}}}]}, {"conditions": {"all": [{"fact": "cash_balance", "operator": "isNumber"}, {"fact": "vendor_bill_type_purchase", "operator": "equal", "value": "cash"}]}, "event": [{"type": "tooltip", "params": {"__title": "[[color:{{isSuccess}}]] Your cash in hand balance is currency({{cash_balance}}) [[color]]", "__field": ["vendor_bill_type_purchase"], "cash_balance": {"fact": "cash_balance"}, "isSuccess": {"fact": "cash_balance"}}}]}, {"conditions": {"all": [{"fact": "bank_balance", "path": "ledger_amount", "operator": "isNumber"}]}, "event": [{"type": "tooltip", "params": {"__title": "[[color:{{isSuccess}}]] Your {{label}} bank balance is currency({{bank_balance}}) [[color]]", "__field": ["bank_ledger"], "bank_balance": {"fact": "bank_balance", "path": "ledger_amount"}, "label": {"fact": "bank_balance", "path": "label"}, "isSuccess": {"fact": "bank_balance", "path": "ledger_amount"}}}]}, {"conditions": {"all": [{"fact": "sequences", "operator": "notEqual", "value": null}]}, "event": [{"type": "uiSchemaReplace", "params": {"sequence.ui:options.tableSchema": {"fact": "sequences", "path": "meta"}}}, {"type": "update", "params": {"sequence": {"fact": "sequences", "path": "data"}}}, {"type": "calculate", "params": {"sequences": {"fact": "sequences", "path": "$.data[*].due_amount"}, "__exp": "sum(sequences)", "__field": "total_due"}}, {"type": "calculate", "params": {"total": {"fact": "sequence", "path": "$..due_amount"}, "writeoff_amount": {"fact": "writeoff_amount"}, "__exp": "sum(total) - writeoff_amount", "__field": "vendor_bill_payment_amount"}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": false}]}, "event": [{"type": "update", "params": {"writeoff_amount": 0}}, {"type": "remove", "params": {"field": ["writeoff_amount"]}}, {"type": "uiOverride", "params": {"vendor_bill_advance_amount": {"ui:disabled": false}}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": true}]}, "event": [{"type": "update", "params": {"vendor_bill_advance_amount": 0}}, {"type": "require", "params": {"field": ["writeoff_amount"]}}, {"type": "schemaOverride", "params": {"writeoff_amount.maximum": {"fact": "total_due"}}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": true}, {"fact": "writeoff_amount", "operator": "greaterThan", "value": {"fact": "total_due"}}]}, "event": [{"type": "uiAppend", "params": {"writeoff_amount": {"ui:options": {"error": "Write off amount should be less than total due amount."}}}}]}], "flow": {"title": "Pay Bill", "children": {"sequence": {"title": "Table", "type": "table", "default": [], "dependent": [{"field": "due_amount"}], "description": "Note :- Bills will be claimed based on the priority"}, "total_due": {"title": "Total Dues", "type": "number", "disabled": true, "required": true, "startAdornment": "₹", "default": 0}, "vendor_bill_payment_date": {"title": "Payment Date", "required": true, "type": "date", "maxDateTime": "now"}, "vendor_bill_receipt_number": {"title": "Payment Number", "type": "string", "placeholder": "Please Enter Payment Number"}, "vendor_bill_advance_amount": {"title": "Use Advance Amount", "placeholder": "Ener the unused advance amount", "type": "number", "startAdornment": "₹", "enableMD": true, "disabled": true}, "vendor_bill_payment_amount": {"title": "Payment Amount", "type": "number", "placeholder": "Please Enter Payment Amount", "startAdornment": "₹", "endAdornment": [{"title": "Writeoff", "icon": "ri-add-line", "showButton": true, "update": {"key": "show_writeoff", "value": true}}]}, "writeoff_amount": {"title": "Write Off Amount", "type": "number", "description": "", "endAdornment": [{"title": "Remove Write Off", "icon": "ri-close-line", "update": {"key": "show_writeoff", "value": false}}]}, "show_writeoff": {"type": "radio", "hidden": true, "default": false}, "vendor_bill_type_purchase": {"title": "Payment Mode", "type": "select", "required": true, "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}], "default": "cash"}, "bank_ledger": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "help": "Select a bank account", "placeholder": "Please Select Bank Account", "onMount": true}, "vendor_bill_payment_cheque_number": {"title": "Reference No", "type": "number", "required": true, "pattern": "[0-9]{6}"}, "vendor_bill_payment_comments": {"title": "Comment", "type": "textarea", "placeholder": "Please Enter Comment"}}}}}