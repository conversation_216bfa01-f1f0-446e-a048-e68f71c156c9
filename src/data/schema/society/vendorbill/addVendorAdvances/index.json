{"meta": {"title": "New Vendor Advance"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "vendorbill/addVendorAdvances", "redirect": "/admin/society/vendorbill/vendorAdvances"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendorbill/vendorAdvances"}], "rules": [{"conditions": {"any": [{"fact": "type", "operator": "equal", "value": "refundable"}]}, "event": [{"type": "remove", "params": {"field": ["adjust_after_date", "adjustable_amount"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account_id", "transaction_reference", "bank_name_branch", "cheque_payment_date", "payment_instrument", "payment_reference"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cashtransfer"}]}, "event": [{"type": "remove", "params": {"field": ["bank_name_branch", "cheque_payment_date", "payment_instrument", "transaction_reference"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cheque"}]}, "event": [{"type": "remove", "params": {"field": ["payment_date", "payment_instrument", "payment_reference"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "online"}]}, "event": [{"type": "remove", "params": {"field": ["cheque_payment_date", "bank_name_branch", "transaction_reference"]}}, {"type": "uiOverride", "params": {"payment_reference": {"ui:title": "Transaction Reference"}}}, {"type": "require", "params": {"field": ["payment_reference"]}}]}, {"conditions": {"any": [{"fact": "type", "operator": "equal", "value": "adjustable"}]}, "event": [{"type": "remove", "params": {"field": ["refundable_amount"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cashtransfer"}]}, "event": [{"type": "uiOverride", "params": {"payment_reference": {"ui:options": {"required": true}}}}]}], "flow": {"children": {"vendor_id": {"title": "Name", "required": true, "type": "dropdown", "placeholder": "Please Search  Name", "apiPath": "/admin/vendor/viewVendorDropdown", "labelKeys": ["vendor_name"], "onMount": true}, "payment_number": {"title": "Payment Number", "type": "string", "placeholder": "Please Enter Payment Number"}, "narration": {"title": "Narration", "type": "textarea", "placeholder": "Please Enter Narration", "required": true}, "type": {"title": "Type", "type": "radio", "required": true, "enum": [{"const": "refundable", "title": "Refundable"}, {"const": "adjustable", "title": "Adjustable"}, {"const": "refundable_adjustable", "title": "Refundable & Adjustable"}], "default": "refundable"}, "adjust_after_date": {"title": "Adjust After Date", "type": "date", "required": true}, "payment_mode": {"title": "Payment Mode", "type": "select", "required": true, "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}, {"const": "online", "title": "Online"}], "default": "cash"}, "refundable_amount": {"title": "Refundable Amount", "type": "number", "min": 0, "required": true}, "adjustable_amount": {"title": "Adjustable Amount", "type": "number", "min": 0, "required": true}, "payment_date": {"title": "Payment Date", "type": "date", "required": true, "maxDateTime": "now"}, "cheque_payment_date": {"title": "Payment Date", "type": "date", "required": true}, "bank_account_id": {"title": "Bank Account", "type": "dropdown", "apiPath": "/admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "onMount": true}, "transaction_reference": {"title": "Transaction Reference (Cheque number)", "required": true, "pattern": "^\\d{6}$"}, "payment_reference": {"title": "Payment Reference", "type": "string", "required": false, "placeholder": "Enter Payment Reference"}, "bank_name_branch": {"title": "Bank Name & Branch", "type": "string", "placeholder": "Enter Bank Name & Branch", "required": true}, "payment_instrument": {"title": "Payment Instrument", "type": "string", "placeholder": "Enter Payment Instrument", "required": true}}}}}