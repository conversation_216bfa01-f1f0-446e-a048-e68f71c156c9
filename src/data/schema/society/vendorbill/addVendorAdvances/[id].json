{"meta": {"title": "New Vendor Advance", "data_source": "vendorbill/addVendorAdvances/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "vendorbill/addVendorAdvances", "redirect": "/admin/society/vendorbill/vendorAdvances", "method": "PUT"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendorbill/vendorAdvances"}], "rules": [{"conditions": {"any": [{"fact": "use_credit_hide", "operator": "equal", "value": "refundable"}]}, "event": [{"type": "uiOverride", "params": {"use_credit": {"ui:enumDisabled": ["refundable_adjustable"]}}}]}, {"conditions": {"any": [{"fact": "use_credit_hide", "operator": "equal", "value": "adjustable"}]}, "event": [{"type": "uiOverride", "params": {"use_credit": {"ui:enumDisabled": ["refundable_adjustable", "refundable"]}, "amount": {"ui:title": "Adjustable Amount"}}}]}, {"conditions": {"any": [{"fact": "use_credit", "operator": "notEqual", "value": "adjustable"}]}, "event": [{"type": "remove", "params": {"field": ["use_credit_after"]}}, {"type": "uiOverride", "params": {"amount": {"ui:title": "Refundable Amount"}}}]}], "flow": {"children": {"account_name": {"title": "Name", "type": "string", "placeholder": "Please Search  Name", "disabled": true, "required": true}, "payment_number": {"title": "Payment Number", "placeholder": "Please Enter Payment Number", "disabled": true}, "narration": {"title": "Narration", "type": "textarea", "placeholder": "Please Enter Narration", "disabled": true, "required": true}, "use_credit": {"title": "Type", "type": "radio", "required": true, "enum": [{"const": "refundable", "title": "Refundable"}, {"const": "adjustable", "title": "Adjustable"}, {"const": "refundable_adjustable", "title": "Refundable & Adjustable"}]}, "use_credit_after": {"title": "Adjust After Date", "type": "date"}, "amount": {"title": "Adjustable Amount", "type": "number", "default": 0.0, "required": true, "disabled": true}, "payment_date": {"title": "Payment Date", "type": "date", "required": true, "disabled": true}, "use_credit_hide": {"title": "helo", "hidden": true}}}}}