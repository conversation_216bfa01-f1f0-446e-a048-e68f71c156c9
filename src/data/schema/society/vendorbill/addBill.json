{"meta": {"title": "New Cash Purchase"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "vendorbill/addBill", "redirect": "/admin/society/vendorbill/vendorBill/"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendorbill/vendorBill/"}], "flow": {"title": "<PERSON>", "children": {"purchase_type": {"title": "Purchase Type", "type": "string", "default": "Cash", "placeholder": "Enter Purchase Type", "disabled": true}, "et_id": {"title": "Expense Account", "type": "dropdown", "placeholder": "Enter Expense Account", "required": true, "apiPath": "/admin/vendorbill/getExpenseLimit", "onMount": true}, "vendor_bill_number": {"title": "<PERSON>", "type": "number", "placeholder": "Enter Bill Number", "required": true, "min": 0}, "vendor_bill_date": {"title": "<PERSON>", "type": "date", "maxDateTime": "now", "minDateTime": "-50 year", "required": true}, "vendor_bill_amount": {"title": "Total Bill Amount", "type": "number", "placeholder": "Enter Total Bill Amount", "min": 0, "required": true}, "upload_scanned_bill": {"title": "Upload Scanned Bill", "type": "file", "placeholder": "Upload Scanned Bill"}, "is_billable_to_all_members": {"title": "Is Billable to All Members?", "type": "radio", "enum": [{"title": "Yes", "const": true}, {"title": "No", "const": false}], "default": false}, "bill_description": {"title": "Bill <PERSON>", "type": "textarea", "placeholder": "Enter Bill <PERSON>"}}}}}