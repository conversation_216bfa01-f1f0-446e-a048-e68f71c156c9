{"meta": {"title": "Add Incidental Bill"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "common-billing/addcommonbill", "redirect": "/admin/society/billable-note/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/billable-note/list"}], "rules": [{"conditions": {"any": [{"fact": "billable", "path": "billable_amount", "operator": "isNumber"}]}, "event": [{"type": "update", "params": {"billable_amount": {"fact": "billable", "path": "billable_amount"}}}, {"type": "calculate", "params": {"billable_amount": {"fact": "billable", "path": "billable_amount"}, "payment_mode": {"fact": "payment_mode"}, "__field": "bill", "__exp": "billable_amount * payment_mode / 100"}}, {"type": "tooltip", "params": {"__title": "currency({{bill}}) divided equally to members", "__field": ["payment_mode"], "bill": {"fact": "bill"}}}]}, {"conditions": {"any": [{"fact": "billable_item_for", "operator": "equal", "value": "all"}]}, "event": [{"type": "remove", "params": {"field": ["member_name", "member_id", "flat_number"]}}, {"type": "calculate", "params": {"total_unit": {"fact": "billable", "path": "total_unit"}, "bill": {"fact": "bill"}, "__field": "member_amount", "__exp": "total_unit ? bill / total_unit : 0"}}]}, {"conditions": {"any": [{"fact": "billable_item_for", "operator": "equal", "value": "some"}]}, "event": [{"type": "calculate", "params": {"selected_units": {"fact": "flat_number"}, "bill": {"fact": "bill"}, "__field": "member_amount", "__exp": "count(selected_units) ? bill / count(selected_units) : 0"}}]}, {"conditions": {"any": [{"fact": "flat_number", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"member_name": {"fact": "flat_number", "path": "$..member_display_name"}, "member_id": {"fact": "flat_number", "path": "$..member_id"}, "unit_id": {"fact": "flat_number", "path": "$..fk_unit_id"}}}]}, {"conditions": {"any": [{"fact": "flat_number", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"member_name": "", "member_id": ""}}]}, {"conditions": {"any": [{"fact": "particular_id", "operator": "isNumber"}]}, "event": [{"type": "update", "params": {"particular_amount": {"fact": "particular_id", "path": "$..amount"}, "invoice_amount": {"fact": "particular_id", "path": "$..amount"}}}]}, {"conditions": {"any": [{"fact": "particular_amount", "operator": "isNumber"}]}, "event": [{"type": "update", "params": {"invoice_amount": {"fact": "particular_amount", "path": "$..amount"}}}]}], "flow": {"children": {"flat_number": {"title": "Unit Number", "type": "dropdown", "required": true, "apiPath": "admin/member/unitWiseMembersList", "onMount": true, "multiple": true, "labelKeys": ["building_unit"], "dependent": [{"field": "member_display_name"}, {"field": "member_id"}, {"field": "fk_unit_id"}]}, "member_name": {"title": "Member Name", "disabled": true, "required": true, "type": "string", "apiPath": "admin/member/list"}, "member_id": {"title": "Member ID", "type": "string", "disabled": true, "hidden": true}, "unit_id": {"title": "Unit ID", "type": "string", "disabled": true, "hidden": true}, "particular_id": {"title": "Particular", "type": "dropdown", "apiPath": "admin/common-billing/getRateByParticularId", "onMount": true, "required": true, "dependent": [{"field": "amount"}]}, "bill_date": {"title": "<PERSON>", "type": "date", "required": true}, "due_date": {"title": "Due Date", "type": "date"}, "from_date": {"title": "Period From", "type": "date", "required": true}, "to_date": {"title": "Period To", "type": "date", "required": true}, "particular_amount": {"title": "Particular Amount", "type": "number", "default": 0, "required": true}, "tax_amount": {"title": "Tax Amount", "type": "number", "default": 0, "disabled": true, "required": true}, "invoice_amount": {"title": "Invoice Amount", "type": "number", "default": 0, "disabled": true, "required": true}}}}}