{"meta": {"title": "Add Incidental Bill", "data_source": "common-billing/addcommonbill/:id?recuring=1"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "particular_amount", "operator": "isNumber"}]}, "event": {"type": "calculate", "params": {"__exp": "particular_amount + invoice_amount", "__field": "invoice_amount"}}}, {"conditions": {"any": [{"fact": "particular_amount", "operator": "isNumber"}]}, "event": {"type": "calculate", "params": {"__exp": "particular_amount + grand_total", "__field": "grand_total"}}}, {"conditions": {"any": [{"fact": "particular_amount", "operator": "isNumber"}]}, "event": {"type": "calculate", "params": {"__exp": "particular_amount + balance_due", "__field": "balance_due"}}}], "actions": [{"title": "Save", "type": "submit", "api_path": "common-billing/addcommonbill", "redirect": "/admin/society/common-billing/listcommonbill"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/common-billing/listcommonbill"}], "flow": {"children": {"flat_number": {"title": "Unit Number Hello", "type": "string", "required": true, "apiPath": "admin/units/list", "multiple": true, "labelKeys": ["unit_flat_number"], "dependant": [{"field": "member_name"}, {"field": "member_id"}]}, "member_name": {"title": "Member Name", "required": true, "disabled": true, "type": "string", "apiPath": "admin/member/list"}, "member_id": {"title": "Member ID", "type": "number", "disabled": true, "hidden": true}, "unit_id": {"title": "Unit ID", "type": "number", "disabled": true, "hidden": true}, "particular_id": {"title": "Particular", "type": "dropdown", "apiPath": "admin/common-billing/getRateByParticularId", "required": true, "onMount": true}, "bill_date": {"title": "<PERSON>", "type": "date", "maxDateTime": "now", "minDateTime": "-3 year", "required": true}, "due_date": {"title": "Due Date", "type": "date"}, "from_date": {"title": "Period From", "type": "date", "required": true}, "to_date": {"title": "Period To", "type": "date", "maxDateTime": "now", "required": true}, "particular_amount": {"title": "Particular Amount", "type": "number", "required": true}, "tax_amount": {"title": "Tax Amount", "type": "number", "disabled": true}, "Delayed Payment Charges": {"title": "Delayed Payment Charges", "type": "number", "disabled": true}, "invoice_amount": {"title": "Invoice Amount", "type": "number", "disabled": true, "required": true}, "advance_amount": {"title": "Advance amount", "type": "number", "disabled": true}, "grand_total": {"title": "Grand Total", "type": "number", "disabled": true}, "principal_arrears": {"title": "Principal Arrears", "type": "number", "disabled": true}, "interest_arrears": {"title": "Interest Arrears", "type": "number", "disabled": true}, "balance_due": {"title": "Balance Due", "type": "number", "disabled": true}}}}}