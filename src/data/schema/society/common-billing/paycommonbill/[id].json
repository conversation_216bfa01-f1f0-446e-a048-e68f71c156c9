{"meta": {"title": "Pay Incidental Bill", "data_source": "/common-billing/paycommonbill/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "common-billing/paycommonbill", "redirect": "/admin/society/ncome-details/incomepaymenttracker"}], "facts": [{"fact": "sequences", "path": "/admin/income-details/getReceiptListLatest/:id", "method": "GET", "dependsOn": "unit_id", "returns": "all"}], "rules": [{"conditions": {"all": [{"fact": "show_tds", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"tds_amount": 0}}, {"type": "remove", "params": {"field": ["tds_amount"]}}]}, {"conditions": {"all": [{"fact": "show_tds", "operator": "truthy"}]}, "event": [{"type": "require", "params": {"field": ["tds_amount"]}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"writeoff_amount": 0}}, {"type": "remove", "params": {"field": ["writeoff_amount"]}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "truthy"}]}, "event": [{"type": "require", "params": {"field": ["writeoff_amount"]}}]}, {"conditions": {"any": [{"fact": "receipt_mode", "operator": "equal", "value": "cashtransfer"}]}, "event": [{"type": "remove", "params": {"field": ["cheque_number", "bank_name", "cheque_date"]}}]}, {"conditions": {"any": [{"fact": "receipt_mode", "operator": "equal", "value": "cash"}, {"fact": "receipt_mode", "operator": "falsy"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "cheque_number", "bank_name", "cheque_date", "transaction_reference"]}}]}, {"conditions": {"any": [{"fact": "receipt_mode", "operator": "equal", "value": "cheque"}]}, "event": [{"type": "remove", "params": {"field": ["transaction_reference"]}}]}, {"conditions": {"any": [{"fact": "sequences", "operator": "equal", "value": "cashtransfer"}]}, "event": [{"type": "remove", "params": {"field": ["cheque_number", "bank_name", "cheque_date"]}}]}, {"conditions": {"all": [{"fact": "sequence", "operator": "notEqual", "value": null}]}, "event": [{"type": "uiSchemaReplace", "params": {"sequence.ui:options.tableSchema": {"fact": "sequences", "path": "meta"}}}, {"type": "update", "params": {"sequence": {"fact": "sequences", "path": "data"}}}]}], "flow": {"children": {"invoice_number": {"title": "Invoice Number", "type": "string", "disabled": true}, "unit_number": {"title": "Unit Number", "disabled": true, "required": true}, "unit_id": {"title": "Unit ID", "type": "number", "disabled": true, "hidden": true}, "member_name": {"title": "Member Name", "disabled": true, "required": true}, "particular": {"title": "Billing Type", "type": "string", "disabled": true, "required": true}, "from_date": {"title": "From Date", "disabled": true}, "to_date": {"title": "To Date", "disabled": true}, "total_due_amount": {"title": "Total Due Amount", "type": "number", "disabled": true, "description": "The total amount due for this bill"}, "received_from": {"title": "Received From", "type": "string", "required": true}, "invoice_amount": {"title": "Invoice Amount", "type": "number", "required": true, "description": "Must be a positive number and cannot exceed the total due amount", "startAdornment": "₹", "endAdornment": [{"title": "TDS", "icon": "ri-add-line", "showButton": true, "update": {"key": "show_writeoff", "value": true}}]}, "tds_amount": {"title": "TDS Amount", "type": "number", "required": true, "endAdornment": [{"title": "Remove TDS", "icon": "ri-close-line", "update": {"key": "show_tds", "value": false}}]}, "writeoff_amount": {"title": "Writeoff Amount", "type": "number", "required": true, "endAdornment": [{"title": "Remove W<PERSON>off", "icon": "ri-close-line", "update": {"key": "show_writeoff", "value": false}}]}, "show_writeoff": {"type": "radio", "hidden": true, "default": false}, "show_tds": {"type": "radio", "hidden": true, "default": false}, "receipt_mode": {"title": "Receipt Mode", "type": "select", "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}], "required": true, "default": "cash"}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "placeholder": "Please Select Bank Account", "onMount": true}, "transaction_reference": {"title": "Payment Reference", "type": "string", "required": true}, "cheque_number": {"title": "Cheque Number", "type": "string", "description": "", "required": true}, "bank_name": {"title": "Bank & Branch Name", "type": "string", "description": "", "required": true}, "cheque_date": {"title": "Cheque Date", "type": "date", "maxDateTime": "now", "required": true}, "receipt_date": {"title": "Receipt Date", "type": "date", "maxDateTime": "now", "required": true}, "receipt_note": {"title": "Receipt Note", "type": "textarea"}, "member_id": {"title": "Member ID", "type": "number", "hidden": true}, "billing_type": {"title": "Billing Type", "type": "string", "hidden": true}, "sequence": {"title": "Last Receipts", "type": "table", "factKey": "sequences", "selectable": false, "default": [], "dependent": [{"field": "due_amount"}]}}}}}