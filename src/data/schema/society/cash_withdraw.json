{"meta": {"formId": "create_group", "title": "App Preferences", "api_path": "/api/schema", "edit": {"allowFor": ["admin"]}, "add": {"allowFor": ["admin"]}, "delete": {"allowFor": ["admin", "complaints"]}}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/api/roles", "redirect": "/roles"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/roles/listSocRoles"}], "rules": [{"conditions": {"all": [{"fact": "amount", "operator": "lessThan", "value": 0}]}, "event": [{"type": "validation", "params": {"field": "amount", "message": "Amount must be a positive number"}}]}, {"conditions": {"all": [{"fact": "transaction_date", "operator": "greaterThan", "value": "{{today}}"}]}, "event": [{"type": "validation", "params": {"field": "transaction_date", "message": "Transaction date cannot be in the future"}}]}, {"conditions": {"all": [{"fact": "amount", "operator": "greaterThan", "value": 10000}]}, "event": [{"type": "validation", "params": {"field": "amount", "message": "Warning: Amount exceeds the recommended limit. Please ensure sufficient balance is available."}}]}], "flow": {"title": "Cash Withdraw", "required": ["transaction_date", "account", "amount", "memo_description"], "children": {"transaction_date": {"title": "Transaction Date", "type": "date", "description": "The date of the transaction (cannot be in the future)", "maxDateTime": "now"}, "account": {"title": "From Account", "type": "select", "enum": ["Axis", "HDFC", "ICICI", "SBI", "Cash in Hand"], "description": "Source account for the withdrawal"}, "amount": {"title": "Amount", "type": "number", "startAdornment": "₹", "description": "Enter a positive amount (ensure sufficient balance is available)", "ui:options": {"inputProps": {"min": 0}}}, "memo_description": {"title": "Memo Description", "type": "textarea", "description": "Enter a description for this transaction"}}}}}