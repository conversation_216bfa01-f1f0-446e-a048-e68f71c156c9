{"meta": {"title": "TD<PERSON>llan"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "tds-challans/add", "redirect": "/admin/society/tds-challans/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/tds-challans/list"}], "rules": [{"conditions": {"any": [{"fact": "payments", "path": "income_tax", "operator": "truthy"}, {"fact": "payments", "path": "surcharges", "operator": "truthy"}, {"fact": "payments", "path": "education_cess", "operator": "truthy"}, {"fact": "payments", "path": "interest", "operator": "truthy"}, {"fact": "payments", "path": "penalty_code", "operator": "truthy"}, {"fact": "payments", "path": "penalty", "operator": "truthy"}, {"fact": "payments", "path": "others", "operator": "truthy"}, {"fact": "payments", "path": "fee_under_sec_234e", "operator": "truthy"}]}, "event": [{"type": "calculate", "params": {"income_tax": {"fact": "payments", "path": "income_tax"}, "surcharges": {"fact": "payments", "path": "surcharges"}, "education_cess": {"fact": "payments", "path": "education_cess"}, "interest": {"fact": "payments", "path": "interest"}, "penalty_code": {"fact": "payments", "path": "penalty_code"}, "penalty": {"fact": "payments", "path": "penalty"}, "others": {"fact": "payments", "path": "others"}, "fee_under_sec_234e": {"fact": "payments", "path": "fee_under_sec_234e"}, "__field": "total", "__exp": "income_tax + surcharges + education_cess + interest + penalty_code + penalty + others + fee_under_sec_234e"}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}, {"fact": "payment_mode", "operator": "falsy"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "transaction_reference"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cheque"}]}, "event": [{"type": "uiOverride", "params": {"transaction_reference": {"ui:title": "Transaction Reference (Cheque number)"}}}]}, {"conditions": {"all": [{"fact": "cash_balance", "operator": "isNumber"}, {"fact": "payment_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "tooltip", "params": {"__title": "[[color:{{isSuccess}}]] Your cash in hand balance is currency({{cash_balance}}) [[color]]", "__field": ["payment_mode"], "cash_balance": {"fact": "cash_balance"}, "isSuccess": {"fact": "cash_balance"}}}]}], "flow": {"children": {"challan_no": {"title": "Challan No./ITNS", "required": true, "placeholder": "Challan No./ITNS"}, "assessment_year": {"title": "Assessment Year", "required": true, "placeholder": "Select Assessment Year", "type": "dropdown", "apiPath": "admin/accountsetting/accountset", "labelKeys": ["year_financial"], "dependent": [{"field": "id", "key": "year_financial"}]}, "tax_applicable": {"title": "Tax Applicable", "type": "radio", "required": true, "enum": [{"const": "(0020)Company Deductees", "title": "(0020)Company Deductees"}, {"const": "(0021)Non-Company Deductees", "title": "(0021)Non-Company Deductees"}], "default": "(0020)Company Deductees"}, "tan_no": {"title": "TAN No", "required": true, "placeholder": "TAN No"}, "type_of_payment": {"title": "Type of Payment", "required": true, "placeholder": "Type of Payment"}, "payments": {"title": "Payments", "layout": "group", "children": {"income_tax": {"title": "Income Tax", "type": "number", "default": 0}, "surcharges": {"title": "Surcharges", "type": "number", "default": 0}, "education_cess": {"title": "Education Cess", "type": "number", "default": 0}, "interest": {"title": "Interest", "type": "number", "default": 0}, "penalty_code": {"title": "Penalty Code", "type": "number", "default": 0}, "penalty": {"title": "Penalty", "type": "number", "default": 0}, "others": {"title": "Others", "type": "number", "default": 0}, "fee_under_sec_234e": {"title": "Fee Under Sec. 234E", "type": "number", "default": 0}, "total": {"title": "Total", "type": "number", "default": 0, "disabled": true}}}, "bsr_code": {"title": "BSR Code", "required": true, "placeholder": "BSR Code"}, "on_account_of": {"title": "On Account of", "placeholder": "On Account of", "required": true}, "challan_serial_no": {"title": "Challan Serial No", "placeholder": "Challan Serial No"}, "payment_mode": {"title": "Payment Mode", "type": "select", "required": true, "placeholder": "Select Payment Mode", "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}, {"const": "online", "title": "Online"}]}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "help": "Select a bank account", "placeholder": "Please Select Bank Account", "onMount": true}, "payment_date": {"title": "Payment Date", "type": "date", "required": true, "placeholder": "mm/dd/yyyy", "maxDateTime": "today", "minDateTime": "today - 50 years"}, "transaction_reference": {"title": "Transaction Reference", "type": "number", "required": true, "pattern": "[0-9]{6}"}, "submitted_bank": {"title": "Bank Name and Branch", "required": true, "placeholder": "Bank Name and Branch"}}}}}