{"meta": {"formId": "app_preferences", "title": "App Preferences", "data_source": "parking-allotments/add"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "parking-allotments/add", "redirect": "/parking-allotments/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/parking-allotments/list"}], "flow": {"title": "Parking Detail", "required": ["allot_parking_to", "Parking unit", "parking_type"], "children": {"allot_parking_to": {"title": "Allot parking to", "type": "dropdown", "apiPath": "admin/units/list", "keys": ["soc_building_name", "-", "unit_flat_number"]}, "parking_unit": {"title": "Parking unit", "type": "dropdown", "apiPath": "admin/parking-allotments/list", "keys": ["soc_building_name", "parking_number"]}, "parking_number": {"title": "Parking Number", "type": "string"}, "parking_type": {"title": "Parking Type", "type": "string"}, "allotment_for": {"title": "Space available for*", "type": "select", "enum": ["2wheeler", "3wheeler", "4wheeler"]}, "effective_date": {"title": "Allotments w.e.f", "type": "date"}, "allowed_number_of_parkings": {"title": "Maximum vehicle(s) allowed", "type": "number"}}}}}