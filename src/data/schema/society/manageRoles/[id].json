{"meta": {"title": "Manage Roles for"}, "schema": {"facts": [{"fact": "roles", "path": "/admin/users/roles/:id", "method": "GET", "dependsOn": "id"}], "rules": [{"conditions": {"all": [{"fact": "roles", "operator": "truthy", "value": true, "params": {"id": "const"}}]}, "event": [{"type": "schemaOverride", "params": {"role_ids.items.enum": {"fact": "roles", "path": "$..id"}, "role_ids.items.enumNames": {"fact": "roles", "path": "$..role_display_name"}, "role_ids.title": " "}}, {"type": "update", "params": {"role_ids": {"fact": "roles", "path": "$.[?(@.checked == true)].id"}}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "users/roles", "redirect": "/admin/society/users/list"}], "flow": {"children": {"role_ids": {"title": "Loading...", "type": "checkbox", "enum": [], "inline": false, "multiple": true, "min": 1}}}}}