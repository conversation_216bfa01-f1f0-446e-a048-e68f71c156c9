{"meta": {"title": "Payment Reversal", "data_source": "vendorbill/fetchDataPaymentTrackerConfirmationReversal/:id"}, "schema": {"actions": [{"title": "submit", "type": "submit", "api_path": "vendorbill/paymentTrackerConfirmation/4", "method": "put"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendorbill/expensePaymentTrackerList/"}], "flow": {"required": ["reversal_comment"], "children": {"sdsd": {"title": " ", "type": "null", "enableMD": true, "description": "## **Are you sure you want to reverse the payment ?**"}, "payment_number": {"title": "Payment Number", "disabled": true}, "voucher_type": {"title": "Voucher Type", "disabled": true}, "invoice_number": {"title": "Voucher Number", "default": "", "type": "string", "disabled": true}, "payment_mode": {"title": "Payment Mode", "disabled": true}, "payment_amount": {"title": "Payment Amount", "type": "number", "disabled": true, "startAdornment": "₹"}, "reversal_comment": {"title": "Reversal Comment", "type": "textarea"}}}}}