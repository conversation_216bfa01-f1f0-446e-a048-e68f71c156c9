{"meta": {"title": "<PERSON> / <PERSON>"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "building_name", "operator": "truthy"}]}, "event": {"Type": "update", "params": {"floor_no": {"fact": "building_name", "path": "$..soc_building_floors"}}}}], "actions": [{"title": "Save", "type": "submit", "api_path": "member/register", "redirect": "/admin/society/member/list"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "member/register"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/member/list"}], "flow": {"children": {"salute": {"title": "Salute", "type": "select", "enum": [{"const": "mr", "title": "Mr."}, {"const": "mrs", "title": "Mrs."}, {"const": "ms", "title": "Ms."}, {"const": "dr", "title": "Dr."}], "placeholder": "Select Salute"}, "first_name": {"title": "First Name", "type": "string", "required": true, "placeholder": "Enter First Name"}, "last_name": {"title": "Last Name", "type": "string", "required": true, "placeholder": "Enter Last Name"}, "email": {"title": "Email Address", "type": "string", "format": "email", "placeholder": "<PERSON><PERSON>"}, "mobile_number": {"title": "Mobile Number (with Country Code)", "type": "string", "placeholder": "Enter Mobile Number (with Country Code)"}, "address": {"title": "Address", "type": "string", "placeholder": "Enter Address"}, "gstin": {"title": "GSTIN", "type": "string", "placeholder": "Enter GSTIN/PAN"}, "pan": {"title": "PAN", "type": "string", "placeholder": "Enter Unique Code"}, "effective_date": {"title": "Effective Date", "type": "date", "required": true}, "date_of_birth": {"title": "Date of Birth", "type": "date"}, "gender": {"title": "Gender", "type": "radio", "enum": [{"const": "male", "title": "Male"}, {"const": "female", "title": "Female"}]}, "member_type": {"title": "Member Type", "type": "select", "placeholder": "Select Member Type", "enum": [{"const": "associate_member", "title": "Associate Member"}, {"const": "primary_member", "title": "Primary Member"}, {"const": "non_member", "title": "Non Member"}, {"const": "tenant", "title": "Tenant"}]}, "building_name": {"title": "Building Name", "type": "dropdown", "required": true, "placeholder": "Select Building Tower", "apiPath": "/admin/building/list", "labelKeys": ["soc_building_name"], "onMount": true, "dependent": [{"field": "soc_building_floors"}]}, "floor_no": {"title": "Floor No", "type": "select", "required": true, "placeholder": "Select Floor", "apiPath": "admin/member/floors", "labelKeys": ["floor_number"]}, "unit": {"title": "Unit", "type": "select", "required": true, "placeholder": "Select Unit", "apiPath": "admin/member/units", "labelKeys": ["unit_number"]}, "unit_category": {"title": "Unit Category", "type": "select", "placeholder": "Select Unit Category", "apiPath": "admin/member/unitCategories", "labelKeys": ["category_name"]}, "is_owner_occupied": {"title": "Is Owner Occupied", "type": "radio", "enum": [{"const": "yes", "title": "Yes"}, {"const": "no", "title": "No"}]}}}}}