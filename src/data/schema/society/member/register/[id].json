{"meta": {"title": "<PERSON> / <PERSON>", "data_source": "/member/register/:id"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "soc_building_id", "path": "id", "operator": "truthy"}]}, "event": {"type": "schemaOverride", "params": {"member_building_floor.enum": {"fact": "soc_building_id", "path": "floor_array"}}}}], "actions": [{"title": "Save", "type": "submit", "api_path": "member/register", "redirect": "/admin/society/member/list"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "member/register"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/member/list"}], "flow": {"children": {"salute": {"title": "Salute", "type": "select", "enum": [{"const": "mr", "title": "Mr."}, {"const": "mrs", "title": "Mrs."}, {"const": "ms", "title": "Ms."}, {"const": "dr", "title": "Dr."}], "placeholder": "Select Salute"}, "member_first_name": {"title": "First Name", "type": "string", "required": true, "placeholder": "Enter First Name"}, "member_last_name": {"title": "Last Name", "type": "string", "required": true, "placeholder": "Enter Last Name"}, "member_email_id": {"title": "Email Address", "type": "string", "format": "email", "placeholder": "<PERSON><PERSON>"}, "member_mobile_number": {"title": "Mobile Number (with Country Code)", "type": "string", "required": true, "placeholder": "Enter Mobile Number (with Country Code)"}, "member_intercom": {"title": "Intercom", "type": "string", "placeholder": "Enter Address"}, "gstin": {"title": "GSTIN", "type": "string", "placeholder": "Enter GSTIN/PAN"}, "ITS_no": {"title": "ITS No", "type": "string", "placeholder": "Enter Unique Code"}, "effective_date": {"title": "Effective Date", "type": "date", "required": true}, "date_of_birth": {"title": "Date of Birth", "type": "date"}, "member_gender": {"title": "Gender", "type": "radio", "enum": [{"const": "M", "title": "Male"}, {"const": "F", "title": "Female"}]}, "member_type_id": {"title": "Member Type", "type": "dropdown", "placeholder": "Select Member Type", "apiPath": "/admin/member/list", "labelKeys": ["soc_building_name"], "onMount": true}, "soc_building_id": {"title": "Building Name", "type": "dropdown", "required": true, "placeholder": "Select Building Tower", "apiPath": "/admin/building/list", "labelKeys": ["soc_building_name"], "onMount": true, "dependent": [{"field": "floor_array"}]}, "member_building_floor": {"title": "Floor No", "type": "select", "required": true, "placeholder": "Select Floor", "apiPath": "/admin/building/list", "labelKeys": ["floor_array"]}, "fk_unit_id": {"title": "Unit", "type": "dropdown", "required": true, "placeholder": "Select Unit", "apiPath": "/admin/building/list", "labelKeys": ["unit_number"]}, "unit_category": {"title": "Unit Category", "type": "string", "placeholder": "Select Unit Category", "required": true, "disabled": true}, "is_occupied": {"title": "Is Owner Occupied", "type": "radio", "enum": [{"const": "1", "title": "Yes"}, {"const": "0", "title": "No"}]}}}}}