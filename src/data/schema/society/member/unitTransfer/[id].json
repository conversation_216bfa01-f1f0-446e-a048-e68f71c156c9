{"meta": {"title": "Transfer Flat - Tower B 0000"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "members", "path": "member_name", "operator": "truthy"}]}, "event": {"type": "uiSchemaReplace", "params": {"office.items.office_member_name.ui:options.loadedOptions": {"fact": "members", "path": "member_name"}}}}], "actions": [{"title": "Save", "type": "submit_and_new", "refetch": true, "api_path": "staffs/settings"}], "flow": {"children": {"Committee": {"title": "Select Option of Transfer", "layout": "group", "children": {"committee_type": {"title": "Transfer Unit To", "type": "radio", "required": true, "enum": [{"title": "New Member", "const": "New Member"}, {"title": "Associate / Nominee / Tenant", "const": "Associate / Nominee / Tenant"}], "default": "New Member"}}}, "members": {"title": "Personal Details ", "layout": "group", "children": {"salute": {"title": "Salute", "type": "select", "enum": [{"const": "mr", "title": "Mr."}, {"const": "mrs", "title": "Mrs."}, {"const": "ms", "title": "Ms."}, {"const": "dr", "title": "Dr."}], "placeholder": "Select Salute"}, "first_name": {"title": "First Name", "type": "string", "required": true, "placeholder": "Enter First Name"}, "last_name": {"title": "Last Name", "type": "string", "required": true, "placeholder": "Enter Last Name"}, "email": {"title": "Email Address", "type": "string", "required": true, "placeholder": "Enter Email Address"}, "mobile": {"title": "Mobile Number", "type": "string", "required": true, "placeholder": "Enter Mobile Number"}, "dtaeob": {"title": "Intercom", "type": "date", "required": true, "placeholder": "Enter Date Of Birth"}, "gstin": {"title": "GSTIN", "type": "string", "required": true, "placeholder": "Enter GSTIN"}, "members": {"title": "Member w.e.f", "type": "date"}, "dob": {"title": "Date Of Birth", "type": "date", "required": true, "placeholder": "Enter Date Of Birth"}, "gender": {"title": "Gender", "type": "select", "enum": [{"const": "male", "title": "Male"}, {"const": "female", "title": "Female"}], "placeholder": "Select Gender"}, "member_type": {"title": "Member Type", "type": "select", "enum": [{"const": "regular", "title": "Regular"}, {"const": "associate", "title": "Associate"}, {"const": "tenant", "title": "Tenant"}], "default": "regular"}}}, "unit_deatils": {"title": "Unit Details", "layout": "group", "children": {"building_name": {"title": "Building Name", "type": "string", "required": true, "placeholder": "Enter Building Name"}, "floor_no": {"title": "Floor No", "type": "string", "required": true, "placeholder": "Enter Floor No"}, "unit": {"title": "Unit", "type": "string", "required": true, "placeholder": "Enter Unit"}, "unit_category": {"title": "Unit Category", "type": "string", "required": true, "placeholder": "Enter Unit Category"}, "Remove previous primary member?": {"title": "Remove previous primary member?", "type": "radio", "enum": [{"title": "Yes", "const": "Yes"}, {"title": "No", "const": "No"}], "default": "No"}}}}}}}