{"meta": {"formId": "new_rule", "title": "New Rule"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/api/rule-based-pricing", "redirect": "/admin/society/rules/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/rules/list"}], "flow": {"children": {"rule_details": {"title": "New Rule", "type": "group", "children": {"income_account": {"title": "Income Account", "type": "dropdown", "placeholder": "Select Income Account", "apiPath": "/admin/income-tracker-setting/readIncomeAccounts", "labelKeys": ["ledger_account_name"], "default": "Maintenance Fee", "required": true, "onMount": true}, "invoice_rule": {"title": "Create Invoicing Rule for", "type": "dropdown", "placeholder": "Select Some Options", "apiPath": "/admin/societies/listUnitType", "labelKeys": ["type"], "required": true, "onMount": true, "multiple": true}, "rate_effective_from": {"title": "Rate Effective From", "type": "date", "placeholder": "dd-mm-yyyy", "required": true}, "deactivation_date": {"title": "Deactivation Date", "type": "date", "placeholder": "dd-mm-yyyy"}, "tax_class": {"title": "Tax Class", "type": "dropdown", "placeholder": "Select Tax Class", "apiPath": "/admin/tax/viewTax", "labelKeys": ["tax_class_name"], "onMount": true}, "late_payment_interest_applicable": {"title": "Late Payment Interest Applicable?", "type": "boolean"}}}, "pricing_rule": {"title": "Rule Settings", "layout": "tabs", "children": {"standard_rule": {"title": "Standard Rule", "children": {"charge_type": {"title": "Charge Type", "type": "radio", "enum": [{"const": "per_sqft_area", "title": "Per Sqft Area"}, {"const": "flat_fee", "title": "Flat Fee"}], "default": "per_sqft_area", "required": true}, "fee_per_sqft_area": {"title": "Fee / Sqft Area / Month", "type": "number", "startAdornment": "₹", "placeholder": "Enter Fee / Sqft Area / Month", "required": true}, "fee_per_sqft_open_area": {"title": "Fee / Sqft Open Area / Month", "type": "number", "startAdornment": "₹", "placeholder": "Enter Fee / Sqft Open Area / Month", "required": true}}}, "floor_based_rule": {"title": "Floor Based Rule", "children": {"floor_fee": {"title": "Fee for Floors", "type": "number", "placeholder": "Enter Fee for Floors", "required": true}, "floor_open_area_fee": {"title": "Open Area Fee for Floors", "type": "number", "placeholder": "Enter Open Area Fee for Floors", "required": true}}}}}}}}}