{"meta": {"title": "New Building"}, "schema": {"actions": [{"title": "Save & New", "type": "submit_and_new", "api_path": "building/add"}, {"title": "Save", "type": "submit", "api_path": "building/add", "redirect": "/admin/society/building/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/building/list"}], "flow": {"title": "Building Details", "required": ["soc_building_name", "soc_building_floors"], "children": {"soc_building_name": {"title": "Building Name", "type": "string"}, "soc_building_floors": {"title": "No of Floors", "type": "number", "min": 0}}}}}