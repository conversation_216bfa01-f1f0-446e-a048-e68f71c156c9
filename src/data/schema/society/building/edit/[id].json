{"meta": {"title": "Edit Building/Block", "data_source": "building/edit/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "building/edit", "redirect": "/admin/society/building/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/building/list"}], "flow": {"title": "Building Details", "required": ["soc_building_name", "soc_building_floors"], "children": {"soc_building_name": {"title": "Building Name", "type": "string"}, "soc_building_floors": {"title": "No of Floors", "type": "number", "min": 0}}}}}