{"meta": {"title": "Add Principal Amount for {{vendor_name}}", "data_source": "vendor/editVendorDetails/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "vendorbill/addOpeningBalanceVendor", "redirect": "/admin/society/vendor/viewVendor"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendor/viewVendor"}], "rules": [{"conditions": {"any": [{"fact": "adjustment_type", "operator": "equal", "value": "advance"}]}, "event": [{"type": "uiOverride", "params": {"outstanding_amt": {"ui:title": "Advance Amount"}}}]}], "flow": {"children": {"adjustment_type": {"title": "Adjustment Type", "type": "radio", "enum": [{"const": "outstanding", "title": "To be paid"}, {"const": "advance", "title": "Advance Paid"}], "default": "outstanding"}, "outstanding_amt": {"title": "Amount to be paid", "type": "number", "min": 0}, "vendor_name": {"title": "", "hidden": true}, "sdsd": {"title": " ", "type": "null", "enableMD": true, "description": "## **Note:- In case of zero To be pay/Advance, cancel this step**"}}}}}