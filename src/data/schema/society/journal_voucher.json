{"meta": {"formId": "Journal Voucher", "title": "Journal Voucher"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "transaction/addJournalEntry"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/transaction/addJournalEntry"}], "rules": [{"conditions": {"all": [{"fact": "amount", "operator": "lessThan", "value": 0}]}, "event": [{"type": "validation", "params": {"field": "amount", "message": "Amount must be a positive number"}}]}, {"conditions": {"all": [{"fact": "payment_date", "operator": "greaterThan", "value": "{{today}}"}]}, "event": [{"type": "validation", "params": {"field": "payment_date", "message": "Journal date cannot be in the future"}}]}], "flow": {"title": "Add Journal Entry Details", "required": ["payment_date", "from_debit_id", "to_credit_id", "amount"], "children": {"payment_date": {"title": "Journal Date", "type": "date", "description": "The date of the journal entry (cannot be in the future)"}, "from_debit_id": {"title": "From/Debit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"], "description": "Select the account to debit"}, "to_credit_id": {"title": "To/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name", "id"], "description": "Select the account to credit"}, "amount": {"title": "Amount", "type": "number", "startAdornment": "₹", "description": "Enter a positive amount", "ui:options": {"inputProps": {"min": 0}}}, "memo_description": {"title": "Narration", "type": "textarea", "description": "Enter a description for this journal entry"}}}}}