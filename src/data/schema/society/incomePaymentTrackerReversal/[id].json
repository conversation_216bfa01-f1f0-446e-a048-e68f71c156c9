{"meta": {"title": "Payment Reversal", "data_source": "income-details/fetchDataIncomePaymentTrackerConfirmationReversal/:id"}, "schema": {"actions": [{"title": "submit", "type": "submit", "api_path": "income-details/incomePaymentTrackerConfirmation/4", "redirect": "/admin/society/income-details/incomepaymenttracker"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/income-details/incomepaymenttracker"}], "rules": [], "flow": {"required": ["reversal_note"], "children": {"sdsd": {"title": " ", "type": "null", "enableMD": true, "description": "## **Are you sure you want to reverse the payment ?**"}, "receipt_number": {"title": "Receipt Number", "disabled": true}, "invoice_number": {"title": "Invoice Number", "disabled": true}, "payment_mode": {"title": "Payment Mode", "disabled": true}, "payment_amount": {"title": "Payment Amount", "type": "number", "disabled": true, "startAdornment": "₹"}, "reversal_note": {"title": "Reversal Comment", "type": "textarea"}}}}}