{"meta": {"title": "Add Outstanding for {{unit_label}}", "data_source": "/units/edit/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/income-details/addoutstanding", "redirect": "/admin/society/income-details/incomemember"}], "rules": [{"conditions": {"any": [{"fact": "adjustment_type", "operator": "notEqual", "value": "advance"}]}, "event": [{"type": "remove", "params": {"field": ["advance_amount"]}}]}, {"conditions": {"any": [{"fact": "adjustment_type", "operator": "equal", "value": "advance"}]}, "event": [{"type": "remove", "params": {"field": ["outstanding_amt", "interest_amt", "delayed_payment_charges"]}}]}], "flow": {"children": {"adjustment_type": {"title": "Adjustment Type", "type": "radio", "enum": [{"const": "outstanding", "title": "Outstanding"}, {"const": "advance", "title": "Advance"}], "default": "outstanding"}, "outstanding_amt": {"title": "Principal Amount", "type": "number", "placeholder": "Enter Principal Amount"}, "interest_amt": {"title": "Interest Amount", "type": "number", "placeholder": "Enter Grace Period", "min": 0, "required": true}, "delayed_payment_charges": {"title": "Delayed Payment Charges", "type": "number", "placeholder": "Enter Delayed Payment Charges", "min": 0, "required": true}, "advance_amount": {"title": "Advance Amount", "type": "number", "placeholder": "Enter Advance Amount", "min": 0}, "unit_label": {"title": "<PERSON> Period", "type": "string", "placeholder": "Enter Grace Period", "disabled": true, "hidden": true}, "unit_id": {"title": "<PERSON> Period", "type": "number", "placeholder": "Enter Grace Period", "disabled": true, "hidden": true}}}}}