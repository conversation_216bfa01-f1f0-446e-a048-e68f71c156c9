{"meta": {"title": "Journal Voucher"}, "schema": {"actions": [{"title": "Save & New", "type": "submit", "api_path": "transaction/addJournalEntry", "redirect": "/admin/society/transaction/addJournalEntry"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/transaction/addJournalEntry"}], "rules": [{"conditions": {"any": [{"fact": "transaction_amount", "operator": "isNumber"}]}, "event": [{"type": "update", "params": {"transaction_amount": {"fact": "transaction_amount"}}}]}], "flow": {"children": {"transaction_date": {"title": "Journal Date", "type": "date", "maxDateTime": "now", "minDateTime": "50 year", "required": true}, "from_ledger": {"title": "From/Debit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"], "required": true}, "to_ledger": {"title": "To/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name", "id"], "required": true}, "transaction_amount": {"title": "Amount", "type": "number", "required": true}, "payment_reference": {"title": "Reference Number", "type": "string", "required": true}, "memo_desc": {"title": "Narration", "type": "textarea", "required": true}}}}}