{"meta": {"title": "Debit Note"}, "schema": {"actions": [{"title": "Save & New", "type": "submit", "api_path": "transaction/addDebitEntry", "redirect": "/"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/"}], "flow": {"title": "Debit Note Details", "children": {"type": {"title": "Type", "type": "radio", "enum": [{"const": "income", "title": "Income"}, {"const": "expense", "title": "Expense"}, {"const": "adjustment", "title": "Adjustment"}], "required": true, "default": "income"}, "from_ledger_debit_id": {"title": "From ledger/Debit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"], "required": true}, "to_ledger_credit_id": {"title": "To ledger/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"], "required": true}, "to_ledger_credit_id1": {"title": "To ledger/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"], "required": false}, "amount": {"title": "Amount", "type": "number", "required": true}, "receipt_date": {"title": "Receipt Date", "type": "date", "required": true, "maxDateTime": "now", "minDateTime": "50 year"}, "reference_number": {"title": "Reference Number", "type": "string", "required": true}, "memo_description": {"title": "Memo Description", "type": "textarea", "required": true}}}}}