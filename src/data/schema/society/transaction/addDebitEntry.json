{"meta": {"title": "Debit Note Details"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "transaction/addDebitEntry", "redirect": "admin/transaction/addDebitEntry"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/"}], "rules": [{"conditions": {"any": [{"fact": "salesType", "operator": "equal", "value": "adjustment"}]}, "event": [{"type": "remove", "params": {"field": ["to_ledger1"]}}]}], "flow": {"children": {"salestype": {"title": "Type", "type": "radio", "enum": [{"const": "income", "title": "Income"}, {"const": "expense", "title": "Expense"}, {"const": "adjustment", "title": "Adjustment"}], "required": true, "default": "income"}, "from_ledger": {"title": "From Ledger/Debit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"], "required": true}, "to_ledger": {"title": "To Ledger/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"], "required": true}, "to_ledger1": {"title": "To Ledger/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"]}, "transaction_amount": {"title": "Amount", "type": "number", "required": true}, "transaction_date": {"title": "Receipt Date", "type": "date", "required": true, "maxDateTime": "now"}, "payment_reference": {"title": "Reference Number", "type": "number", "required": true}, "memo_desc": {"title": "Memo Description", "type": "textarea", "required": true}}}}}