{"meta": {"title": "Multiple Payment Voucher"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "transaction/addMultiplePaymentVoucher", "redirect": "/admin/society/transaction/addMultiplePaymentVoucher"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/transaction/addMultiplePaymentVoucher"}], "flow": {"children": {"params": {"title": "Multiple Payment Voucher", "type": "array", "default": [{"type": "", "soc_unit_type": "", "use_category": "residential", "chargeable": false}], "children": {"type": {"title": "Payemnt Date", "type": "date", "required": true, "maxDateTime": "now"}, "from_ledger": {"title": "From/Debit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"], "required": true}, "to_ledger": {"title": "To/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name", "id"], "required": true}, "transaction_amount": {"title": "Amount", "type": "number", "required": true}, "payment_reference": {"title": "Reference Number", "type": "string", "required": true}, "memo_desc": {"title": "Narration", "type": "textarea", "required": true}}}}}}}