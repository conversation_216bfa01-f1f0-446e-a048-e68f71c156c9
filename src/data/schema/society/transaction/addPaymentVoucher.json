{"meta": {"title": "Add Payment Voucher"}, "schema": {"actions": [{"title": "Save & Add New", "type": "submit_and_new", "api_path": "transaction/addPaymentVoucher", "redirect": "admin/transaction/addPaymentVoucher"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "transaction/addPaymentVoucher"}], "flow": {"children": {"transaction_date": {"title": "Payment Date", "type": "date", "maxDateTime": "now", "minDateTime": "50 year", "required": true}, "from_ledger": {"title": "From/Debit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["liability"], "required": true}, "to_ledger": {"title": "To/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": [], "required": true}, "transaction_amount": {"title": "Amount", "type": "number", "required": true}, "payment_reference": {"title": "Payment reference*", "type": "number", "required": true}, "memo_desc": {"title": "Memo Description", "type": "textarea", "required": true}}}}}