{"meta": {"title": "New Vendors"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "vendor/addVendor", "redirect": "/admin/society/vendor/viewVendor"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendor/addVendor"}], "rules": [{"conditions": {"any": [{"fact": "isgstapplicable", "operator": "equal", "value": 1}]}, "event": {"type": "remove", "params": {"field": ["vendor_rcm"]}}}, {"conditions": {"any": [{"fact": "isgstapplicable", "operator": "equal", "value": 0}]}, "event": {"type": "remove", "params": {"field": ["vendor_service_regn"]}}}], "flow": {"children": {"vendor_name": {"title": "Vendor Name", "placeholder": "<PERSON>ter Vendor Name", "type": "string", "enableMD": true, "required": true}, "vendor_contact_number": {"title": "Vendor Mobile No", "placeholder": "Enter Vendor Mobile No", "type": "number", "min": 0, "required": true}, "vendor_phone_number": {"title": "Vendor Phone", "placeholder": "<PERSON>ter Vendor Phone", "type": "number", "min": 0}, "vendor_email": {"title": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON>", "type": "string"}, "vendor_notes": {"title": "Notes", "placeholder": "Enter Notes", "type": "textarea"}, "vendor_is_company": {"title": "Company/Non-Company/Individual", "type": "radio", "enum": [{"const": 1, "title": "Company"}, {"const": 2, "title": "Non-Company"}, {"const": 0, "title": "Individual"}], "default": 1}, "vendor_credit_period": {"title": "Credit Period", "placeholder": "Enter Credit Period", "type": "number", "endAdornment": "Days", "min": 0}, "isgstapplicable": {"title": "Is GST applicable", "type": "radio", "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "NO"}], "default": 1}, "vendor_service_regn": {"title": "GST No", "type": "string", "placeholder": "Enter GST No", "pattern": "^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}Z[0-9A-Z]{1}$", "required": true}, "vendor_rcm": {"title": "RCM No", "type": "string", "required": true, "placeholder": "Enter GST No"}, "vendor_pan_num": {"title": "Pan No", "type": "string", "placeholder": "Enter Pan No", "pattern": "^[A-Z]{5}[0-9]{4}[A-Z]{1}$"}, "tds": {"title": "TDS", "type": "dropdown", "enableMD": true, "description": "`TDS Rate` is auto calculated based on `Section?`", "apiPath": "/admin/expensetracker/expensetdsrate", "labelKeys": ["section"], "onMount": true}, "place_of_supply": {"title": "Place of Supply", "type": "string", "default": "27-Maharashtra", "disabled": true}, "vendor_payee_name": {"title": "Payee Name", "placeholder": "Enter Payee Name", "required": true}, "vendor_address": {"title": "<PERSON><PERSON>or Address", "placeholder": "<PERSON><PERSON>or Address", "type": "string", "required": true}}}}}