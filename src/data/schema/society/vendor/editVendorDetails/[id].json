{"meta": {"title": "<PERSON>", "data_source": "vendor/editVendorDetails/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "vendor/editVendorDetails", "redirect": "/admin/society/vendor/viewVendor/"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "vendor/editVendorDetails", "redirect": "/admin/society/vendor/addVendor/"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendor/viewVendor/"}], "rules": [{"conditions": {"any": [{"fact": "is_gst_applicable", "operator": "equal", "value": 1}]}, "event": [{"type": "remove", "params": {"field": ["vendor_rcm"]}}]}, {"conditions": {"any": [{"fact": "is_gst_applicable", "operator": "equal", "value": 0}]}, "event": {"type": "remove", "params": {"field": ["vendor_service_regn"]}}}, {"conditions": {"any": [{"fact": "isgstapplicable", "operator": "equal", "value": 1}]}, "event": [{"type": "update", "params": {"is_gst_applicable": 1}}, {"type": "remove", "params": {"field": ["is_gst_applicable", "vendor_rcm"]}}, {"type": "uiOverride", "params": {"vendor_service_regn": {"ui:disabled": true}}}]}], "flow": {"title": "<PERSON> <PERSON><PERSON><PERSON>", "children": {"vendor_name": {"title": "Vendor Name", "type": "string", "required": true}, "vendor_contact_number": {"title": "Vendor Mobile No", "type": "string", "required": true}, "vendor_phone_number": {"title": "Vendor Phone", "type": "string"}, "vendor_email": {"title": "<PERSON><PERSON><PERSON>", "type": "email"}, "vendor_notes": {"title": "Notes", "type": "textarea"}, "status": {"title": "Is Vendor Active ?", "type": "boolean"}, "vendor_is_company": {"title": "Is Company", "type": "radio", "enum": [{"const": 1, "title": "Company"}, {"const": 2, "title": "Non-Company"}, {"const": 0, "title": "Individual"}]}, "vendor_credit_period": {"title": "Credit Period", "type": "number", "endAdornment": "Days"}, "isgstapplicable": {"title": "Is GST applicable", "type": "number", "hidden": true, "default": 0}, "is_gst_applicable": {"title": "Is GST applicable", "type": "radio", "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "NO"}], "default": 0}, "vendor_service_regn": {"title": "GST No", "type": "string", "placeholder": "Enter GST No", "pattern": "^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}Z[0-9A-Z]{1}$", "required": true}, "vendor_rcm": {"title": "RCM No", "type": "number", "required": true, "placeholder": "Enter RCM No", "min": 0}, "vendor_pan_num": {"title": "PAN No", "type": "string"}, "tds": {"title": "TDS", "type": "dropdown", "apiPath": "/admin/expensetracker/expensetdsrate", "placeholder": "Select TDS", "labelKeys": ["section"], "onMount": true}, "place_of_supply": {"title": "Place Of Supply", "type": "string", "default": "27-Maharashtra", "disabled": true}, "vendor_payee_name": {"title": "Payee Name", "type": "string", "required": true}, "vendor_address": {"title": "<PERSON><PERSON>or Address", "type": "textarea", "required": true}}}}}