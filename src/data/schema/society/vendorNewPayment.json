{"meta": {"title": "New Payment"}, "schema": {"actions": [{"title": "Pay", "type": "submit", "api_path": "vendorbill/newBillPayment", "redirect": "/admin/society/vendorbill/expensePaymentTrackerList"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendor/viewVendor/"}], "facts": [{"fact": "advance_amount", "path": "/admin/vendorbill/fetchDataVendorAdvance/:id", "xpath": "$.id", "method": "GET", "dependsOn": "vendor_id", "returns": "total_advances"}, {"fact": "cash_balance", "path": "/admin/vendorbill/fetchDataAddVendorPayment/cash", "method": "GET", "returns": "ledger_amount"}, {"fact": "bank_balance", "path": "/admin/vendorbill/fetchDataAddVendorPayment/:id", "method": "GET", "dependsOn": "bank_ledger", "xpath": "$.id"}, {"fact": "sequences", "path": "/admin/vendorbill/fetchDataVendorPaymentList/:id", "xpath": "$.id", "method": "GET", "dependsOn": "vendor_id", "returns": "all", "defaultParams": [{"path": "data", "params": {"selected": true}}]}], "rules": [{"conditions": {"any": [{"fact": "vendor_id", "path": "id", "operator": "falsy"}]}, "event": [{"type": "remove", "params": {"field": ["sequence"]}}, {"type": "update", "params": {"total_due": 0}}]}, {"conditions": {"any": [{"fact": "vendor_id", "path": "id", "operator": "isNumber"}]}, "event": [{"type": "update", "params": {"total_due": {"fact": "vendor_id", "path": "total_dues"}}}]}, {"conditions": {"any": [{"fact": "advance_amount", "operator": "isNumber"}]}, "event": [{"type": "tooltip", "params": {"__title": "[[color:{{isSuccess}}]] Your Adjustable Advance Balance is currency({{advance_amount}})[[color]]", "__field": ["vendor_bill_advance_amount"], "advance_amount": {"fact": "advance_amount"}, "isSuccess": {"fact": "advance_amount"}}}]}, {"conditions": {"any": [{"fact": "vendor_bill_type_purchase", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["bank_ledger", "vendor_bill_payment_cheque_number"]}}]}, {"conditions": {"any": [{"fact": "vendor_bill_type_purchase", "operator": "equal", "value": "cheque"}]}, "event": [{"type": "uiOverride", "params": {"vendor_bill_payment_cheque_number": {"ui:title": "Cheque Number"}}}]}, {"conditions": {"all": [{"fact": "cash_balance", "operator": "isNumber"}, {"fact": "vendor_bill_type_purchase", "operator": "equal", "value": "cash"}]}, "event": [{"type": "tooltip", "params": {"__title": "[[color:{{isSuccess}}]]Your cash in hand balance is currency({{cash_balance}})[[color]]", "__field": ["vendor_bill_type_purchase"], "cash_balance": {"fact": "cash_balance"}, "isSuccess": {"fact": "cash_balance"}}}]}, {"conditions": {"all": [{"fact": "bank_balance", "path": "ledger_amount", "operator": "isNumber"}]}, "event": [{"type": "tooltip", "params": {"__title": "[[color:{{isSuccess}}]]Your {{label}} bank balance is currency({{bank_balance}})[[color]]", "__field": ["bank_ledger"], "bank_balance": {"fact": "bank_balance", "path": "ledger_amount"}, "label": {"fact": "bank_balance", "path": "label"}, "isSuccess": {"fact": "bank_balance", "path": "ledger_amount"}}}]}, {"conditions": {"all": [{"fact": "sequence", "operator": "notEqual", "value": null}]}, "event": [{"type": "uiSchemaReplace", "params": {"sequence.ui:options.tableSchema": {"fact": "sequences", "path": "meta"}}}, {"type": "update", "params": {"sequence": {"fact": "sequences", "path": "data"}}}, {"type": "calculate", "params": {"total": {"fact": "sequence", "path": "$..due_amount"}, "__exp": "sum(total)", "__field": "vendor_bill_payment_amount"}}]}, {"conditions": {"all": [{"fact": "total_due", "operator": "falsy"}]}, "event": [{"type": "tooltip", "params": {"__title": "No Pending Dues", "__field": ["total_due"]}}]}], "flow": {"order": ["*", "vendor_bill_payment_date", "vendor_bill_payment_comments", "sequence"], "children": {"vendor_id": {"title": "Vendor Name", "type": "dropdown", "required": true, "placeholder": "Please Search Vendor Name", "apiPath": "/admin/vendor/viewVendorDropdown", "labelKeys": ["vendor_name"], "dependent": [{"field": "total_dues"}], "onMount": true}, "total_due": {"title": "Total Dues", "type": "number", "disabled": true, "startAdornment": "₹", "default": 0}, "vendor_bill_receipt_number": {"title": "Payment Number", "type": "string", "placeholder": "Please Enter Payment Number"}, "vendor_bill_advance_amount": {"title": "Use Advance Amount", "placeholder": "Ener the unused advance amount", "type": "number", "startAdornment": "₹", "help": "Select any vendor to show advance amount", "enableMD": true}, "vendor_bill_payment_amount": {"title": "Amount", "type": "number", "placeholder": "Please Enter Payment Amount", "startAdornment": "₹"}, "vendor_bill_payment_date": {"title": "Payment Date", "required": true, "type": "date", "maxDateTime": "now"}, "vendor_bill_type_purchase": {"title": "Payment Mode", "type": "select", "required": true, "enableMD": true, "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}], "default": "cash"}, "bank_ledger": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "help": "Select a bank account", "placeholder": "Please Select Bank Account", "onMount": true, "enableMD": true}, "vendor_bill_payment_cheque_number": {"title": "Reference No", "type": "number", "pattern": "^\\d{6}$", "required": true}, "vendor_bill_payment_comments": {"title": "Comment", "type": "textarea", "placeholder": "Please Enter Comment"}, "sequence": {"title": "Table", "type": "table", "default": [], "dependent": [{"field": "due_amount"}], "description": "Note :- Bills will be claimed based on the priority"}}}}}