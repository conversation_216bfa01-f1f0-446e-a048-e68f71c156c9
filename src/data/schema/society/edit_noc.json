{"meta": {"formId": "app_preferences", "title": "App Preferences", "data_source": "noc/add_noc"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "noc/add_noc", "redirect": "/admin/society/noc/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/noc/list"}], "flow": {"title": "NOC", "required": ["attached_header", "unit"], "children": {"attached_header": {"title": "Attach header", "type": "radio", "enum": ["Yes", "No"]}, "unit": {"title": "Unit", "default": "<PERSON><PERSON><PERSON>(TowerC-703)", "disabled": true}, "status": {"title": "Status", "type": "select", "enum": ["Unapproved", "Approved", "Rejected"]}, "purpose": {"title": "Templates", "type": "select", "disabled": "true", "enum": ["Select Template", "NOC Passport", "NOC Bank loan", "NOC Domicile", "Noc Property Sale", "NOC Electricity Meter Change", "NOC Leave License"]}, "preview": {"title": "Body", "format": "textarea"}}}}}