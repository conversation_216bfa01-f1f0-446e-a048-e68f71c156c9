{"meta": {"title": "Expense GST Rates", "data_source": "expensetracker/expensetaxsetup"}, "schema": {"facts": [{"fact": "expense", "path": "/admin/expensetracker/expensetaxsetup", "method": "GET"}], "rules": [{"conditions": {"all": [{"fact": "expense", "operator": "falsy", "value": true}]}, "event": [{"type": "remove", "params": {"fields": "expense_gst_rates"}}]}, {"conditions": {"all": [{"fact": "expense", "operator": "truthy", "value": true}]}, "event": [{"type": "update", "params": {"expense_gst_rates": {"fact": "expense", "path": "data"}}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "expensetracker/expensetdsrate/", "redirect": "/admin/society/expensetracker/expensetdsrate/"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/expensetracker/expensetdsrate/"}, {"title": "Reset", "type": "reset"}], "flow": {"title": "Expense GST Rates", "children": {"expense_gst_rates": {"title": "Rate", "type": "array", "children": {"type": {"title": "Rule Type", "type": "select", "enum": [{"const": "gst", "title": "GST"}, {"const": "igst", "title": "IGST"}]}, "name": {"title": "Rule", "type": "select", "oneOf": [{"const": "cgst", "title": "CGST"}, {"const": "sgst", "title": "SGST"}, {"const": "igst", "title": "IGST"}]}, "rate": {"title": "Rate", "type": "number"}}}}}}}