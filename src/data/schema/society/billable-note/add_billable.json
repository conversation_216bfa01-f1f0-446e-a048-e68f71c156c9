{"meta": {"title": "Billable Item Form"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "billable-note/add_billable", "redirect": "/admin/society/billable-note/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/billable-note/list"}], "rules": [{"conditions": {"any": [{"fact": "billable_item_for", "operator": "equal", "value": "all"}]}, "event": [{"type": "remove", "params": {"field": ["unit_id", "member_name"]}}]}, {"conditions": {"any": [{"fact": "unit_id", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"member_name": {"fact": "unit_id", "path": "$..member_display_name"}, "member_id": {"fact": "unit_id", "path": "$..member_id"}}}]}], "flow": {"children": {"billable_item_for": {"title": "Billable Item For", "type": "radio", "enum": [{"const": "all", "title": "All Members"}, {"const": "some", "title": "Some Members"}], "default": "some", "required": true}, "unit_id": {"title": "Unit Number", "type": "dropdown", "placeholder": "Enter Unit Number", "required": true, "apiPath": "admin/member/unitWiseMembersList", "onMount": true, "multiple": true, "labelKeys": ["building_unit"], "dependent": [{"field": "member_display_name"}, {"field": "member_id"}]}, "member_name": {"title": "Member Name", "type": "string", "placeholder": "Enter Member Name", "disabled": true}, "member_id": {"title": "Member ID", "type": "array", "dataType": "number", "placeholder": "Enter Member ID", "disabled": true, "hidden": true}, "debit_note_type": {"title": "Type", "type": "string", "default": "Debit Note", "disabled": true}, "income_account_id": {"title": "Particular/Income Accounts", "type": "dropdown", "apiPath": "admin/common-billing/getRateByParticularId", "placeholder": "Choose...", "required": true, "onMount": true}, "amount": {"title": "Monthly Amount", "type": "number", "startAdornment": "₹", "default": 0.0, "required": true}, "billing_period": {"title": "Billing Period", "type": "dropdown", "placeholder": "Select Billable Period", "apiPath": "/admin/billable-note/get_billing_period", "labelKeys": ["range"], "required": true, "onMount": true, "multiple": true}, "late_payment_interest": {"title": "Late Payment Interest Applicable?", "type": "boolean", "enum": [{"const": true, "title": "Yes"}, {"const": false, "title": "No"}], "default": false}, "note": {"title": "Note", "type": "textarea", "placeholder": "Enter Note"}}}}}