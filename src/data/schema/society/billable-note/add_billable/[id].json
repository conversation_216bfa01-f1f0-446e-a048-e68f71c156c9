{"meta": {"title": "Edit Billable Item", "data_source": "billable-note/add_billable/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/billable-note/add_billable/", "redirect": "/admin/society/billable-note/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/billable-note/list"}], "rules": [{"conditions": {"any": [{"fact": "late_payment_interest", "operator": "equal", "value": false}]}, "event": [{"type": "remove", "params": {"field": ["late_payment_interest_rate"]}}]}], "flow": {"title": "Billable Item", "children": {"unit_name": {"title": "Unit Number*", "type": "string", "disabled": true, "required": true}, "member_name": {"title": "Member Name", "type": "string", "disabled": true}, "type": {"title": "Type", "type": "string", "default": "Debit Note", "disabled": true}, "income_account_id": {"title": "Particular/Income Accounts*", "type": "dropdown", "placeholder": "Choose...", "apiPath": "admin/common-billing/getRateByParticularId", "onMount": true, "required": true}, "amount": {"title": "Monthly Amount", "type": "number", "startAdornment": "₹"}, "billing_period": {"title": "Billing Period", "type": "string", "disabled": true}, "late_payment_interest": {"title": "Late Payment Interest Applicable?", "type": "boolean", "enum": [{"const": true, "title": "Yes"}, {"const": false, "title": "No"}], "default": false}, "late_payment_interest_rate": {"title": "Late Payment Interest Applicable Froms", "type": "date"}, "note": {"title": "Note", "type": "textarea", "placeholder": "Notes..."}}}}}