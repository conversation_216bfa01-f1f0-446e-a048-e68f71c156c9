{"meta": {"title": "Add Tax"}, "schema": {"actions": [{"title": "Submit", "type": "submit", "api_path": "/accounts/addTax", "redirect": "/admin/society/tax/viewTax"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/tax/viewTax"}], "flow": {"children": {"tax_class_name": {"title": "Class", "required": true, "placeholder": "Enter Class Name"}, "tax_categories_description": {"title": "Description", "required": true, "type": "textarea", "placeholder": "Enter Class Description"}, "tax_categories_footer": {"title": "Tax Footer (invoice footer)", "type": "textarea", "placeholder": "Enter Class Footer", "required": true}, "taxclassruledetails": {"title": "Tax Rule", "type": "array", "hideLabel": true, "children": {"tax_categories_name": {"title": "Name", "placeholder": "Enter Rule Name", "required": true}, "tax_categories_amount": {"title": "Rate", "type": "number", "default": 0, "placeholder": "Enter Amount", "startAdornment": "₹", "required": true}, "tax_categories_type": {"title": "Type", "type": "select", "enum": [{"const": "percentage", "title": "Percentage"}, {"const": "fixed", "title": "Fixed"}], "default": "percentage"}, "tax_categories_code": {"title": "Tax Code", "placeholder": "Enter Rule Code"}, "tax_categories_from_date": {"title": "Effective Date From", "type": "daterange", "required": true}}}}}}}