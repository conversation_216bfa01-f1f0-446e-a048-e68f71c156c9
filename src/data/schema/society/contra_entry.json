{"meta": {"formId": "app_preferences", "title": "<PERSON><PERSON> Voucher"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "transaction/addContraEntry"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "transaction/addContraEntry"}], "rules": [{"conditions": {"all": [{"fact": "amount", "operator": "lessThan", "value": 0}]}, "event": [{"type": "validation", "params": {"field": "amount", "message": "Amount must be a positive number"}}]}, {"conditions": {"all": [{"fact": "payment_date", "operator": "greaterThan", "value": "{{today}}"}]}, "event": [{"type": "validation", "params": {"field": "payment_date", "message": "Transaction date cannot be in the future"}}]}], "flow": {"title": "Contra Details", "required": ["payment_date", "from_debit_id", "to_credit_id", "amount"], "children": {"payment_date": {"title": "Transaction Date", "type": "date", "description": "The date of the transaction (cannot be in the future)"}, "from_debit_id": {"title": "From/Debit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"], "description": "Select the account to debit"}, "to_credit_id": {"title": "Receipt Mode", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "keys": ["ledger_account_name"], "description": "Select the account to credit"}, "amount": {"title": "Amount", "type": "number", "startAdornment": "₹", "description": "Enter a positive amount", "ui:options": {"inputProps": {"min": 0}}}, "reference_number": {"title": "Receipt Reference", "type": "string", "description": "Enter a reference number for this transaction"}, "memo_description": {"title": "Memo Description", "type": "textarea", "description": "Enter a description for this transaction"}}}}}