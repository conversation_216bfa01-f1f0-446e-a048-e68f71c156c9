{"meta": {"title": "Asset Categories"}, "schema": {"facts": [{"fact": "sequences", "path": "/admin/assets/settings", "method": "GET", "returns": "all", "onMount": true}], "rules": [{"conditions": {"all": [{"fact": "sequences", "operator": "truthy"}]}, "event": [{"type": "uiSchemaReplace", "params": {"sequence.ui:options.tableSchema": {"fact": "sequences", "path": "meta"}}}, {"type": "update", "params": {"sequence": {"fact": "sequences", "path": "data"}}}]}], "actions": [{"title": "Save", "type": "submit_and_new", "refetch": true, "api_path": "assets/settings"}], "flow": {"children": {"params": {"title": "New asset Category", "type": "array", "default": [{"type": "", "soc_unit_type": "", "use_category": "residential", "chargeable": false}], "children": {"assets_categories_name": {"title": "Asset Categories", "type": "string", "required": true, "placeholder": "Enter Asset Category"}, "opening_balance": {"title": "Opning Balance", "type": "number", "placeholder": "Enter Quantity", "required": true, "min": 0}, "assets_categories_type": {"title": "Use Category", "type": "select", "enum": [{"const": "moveable", "title": "Moveable"}, {"const": "immoveable", "title": "Immoveable"}], "default": "moveable", "required": true}}}, "sequence": {"title": "Asset Categories", "type": "table", "selectable": false, "default": []}}}}}