{"meta": {"title": "edit Asset", "data_source": "assets/assetDetails/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "assets/editAsset", "redirect": "/admin/society/assets/assetsList"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/assets/assetsList"}], "flow": {"children": {"assets_name": {"title": "Asset Name", "type": "string"}, "assets_tag_number": {"title": "Asset Tag No", "type": "string"}, "assets_categories_id": {"title": "Asset Category", "type": "dropdown", "apiPath": "admin/assets/assetsList", "labelKeys": ["assets_categories_name"], "onMount": true}, "vendor_bill_type_purchase": {"title": "Purchase Type", "type": "select", "enum": [{"const": "cash", "title": "Cash"}, {"const": "credit", "title": "Credit"}, {"const": "bank", "title": "Bank"}]}, "assets_vendor_id": {"title": "Vender Name", "type": "dropdown", "apiPath": "admin/vendor/viewVendor", "labelKeys": ["vendor_name"], "endAdornment": {"icon": "mdi:plus", "title": "New", "form": "vendor/addVendor"}}, "assets_location": {"title": "Location", "type": "string"}, "assets_purchase_date": {"title": "Purchase date", "type": "string"}, "assets_cost": {"title": "Asset cost as on", "type": "number"}, "assets_appreciation": {"title": "Asset Appreciation Value", "type": "number"}, "assets_depreciation": {"title": "Asset Depreciation Value", "type": "number"}}}}}