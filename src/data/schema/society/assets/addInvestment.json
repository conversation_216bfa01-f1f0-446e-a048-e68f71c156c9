{"meta": {"title": "Investment Details"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "bank"}]}, "event": [{"type": "require", "params": {"field": ["bank_account"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account"]}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "accounts/addInvestment", "redirect": "/admin/society/assets/investmentslist"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/assets/investmentslist"}], "flow": {"children": {"type": {"title": "Investment Details", "type": "select", "enum": [{"const": "rd", "title": "RD(Recurring Deposit)"}, {"const": "fd", "title": "FD(Fixed Deposit)"}], "default": "rd"}, "year": {"title": "Year of investment", "type": "select", "enum": [{"const": "current", "title": "Current Financial Year (2025)"}, {"const": "previous", "title": "Previous Financial Year"}], "default": "current"}, "investment_account_number": {"title": "Account Number", "type": "string", "required": true}, "investment_bank_name": {"title": "Bank Name", "type": "string", "required": true}, "investment_bank_branch": {"title": "Bank Branch", "type": "string", "required": true}, "investment_bank_address": {"title": "Bank Address", "type": "string", "required": true}, "investment_bank_city": {"title": "Bank City", "type": "string", "required": true}, "investment_bank_ifsc": {"title": "IFSC Code", "type": "string", "required": true}, "amount": {"title": "Amount", "type": "number", "required": true}, "payment_mode": {"title": "Payment Mode", "type": "select", "enum": [{"const": "cash", "title": "Cash"}, {"const": "bank", "title": "Bank"}], "required": true, "default": "cash"}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "placeholder": "Please Select Bank Account", "onMount": true}, "start_date": {"title": "Date of Investment", "type": "date", "required": true}, "maturity_date": {"title": "Date of Maturity", "type": "date", "required": true}}}}}