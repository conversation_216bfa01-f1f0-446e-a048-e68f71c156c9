{"meta": {"title": "New Asset"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "vendor_bill_type_purchase", "operator": "equal", "value": "cash"}]}, "event": [{"type": "require", "params": {"field": ["cash_ledger"]}}]}, {"conditions": {"any": [{"fact": "vendor_bill_type_purchase", "operator": "equal", "value": "bank"}]}, "event": [{"type": "require", "params": {"field": ["card_ledger"]}}]}, {"conditions": {"any": [{"fact": "vendor_bill_type_purchase", "operator": "notEqual", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["cash_ledger"]}}]}, {"conditions": {"any": [{"fact": "vendor_bill_type_purchase", "operator": "notEqual", "value": "bank"}]}, "event": [{"type": "remove", "params": {"field": ["card_ledger"]}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "assets/addAsset", "redirect": "/admin/society/assets/assetsList"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "assets/addAsset", "redirect": "/admin/society/assets/addAsset"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/assets/assetsList"}], "flow": {"children": {"assets_name": {"title": "Asset Name", "type": "string", "placeholder": "Enter Asset Name", "required": true}, "assets_tag_number": {"title": "Asset Tag No", "type": "number", "required": true}, "assets_categories_id": {"title": "Asset Category", "type": "dropdown", "apiPath": "admin/assets/assetsList", "labelKeys": ["assets_categories_name"], "onMount": true}, "vendor_bill_type_purchase": {"title": "Purchase Type", "type": "select", "required": true, "enum": [{"const": "cash", "title": "Cash"}, {"const": "credit", "title": "Credit"}, {"const": "bank", "title": "Bank"}]}, "cash_ledger": {"title": "Cash Ledger", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers?current_tab=cash", "labelKeys": ["ledger_account_name"], "required": true, "placeholder": "Select Cash Ledger", "onMount": true}, "card_ledger": {"title": "<PERSON> Ledger", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "placeholder": "Select Bank Account", "onMount": true}, "assets_vendor_id": {"title": "Vender Name", "type": "dropdown", "apiPath": "admin/vendor/viewVendor", "labelKeys": ["vendor_name"], "endAdornment": {"icon": "mdi:plus", "title": "New", "form": "vendor/addVendor"}}, "assets_location": {"title": "Location", "type": "string"}, "assets_purchase_date": {"title": "Purchase date", "type": "date"}, "assets_cost": {"title": "Asset cost as on", "type": "number", "required": true}, "assets_appreciation": {"title": "Asset Appreciation Value", "type": "number"}, "assets_depreciation": {"title": "Asset Depreciation Value", "type": "number"}}}}}