{"meta": {"formId": "app_preferences", "title": "App Preferences", "api_path": "/api/schema"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "income-details/addnonmemberMaster", "redirect": "/admin/society/income-details/nonmembermaster/"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/income-details/nonmembermaster/"}], "flow": {"title": "Personal Details", "required": [], "children": {"first_name": {"title": "First Name", "type": "string"}, "last_name": {"title": "Last Name", "type": "string"}, "gender": {"title": "Gender", "type": "radio", "enum": ["Male", "Female"]}, "email_id": {"title": "Email Address", "type": "string"}, "address": {"title": "Address", "type": "textarea"}, "hsn": {"title": "HSN/SAC", "type": "string"}, "mobile_number": {"title": "Mobile Number", "type": "number"}, "gstin": {"title": "GSTIN Number", "type": "string"}, "pan_number": {"title": "PAN Number", "type": "string"}, "Gst_state_code": {"title": "GST State Code", "type": "slect", "enum": ["27-Maharashtra", "28-karnataka", "29-<PERSON><PERSON><PERSON><PERSON><PERSON>", "30-kerala", "31-andhra", "32-<PERSON><PERSON><PERSON>", "33-<PERSON><PERSON><PERSON>", "34-chattisgarh", "35-<PERSON><PERSON> prade<PERSON>", "36-gujarat", "37-<PERSON><PERSON><PERSON><PERSON>", "38-punja<PERSON>", "39-<PERSON><PERSON><PERSON>", "40-jammu and kashmir", "41-himachal pradesh", "42-uttarakhand", "43-u<PERSON><PERSON> pradesh", "44-<PERSON><PERSON>"]}, "create_non_member_ledger": {"title": "", "type": "checkbox", "enum": [""]}}}}}