{"meta": {"title": "Inward Inventory", "data_source": "/inventory/listInventory/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "inventory/inward", "redirect": "/admin/society/inventory/listInventory"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/inventory/listInventory"}], "flow": {"children": {"inventory_categories_name": {"title": "Product Name", "type": "string", "readonly": true}, "inventory_txns_date": {"title": "Date", "type": "date", "required": true}, "inventory_txns_quantity": {"title": "Quantity", "type": "number", "required": true}, "inventory_txns_cost": {"title": "Total Cost", "type": "currency", "required": true, "startAdornment": "₹"}, "location": {"title": "Location", "type": "string", "required": true}, "transaction_by": {"title": "Inward By", "type": "string", "required": true}, "inventory_txns_comments": {"title": "Comments", "type": "textarea", "required": true}, "payment_mode": {"title": "Mode Of Payment", "type": "select", "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}]}, "ledger_account_id": {"title": "Ledger", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers", "labelKeys": ["ledger_account_name"], "placeholder": "--Select--"}}}}}