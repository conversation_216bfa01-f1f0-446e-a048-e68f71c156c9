{"meta": {"title": "Receipt Tracker", "data_source": "income-details/updatePaymentTracker/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "income-details/updatePaymentTracker", "redirect": "/admin/society/income-details/incomepaymenttracker"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/income-details/incomepaymenttracker"}, {"title": "Reset", "type": "reset"}], "rules": [{"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": true}]}, "event": [{"type": "update", "params": {"rec_amount": {"fact": "receipt_amount"}, "vendor_bill_advance_amount": 0}}, {"type": "calculate", "params": {"writeoff_amount": {"fact": "writeoff_amount"}, "receipt_amount": {"fact": "receipt_amount"}, "__field": "receipt_amount", "__exp": "receipt_amount - writeoff_amount"}}, {"type": "require", "params": {"field": ["writeoff_amount"]}}, {"type": "schemaOverride", "params": {"writeoff_amount.maximum": {"fact": "total_due"}}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": false}]}, "event": [{"type": "update", "params": {"writeoff_amount": 0}}, {"type": "remove", "params": {"field": ["writeoff_amount"]}}, {"type": "uiOverride", "params": {"vendor_bill_advance_amount": {"ui:disabled": false}}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": true}, {"fact": "writeoff_amount", "operator": "greaterThan", "value": {"fact": "total_due"}}]}, "event": [{"type": "uiAppend", "params": {"writeoff_amount": {"ui:options": {"error": "Write off amount should be less than total due amount."}}}}]}], "flow": {"children": {"received_from": {"title": "Received From", "type": "string", "required": true}, "transaction_reference": {"title": "Transaction Reference (Cheque number)", "type": "number", "pattern": "[0-9]{6}", "required": true}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "help": "Select a bank account", "placeholder": "Please Select Bank Account", "onMount": true}, "cheque_date": {"title": "Cheque Date", "type": "date"}, "bank_and_branch_name": {"title": "Bank and Branch Name", "type": "string", "required": true}, "late_payment_charges": {"title": "Late Payment Charges", "type": "number", "disabled": true}, "total_due_amount": {"title": "Total Due Amount", "type": "number", "disabled": true}, "rec_amount": {"title": "Receipt Amount", "type": "number", "required": true}, "receipt_amount": {"title": "Receipt Amount", "type": "number", "required": true, "endAdornment": [{"title": "TDS", "showButton": true, "update": {"key": "show_writeoff", "value": true}}]}, "writeoff_amount": {"title": "TDS Amount", "type": "number", "description": "", "endAdornment": [{"title": "Remove Write Off", "icon": "ri-close-line", "update": {"key": "show_writeoff", "value": false}}]}, "show_writeoff": {"type": "radio", "hidden": true, "default": false}, "receipt_date": {"title": "Receipt Date", "type": "date", "required": true}}}}}