{"meta": {"title": "Receipt", "data_source": "income-details/paymemberbill/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "income-details/paymemberbill", "redirect": "/admin/society/income-details/incomemember"}], "facts": [{"fact": "sequences", "path": "/admin/income-details/getReceiptListLatest/:id", "method": "GET", "dependsOn": "id", "returns": "all"}, {"fact": "invoices", "path": "/admin/income-details/getUnpaidInvoices/:id", "method": "GET", "dependsOn": "id", "returns": "all"}], "rules": [{"conditions": {"all": [{"fact": "invoices", "operator": "notEqual", "value": null}]}, "event": [{"type": "uiSchemaReplace", "params": {"member_paid_invoice.ui:options.tableSchema": {"fact": "invoices", "path": "meta"}}}, {"type": "update", "params": {"member_paid_invoice": {"fact": "invoices", "path": "data"}}}]}, {"conditions": {"all": [{"fact": "member_paid_invoice", "path": "$..selected", "operator": "truthy"}]}, "event": [{"type": "calculate", "params": {"data": {"fact": "member_paid_invoice", "operator": "filterBy", "value": {"selected": true}}, "__exp": "sum(map(filter(member_paid_invoice, f(x) = x.selected), f(x) = x.invoice_balance_due))", "__field": "total_unpaid_amount"}}, {"type": "calculate", "params": {"late_charges": {"fact": "member_paid_invoice"}, "__exp": "sum(map(filter(member_paid_invoice, f(x) = x.selected), f(x) = x.late_payment_charges))", "__field": "late_payment_charges"}}]}, {"conditions": {"all": [{"fact": "sequences", "operator": "notEqual", "value": null}]}, "event": [{"type": "uiSchemaReplace", "params": {"sequence.ui:options.tableSchema": {"fact": "sequences", "path": "meta"}}}, {"type": "update", "params": {"sequence": {"fact": "sequences", "path": "data"}}}]}, {"conditions": {"all": [{"fact": "show_tds", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"tds_amount": 0}}, {"type": "remove", "params": {"field": ["tds_amount"]}}]}, {"conditions": {"all": [{"fact": "show_tds", "operator": "truthy"}]}, "event": [{"type": "require", "params": {"field": ["tds_amount"]}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"writeoff_amount": 0}}, {"type": "remove", "params": {"field": ["writeoff_amount"]}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "truthy"}]}, "event": [{"type": "require", "params": {"field": ["writeoff_amount"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cashtransfer"}]}, "event": [{"type": "remove", "params": {"field": ["cheque_number", "bank_name", "cheque_date"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}, {"fact": "payment_mode", "operator": "falsy"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "cheque_number", "bank_name", "cheque_date", "transaction_reference"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cheque"}]}, "event": [{"type": "remove", "params": {"field": ["transaction_reference"]}}]}], "flow": {"children": {"member_paid_invoice": {"title": "Select Invoice to add Receipt", "type": "table", "factKey": "invoices", "default": [], "dependent": [{"field": "late_payment_charges"}, {"field": "invoice_balance_due"}]}, "total_due_amount": {"title": "Total Due Amount", "type": "number", "disabled": true, "startAdornment": "₹"}, "received_from": {"title": "Received From", "type": "string", "required": true}, "late_payment_charges": {"title": "Late Payment Charges", "type": "number", "default": 0.0, "disabled": true}, "total_unpaid_amount": {"title": "Total Payable Amount", "type": "number", "default": 0.0, "disabled": true}, "payment_amount": {"title": "Amount", "type": "number", "required": true, "default": 0.0, "endAdornment": [{"title": "TDS", "icon": "ri-add-line", "showButton": true, "update": {"key": "show_tds", "value": true}}, {"title": "Writeoff", "icon": "ri-add-line", "showButton": true, "update": {"key": "show_writeoff", "value": true}}]}, "tds_amount": {"title": "TDS Amount", "type": "number", "required": true, "endAdornment": [{"title": "Remove TDS", "icon": "ri-close-line", "update": {"key": "show_tds", "value": false}}]}, "writeoff_amount": {"title": "Writeoff Amount", "type": "number", "required": true, "endAdornment": [{"title": "Remove W<PERSON>off", "icon": "ri-close-line", "update": {"key": "show_writeoff", "value": false}}]}, "show_writeoff": {"type": "radio", "hidden": true, "default": false}, "show_tds": {"type": "radio", "hidden": true, "default": false}, "payment_mode": {"title": "Receipt Mode", "type": "select", "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}], "required": true, "default": "cash"}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "placeholder": "Please Select Bank Account", "onMount": true}, "transaction_reference": {"title": "Payment Reference", "type": "string", "required": true}, "cheque_number": {"title": "Cheque Number", "type": "string", "description": "", "required": true}, "bank_name": {"title": "Bank & Branch Name", "type": "string", "description": "", "required": true}, "cheque_date": {"title": "Cheque Date", "type": "date", "maxDateTime": "now", "required": true}, "payment_date": {"title": "Receipt Date", "type": "date", "maxDateTime": "now", "required": true}, "payment_note": {"title": "Receipt Note", "type": "textarea"}, "sequence": {"title": "Last Receipts", "type": "table", "factKey": "sequences", "selectable": false, "default": [], "dependent": [{"field": "due_amount"}]}}}}}