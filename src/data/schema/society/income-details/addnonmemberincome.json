{"meta": {"title": "New Non Member Bill"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "income-details/addnonmemberincome", "redirect": "/admin/society/income-details/incomenonmember", "default_params": [{"nonmem_ledger": 1}]}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/income-details/incomenonmember"}, {"title": "Reset", "type": "reset"}], "rules": [{"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "select_payment_mode"}]}, "event": [{"type": "remove", "params": {"field": ["recevie_from", "amount", "receipt_date", "receipt_note", "bank_account_id", "cheque_no", "cheque_date", "bank_branch", "payment_date", "payment_amount", "payment_note"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account_id", "cheque_no", "cheque_date", "bank_branch"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "etf"}]}, "event": [{"type": "remove", "params": {"field": ["cheque_date", "bank_branch"]}}]}, {"conditions": {"any": [{"fact": "booker_name", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"booker_email_address": {"fact": "booker_name", "path": "$..booker_email_address"}, "hsn": {"fact": "booker_name", "path": "$..hsn"}, "booker_address": {"fact": "booker_name", "path": "$..address"}, "recevie_from": {"fact": "booker_name", "path": "$..billed_name"}, "gstin": {"fact": "booker_name", "path": "$..gstin"}}}, {"type": "update", "params": {"mobile_number": {"fact": "booker_name", "path": "$..booker_mobile_number"}}}]}, {"conditions": {"any": [{"fact": "booking_charge", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"total_due_amount": {"fact": "booking_charge"}}}]}], "flow": {"children": {"nonmemberincomeaccount": {"title": "Non Member Income Account", "type": "dropdown", "apiPath": "admin/income-tracker-setting/readIncomeAccounts?&current_tab=non_member_income_account", "labelKeys": ["ledger_account_name"], "onMount": true, "required": true}, "booker_name": {"title": "Bill <PERSON>", "type": "dropdown", "required": true, "apiPath": "admin/income-details/hugeincomenonmember", "labelKeys": ["billed_name"], "onMount": true, "dependent": [{"field": "booker_email_address"}, {"field": "hsn"}, {"field": "address"}, {"field": "booker_mobile_number"}, {"field": "gstin"}]}, "booker_email_address": {"title": "E-mail", "type": "string"}, "booker_address": {"title": "Address", "type": "textarea"}, "hsn": {"title": "HSN/SAC", "type": "string"}, "gstin": {"title": "GSTIN", "type": "string", "pattern": "^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}Z[A-Z0-9]{1}$"}, "mobile_number": {"title": "Mobile Number", "type": "number"}, "bill_date": {"title": "<PERSON>", "type": "date", "required": true}, "narration": {"title": "Bill for", "type": "textarea", "required": true}, "from_date": {"title": "Billing Period Form", "type": "date"}, "end_date": {"title": "Billing Period To", "type": "date"}, "booking_charge": {"title": "<PERSON>", "type": "number", "required": true}, "applicable _taxes": {"title": "Applicable Taxes", "type": "dropdown", "apiPath": "admin/tax/viewTax", "labelKeys": ["tax_class_name"], "onMount": true, "required": true}, "total_tax": {"title": "Total Tax", "type": "number", "disabled": true, "startAdornment": "₹"}, "total_due_amount": {"title": "Total Due Amount", "type": "number", "disabled": true, "startAdornment": "₹"}, "is_default_ladger": {"title": "Create Non Member Ledger", "type": "boolean", "default": false}, "payment_mode": {"title": "Payment Mode", "type": "select", "enum": [{"const": "select_payment_mode", "title": "Select a payment mode"}, {"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "etf", "title": "Elctronic Fund Transfer"}], "default": "select_payment_mode"}, "bank_account_id": {"title": "Bank Account", "type": "dropdown", "apiPath": "/admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "onMount": true}, "recevie_from": {"title": "Received From", "type": "string", "required": true}, "payment_amount": {"title": "Amount", "type": "number", "required": true}, "cheque_no": {"title": "Cheque Number", "type": "number", "pattern": "[0-9]{6}", "required": true}, "cheque_date": {"title": "Cheque Date", "type": "date"}, "payment_date": {"title": "Recepite date", "type": "date", "required": true, "maxDate": "now"}, "bank_branch": {"title": "Bank and Branch Name", "type": "string", "required": true}, "payment_note": {"title": "Receipt Note", "type": "textarea"}}}}}