{"meta": {"title": "Pay Non Member Bill", "data_source": "/income-details/pay_nonmember_bill/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/income-details/pay_nonmember_bill", "redirect": "/admin/society/income-details/incomenonmember", "default_params": [{"nonmem_ledger": 1}]}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/income-details/incomenonmember"}, {"title": "Reset", "type": "reset"}], "rules": [{"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": true}, {"fact": "writeoff_amount", "operator": "notEqual", "value": {"fact": "payment_amount"}}]}, "event": [{"type": "validation", "params": {"field": "writeoff_amount", "message": "TDS Amount must be equal to Payment Amount"}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "equal", "value": true}, {"fact": "writeoff_amount", "operator": "greaterThan", "value": {"fact": "payment_amount"}}]}, "event": [{"type": "validation", "params": {"field": "writeoff_amount", "message": "TDS Amount cannot be greater than Payment Amount"}}]}, {"conditions": {"any": [{"fact": "show_writeoff", "operator": "equal", "value": false}]}, "event": [{"type": "remove", "params": {"field": ["writeoff_amount"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "select_payment_mode"}]}, "event": [{"type": "remove", "params": {"field": ["receipt_date", "receipt_note", "bank_account", "cheque_number", "cheque_date", "payer_bank_details"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "cheque_number", "cheque_date", "payer_bank_details"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cashtransfer"}]}, "event": [{"type": "remove", "params": {"field": ["cheque_date", "payer_bank_details"]}}]}, {"conditions": {"any": [{"fact": "booker_name", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"recevie_from": {"fact": "booker_name", "path": "$..booker_name"}, "gstin": {"fact": "booker_name", "path": "$..gstin"}}}, {"type": "update", "params": {"mobile_number": {"fact": "booker_name", "path": "$..booker_mobile_number"}}}]}, {"conditions": {"any": [{"fact": "booking_charge", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"total_due_amount": {"fact": "booking_charge"}}}]}, {"conditions": {"any": [{"fact": "show_writeoff", "operator": "equal", "value": false}]}, "event": [{"type": "remove", "params": {"field": ["writeoff_amount"]}}]}], "flow": {"children": {"booker_name": {"title": "Name", "type": "string", "required": true, "disabled": true}, "invoice_number": {"title": "Invoice Number", "type": "string", "required": true, "disabled": true}, "bill_total_amount": {"title": "Invoice Total Amount", "type": "number", "required": true, "disabled": true}, "advance_amount": {"title": "Advance Amount", "type": "number", "required": true, "disabled": true}, "partial_paid_amount": {"title": "Partial Paid Amount", "type": "number", "disabled": true}, "discount_amount": {"title": "Total Discount Amount", "type": "number", "disabled": true, "required": true}, "tax_deduction": {"title": "Tax Deduction", "type": "number", "required": true, "disabled": true}, "due_amount": {"title": "Total Due Amount", "type": "number", "required": true, "disabled": true, "startAdornment": "₹"}, "received_from": {"title": "Received From", "type": "string", "required": true}, "payment_amount": {"title": "Amount", "type": "number", "required": true, "endAdornment": [{"title": "TDS", "icon": "ri-add-line", "showButton": true, "update": {"key": "show_writeoff", "value": true}}]}, "writeoff_amount": {"title": "TDS Amount", "type": "number", "description": "Must be equal to Payment Amount when TDS is active", "endAdornment": [{"title": "Remove TDS", "icon": "ri-close-line", "update": {"key": "show_writeoff", "value": false}}]}, "show_writeoff": {"type": "radio", "hidden": true, "default": false}, "payment_mode": {"title": "Receipt Mode", "type": "select", "required": true, "enum": [{"const": "select_payment_mode", "title": "Select a payment mode"}, {"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Elctronic Fund Transfer"}], "default": "select_payment_mode"}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "/admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "onMount": true}, "cheque_number": {"title": "Cheque Number", "type": "number", "pattern": "[0-9]{6}", "required": true}, "cheque_date": {"title": "Cheque Date", "type": "date"}, "payment_date": {"title": "Recepite date", "type": "date", "required": true, "maxDateTime": "now"}, "payer_bank_details": {"title": "Bank and Branch Name", "type": "string", "required": true}, "payment_note": {"title": "Receipt Note", "type": "textarea"}}}}}