{"meta": {"formId": "New Role", "title": "New Role"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "income-tracker-setting/rule"}], "flow": {"title": "New rule", "children": {"particular": {"title": "Particular", "required": true, "type": "string", "placeholder": "Enter Particular"}, "rate": {"title": "Charges", "required": true, "type": "number", "placeholder": "Enter Charges", "default": 0.0, "min": 0}, "ledger_id": {"title": "Income Account", "type": "dropdown", "required": true, "apiPath": "/admin/accounts/viewLedgers?current_tab=income", "labelKeys": ["ledger_account_name"], "onMount": true, "endAdornment": [{"title": "<PERSON><PERSON>", "icon": "mdi:plus", "form": "accounts/createLedger"}]}, "effective_date": {"title": "Effective Date", "type": "date", "maxDateTime": "50 year", "minDateTime": "now", "required": true}, "tax_class_id": {"title": "Tax Class ", "type": "dropdown", "placeholder": "Select Tax Class", "apiPath": "/admin/tax/viewTax", "labelKeys": ["tax_class_name"], "onMount": true}, "apply_late_payment_charge": {"title": "Apply Late Payment Charge", "type": "boolean"}}}}}