{"meta": {"formId": "edit_late_charges_rule", "title": "Edit Late Charges Rule", "data_source": "income-tracker-invoice-setting/late_payment_charge_rule/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "income-tracker-invoice-setting/late_payment_charge_rule"}], "rules": [{"conditions": {"any": [{"fact": "interest_amount_type", "operator": "equal", "value": "fixed"}]}, "event": [{"type": "remove", "params": {"field": ["interest_type"]}}]}], "flow": {"title": "Edit Late Charges Rule", "children": {"interest_amount_type": {"title": "Charges Type", "type": "select", "enum": [{"const": "percentage", "title": "Percentage"}, {"const": "fixed", "title": "Fixed"}]}, "simple_interest": {"title": "Amount Monthly", "type": "number", "placeholder": "Enter Interest Per Year"}, "interest_type": {"title": "Interest Type", "type": "select", "enum": [{"const": "simple_interest", "title": "Simple Interest"}, {"const": "Monthly", "title": "Compound Interest"}]}, "grace_period": {"title": "<PERSON> Period", "type": "number", "placeholder": "Enter Grace Period", "min": 0, "required": true}, "effective_date": {"title": "Effective Date", "type": "date", "placeholder": "dd-mm-yyyy", "required": true}, "calculate_from": {"title": "Interest is calculated from", "type": "select", "enum": [{"const": "billdate", "title": "<PERSON>"}, {"const": "duedate", "title": "Due Date"}, {"const": "both", "title": "Both (Bill Date and Due Date)"}], "required": true}, "calculate_for": {"title": "Interest will calculate for", "type": "select", "enum": [{"const": "billperiod", "title": "<PERSON> End"}, {"const": "perday", "title": "Per Day"}], "required": true}, "applicable_taxes": {"title": "Tax Class", "type": "dropdown", "placeholder": "Select Tax Class", "apiPath": "/admin/tax/viewTax", "labelKeys": ["tax_class_name"], "onMount": true}}}}}