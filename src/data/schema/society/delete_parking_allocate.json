{"meta": {"title": "Delete Parking Allotment"}, "schema": {"actions": [{"title": "Revoke", "type": "delete", "api_path": "parking-allotments/delete", "redirect": "/admin/society/parking-allotments/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/parking-allotments/list"}], "flow": {"title": "Parking Detail", "required": [], "children": {"allot_parking_to": {"title": "Impact this action could cause", "type": "string", "default": "The parking lot is vacant.", "disabled": true}, "cancel_date": {"title": "Cancel Allotment w.e.f.", "type": "string"}, "cancellation_reason": {"title": "Cancellation Reason", "type": "string"}}}}}