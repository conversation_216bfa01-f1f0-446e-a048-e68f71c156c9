{"meta": {"title": "Edit Unit", "data_source": "societies/unitTypeDetails/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "societies/editUnitType", "redirect": "/admin/society/societies/listUnitType"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/societies/listUnitType"}, {"title": "Reset", "type": "reset"}], "flow": {"title": "Unit Category Details", "children": {"type": {"title": "Unit Category Name", "type": "string", "required": true}, "soc_units_type_id": {"title": "Unit Type", "type": "dropdown", "apiPath": "/admin/societies/listUnitType", "labelKeys": ["soc_unit_type"], "onMount": true, "required": true}, "quantity": {"title": "Quantity", "type": "number", "min": 0, "required": true}, "area_sq_ft": {"title": "Area", "type": "number", "min": 0, "required": true}, "open_space_area_sq_ft": {"title": "Open Area", "type": "number", "min": 0}, "water_inlets_num": {"title": "Water Inlet", "type": "number", "min": 0}, "chargeable": {"title": "Chargeable", "type": "boolean", "enum": [{"title": "No", "const": true}, {"title": "Yes", "const": false}]}}}}}