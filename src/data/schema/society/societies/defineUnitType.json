{"meta": {"formId": "New Role", "title": "New Unit Category"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "societies/defineUnitType", "redirect": "/admin/society/societies/listUnitType"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/societies/listUnitType"}], "flow": {"children": {"params": {"title": "New Unit Category", "type": "array", "default": [{"type": "", "soc_unit_type": "", "use_category": "residential", "chargeable": false}], "children": {"type": {"title": "Unit Category", "type": "string", "required": true, "placeholder": "Enter Unit Category"}, "soc_unit_type": {"title": "Unit Type", "type": "dropdown", "apiPath": "/admin/societies/listUnitType", "placeholder": "Please select Unit Type", "labelKeys": ["soc_unit_type"], "onMount": true, "required": true}, "use_category": {"title": "Use Category", "type": "select", "enum": [{"const": "residential", "title": "Residential"}, {"const": "commercial", "title": "Commercial"}, {"const": "common", "title": "Common"}], "default": "residential", "required": true}, "quantity": {"title": "Quantity", "type": "number", "placeholder": "Enter Quantity", "required": true, "min": 0}, "area_sq_ft": {"title": "Area/Unit(sqft)", "type": "number", "placeholder": "Enter Area/Unit(sqft)", "required": true, "min": 0}, "open_space_area_sq_ft": {"title": "Open Area/Unit(sqft)", "type": "number", "placeholder": "Enter Open Area/Unit(sqft)", "required": true, "min": 0}, "chargeable": {"title": "Chargeable", "type": "boolean"}, "water_inlets_num": {"title": "Water Inlets", "type": "number", "placeholder": "Enter Water Inlets", "min": 0}}}}}}}