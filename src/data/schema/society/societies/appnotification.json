{"meta": {"formId": "New Role", "title": "New Role", "api_path": "/api/schema"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/api/roles", "redirect": "/roles"}], "flow": {"title": "Notification", "required": ["title", "purpose", "text"], "children": {"send_to": {"title": "Send", "type": "radio", "enum": ["Admin", "Member", "All"]}, "dont_send": {"title": "Don't send to society"}, "title": {"title": "Title"}, "text": {"title": "Text", "type": "number"}}}}}