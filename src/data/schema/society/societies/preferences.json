{"meta": {"formId": "app_preferences", "title": "App Preferences"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "societies/preferences", "redirect": "/admin/society/societies/preferences"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/societies/preferences"}, {"title": "Reset", "type": "reset"}], "flow": {"children": {"general_settings": {"title": "App Preferences", "type": "group", "children": {"date_display_in": {"title": "Date Displayed In", "type": "select", "enum": [{"const": "d/m/y", "title": "d/m/y"}, {"const": "m/d/y", "title": "m/d/y"}], "default": "d/m/y"}, "time_display_in": {"title": "Time Displayed In", "type": "select", "enum": [{"const": "24 hour", "title": "24 hour"}, {"const": "12 hour", "title": "12 hour"}], "default": "24 hour"}, "currency_used": {"title": "Currency Used", "type": "select", "enum": [{"const": "INR", "title": "Indian National Rupee"}, {"const": "USD", "title": "US Dollar"}], "default": "INR"}}}, "number_formatting": {"title": "Number Formatting", "type": "group", "children": {"separate_numbers_every": {"title": "Separate Numbers", "type": "number", "default": 2, "endAdornment": "Digits"}, "separate_digits_by": {"title": "Separate Digits By", "type": "select", "enum": [{"const": ";", "title": "; (semi-colon)"}, {"const": ",", "title": ", (comma)"}, {"const": ":", "title": ": (colon)"}, {"const": "-", "title": "- (dash)"}, {"const": "space", "title": "Space"}], "default": ","}}}, "privacy": {"title": "Privacy", "type": "group", "children": {"complex_contact_info_private": {"title": "Keep Complex Contact Information Private", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "default": "Yes"}}}}}}}