{"meta": {"title": "Unit Category - **[{{unit_category_id}}](/admin/income-tracker-invoice-setting/unitrules/{{id}})**"}, "schema": {"facts": [{"fact": "unit_cat", "path": "/admin/income-details/incomemember/view_applied_rules/:unit_category_id/:id", "method": "GET", "dependsOn": "id", "params": {"unit_category_id": {"fact": "unit_category_id"}}}], "rules": [{"conditions": {"any": [{"fact": "unit_cat", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"date": {"fact": "unit_cat"}}}]}], "flow": {"required": [], "order": ["*"], "children": {"date": {"title": "Income Rule", "type": "array", "disabled": true, "hideLabel": true, "addable": false, "removable": false, "layout": "accordion", "startAdornment": [{"title": "Edit Rule", "icon": "ri-edit-box-line", "redirect": "/admin/society/income-tracker-invoice-setting/add_rule/:id"}], "children": {"account_name": {"title": "Account Name"}, "rule": {"title": "Rule Type", "type": "select", "enum": [{"const": "standard", "title": "Standard"}]}, "effective_date": {"title": "Effective From Date", "type": "date", "maxDateTime": "financialYearEnd", "minDateTime": "financialYearStart"}, "apply_late_payment_interest": {"title": "Late Payment Applicable Interest", "type": "boolean"}, "unit_area": {"title": "Total Area", "type": "number", "endAdornment": "Sqft"}}}}}}}