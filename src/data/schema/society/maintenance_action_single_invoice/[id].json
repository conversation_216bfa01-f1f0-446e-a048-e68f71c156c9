{"meta": {"title": "Edit Receipt", "data_source": "income-details/generatemanualbill/:id"}, "schema": {"actions": [{"title": "Preview", "type": "view", "color": "info", "icon": "ri-search-line", "api_path": "/admin/society/income-details/previewWithGenerate/:id/download?invoice_date&due_date&start_date&end_date&last_invoice_period_start_date&last_invoice_period_end_date&unit_name", "redirect": "/admin/society/income-details/incomemember"}, {"title": "Generate", "type": "download", "icon": "ri-apps-2-add-fill", "api_path": "/admin/society/income-details/previewWithGenerate/:id/download?invoice_date&due_date&start_date&end_date&last_invoice_period_start_date&last_invoice_period_end_date&unit_name", "redirect": "/admin/society/income-details/incomemember"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/income-details/incomemember"}], "rules": [{"conditions": {"all": [{"fact": "date", "path": "invoice_date", "operator": "truthy"}]}, "event": [{"type": "updateDate", "params": {"invoice_date": {"fact": "date", "path": "invoice_date"}, "interval": {"fact": "interval"}, "__field": "date.due_date", "__exp": "{{invoice_date}} + {{interval}} days"}}]}], "flow": {"title": "{{unit_name}}", "required": [], "order": ["*"], "children": {"date": {"layout": "tabular", "children": {"invoice_date": {"title": "Invoice Date", "type": "date", "maxDateTime": "financialYearEnd", "minDateTime": "financialYearStart"}, "due_date": {"title": "Due Date", "type": "date", "maxDateTime": "financialYearEnd", "minDateTime": "financialYearStart"}}}, "period": {"layout": "tabular", "children": {"start_date": {"title": "Invoice Period", "type": "date", "minDateTime": "-94 year"}, "end_date": {"title": "To", "type": "date", "minDateTime": "-94 year"}}}, "last": {"layout": "tabular", "children": {"last_invoice_period_start_date": {"title": "Last Invoice Period", "type": "date", "minDateTime": "-94 year", "disabled": true}, "last_invoice_period_end_date": {"title": "To", "type": "date", "minDateTime": "-94 year", "disabled": true}}}, "unit_name": {"disabled": true, "hidden": true}, "interval": {"disabled": true, "hidden": true, "type": "number"}}}}}