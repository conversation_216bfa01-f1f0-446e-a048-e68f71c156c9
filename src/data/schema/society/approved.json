{"meta": {"formId": "New Role", "title": "New Role", "api_path": "/api/schema", "edit": {"allowFor": ["admin"]}, "add": {"allowFor": ["admin"]}, "delete": {"allowFor": ["admin", "complaints"]}}, "schema": {"actions": [{"title": "Confirm", "type": "submit", "api_path": "/api/roles", "redirect": "/roles"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/roles/listSocRoles"}], "flow": {"title": "Confirm Approval", "children": {"account": {"title": "Add Primary fgdf as Primary", "type": "radio", "enum": ["Member"]}}}}}