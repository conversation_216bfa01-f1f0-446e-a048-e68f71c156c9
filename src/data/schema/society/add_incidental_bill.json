{"meta": {"title": "New Escalation"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "common-billing/addcommonbill", "redirect": "/admin/society/common-billing/listcommonbill"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/common-billing/listcommonbill"}], "flow": {"title": "Add Incidental Bill", "required": ["unit_number", "member_name", "particular", "period_from", "period_to", "particular_amount"], "children": {"unit_number": {"title": "Unit Number", "type": "string", "apiPath": "admin/units/list", "multiple": true, "keys": ["soc_building_name", "unit_flat_number"], "dependant": [{"field": "member_name", "key": "effective_date"}]}, "member_name": {"title": "Member Name", "disabled": true, "type": "string", "apiPath": "admin/member/list"}, "particular": {"title": "Particular", "type": "dropdown", "apiPath": "admin/common-billing/getRateByParticularId"}, "bill_date": {"title": "<PERSON>", "type": "date"}, "due_date": {"title": "Due Date", "type": "date"}, "period_from": {"title": "Period From", "type": "date"}, "period_to": {"title": "Period To", "type": "date"}, "particular_amount": {"title": "Particular Amount", "type": "number", "default": "1000"}, "tax_amount": {"title": "Tax Amount", "type": "number", "default": "0.00", "disabled": true}, "Delayed Payment Charges": {"title": "Delayed Payment Charges ( on amount 5777 )", "type": "number", "default": "0.00", "disabled": true}, "invoice_amount": {"title": "Invoice Amount", "type": "number", "default": "0.00", "disabled": true}, "advance_amount": {"title": "Advance amount", "type": "number", "default": "0.00", "disabled": true}, "grand_total": {"title": "Grand Total", "type": "number", "default": "0.00", "disabled": true}, "principal_arrears": {"title": "Principal Arrears", "type": "number", "default": "0.00", "disabled": true}, "interest_arrears": {"title": "Interest Arrears", "type": "number", "default": "0.00", "disabled": true}, "balance_due": {"title": "Balance Due", "type": "number", "default": "0.00", "disabled": true}}}}}