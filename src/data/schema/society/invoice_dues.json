{"meta": {"formId": "app_preferences", "title": "App Preferences", "api_path": "/api/schema", "edit": {"allowFor": ["admin"]}, "add": {"allowFor": ["admin"]}, "delete": {"allowFor": ["admin", "complaints", "status", "templates"]}}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/api/roles", "redirect": "/roles"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/roles/listSocRoles"}], "flow": {"title": "Receipt", "required": ["unit_number"], "children": {"total_amount_dues": {"title": "16940.00", "disabled": true}, "received_form": {"title": "Received From", "default": "<PERSON><PERSON>"}, "receipt_mode": {"title": "Receipt Mode", "type": "select", "enum": ["Select a Payment Mode", "Cash", "Cheque", "Electronic Fund Transfer", "Online"]}, "late_payment_charge": {"title": "Late Payment Charge", "default": "1934.00", "disabled": true}, "amount": {"title": "Amount", "default": "16940.00"}, "receipt_date": {"title": "Receipt Date", "type": "date"}, "receipt_note": {"title": "Receipt Note", "type": "textarea"}}}}}