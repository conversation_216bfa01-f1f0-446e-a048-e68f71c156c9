{"meta": {"title": "New Vendor Advance", "data_source": "vendorbill/viewAllTransactionsVendors"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "vendorbill/addVendorAdvances", "redirect": "/admin/society/vendorbill/vendorAdvances"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendorbill/vendorAdvances"}], "flow": {"required": ["vendor_id", "narration", "type", "payment_mode", "refundable_amount"], "children": {"vendor_id": {"title": "Name", "type": "dropdown", "placeholder": "Please Search  Name", "apiPath": "/admin/vendor/viewVendor/", "keys": ["vendor_name"]}, "payment_number": {"title": "Payment Number", "type": "string", "placeholder": "Please Enter Payment Number"}, "narration": {"title": "Narration", "type": "textarea", "placeholder": "Please Enter Narration"}, "type": {"title": "Type", "type": "radio", "enum": [{"const": "refundable", "title": "Refundable"}, {"const": "adjustable", "title": "Adjustable"}, {"const": "refundable_adjustable", "title": "Refundable & Adjustable"}], "default": "refundable", "children": {"refundable": {"refundable_amount": {"title": "Refundable Amount", "type": "number", "startAdornment": "₹", "default": 0}}, "adjustable": {"adjust_after_date": {"title": "Adjust After Date", "type": "date", "order": 1}, "adjustable_amount": {"title": "Adjustable Amount", "type": "number", "required": true, "default": 0, "min": 0.5}}, "refundable_adjustable": {"adjust_after_date": {"title": "Adjust After Date", "type": "date"}, "adjustable_amount": {"title": "Adjustable Amount", "type": "number", "required": true, "default": 0, "min": 0.5}, "refundable_amount": {"title": "Refundable Amount", "type": "number", "default": 0}}}}, "payment_mode": {"title": "Payment Mode", "type": "select", "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "online", "title": "Online"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}], "default": "cash", "required": true, "children": {"cash": {"payment_date": {"title": "Payment Date", "type": "date", "maxDateTime": "now", "minDateTime": "-24 year", "order": 1, "required": true}}, "cheque": {"cheque_payment_date": {"title": "Payment Date", "type": "date", "required": true}, "bank_account_id": {"title": "Bank Account", "type": "dropdown", "apiPath": "/admin/accounts/getBankAccounts", "keys": ["ledger_account_name"], "required": true}, "transaction_reference": {"title": "Transaction Reference (Cheque number)", "type": "number", "default": "0.00", "required": true}, "bank_name_branch": {"title": "Bank Name & Branch", "type": "string", "placeholder": "Enter Bank Name & Branch", "required": true}}, "cashtransfer": {"bank_account_id": {"title": "Bank Account", "type": "dropdown", "apiPath": "/admin/accounts/getBankAccounts", "keys": ["ledger_account_name"], "required": true}, "transaction_reference": {"title": "Payment Reference", "type": "number", "required": true}}, "online": {"payment_date": {"title": "Payment Date", "type": "date", "maxDateTime": "now", "minDateTime": "-24 year"}, "bank_account_id": {"title": "Bank Account", "type": "dropdown", "apiPath": "/admin/accounts/getBankAccounts", "keys": ["ledger_account_name"], "required": true}, "transaction_reference": {"title": "Transaction Reference", "type": "number", "default": "0.00"}, "payment_instrument": {"title": "Payment Instrument", "type": "string", "placeholder": "Enter Payment Instrument", "required": true}}}}}}}}