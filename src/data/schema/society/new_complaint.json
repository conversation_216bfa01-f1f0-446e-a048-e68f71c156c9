{"meta": {"title": "New Complaint"}, "schema": {"actions": [{"title": "continue", "api_path": "income-details/incomemember/sendNotification/:invoice_number"}], "flow": {"children": {"member": {"title": "Search a Unit or Member ", "type": "dropdown", "required": true, "apiPath": "admin/income-details/incomemember", "labelKeys": ["display_member_name"], "dependant": [{"field": "member_name"}, {"field": "email_id"}]}, "member_name": {"title": "Member name", "type": "string", "required": true, "disabled": true}, "email_id": {"title": "Email ID", "type": "text", "required": true, "disabled": true}, "mobile_number": {"title": "Mobile Number", "type": "text", "required": true, "disabled": true}}}}}