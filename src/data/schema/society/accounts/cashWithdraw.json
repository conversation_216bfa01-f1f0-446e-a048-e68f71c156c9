{"meta": {"title": "Withdraw Cash"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "accounts/addBankCashTransaction/w", "redirect": "/admin/society/accounts/viewBankAccounts"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/viewBankAccounts"}], "flow": {"children": {"transaction_date": {"title": "Transaction Date", "type": "date", "required": true}, "to_ledger": {"title": "Account Number", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true}, "from_ledger": {"title": "To Account", "type": "string", "required": true, "disabled": true, "default": "Cash in Hand"}, "amount": {"title": "Amount", "type": "number", "required": true}, "memo_desc": {"title": "Memo Description", "type": "textarea", "required": true}}}}}