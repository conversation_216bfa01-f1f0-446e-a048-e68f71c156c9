{"meta": {"title": "Bank Reconciliation Form"}, "schema": {"facts": [{"fact": "monthData", "path": "/utils/getMonthsFromYear?start&end", "xpath": "$.id", "method": "GET", "baseURL": "/api/", "dependsOn": "financial_year", "params": {"start": {"fact": "financial_year", "path": "fy_start_date"}, "end": {"fact": "financial_year", "path": "fy_end_date"}}}, {"fact": "displayTable", "path": "/admin/accounts/bankReconciliationFormTable?yearSelected=[id]", "xpath": "$.id", "method": "GET", "dependsOn": "financial_year", "returns": "all"}, {"fact": "fetchBalances", "path": "/admin/accounts/bankReconciliationForm?yearSelected=[yearSelected]&month_no=[month_no]&bank_ledger_id=[id]", "xpath": "$.id", "method": "GET", "dependsOn": "bank_account", "params": {"month_no": {"fact": "month", "path": "$.id"}, "yearSelected": {"fact": "financial_year", "path": "$.title"}}}], "rules": [{"conditions": {"all": [{"fact": "financial_year", "path": "fy_start_date", "operator": "truthy"}, {"fact": "financial_year", "path": "fy_end_date", "operator": "truthy"}, {"fact": "monthData", "path": "$..id", "operator": "truthy"}]}, "event": [{"type": "uiSchemaReplace", "params": {"month.ui:options.loadedOptions": {"fact": "monthData"}}}, {"type": "uiOverride", "params": {"month": {"ui:disabled": false}}}]}, {"conditions": {"all": [{"fact": "month", "path": "$.id", "operator": "truthy"}]}, "event": [{"type": "uiOverride", "params": {"bank_account": {"ui:disabled": false}}}, {"type": "tooltip", "params": {"__title": "Opening Balance of {{month}} as per Ledger", "__field": ["opening_balance"], "month": {"fact": "month", "path": "$.title"}}}, {"type": "tooltip", "params": {"__title": "Reconcilied Closing Balance of {{month}} as per Ledger", "__field": ["reconcilied_closing_balance"], "month": {"fact": "month", "path": "$.title"}}}, {"type": "tooltip", "params": {"__title": "Closing Balance of {{month}} as per Ledger", "__field": ["closing_balance_as_per_ledger"], "month": {"fact": "month", "path": "$.title"}}}, {"type": "tooltip", "params": {"__title": "Closing Balance of {{month}} as per Bank Statement", "__field": ["closing_balance_as_per_bank_statement"], "month": {"fact": "month", "path": "$.title"}}}, {"type": "tooltip", "params": {"__title": "Reconcilied Closing Balance of {{month}}", "__field": ["reconcilied_closing_balance"], "month": {"fact": "month", "path": "$.title"}}}, {"type": "tooltip", "params": {"__title": "Closing Balance of {{month}} as per Ledger", "__field": ["closing_balance_as_per_ledger"], "month": {"fact": "month", "path": "$.title"}}}]}, {"conditions": {"all": [{"fact": "displayTable", "path": "meta", "operator": "truthy"}]}, "event": [{"type": "uiSchemaReplace", "params": {"table.ui:options.tableSchema": {"fact": "displayTable", "path": "meta"}}}, {"type": "update", "params": {"table": {"fact": "displayTable", "path": "data"}}}]}, {"conditions": {"all": [{"fact": "fetchBalances", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"opening_balance": {"fact": "fetchBalances", "path": "openingBalOfYearByLedger"}, "reconcilied_closing_balance": {"fact": "fetchBalances", "path": "reconciledOpeningBalance"}, "closing_balance_as_per_ledger": {"fact": "fetchBalances", "path": "closingBalanceAsPerLedger"}, "closing_balance_as_per_bank_statement": {"fact": "fetchBalances", "path": "closing_balance_as_per_bank_statement"}}}]}], "actions": [{"title": "Save", "type": "submit", "redirect": "/admin/society/accounts/bankReconciliation?year=[financial_year]&month=[month]&bank_ledger_id=[bank_account]&bankClosingAmount=[closing_balance_as_per_bank_statement]"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/getAllLedgers"}], "flow": {"children": {"financial_year": {"title": "Financial Year", "type": "dropdown", "required": true, "apiPath": "admin/accountsetting/accountset", "labelKeys": ["year_financial"], "onMount": true, "dependent": [{"field": "fy_start_date"}, {"field": "fy_end_date"}, {"field": "id", "key": "year_financial"}]}, "month": {"title": "Month", "required": true, "disabled": true, "type": "dropdown", "placeholder": "Select Month"}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "placeholder": "Please Select Bank Account", "onMount": true, "disabled": true}, "opening_balance": {"title": "Opening Balance", "type": "number", "required": true, "description": "As <PERSON>", "disabled": true}, "reconcilied_closing_balance": {"title": "Reconcilied Closing Balance", "type": "number", "required": true, "disabled": true}, "closing_balance_as_per_ledger": {"title": "Closing Balance", "type": "number", "required": true, "description": "As <PERSON>", "disabled": true}, "closing_balance_as_per_bank_statement": {"title": "Closing Balance", "type": "number", "required": true, "description": "As Per Bank Statement", "placeholder": "Enter Closing Balance As Per Bank Statement"}, "table": {"title": "Last Receipts", "type": "table", "selectable": false, "default": []}}}}}