{"meta": {"title": "Edit Cash A/C", "data_source": "accounts/editCashAccount/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "accounts/editCashAccount", "redirect": "/admin/society/accounts/viewCashAccounts"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/viewCashAccounts"}], "flow": {"children": {"bank_name": {"title": "Cash Name", "type": "string", "required": true}, "ledger_account_name": {"title": "Corresponding ledger", "type": "string", "default": "Cash in Hand", "disabled": true}}}}}