{"meta": {"title": "Create Group"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "select_group", "path": "nature_of_account", "operator": "equal", "value": "dr"}]}, "event": [{"type": "update", "params": {"nature": "Debit"}}]}, {"conditions": {"any": [{"fact": "select_group", "path": "nature_of_account", "operator": "equal", "value": "cr"}]}, "event": [{"type": "update", "params": {"nature": "Credit"}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "accounts/postNewGroup", "redirect": "/admin/society/accounts/viewGroups"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/viewGroups"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "accounts/postNewGroup"}], "flow": {"title": "Group Details", "children": {"select_group": {"title": "Select group", "type": "dropdown", "apiPath": "/admin/accounts/viewGroups", "labelKeys": ["ledger_account_name"], "dependent": [{"field": "nature_of_account"}], "onMount": true, "placeholder": "Please select a group"}, "ledger_account_name": {"title": "Group Name", "type": "string", "required": true}, "nature": {"title": "Nature of Group", "type": "string", "default": "You should select a Group first", "disabled": true}}}}}