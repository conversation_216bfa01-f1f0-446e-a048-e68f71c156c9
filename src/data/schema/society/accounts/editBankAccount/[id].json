{"meta": {"title": "Bank A/c Details", "data_source": "accounts/editBankAccount/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "accounts/editBankAccount", "redirect": "/admin/society/accounts/viewBankAccounts"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/viewBankAccounts"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "/accounts/editBankAccount", "redirect": "/admin/society/accounts/editBankAccount"}], "flow": {"children": {"bank_name": {"title": "Bank Name", "type": "string"}, "account_number": {"title": "Account Number", "type": "number", "required": true, "pattern": "[0-9]{9,18}"}, "bank_address": {"title": "Bank Address", "type": "textarea"}, "bank_city": {"title": "Bank City", "type": "string"}, "account_name": {"title": "Account Name", "type": "string"}, "branch": {"title": "Branch Name", "type": "string"}, "bank_ifsc": {"title": "IFs Code", "type": "string"}, "default": {"title": "<PERSON><PERSON><PERSON>", "type": "boolean"}, "default_bank_for_incidental": {"title": "Default Bank For Incidental", "type": "boolean"}, "default bank_for_nonmember": {"title": "Default Bank For Nonmember", "type": "boolean"}}}}}