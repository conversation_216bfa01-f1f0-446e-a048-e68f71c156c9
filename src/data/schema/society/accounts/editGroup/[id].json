{"meta": {"title": "Edit Group", "data_source": "/accounts/getGroupById/:id"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "nature_of_account", "operator": "equal", "value": "dr"}]}, "event": [{"type": "update", "params": {"nature_of_account": "debit"}}]}, {"conditions": {"any": [{"fact": "nature_of_account", "operator": "equal", "value": "cr"}]}, "event": [{"type": "update", "params": {"nature_of_account": "credit"}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "/accounts/editGroup", "redirect": "/admin/society/accounts/viewGroups"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/viewGroups"}, {"title": "Reset", "type": "reset"}], "flow": {"children": {"ledger_account_id": {"title": "Select group", "type": "dropdown", "required": true, "apiPath": "/admin/accounts/viewGroups", "labelKeys": ["ledger_account_name"], "dependent": [{"field": "nature_of_account"}], "onMount": true, "placeholder": "Please select a group"}, "ledger_account_name": {"title": "Group Name", "type": "string", "required": true}, "nature_of_account": {"title": "Nature of Group", "type": "string", "disabled": true}}}}}