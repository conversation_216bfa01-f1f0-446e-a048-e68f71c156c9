{"meta": {"title": "<PERSON>", "data_source": "accounts/getLedgerById/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "accounts/editLedger", "redirect": "/admin/society/accounts/getAllLedgers"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/getAllLedgers"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "accounts/editLedger"}], "flow": {"children": {"context": {"title": "Select group", "disabled": true, "required": true, "type": "string"}, "ledger_account_name": {"title": "Ledger Name", "default": "Non occupancy charges", "disabled": true}, "opning_balance": {"title": "Opning Balance", "type": "number", "required": true}, "name_of_grp": {"title": "Financial Year", "default": "Credit", "disabled": true}}}}}