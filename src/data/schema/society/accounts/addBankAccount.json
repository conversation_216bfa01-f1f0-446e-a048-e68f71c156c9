{"meta": {"title": "New Bank A/c"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "accounts/addBankAccount", "redirect": "/admin/society/accounts/viewBankAccounts"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/viewBankAccounts"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "/api/roles"}], "flow": {"required": ["bank_name", "account_number", "bank_address", "opening_balance"], "children": {"bank_name": {"title": "Bank Name", "type": "string"}, "account_number": {"title": "Account Number", "type": "number"}, "bank_address": {"title": "Bank Address", "type": "textarea"}, "bank_city": {"title": "Bank City", "type": "string"}, "account_name": {"title": "Account Name", "type": "string"}, "branch": {"title": "Branch Name", "type": "string"}, "bank_ifsc": {"title": "IFs Code", "type": "string"}, "default_bank": {"title": "<PERSON><PERSON><PERSON>", "type": "boolean"}, "default_bank_for_incidental": {"title": "Default Bank For Incidental            ", "type": "boolean"}, "default_bank_for_nonmember": {"title": "Default Bank For Nonmember", "type": "boolean"}, "opening_balance": {"title": "Opening Balance            ", "type": "string", "default": "0.00"}}}}}