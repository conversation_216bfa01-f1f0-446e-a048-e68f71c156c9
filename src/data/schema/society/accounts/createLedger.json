{"meta": {"title": "New Ledger"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "lstParentAccount", "operator": "truthy"}]}, "event": [{"type": "remove", "params": {"field": ["type_of_nature"]}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "nature_of_account", "operator": "equal", "value": "dr"}]}, "event": [{"type": "update", "params": {"nature": "dr"}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "nature_of_account", "operator": "equal", "value": "cr"}]}, "event": [{"type": "update", "params": {"nature": "cr"}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "accounts/createLedger", "redirect": "/admin/society/accounts/getAllLedgers"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "accounts/createLedger", "redirect": "/admin/society/accounts/createLedger"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/getAllLedgers"}], "flow": {"children": {"lstParentAccount": {"title": "Select group", "type": "dropdown", "required": true, "apiPath": "admin/accounts/viewGroups", "labelKeys": ["ledger_account_name"], "onMount": true, "dependent": [{"field": "nature_of_account"}]}, "ledger_account_name": {"title": "Ledger Name", "required": true}, "opning_balance": {"title": "Opening Balance", "type": "number"}, "type_of_nature": {"title": "Nature of Group", "disabled": true, "default": "You should select a Group first", "condition": {"lstParentAccount": [""]}}, "nature": {"title": "Nature of Account", "type": "radio", "enum": [{"title": "Debit", "const": "dr"}, {"title": "Credit", "const": "cr"}], "condition": {"lstParentAccount": ["*"]}, "default": null}}}}}