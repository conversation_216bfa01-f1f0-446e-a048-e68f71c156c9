{"meta": {"title": "New Purchase/Work Order"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "purchaseform/add", "redirect": "/admin/society/purchaseform/list/"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "purchaseform/add", "redirect": "/admin/society/purchaseform/add"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/purchaseform/list"}], "rules": [{"conditions": {"all": [{"fact": "purchase_order_items", "operator": "truthy", "value": true}]}, "event": [{"type": "calculate", "params": {"costs": {"fact": "purchase_order_items", "path": "$..item_cost"}, "quantities": {"fact": "purchase_order_items", "path": "$..item_quantity"}, "__field": "purchase_form_amount", "__exp": "sum(dotMultiply(costs, quantities))"}}]}], "flow": {"title": "PO/WO Details", "children": {"purchase_form_type": {"title": "<PERSON><PERSON>", "type": "radio", "required": true, "enum": [{"const": "PO", "title": "Purchase Order"}, {"const": "WO", "title": "Work Order"}], "default": "PO"}, "purchase_form_title": {"title": "Title", "type": "string", "placeholder": "Enter Title", "required": true}, "purchase_form_desc": {"title": "Description", "type": "textarea", "placeholder": "Enter Description", "required": true}, "purchase_form_vendor": {"title": "<PERSON><PERSON><PERSON>", "type": "dropdown", "apiPath": "/admin/vendor/viewVendorDropdown", "required": true, "onMount": true, "labelKeys": ["vendor_name"], "endAdornment": {"icon": "mdi:plus", "title": "New", "form": "vendor/addVendor"}}, "purchase_order_items": {"title": "PO Items", "type": "array", "layout": "tabular", "required": true, "children": {"item_name": {"title": "<PERSON><PERSON>"}, "item_cost": {"title": "Price", "type": "number"}, "item_quantity": {"title": "Quantity", "type": "number", "default": 0}}, "default": [{"item_cost": 0, "item_quantity": 0}]}, "purchase_form_amount": {"title": "Total Amount", "disabled": true, "type": "number", "default": 0, "required": true}, "purchase_form_approvers": {"title": "Select Approvers", "type": "dropdown", "multiple": "true", "onMount": true, "required": true, "apiPath": "/admin/helpdesk/add/staff_customer_tree", "labelKeys": ["staff_first_name", "ledger_account_name"]}, "purchase_form_reviewers": {"title": "Select Reviewers", "type": "dropdown", "multiple": "true", "onMount": true, "required": true, "apiPath": "/admin/helpdesk/add/staff_customer_tree", "labelKeys": ["staff_first_name", "ledger_account_name"]}, "attachment": {"title": "Attachment", "type": "file"}}}}}