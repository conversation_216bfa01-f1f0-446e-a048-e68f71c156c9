{"meta": {"title": "Incident Receipt"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "common-billing/paycommonbill", "redirect": "admin/ncome-details/incomepaymenttracker"}], "facts": [{"fact": "sequences", "path": "/admin/income-details/getReceiptListLatest/:id", "xpath": "$.id", "method": "GET", "dependsOn": "unit_id", "returns": "all"}], "rules": [{"conditions": {"any": [{"fact": "suspense_receipt", "operator": "truthy"}]}, "event": [{"type": "remove", "params": {"field": ["unit_id", "member", "total_due_amount", "sequence"]}}]}, {"conditions": {"any": [{"fact": "suspense_receipt", "operator": "falsy"}]}, "event": [{"type": "remove", "params": {"field": ["mode_of_payment"]}}]}, {"conditions": {"any": [{"fact": "unit_id", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"member_id": {"fact": "unit_id", "path": "$..member_id"}, "total_due_amount": {"fact": "unit_id", "path": "$..total_due_amount"}, "invoice_amount": {"fact": "unit_id", "path": "$..total_due_amount"}, "received_from": {"fact": "unit_id", "path": "$..member_name"}, "member": {"fact": "unit_id", "path": "$..unit_building_name"}}}]}, {"conditions": {"any": [{"fact": "unit_id", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"total_due_amount": 0, "invoice_amount": 0, "received_from": "", "member": ""}}, {"type": "remove", "params": {"field": ["sequence", "receipt_mode"]}}]}, {"conditions": {"all": [{"fact": "show_tds", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"tds_amount": 0}}, {"type": "remove", "params": {"field": ["tds_amount"]}}, {"type": "uiOverride", "params": {"vendor_bill_advance_amount": {"ui:disabled": false}}}]}, {"conditions": {"all": [{"fact": "show_tds", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"vendor_bill_advance_amount": 0}}, {"type": "require", "params": {"field": ["tds_amount"]}}]}, {"conditions": {"any": [{"fact": "receipt_mode", "operator": "equal", "value": "cashtransfer"}]}, "event": [{"type": "remove", "params": {"field": ["cheque_number", "bank_name", "cheque_date"]}}]}, {"conditions": {"any": [{"fact": "receipt_mode", "operator": "equal", "value": "cash"}, {"fact": "receipt_mode", "operator": "falsy"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "cheque_number", "bank_name", "cheque_date", "transaction_reference"]}}]}, {"conditions": {"any": [{"fact": "receipt_mode", "operator": "equal", "value": "cheque"}]}, "event": [{"type": "remove", "params": {"field": ["transaction_reference"]}}]}, {"conditions": {"all": [{"fact": "sequences", "operator": "notEqual", "value": null}]}, "event": [{"type": "uiSchemaReplace", "params": {"sequence.ui:options.tableSchema": {"fact": "sequences", "path": "meta"}}}, {"type": "update", "params": {"sequence": {"fact": "sequences", "path": "data"}}}]}], "flow": {"children": {"suspense_receipt": {"title": "Is Suspense Receipt ?", "type": "boolean", "enum": [{"const": true, "title": "Yes"}, {"const": false, "title": "No"}], "default": false}, "unit_id": {"title": "Enter a Member or Unit", "type": "dropdown", "apiPath": "admin/common-billing/listcommonbill", "labelKeys": ["member_name"], "dependent": [{"field": "unit_building_name"}, {"field": "total_due_amount"}, {"field": "member_name"}, {"field": "member_id"}]}, "member_id": {"type": "number", "hidden": true}, "member": {"title": "Unit Details", "type": "string", "disabled": true}, "received_from": {"title": "Received From", "type": "string", "required": true}, "total_due_amount": {"title": "Total Due", "type": "number", "default": 0.0, "disabled": true}, "invoice_amount": {"title": "Amount", "type": "number", "default": 0.0, "endAdornment": [{"title": "TDS", "icon": "ri-add-line", "showButton": true, "update": {"key": "show_tds", "value": true}}]}, "tds_amount": {"title": "TDS Amount", "type": "number", "description": "", "endAdornment": [{"title": "Remove TDS", "icon": "ri-close-line", "update": {"key": "show_tds", "value": false}}]}, "mode_of_payment": {"title": "Mode of Payment", "type": "select", "enum": [{"const": "cashtransfer", "title": "Electronic Fund Transfer"}], "required": true}, "show_tds": {"type": "radio", "hidden": true, "default": false}, "receipt_mode": {"title": "Mode of Payment", "type": "select", "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}], "required": true, "default": "cash"}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "placeholder": "Please Select Bank Account", "onMount": true}, "transaction_reference": {"title": "Payment Reference", "type": "string", "required": true}, "cheque_number": {"title": "Cheque Number", "type": "string", "description": "", "required": true}, "bank_name": {"title": "Bank & Branch Name", "type": "string", "description": "", "required": true}, "cheque_date": {"title": "Cheque Date", "type": "date", "maxDateTime": "now", "required": true}, "receipt_date": {"title": "Receipt Date", "type": "date", "maxDateTime": "now", "required": true}, "payment_note": {"title": "Receipt Note", "type": "textarea"}, "sequence": {"title": "Last Receipts", "type": "table", "selectable": false, "default": []}}}}}