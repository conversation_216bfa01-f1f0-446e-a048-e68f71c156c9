{"meta": {"formId": "app_preferences", "title": "App Preferences", "api_path": "/api/schema"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "notices/add_notice", "redirect": "/admin/society/notices/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/notices/list"}], "flow": {"title": "Notice / Circular / MoM / Announcement / Survey", "required": ["type", "email_address", "parking_type", ""], "children": {"type": {"title": "Type", "type": "radio", "enum": ["Notice", "Circular", "MoM", "Announcement", "Survey"]}, "templates": {"title": "Templates", "type": "dropdown", "apiPath": "admin/notices/list_template", "keys": ["subject"], "dependant": [{"field": "subject", "keys": "subject"}]}, "subject": {"title": "Subject", "type": "string", "apiPath": "admin/notices/list"}, "body": {"title": "body", "type": "richtext"}, "effective_from": {"title": "Effective From", "type": "date"}, "published_on": {"title": "Published On", "type": "date"}, "nofity_via": {"title": "Notify Via", "type": "checkbox", "enum": ["Email", "SMS"], "children": {"SMS": {"": {"title": "SMS Value", "type": "radio", "enum": ["Default SMS", "Custom SMS"]}}}}, "send_to": {"title": "Send To", "type": "radio", "enum": ["Committee Members", "All Members", "Individual Members", "Overdue Members"], "children": {"Individual Members": {"Members": {"title": "Members", "type": "select", "enum": ["Select some options", "All", "Owner", "Tenant", "Owner & Tenant"]}}}}, "visibility": {"title": "Attachment", "type": "file"}}}}}