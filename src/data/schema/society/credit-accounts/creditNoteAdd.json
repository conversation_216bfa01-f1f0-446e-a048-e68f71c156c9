{"meta": {"title": "Credit Note"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "credit-accounts/creditNoteAdd", "redirect": "/admin/society/credit-accounts/creditNote"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/credit-accounts/creditNote"}], "facts": [{"fact": "member_invoices_list", "path": "/admin/income/getInvoices?account_type=member&account_id=[account]&bill_type=[id]", "method": "GET", "dependsOn": "bill_type", "params": {"account": {"fact": "account_id", "path": "unit_id"}}}, {"fact": "nonmember_invoices_list", "path": "/admin/income/getInvoices?account_type=non_member&account_id=[account_id]&bill_type=[bill_type]", "method": "GET", "dependsOn": "account_id", "xpath": "$.id", "allowOn": {"account_type": "non_member"}}, {"fact": "sequences", "path": "/admin/credit-accounts/creditNote/invoiceParticular/:id", "xpathx": "$.id", "method": "GET", "dependsOnx": "invoice_id", "dependsOn": "due_amount", "returns": "all"}], "rules": [{"conditions": {"all": [{"fact": "account_id", "path": "id", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"bill_type": null, "invoice_id": null}}]}, {"conditions": {"all": [{"fact": "account_id", "path": "id", "operator": "truthy"}, {"fact": "account_type", "operator": "equal", "value": "member"}]}, "event": [{"type": "uiOverride", "params": {"bill_type": {"ui:disabled": false}}}, {"type": "update", "params": {"unit_id": {"fact": "account_id", "path": "unit_id"}}}]}, {"conditions": {"all": [{"fact": "account_id", "path": "unit_id", "operator": "truthy"}, {"fact": "bill_type", "operator": "truthy"}, {"fact": "account_type", "operator": "equal", "value": "member"}]}, "event": [{"type": "uiOverride", "params": {"invoice_id": {"ui:disabled": false}}}, {"type": "uiSchemaReplace", "params": {"invoice_id.ui:options.loadedOptions": {"fact": "member_invoices_list"}}}]}, {"conditions": {"all": [{"fact": "account_id", "path": "id", "operator": "truthy"}, {"fact": "account_type", "operator": "equal", "value": "nonmember"}]}, "event": [{"type": "uiOverride", "params": {"invoice_id": {"ui:disabled": false}}}, {"type": "uiSchemaReplace", "params": {"invoice_id.ui:options.loadedOptions": {"fact": "nonmember_invoices_list"}}}]}, {"conditions": {"any": [{"fact": "account_type", "operator": "equal", "value": "nonmember"}]}, "event": [{"type": "remove", "params": {"field": ["bill_type"]}}]}, {"conditions": {"all": [{"fact": "bill_type", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"invoice_id": null}}]}, {"conditions": {"all": [{"fact": "invoice_id", "operator": "truthy"}]}, "event": [{"type": "remove", "params": {"field": ["particular_data"]}}]}, {"conditions": {"all": [{"fact": "sequences", "operator": "truthy"}]}, "event": [{"type": "uiSchemaReplace", "params": {"particular_data.ui:options.tableSchema": {"fact": "sequences", "path": "meta"}}}, {"type": "update", "params": {"particular_data": {"fact": "sequences", "path": "data"}}}]}], "flow": {"children": {"account_type": {"title": "Account", "type": "radio", "required": true, "enum": [{"const": "member", "title": "Member"}, {"const": "nonmember", "title": "Non Member"}], "default": "member"}, "account_id": {"title": "Name", "type": "autocomplete", "required": true, "description": "Search Member Name, Unit Number, or Building Name", "apiPath": "/admin/income/getMembers", "defaultParams": ["account_type"], "labelKeys": ["title"], "dependent": [{"field": "unit_id"}, {"field": "account_type"}]}, "unit_id": {"title": "Unit", "disabled": true, "type": "number", "hidden": true}, "bill_type": {"title": "<PERSON>", "type": "select", "required": true, "disabled": true, "placeholder": "Select Bill Type", "enum": [{"const": "incidental", "title": "Incidental"}, {"const": "maintenance", "title": "Maintenance"}]}, "invoice_id": {"title": "Invoice Number", "type": "dropdown", "onMount": true, "labelKeys": ["combined_range"], "disabled": true, "required": true}, "payment_date": {"title": "Rectification Date", "type": "date", "required": true}, "narration": {"title": "Narration", "type": "textarea", "required": true}, "due_amount": {"title": "Due Amount", "type": "number", "required": true, "default": 39}, "particular_data": {"title": "Last Receipts", "type": "table", "selectable": false, "editable": false, "default": [], "dependent": [{"field": "due_amount"}]}}}}}