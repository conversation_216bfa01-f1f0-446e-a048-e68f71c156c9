{"meta": {"title": "New Advances"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "credit-accounts/add", "redirect": "/admin/society/credit-accounts/memberAdvances", "default_params": [{"used_for": "maintenance", "account_context": "unit"}]}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/credit-accounts/memberAdvances"}], "rules": [{"conditions": {"any": [{"fact": "credit_used_type", "operator": "equal", "value": "refundable"}]}, "event": [{"type": "remove", "params": {"field": ["adjustable_date", "adjustable_amount", "adjust_against", "refundable_amount"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "falsy"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "transaction_reference", "bank_name_branch", "payment_instrument", "payment_reference"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cashtransfer"}]}, "event": [{"type": "remove", "params": {"field": ["bank_name_branch", "cheque_payment_date", "payment_instrument", "transaction_reference"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cheque"}]}, "event": [{"type": "remove", "params": {"field": ["payment_instrument", "payment_reference"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "online"}]}, "event": [{"type": "remove", "params": {"field": ["cheque_payment_date", "bank_name_branch", "transaction_reference"]}}, {"type": "uiOverride", "params": {"payment_reference": {"ui:title": "Transaction Reference"}}}, {"type": "require", "params": {"field": ["payment_reference"]}}]}, {"conditions": {"any": [{"fact": "credit_used_type", "operator": "equal", "value": "adjustable"}]}, "event": [{"type": "remove", "params": {"field": ["refundable_amount", "adjustable_amount"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cashtransfer"}]}, "event": [{"type": "uiOverride", "params": {"payment_reference": {"ui:options": {"required": true}}}}]}, {"conditions": {"any": [{"fact": "refundable_amount", "operator": "isNumber"}, {"fact": "adjustable_amount", "operator": "isNumber"}]}, "event": [{"type": "calculate", "params": {"adjusable_amount": {"fact": "adjustable_amount"}, "refundable_amount": {"fact": "refundable_amount"}, "__exp": "refundable_amount + adjusable_amount", "__field": "payment_amount"}}]}, {"conditions": {"any": [{"fact": "account_id", "path": "account_type", "operator": "notEqual", "value": {"fact": "account_type"}}]}, "event": [{"type": "update", "params": {"account_id": null}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}]}, "event": [{"type": "remove", "params": {"field": ["transaction_reference", "payment_reference", "bank_name_branch", "payment_instrument"]}}]}], "flow": {"children": {"account_type": {"title": "Account", "type": "radio", "enum": [{"const": "member", "title": "Member"}, {"const": "nonmember", "title": "Non Member"}], "default": "member", "required": true}, "account_id": {"title": "Name", "required": true, "type": "dropdown", "placeholder": "Please Search  Name", "apiPath": "admin/income/getMembers?account_type=:account_type", "onMount": false, "dependent": [{"field": "account_type"}]}, "narration": {"title": "Narration", "type": "textarea", "placeholder": "Please Enter Narration", "required": true}, "credit_used_type": {"title": "Type", "type": "radio", "required": true, "enum": [{"const": "refundable", "title": "Refundable"}, {"const": "adjustable", "title": "Adjustable"}, {"const": "both", "title": "Refundable & Adjustable"}], "default": "refundable"}, "adjustable_date": {"title": "Adjust After Date", "type": "date", "required": true}, "adjust_against": {"title": "Adjust Against", "type": "select", "required": true, "enum": [{"const": "maintenance", "title": "Maintenance"}, {"const": "incidental", "title": "Incidental"}], "default": "incidental"}, "payment_mode": {"title": "Receipt Mode", "type": "select", "required": true, "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}], "default": "cash"}, "refundable_amount": {"title": "Refundable Amount", "type": "number", "min": 0, "required": true}, "adjustable_amount": {"title": "Adjustable Amount", "type": "number", "min": 0, "required": true}, "payment_amount": {"title": "Amount", "type": "number", "min": 0, "required": true}, "payment_date": {"title": "Receipt Date", "type": "date", "required": true, "maxDateTime": "now"}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "/admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "onMount": true}, "transaction_reference": {"title": "Transaction Reference (Cheque number)", "required": true, "pattern": "^\\d{6}$"}, "payment_reference": {"title": "Payment Reference", "type": "string", "required": true, "placeholder": "Enter Payment Reference"}, "bank_name_branch": {"title": "Bank Name & Branch", "type": "string", "placeholder": "Enter Bank Name & Branch", "required": true}, "payment_instrument": {"title": "Payment Instrument Hello", "type": "string", "placeholder": "Enter Payment Instrument", "required": true}}}}}