{"meta": {"formId": "billable_items", "data_source": "credit-accounts/add/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "credit-accounts/add", "redirect": "/admin/society/credit-accounts/memberAdvances", "default_params": [{"used_for": "maintenance"}]}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/credit-accounts/memberAdvances"}], "rules": [{"conditions": {"any": [{"fact": "credit_used_type", "operator": "notEqual", "value": "adjustable"}]}, "event": [{"type": "remove", "params": {"field": ["adjust_after_date"]}}]}], "flow": {"title": "Edit Advances", "children": {"account_name": {"title": "Name", "type": "string", "required": true, "disabled": true}, "narration": {"title": "Narration", "type": "textarea", "required": true, "disabled": true}, "credit_used_type": {"title": "Type", "type": "radio", "required": true, "enum": [{"const": "refundable", "title": "Refundable"}, {"const": "adjustable", "title": "Adjustable"}], "default": "refundable"}, "adjust_after_date": {"title": "Adjust After Date", "type": "date", "required": true}, "amount": {"title": "Amount", "type": "number", "required": true, "startAdornment": "₹", "disabled": true}, "payment_date": {"title": "Date", "type": "date", "required": true, "disabled": true}}}}}