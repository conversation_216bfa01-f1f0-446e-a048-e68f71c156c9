{"meta": {"formId": "new_canned_responses", "title": "New Canned Response"}, "schema": {"flow": {"title": "New Canned Responses", "required": [], "children": {"title": {"title": "Title", "type": "string"}, "email_response*": {"title": "Email Response", "type": "richtext"}, "sms_response*": {"title": "SMS Response", "type": "string"}, "status": {"title": "Status", "type": "radio", "enum": ["resolved", "closed", "open", "On Hold"]}}}}}