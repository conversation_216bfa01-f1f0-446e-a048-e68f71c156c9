{"meta": {"title": "New Vendors"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "vendor/addVendor", "redirect": "/admin/society/vendor/viewVendor"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "vendor/addVendor"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/vendor/viewVendor"}], "facts": [{"fact": "wallet_balance", "path": "https://jsonplaceholder.typicode.com/todos/", "method": "GET", "dependsOn": "vendor_details", "xpath": "$.vendor_name", "params": {"id": 1, "name": "test"}}], "rules": [{"conditions": {"all": [{"fact": "vendor_details", "path": "$.vendor_name", "operator": "truthy", "value": true}, {"fact": "wallet_balance", "path": "$.id", "operator": "truthy", "value": true}]}, "event": {"type": "tooltip", "params": {"__title": "Your **{{bank_name}}** bank balance is currency({{wallet_balance}})`", "bank_name": {"fact": "wallet_balance", "path": "$.title"}, "wallet_balance": {"fact": "wallet_balance", "path": "$.id"}, "__field": ["vendor_details", "vendor_name"]}}}, {"conditions": {"all": [{"fact": "credit_period", "operator": "inRange", "value": [0, 20]}, {"fact": "wallet_balance", "operator": "greaterThan", "value": 0, "path": "$.id"}]}, "event": {"type": "calculate", "params": {"hello": {"fact": "credit_period"}, "wallet": {"fact": "wallet_balance", "path": "$.id"}, "vendor": {"fact": "vendor", "path": "$.sample2.kk2"}, "__exp": "hello + wallet - vendor"}}}], "flow": {"title": "Vendors Details", "children": {"vendor": {"title": "sample", "layout": "tabs", "description": "sample", "children": {"sample1": {"title": "sample1", "layout": "accordion", "description": "sample1 the form", "children": {"kk": {"title": "kk", "layout": "tabs", "children": {"kk1": {"title": "kk1", "type": "number", "endAdornment": "Days"}, "kk2": {"title": "kk2", "tooltip": {"title": "Your {{label}} bank balance is {{ledger_amount}}", "url": "/admin/society/vendorbill/fetchDataAddVendorPayment/:kk1", "onMount": true}}}}, "ll": {"title": "ll", "layout": "card", "children": {"ll1": {"title": "ll1", "type": "select", "enum": ["2", "5"], "children": {"2": {"title": "hell", "children": {"hhhhh": {"title": "oiiiiiiii"}}}}}, "ll2": {"title": "ll2"}}}}}, "sample2": {"title": "sample2", "description": "sample2 the form2", "layout": "accordion", "children": {"kk2": {"title": "kk2"}, "ll2": {"title": "ll2"}}}}}, "tabs": {"title": "Tabs", "layout": "tabs", "children": {"tab1": {"title": "Tab1", "children": {"tab1_1": {"title": "Tab1_1", "type": "number"}, "tab1_2": {"title": "Tab1_2", "type": "number"}}}, "tab2": {"title": "Tab2", "children": {"tab2_1": {"title": "Tab2_1", "type": "number", "required": true}, "tab2_2": {"title": "Tab2_2", "type": "number", "required": true}}}}}, "vendor_details": {"title": "<PERSON><PERSON><PERSON>", "children": {"vendor_name": {"title": "Vendor Name", "placeholder": "<PERSON>ter Vendor Name", "type": "string", "enableMD": true}, "vendor_contact_number": {"title": "Vendor Mobile No", "placeholder": "Enter Vendor Mobile No", "type": "number"}, "vendor_phone_number": {"title": "Vendor Phone", "placeholder": "<PERSON>ter Vendor Phone", "type": "number"}, "vendor_email": {"title": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON>", "type": "string"}, "notes": {"title": "Notes", "placeholder": "Enter Notes", "type": "textarea"}, "credit_period": {"title": "Credit Period", "placeholder": "Enter Credit Period", "type": "number", "endAdornment": "Days"}}}, "tax_details": {"title": "Tax Details", "children": {"gst_no": {"title": "GST NO", "placeholder": "Enter GST NO", "pattern": "[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}", "endAdornment": "Hello"}, "pan_no": {"title": "PAN No", "placeholder": "Enter PAN No", "pattern": "[A-Z]{5}[0-9]{4}[A-Z]{1}", "startAdornment": {"icon": "ri:account-circle-2-line", "title": "Oii", "form": "newVendor"}}, "tds": {"title": "TDS", "type": "dropdown", "enableMD": true, "description": "> `TDS Rate` is auto calculated based on `Section?`", "apiPath": "/admin/expensetracker/expensetdsrate", "labelKeys": ["section"], "onMount": true}, "place_of_supply": {"title": "Place of Supply", "type": "string", "default": "27-Maharashtra", "disabled": true}}}, "payee_details": {"title": "Payee Details", "children": {"vendor_payee_name": {"title": "Payee Name", "placeholder": "Enter Payee Name", "required": true}, "vendor_address": {"title": "<PERSON><PERSON>or Address", "placeholder": "<PERSON><PERSON>or Address", "type": "string", "required": true}}}}}}}