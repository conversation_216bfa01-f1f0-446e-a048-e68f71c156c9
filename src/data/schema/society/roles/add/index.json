{"meta": {"title": "New Role"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "roles/add", "default_params_values": {"status": "status"}, "redirect": "/admin/society/roles/listSocRoles"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/roles/listSocRoles"}], "flow": {"order": ["role_display_name", "role_name", "role_type", "status", "sample", "*"], "title": "Details", "required": ["role_name", "role_display_name", "role_type", "status"], "children": {"role_name": {"title": "Role Name", "placeholder": "Enter Role Name", "max": 10, "min": 3}, "role_display_name": {"title": "Role Display Name", "placeholder": "Enter Role Display Name", "max": 20, "min": 3}, "role_type": {"title": "Role Type", "type": "select", "enum": [{"const": "admin", "title": "Admin"}, {"const": "user", "title": "User"}], "default": "admin"}, "status": {"title": "Status", "type": "select", "enum": [{"const": 1, "title": "Active"}, {"const": 0, "title": "Inactive"}], "default": 0}}}}}