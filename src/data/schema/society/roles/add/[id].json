{"meta": {"title": "New Role", "data_source": "roles/add/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "roles/add", "redirect": "/admin/society/roles/listSocRoles/"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/roles/listSocRoles/"}], "flow": {"title": "Details", "children": {"role_name": {"title": "Role Name", "type": "string", "required": true, "max": 10, "min": 3}, "role_display_name": {"title": "Role Display Name", "type": "string", "required": true, "max": 20, "min": 3}, "role_type": {"title": "Role Type", "type": "select", "required": true, "enum": [{"const": "admin", "title": "Admin"}, {"const": "user", "title": "User"}]}, "status": {"title": "Status", "type": "select", "required": true, "enum": [{"const": 0, "title": "Inactive"}, {"const": 1, "title": "Active"}]}}}}}