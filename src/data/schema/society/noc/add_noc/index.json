{"meta": {"title": "Add/Edit NOC"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "purpose", "operator": "falsy"}]}, "event": {"type": "remove", "params": {"field": ["society_name", "society_registration_no", "address_line_1", "address_line_2", "letter_date", "aaplier_name", "relation_with_owner", "owner_name", "owner_address", "since_staying_date", "HIM_HER", "HIS_HER", "society_city", "authorized_signatory", "bank_name", "bank_address", "flat_number", "cc_number", "cc_expiry_date", "flat_cost", "purches_name", "purches_address", "flat_name", "current_electricity_name", "HE_SHE", "tenant"]}}}, {"conditions": {"any": [{"fact": "purpose", "path": "purpose", "operator": "equal", "value": "NOC Bank Loan"}]}, "event": {"type": "update", "params": {"field": ["authorized_signatory", "bank_name", "bank_address", "flat_number", "HE_SHE", "aaplier_name", "relation_with_owner", "owner_address", "since_staying_date", "HIM_HER", "HIS_HER", "society_city", "purches_address", "purches_name", "flat_name", "current_electricity_name", "tenant"]}}}, {"conditions": {"any": [{"fact": "purpose", "path": "purpose", "operator": "equal", "value": "NOC Passport"}]}, "event": {"type": "remove", "params": {"field": ["authorized_signatory", "bank_name", "bank_address", "flat_number", "cc_number", "cc_expiry_date", "flat_cost", "purches_address", "purches_name", "flat_name", "current_electricity_name", "HE_SHE", "tenant"]}}}, {"conditions": {"any": [{"fact": "purpose", "path": "purpose", "operator": "equal", "value": "NOC Domicile"}]}, "event": {"type": "remove", "params": {"field": ["authorized_signatory", "bank_name", "bank_address", "flat_number", "cc_number", "cc_expiry_date", "flat_cost", "society_city", "purches_name", "purches_address", "flat_name", "current_electricity_name", "tenant"]}}}, {"conditions": {"any": [{"fact": "purpose", "path": "purpose", "operator": "equal", "value": "NOC Property Sale"}]}, "event": {"type": "remove", "params": {"field": ["authorized_signatory", "bank_name", "bank_address", "flat_number", "cc_number", "cc_expiry_date", "flat_cost", "aaplier_name", "relation_with_owner", "since_staying_date", "society_city", "flat_name", "current_electricity_name", "HIM_HER", "tenant"]}}}, {"conditions": {"any": [{"fact": "purpose", "path": "purpose", "operator": "equal", "value": "NOC Electricity Meter Change"}]}, "event": {"type": "remove", "params": {"field": ["aaplier_name", "relation_with_owner", "since_staying_date", "HIM_HER", "authorized_signatory", "bank_name", "bank_address", "flat_number", "cc_number", "cc_expiry_date", "flat_cost", "purches_address", "purches_name", "HE_SHE", "tenant"]}}}, {"conditions": {"any": [{"fact": "purpose", "path": "purpose", "operator": "equal", "value": "NOC Leave License"}]}, "event": {"type": "remove", "params": {"field": ["authorized_signatory", "bank_name", "bank_address", "flat_number", "cc_number", "aaplier_name", "relation_with_owner", "owner_address", "cc_expiry_date", "flat_cost", "purches_address", "purches_name", "HIS_HER", "HE_SHE", "since_staying_date", "society_city", "current_electricity_name"]}}}], "actions": [{"title": "Save", "type": "submit", "api_path": "noc/add_noc", "redirect": "/admin/society/noc/list"}, {"title": "Reset", "type": "reset"}, {"title": "NOC (Preview)", "type": "preview", "api_path": "noc/preview"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/noc/list"}], "flow": {"children": {"is_header_attached": {"title": "Attach header", "type": "radio", "required": true, "enum": [{"const": "yes", "title": "Yes"}, {"const": "no", "title": "No"}], "default": "yes"}, "unit_id": {"title": "Unit", "type": "dropdown", "required": true, "placeholder": "Search name", "apiPath": "/admin/income-details/incomemember", "labelKeys": ["display_member_name"]}, "status": {"title": "Status", "type": "select", "required": true, "enum": [{"const": 1, "title": "Unapproved"}, {"const": 2, "title": "Approved"}, {"const": 3, "title": "Rejected"}], "default": 1}, "purpose": {"title": "Templates", "type": "dropdown", "placeholder": "Select Template", "apiPath": "/admin/societies/nocForms", "labelKeys": ["purpose"], "default": "NOC Passport"}, "society_name": {"title": "Society Name", "type": "string", "required": true}, "society_registration_no": {"title": "Society Registration Number", "type": "string", "required": true, "default": "Reg001"}, "address_line_1": {"title": "Address Line 1", "type": "string", "required": true, "default": "5 <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "address_line_2": {"title": "Address Line 2", "type": "string", "default": "Sector 30, Vashi"}, "letter_date": {"title": "Letter Date", "type": "date", "required": true, "maxDateTime": "now"}, "authorized_signatory": {"title": "Authorized Signatory", "type": "string", "required": true}, "bank_name": {"title": "Bank Name", "type": "string", "required": true}, "bank_address": {"title": "Bank Address", "type": "string", "required": true}, "flat_number": {"title": "Flat Number", "type": "string", "required": true}, "aaplier_name": {"title": "Aaplier Name", "type": "string", "required": true, "default": "<PERSON><PERSON><PERSON>"}, "relation_with_owner": {"title": "Relation with Owner", "type": "string", "required": true, "default": "Relation with Owner"}, "owner_name": {"title": "Owner Name", "type": "string", "required": true, "default": "<PERSON><PERSON><PERSON>"}, "cc_number": {"title": "CC Number", "type": "string", "required": true}, "cc_expiry_date": {"title": "CC  Date", "type": "date", "required": true, "maxDateTime": "now"}, "flat_cost": {"title": "Flat Cost", "type": "number", "required": true}, "owner_address": {"title": "Owner Address", "type": "string", "required": true, "default": "TowerA-102 Futurescape Technologies Pvt Ltd,5 Pranavanadraj Marg, Sector 30, Vashi"}, "since_staying_date": {"title": "Since Staying Date", "type": "date", "required": true, "maxDateTime": "now"}, "HIM_HER": {"title": "Him/Her", "type": "radio", "required": true, "enum": [{"const": "him", "title": "Him"}, {"const": "her", "title": "Her"}], "default": "him"}, "purches_name": {"title": "Purches Name", "type": "string", "required": true}, "purches_address": {"title": "Purches Address", "type": "string", "required": true}, "HIS_HER": {"title": "His/Her", "type": "radio", "required": true, "enum": [{"const": "his", "title": "His"}, {"const": "her", "title": "Her"}], "default": "his"}, "HE_SHE": {"title": "He/She", "type": "radio", "required": true, "enum": [{"const": "he", "title": "He"}, {"const": "she", "title": "She"}], "default": "he"}, "society_city": {"title": "Society City", "type": "string", "required": true, "default": "Navi Mumbai"}, "flat_name": {"title": "Flat Name", "type": "string", "required": true}, "current_electricity_name": {"title": "Current Electricity Name", "type": "string", "required": true}, "tenant": {"title": "Tenant", "type": "string", "required": "true"}}}}}