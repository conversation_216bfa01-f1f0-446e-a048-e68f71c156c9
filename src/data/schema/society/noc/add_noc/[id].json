{"meta": {"title": "Edit NOC", "data_source": "noc/add_noc/:id"}, "schema": {"rules": [], "actions": [{"title": "Save", "type": "submit", "api_path": "noc/update", "redirect": "/admin/society/noc/list"}, {"title": "Reset", "type": "reset"}, {"title": "NOC (Preview)", "type": "preview", "api_path": "noc/preview"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/noc/list"}], "flow": {"children": {"is_header_attached": {"title": "Attach header", "type": "radio", "required": true, "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}]}, "unit_id": {"title": "Unit", "type": "dropdown", "required": true, "placeholder": "Search name", "apiPath": "/admin/income-details/incomemember", "labelKeys": ["display_member_name"]}, "status": {"title": "Status", "type": "select", "required": true, "enum": [{"const": 1, "title": "Unapproved"}, {"const": 2, "title": "Approved"}, {"const": 3, "title": "Rejected"}]}, "purpose": {"title": "Templates", "type": "string", "disabled": true}, "preview": {"title": "Body", "type": "textarea", "required": true, "rows": 10, "placeholder": "Enter HTML content for the NOC"}}}}}