{"meta": {"title": "Maintenance Receipt"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "income-details/createReceipt", "redirect": "admin/income-details/incomemember"}], "facts": [{"fact": "sequences", "path": "/admin/income-details/getReceiptListLatest/:id", "xpath": "$.id", "method": "GET", "dependsOn": "unit_key", "returns": "all"}], "rules": [{"conditions": {"any": [{"fact": "suspense_receipt", "operator": "truthy"}]}, "event": [{"type": "remove", "params": {"field": ["unit_key", "member", "total_unpaid_amount", "sequence"]}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"member_id": {"fact": "unit_key", "path": "$..member_id"}, "total_unpaid_amount": {"fact": "unit_key", "path": "$..due_amount"}, "payment_amount": {"fact": "unit_key", "path": "$..due_amount"}, "received_from": {"fact": "unit_key", "path": "$..member_name"}, "member": {"fact": "unit_key", "path": "$..display_member_name"}}}]}, {"conditions": {"any": [{"fact": "unit_key", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"total_unpaid_amount": 0, "payment_amount": 0, "received_from": "", "member": ""}}, {"type": "remove", "params": {"field": ["sequence", "payment_mode"]}}]}, {"conditions": {"all": [{"fact": "show_tds", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"tds_amount": 0}}, {"type": "remove", "params": {"field": ["tds_amount"]}}, {"type": "uiOverride", "params": {"vendor_bill_advance_amount": {"ui:disabled": false}}}]}, {"conditions": {"all": [{"fact": "show_tds", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"vendor_bill_advance_amount": 0}}, {"type": "require", "params": {"field": ["tds_amount"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cashtransfer"}]}, "event": [{"type": "remove", "params": {"field": ["cheque_number", "bank_name", "cheque_date"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cash"}, {"fact": "payment_mode", "operator": "falsy"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account", "cheque_number", "bank_name", "cheque_date", "transaction_reference"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "cheque"}]}, "event": [{"type": "remove", "params": {"field": ["transaction_reference"]}}]}, {"conditions": {"all": [{"fact": "sequences", "operator": "notEqual", "value": null}]}, "event": [{"type": "uiSchemaReplace", "params": {"sequence.ui:options.tableSchema": {"fact": "sequences", "path": "meta"}}}, {"type": "update", "params": {"sequence": {"fact": "sequences", "path": "data"}}}]}], "flow": {"children": {"suspense_receipt": {"title": "Is Suspense Receipt ?", "type": "boolean", "enum": [{"const": true, "title": "Yes"}, {"const": false, "title": "No"}], "default": false}, "unit_key": {"title": "Enter a Member or Unit", "type": "dropdown", "apiPath": "admin/income-details/incomemember", "labelKeys": ["member_name"], "dependent": [{"field": "display_member_name"}, {"field": "due_amount"}, {"field": "member_name"}, {"field": "member_id"}]}, "member_id": {"type": "number", "hidden": true}, "member": {"title": "Unit Details", "type": "string", "disabled": true}, "received_from": {"title": "Received From", "type": "string", "required": true}, "total_unpaid_amount": {"title": "Total Due", "type": "number", "default": 0.0, "disabled": true}, "payment_amount": {"title": "Amount", "type": "number", "default": 0.0, "endAdornment": [{"title": "TDS", "icon": "ri-add-line", "showButton": true, "update": {"key": "show_tds", "value": true}}]}, "tds_amount": {"title": "TDS Amount", "type": "number", "description": "", "endAdornment": [{"title": "Remove TDS", "icon": "ri-close-line", "update": {"key": "show_tds", "value": false}}]}, "show_tds": {"type": "radio", "hidden": true, "default": false}, "payment_mode": {"title": "Mode of Payment", "type": "select", "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}], "required": true, "default": "cash"}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "placeholder": "Please Select Bank Account", "onMount": true}, "transaction_reference": {"title": "Payment Reference", "type": "string", "required": true}, "cheque_number": {"title": "Cheque Number", "type": "string", "description": "", "required": true}, "bank_name": {"title": "Bank & Branch Name", "type": "string", "description": "", "required": true}, "cheque_date": {"title": "Cheque Date", "type": "date", "maxDateTime": "now", "required": true}, "payment_date": {"title": "Receipt Date", "type": "date", "maxDateTime": "now", "required": true}, "payment_note": {"title": "Receipt Note", "type": "textarea"}, "sequence": {"title": "Last Receipts", "type": "table", "selectable": false, "default": [], "dependent": [{"field": "due_amount"}]}}}}}