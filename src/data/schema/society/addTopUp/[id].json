{"meta": {"title": "Top Up"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "payment_mode", "operator": "equal", "value": "bank"}]}, "event": [{"type": "require", "params": {"field": ["bank_account"]}}]}, {"conditions": {"any": [{"fact": "payment_mode", "operator": "notEqual", "value": "bank"}]}, "event": [{"type": "remove", "params": {"field": ["bank_account"]}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "transaction/addTopupInterestTransaction/topup"}], "flow": {"children": {"transaction_date": {"title": "Transaction Date", "type": "date", "required": true}, "payment_mode": {"title": "Payment Mode", "type": "select", "enum": [{"const": "cash", "title": "Cash"}, {"const": "bank", "title": "Bank"}], "required": true, "default": "cash"}, "bank_account": {"title": "Bank Account", "type": "dropdown", "apiPath": "/admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "placeholder": "Please Select Bank Account", "onMount": true}, "amount": {"title": "Amount", "type": "number", "required": true}, "memo_description": {"title": "Memo Description", "type": "textarea"}}}}}