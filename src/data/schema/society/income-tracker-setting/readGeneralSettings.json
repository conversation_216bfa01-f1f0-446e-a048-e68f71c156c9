{"meta": {"data_source": "income-tracker-setting/readGeneralSettings"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "income-tracker-setting/readGeneralSettings"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/income-tracker-setting/readGeneralSettings"}], "rules": [{"conditions": {"any": [{"fact": "sample1", "path": "PARTICULAR_AMOUNT_ROUNDOFF", "operator": "equal", "value": "No"}]}, "event": [{"type": "remove", "params": {"field": ["sample1.PARTICULAR_AMOUNT_ROUNDOFF_TYPE"]}}]}, {"conditions": {"any": [{"fact": "sample1", "path": "INVOICE_AMOUNT_ROUNDOFF", "operator": "equal", "value": "No"}]}, "event": [{"type": "remove", "params": {"field": ["sample1.INVOICE_AMOUNT_ROUNDOFF_TYPE"]}}]}], "flow": {"children": {"sample1": {"title": "Invoice Generation Settings", "layout": "accordion", "required": true, "children": {"invoicing_frequency": {"title": "Invoice Frequency", "type": "select", "required": true, "enum": [{"const": "Monthly", "title": "Monthly"}, {"const": "Quarterly", "title": "Quarterly"}, {"const": "Half_Yearly", "title": "Half Yearly"}, {"const": "Yearly", "title": "Yearly"}]}, "effective_date": {"title": "Effective Date", "format": "date", "maxDateTime": "now", "required": true}, "PARKING_CHARGES_CALCULATION_BY": {"title": "Parking Charges By", "type": "select", "enum": [{"const": "parking", "title": "No of Parking Alloted"}, {"const": "vehicle", "title": "No of Vehicle Alloted"}, {"const": "both", "title": "Max(Whichever is higher No. of Parking Alloted Or No. of Vehicle Registered)"}], "required": true}, "PARTICULAR_AMOUNT_ROUNDOFF": {"title": "Particular Roundoff", "type": "radio", "required": true, "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "PARTICULAR_AMOUNT_ROUNDOFF_TYPE": {"title": "Particular Roundoff Type", "type": "select", "required": true, "enum": [{"const": "Upper (Convert to upper decimal)", "title": "Upper (Convert to upper decimal)"}, {"const": "Lower (Convert to lower decimal)", "title": "Lower (Convert to lower decimal)"}, {"const": "round", "title": "Round"}]}}, "INVOICE_AMOUNT_ROUNDOFF": {"title": "Invoice Roundoff", "type": "radio", "required": true, "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}]}, "PARTICULAR_AMOUNT_ROUNDOFF_TYPE": {"title": "Particular RoundOff Type", "type": "select", "required": true, "enum": [{"const": "upper", "title": "Upper (Convert to upper decimal)"}, {"const": "lower", "title": "Lower (Convert to lower decimal)"}, {"const": "round", "title": "Round"}]}, "INVOICE_AMOUNT_ROUNDOFF_TYPE": {"title": "Invoice Roundoff Type", "type": "select", "required": true, "enum": [{"const": "upper", "title": "Upper (Convert to upper decimal)"}, {"const": "lower", "title": "Lower (Convert to lower decimal)"}, {"const": "round", "title": "Round"}]}}}, "sample2": {"title": "Receipt Generation Settings", "layout": "accordion", "required": true, "children": {"ALLOWED_PARTIAL_PAYMENT": {"title": "Allow Partial Receipt", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "Late_Charges_Calculation_From": {"title": "Consider Checque Receipt", "type": "radio", "enum": [{"const": "clearance", "title": "On Clearance"}, {"const": "submission", "title": "On Submission"}], "required": true}, "INCOME_RECEIPT_PREFIX": {"title": "Prefix", "type": "string", "required": true}, "INCOME_RECEIPT_START_NUMBER": {"title": "Starting From", "type": "number", "required": true, "min": 0}, "INCOME_RECEIPT_NUMBER_INCREMENT": {"title": "Auto Increment", "type": "select", "enum": [{"const": 1, "title": "1"}, {"const": 2, "title": "2"}, {"const": 3, "title": "3"}, {"const": 4, "title": "4"}, {"const": 5, "title": "5"}, {"const": 6, "title": "6"}, {"const": 7, "title": "7"}, {"const": 8, "title": "8"}, {"const": 9, "title": "9"}, {"const": 10, "title": "10"}], "required": true}, "RECEIPT_LOGO": {"title": "Show Logo On Receipt", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "SHOW_SOCIETY_SIGNATURE": {"title": "Show Powered By OneSociety", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "INCOME_RECEIPT_INSTRUCTION": {"title": "Receipt Instruction", "type": "textarea", "required": true}, "INCOME_PAYMENT_MODE": {"title": "Receipt Modes", "type": "checkbox", "enum": [{"const": "cash", "title": "Cash"}, {"const": "cheque", "title": "Cheque"}, {"const": "cashtransfer", "title": "Electronic Fund Transfer"}, {"const": "online", "title": "Online"}]}}}, "sample3": {"title": "Non Member Invoice Number Settings", "layout": "accordion", "required": true, "children": {"INCOME_NONMEMBER_INVOICE_PREFIX": {"title": "Prefix", "type": "string", "required": true}, "INCOME_NONMEMBER_INVOICE_START_NUMBER": {"title": "Starting From", "type": "number", "required": true, "min": 0}, "INCOME_NONMEMBER_INVOICE_NUMBER_INCREMENT": {"title": "Auto Increment", "type": "select", "enum": [{"const": 1, "title": "1"}, {"const": 2, "title": "2"}, {"const": 3, "title": "3"}, {"const": 4, "title": "4"}, {"const": 5, "title": "5"}, {"const": 6, "title": "6"}, {"const": 7, "title": "7"}, {"const": 8, "title": "8"}, {"const": 9, "title": "9"}, {"const": 10, "title": "10"}], "required": true}}}, "sample4": {"title": "Maintainance Invoice Number Settings", "layout": "accordion", "required": true, "children": {"INCOME_INVOICE_PREFIX": {"title": "Prefix", "type": "string", "required": true}, "INCOME_INVOICE_START_NUMBER": {"title": "Starting From", "type": "number", "required": true, "min": 0}, "INCOME_INVOICE_NUMBER_INCREMENT": {"title": "Auto Increment", "type": "select", "enum": [{"const": 1, "title": "1"}, {"const": 2, "title": "2"}, {"const": 3, "title": "3"}, {"const": 4, "title": "4"}, {"const": 5, "title": "5"}, {"const": 6, "title": "6"}, {"const": 7, "title": "7"}, {"const": 8, "title": "8"}, {"const": 9, "title": "9"}, {"const": 10, "title": "10"}], "required": true}}}, "sample5": {"title": "Incidental Invoice Number Settings", "layout": "accordion", "required": true, "children": {"INCOME_INCIDENTAL_INVOICE_PREFIX": {"title": "Prefix", "type": "string", "required": true}, "INCOME_INCIDENTAL_INVOICE_START_NUMBER": {"title": "Starting From", "type": "number", "required": true, "min": 0}, "INCOME_INCIDENTAL_INVOICE_NUMBER_INCREMENT": {"title": "Auto Increment", "type": "select", "enum": [{"const": 1, "title": "1"}, {"const": 2, "title": "2"}, {"const": 3, "title": "3"}, {"const": 4, "title": "4"}, {"const": 5, "title": "5"}, {"const": 6, "title": "6"}, {"const": 7, "title": "7"}, {"const": 8, "title": "8"}, {"const": 9, "title": "9"}, {"const": 10, "title": "10"}], "required": true}}}, "sample6": {"title": "Non Member Invoice Print Settings", "layout": "accordion", "required": true, "children": {"INCOME_NONMEMBER_NAME_LABEL": {"title": "Custom Label For Name", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_NONMEMBER_HIDE_GSTUIN": {"title": "Hide GSTUIN", "type": "radio", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true, "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}]}, "INCOME_NONMEMBER_GSTUIN_LABEL": {"title": "Custom Label For GST/UIN", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_NONMEMBER_HIDE_HSNSAC": {"title": "Hide HSN/SAC", "type": "radio", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true, "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}]}, "INCOME_NONMEMBER_HSNSAC_LABEL": {"title": "Custom Label for HSN/SAC", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_NONMEMBER_HIDE_EMAIL": {"title": "<PERSON><PERSON>", "type": "radio", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true, "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}]}, "INCOME_NONMEMBER_EMAIL_LABEL": {"title": "Custom Label For Email", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_NONMEMBER_HIDE_ADDRESS": {"title": "Hide Address", "type": "radio", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true, "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}]}, "INCOME_NONMEMBER_ADDRESS_LABEL": {"title": "Custom Label for Address", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_NONMEMBER_HIDE_CONTACTNO": {"title": "Hide Contact", "type": "radio", "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}], "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_NONMEMBER_CONTACTNO_LABEL": {"title": "Custom Label For Contact", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_NONMEMBER_SIGNATURE_LABEL": {"title": "Custom Label For Signature", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}}}, "sample8": {"title": "Maintenance Invoice Print Settings", "layout": "accordion", "required": true, "children": {"INCOME_MAINTENANCE_NAME_LABEL": {"title": "Custom Label for Name", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_MAINTENANCE_UNIT_NUMBER_LABEL": {"title": "Custom Label for Unit Number", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_MAINTENANCE_HIDE_PARKING_UNIT": {"title": "Hide Parking Unit", "type": "radio", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true, "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}]}, "INCOME_MAINTENANCE_PARKING_UNIT_LABEL": {"title": "Custom Label for Unit Number", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_MAINTENANCE_HIDE_BUILTUP_AREA": {"title": "Hide Builtup Area", "type": "radio", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true, "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}]}, "INCOME_MAINTENANCE_SHOW_INCIDENTAL_RECEIPT": {"title": "Show Last Paid Incidental Receipts", "type": "radio", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true, "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}]}, "INCOME_MAINTENANCE_BUILTUP_AREA_LABEL": {"title": "Custom Label for Builtup Area", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_MAINTENANCE_HIDE_GSTUIN": {"title": "Hide GSTUIN", "type": "radio", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true, "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}]}, "INCOME_MAINTENANCE_GSTUIN_LABEL": {"title": "Custom Label for GSTIN/UIN", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_MAINTENANCE_SIGNATURE_LABEL": {"title": "Custom Label for Signature", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}}}, "sample9": {"title": "Incidental Invoice Print Settings", "layout": "accordion", "required": true, "children": {"INCOME_INCIDENTAL_NAME_LABEL": {"title": "Custom Label for Name", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_INCIDENTAL_UNIT_NUMBER_LABEL": {"title": "Custom Label for Unit Number", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_INCIDENTAL_HIDE_GSTUIN": {"title": "Hide GST/UIN", "type": "radio", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true, "enum": [{"const": 1, "title": "Yes"}, {"const": 0, "title": "No"}]}, "INCOME_MAINTENANCE_GSTUIN_LABEL": {"title": "Custom Label for GSTIN/UIN", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}, "INCOME_MAINTENANCE_SIGNATURE_LABEL": {"title": "Custom Label for Signature", "type": "string", "description": "> [ℹ `Info`](https://google.co.in?)", "enableMD": true}}}, "sample10": {"title": "General Print Settings", "layout": "accordion", "required": true, "children": {"INVOICE_LOGO": {"title": "Show Logo On Invoice ", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "PAYMENT_TRANSACTION_RECEIPT_LAST_PERIOD": {"title": "Show Payment Receipt ", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "SHOW_SOCIETY_SIGNATURE": {"title": "Show Society Signature", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "SHOW_RECEIPT_FOOTER": {"title": "Show Powered By OneSociety", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "INVOICE_DPC": {"title": "Show DPC Breakup", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "SHOW_RULE_CHARGES": {"title": "Show Rule Charges", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "SHOW_BANK_DETAIL": {"title": "Show Bank Detail", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "SHOW_NEW_INVOICE_TEMPLATE": {"title": "Show New Invoice Template", "type": "radio", "enum": [{"const": "Yes", "title": "Yes"}, {"const": "No", "title": "No"}], "required": true}, "INVOICE_FONT_SIZE": {"title": "Font Size", "type": "select", "enum": [{"const": 9, "title": "Small "}, {"const": 10, "title": "Medium"}, {"const": 11, "title": "Large"}], "required": true}, "INCOME_PUBLISH_RESIDENT_DUES": {"title": "Publish Dues to all Residents", "type": "boolean", "enum": [{"const": true, "title": "Yes"}, {"const": false, "title": "No"}], "required": true}, "INCOME_SHOW_INTEREST_BREAKUP": {"title": "Show Interest Breakup in the bill", "type": "boolean", "enum": [{"const": true, "title": "Yes"}, {"const": false, "title": "No"}], "required": true}, "INCOME_PAYMENT_INSTRUCTION": {"title": "Maintainance Payment Instruction", "type": "textarea", "required": true}, "COMMON_INCOME_PAYMENT_INSTRUCTION": {"title": "Incidental Payment Instruction", "type": "textarea", "required": true}, "NON_MEMBER_INCOME_PAYMENT_INSTRUCTION": {"title": "Custom Label for Signature", "type": "textarea", "required": true}}}}}}}