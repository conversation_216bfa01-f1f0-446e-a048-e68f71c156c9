{"meta": {"formId": "miscellaneous", "title": "New Miscellaneous    "}, "facts": [{"fact": "cash_balance", "path": "/admin/vendorbill/fetchDataAddVendorPayment/cash", "method": "GET", "returns": "ledger_amount"}], "rules": [{"conditions": {"any": [{"fact": "purchase_amount", "operator": "truthy", "value": true}]}, "event": [{"type": "tooltip", "params": {"__title": "Your cash in hand balance is currency({{cash_balance}})", "__field": ["purchase_amount"], "cash_balance": {"fact": "cash_balance"}}}]}, {"conditions": {"all": [{"fact": "purchase_amount", "operator": "lessThan", "value": 0}]}, "event": [{"type": "validation", "params": {"field": "purchase_amount", "message": "Amount must be a positive number"}}]}, {"conditions": {"all": [{"fact": "purchase_date", "operator": "greaterThan", "value": "{{today}}"}]}, "event": [{"type": "validation", "params": {"field": "purchase_date", "message": "Purchase date cannot be in the future"}}]}, {"conditions": {"all": [{"fact": "purchase_amount", "operator": "greaterThan", "value": {"fact": "cash_balance"}}]}, "event": [{"type": "validation", "params": {"field": "purchase_amount", "message": "Purchase amount cannot exceed your cash in hand balance"}}]}], "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "otherexpense/addExpense", "redirect": "/admin/society/otherexpense/expenses"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "otherexpense/addExpense", "redirect": "/admin/society/otherexpense/addExpense"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/otherexpense/expenses"}], "flow": {"title": "Miscellaneous Details", "required": ["purchase_item_name", "purchase_amount", "purchase_date"], "children": {"purchase_item_name": {"title": "Purchase Item Name", "type": "string", "placeholder": "Enter Purchase Item Name"}, "purchase_amount": {"title": "Amount (Cash)", "type": "number", "placeholder": "Enter Amount", "min": 0, "startAdornment": "₹", "description": "> Your Cash In Hand Balance is ₹ {{cash_balance}}. Amount must not exceed this balance.", "enableMD": true, "ui:options": {"inputProps": {"min": 0}}}, "purchase_date": {"title": "Purchase Date", "type": "date", "maxDateTime": "now", "minDateTime": "-4 year", "description": "The date of purchase (cannot be in the future)"}, "item_description": {"title": "Item Description", "type": "string", "placeholder": "Enter Item Description"}}}}}