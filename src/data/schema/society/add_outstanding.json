{"meta": {"formId": "Add Outstanding", "title": "Add OutStanding"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "/admin/society/noc/list", "redirect": "/admin/society/noc/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/noc/list"}], "flow": {"title": "TowerA-0-Shop02", "children": {"adjustment_type": {"title": "Adjustment Type", "type": "radio", "enum": ["Outstanding", "Advance"], "children": {"Advance": {"advance_amount": {"title": "Advance Amount", "default": "0.00"}}, "Outstanding": {"principal_amount": {"title": "Principal Amount", "default": "0.00"}, "interest_amount": {"title": "Interest Amount", "default": "0.00"}, "delayed_payment": {"title": "Delayed Payment Charges", "default": "0.00"}}}}}}}}