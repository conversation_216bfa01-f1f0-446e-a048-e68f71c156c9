{"meta": {"title": "Add Gate"}, "schema": {"actions": [{"title": "Submit", "type": "submit", "api_path": "gate/add", "redirect": "/admin/gate/gates/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/gate/gates/list"}], "flow": {"title": "Gate Details", "children": {"gate_name": {"title": "Gate name", "type": "string", "placeholder": "Gate name", "required": true}, "gate_type": {"title": "Gate type", "type": "select", "placeholder": "Select gate type", "required": true, "enum": [{"title": "In", "const": "in"}, {"title": "Out", "const": "out"}, {"title": "Both", "const": "both"}]}}}}}