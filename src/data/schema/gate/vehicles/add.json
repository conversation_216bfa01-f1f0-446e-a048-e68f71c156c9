{"meta": {"title": "Add Vehicle"}, "schema": {"actions": [{"title": "Submit", "type": "submit", "api_path": "vehicles/add", "redirect": "/admin/gate/vehicles/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/gate/vehicles/list"}], "flow": {"title": "Vehicle Details", "children": {"vehicle_no": {"title": "Vehicle no", "type": "string", "placeholder": "Vehicle no", "required": true, "errorMessage": "Please enter vehicle no"}, "vehicle_color": {"title": "Vehicle color", "type": "string", "placeholder": "Vehicle color"}, "parking_batch_no": {"title": "Parking batch no", "type": "string", "placeholder": "Parking no"}, "parking": {"title": "Parking", "type": "dropdown", "placeholder": "Select parking", "required": true, "errorMessage": "Please select parking", "apiPath": "/admin/parkings/list", "labelKeys": ["parking_name"]}, "access_to_give": {"title": "Access to give", "type": "dropdown", "placeholder": "Select access to give", "required": true, "errorMessage": "Please select access to give", "enum": [{"title": "All Gates", "const": "all"}, {"title": "Specific Gates", "const": "specific"}]}}}}}