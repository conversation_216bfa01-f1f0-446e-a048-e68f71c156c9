{"meta": {"title": "Add Building"}, "schema": {"actions": [{"title": "Submit", "type": "submit", "api_path": "buildings/add", "redirect": "/admin/buildings/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/buildings/list"}], "flow": {"title": "Building Details", "children": {"building_name": {"title": "Building name", "type": "string", "placeholder": "Building name", "required": true}, "no_of_floors": {"title": "No of floors", "type": "number", "placeholder": "No of floors", "min": 1, "required": true}, "units_per_floor": {"title": "Units per floor", "type": "string", "placeholder": "Units per floor"}, "structure_type": {"title": "Structure type", "type": "dropdown", "placeholder": "Select structure type", "enum": [{"title": "Apartment", "const": "apartment"}, {"title": "Villa", "const": "villa"}, {"title": "Row House", "const": "row_house"}, {"title": "Duplex", "const": "duplex"}, {"title": "Penthouse", "const": "penthouse"}, {"title": "Studio", "const": "studio"}, {"title": "Other", "const": "other"}]}}}}}