{"meta": {"title": "Add Member"}, "schema": {"actions": [{"title": "Submit", "type": "submit", "api_path": "visitors/members/add", "redirect": "/admin/gate/members/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/gate/members/list"}], "flow": {"title": "Member Details", "children": {"member_type": {"title": "Member type", "type": "radio", "required": true, "enum": [{"title": "Primary", "const": "primary"}, {"title": "Associate", "const": "associate"}, {"title": "Tenant", "const": "tenant"}], "default": "primary"}, "mobile_no": {"title": "Mobile no", "type": "string", "placeholder": "Mobile no", "pattern": "[0-9]{10}", "required": true}, "email_id": {"title": "Email-id", "type": "string", "placeholder": "Email-id"}, "first_name": {"title": "First name", "type": "string", "placeholder": "First name", "required": true}, "last_name": {"title": "Last name", "type": "string", "placeholder": "Last Name"}, "gender": {"title": "Gender", "type": "radio", "enum": [{"title": "Male", "const": "male"}, {"title": "Female", "const": "female"}, {"title": "Other", "const": "other"}], "default": "male"}, "units_available": {"title": "Units available", "type": "dropdown", "placeholder": "Select units", "apiPath": "/admin/units/list", "labelKeys": ["unit_name"]}, "parkings_available": {"title": "Parkings available", "type": "dropdown", "placeholder": "Select parking", "apiPath": "/admin/parkings/list", "labelKeys": ["parking_name"]}, "start_date": {"title": "Start date", "type": "date", "required": true, "default": "{{today}}"}, "invite_member_to_join": {"title": "Invite member to join", "type": "boolean", "default": false}}}}}