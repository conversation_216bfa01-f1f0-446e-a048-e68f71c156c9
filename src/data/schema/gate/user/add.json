{"meta": {"title": "Add User"}, "schema": {"actions": [{"title": "Submit", "type": "submit", "api_path": "user/add", "redirect": "/admin/gate/user/list"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/gate/user/list"}], "flow": {"title": "User Details", "children": {"first_name": {"title": "First name", "type": "string", "placeholder": "First name", "required": true}, "last_name": {"title": "Last name", "type": "string", "placeholder": "Last name"}, "email": {"title": "Email", "type": "string", "placeholder": "Email", "required": true, "format": "email"}, "mobile": {"title": "Mobile", "type": "string", "placeholder": "Mobile", "required": true, "pattern": "[0-9]{10}"}, "role": {"title": "Role", "type": "select", "placeholder": "Select user's role", "required": true, "enum": [{"title": "Master", "const": "master", "description": "Permission to read, write, update and delete the records"}, {"title": "Manager", "const": "manager", "description": "Permission to read, write and update the records"}, {"title": "Staff", "const": "staff", "description": "Permission to read and write the records only"}, {"title": "Gatekeeper", "const": "gatekeeper", "description": "Permission to read, write and update the records"}]}, "gender": {"title": "Gender", "type": "radio", "enum": [{"title": "Male", "const": "male"}, {"title": "Female", "const": "female"}]}, "city": {"title": "City", "type": "string", "placeholder": "City"}}}}}