{"meta": {"title": "Notification Settings", "data_source": "society/notification_settings"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "society/notification_settings", "redirect": "/admin/society/notification_settings"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/notification_settings"}], "flow": {"children": {"sms_settings": {"title": "SMS Settings", "layout": "card", "children": {"sms_guest_invite": {"help": "Send gate pass to expected guest via SMS.", "title": "Expected guest invite", "type": "boolean", "default": false}}}, "email_settings": {"title": "<PERSON><PERSON>s", "children": {"email_guest_invite": {"title": "Expected guest invite", "help": "Send digital pass via email.", "type": "boolean", "default": false}}}}}}}