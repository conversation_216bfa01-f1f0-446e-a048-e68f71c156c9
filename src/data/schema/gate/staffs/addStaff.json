{"meta": {"title": "Add Staff"}, "schema": {"actions": [{"title": "Submit", "type": "submit", "api_path": "visitors/staffs/add", "redirect": "/admin/visitors/staffs/list"}, {"title": "Back", "type": "cancel", "redirect": "/admin/visitors/staffs/list"}], "flow": {"title": "Staff Details", "children": {"staff_type": {"title": "Staff Type", "type": "radio", "enum": [{"title": "Unit staff", "const": "unit"}, {"title": "Office staff", "const": "office"}], "default": "unit", "required": true}, "fullName": {"title": "First name", "type": "string", "placeholder": "First name", "required": true}, "staff_last_name": {"title": "Last name", "type": "string", "placeholder": "Last name"}, "staff_gender": {"title": "Gender", "type": "radio", "enum": [{"title": "Male", "const": "M"}, {"title": "Female", "const": "F"}], "default": "M", "required": true}, "country_code": {"title": "Country code", "type": "dropdown", "placeholder": "Select country code", "default": "+91", "enum": [{"title": "India (+91)", "const": "+91"}, {"title": "USA (+1)", "const": "+1"}, {"title": "UK (+44)", "const": "+44"}]}, "mobileNumber": {"title": "Mobile no", "type": "string", "pattern": "[0-9]{10}", "placeholder": "Mobile no", "required": true}, "staff_email_id": {"title": "Email-id", "type": "string", "placeholder": "Email-id"}, "units": {"title": "Units", "type": "dropdown", "placeholder": "Select units", "apiPath": "/admin/units/list", "labelKeys": ["unit_name"]}, "domestic_staff_category": {"title": "Domestic Staff Category", "type": "dropdown", "placeholder": "Select Domestic staff category", "apiPath": "/admin/staffs/categories", "labelKeys": ["category_name"]}, "coming_from": {"title": "Coming From", "type": "string", "placeholder": "Coming from"}, "id_proof_type": {"title": "Id proof type", "type": "dropdown", "placeholder": "Select ID proof type", "enum": [{"title": "<PERSON><PERSON><PERSON>", "const": "<PERSON><PERSON><PERSON>"}, {"title": "PAN Card", "const": "pan"}, {"title": "Driving License", "const": "driving_license"}, {"title": "Voter ID", "const": "voter_id"}, {"title": "Passport", "const": "passport"}]}, "id_proof_number": {"title": "Id proof number", "type": "string", "placeholder": "ID proof number"}, "upload_files": {"title": "Upload Files", "type": "file", "description": "Allowed file types - pdf( pdf ) ,image( png,jpeg,jpg,gif,bmp,tif )", "accept": ".pdf,image/png,image/jpeg,image/jpg,image/gif,image/bmp,image/tif"}, "pass_type": {"title": "Pass type", "type": "radio", "enum": [{"title": "Days", "const": "days"}, {"title": "Visit", "const": "visit"}], "default": "days", "required": true}, "no_of_days": {"title": "No of days", "type": "number", "placeholder": "No of days", "required": true}, "create_user_account": {"title": "Create user account", "type": "radio", "enum": [{"title": "Yes", "const": "yes"}, {"title": "No", "const": "no"}], "default": "no"}, "status": {"title": "Status", "type": "radio", "enum": [{"title": "Active", "const": "active"}, {"title": "Inactive", "const": "inactive"}], "default": "active"}}}}}