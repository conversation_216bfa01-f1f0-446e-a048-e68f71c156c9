// Type Imports
import type { HorizontalMenuDataType, VerticalMenuDataType } from '@/types/menuTypes';


// Society Menu Imports
import { getDashboardMenu as societyDashboard } from './modules/society/dashboard';
import { getIncomeMenu } from './modules/society/income';
import { getExpanseMenu } from './modules/society/expense';
import { getHelpDeskMenu } from './modules/society/helpdesk';
import { getVendorMenu } from './modules/society/vendor';
import { getAccountMenu } from './modules/society/account';
import { getDCOMenu } from './modules/society/dco';
import { getReportMenu } from './modules/society/report';
import { getSettingMenu } from './modules/society/settings';

// Gate Menu Imports
import { getDashboardMenu as gateDashboard } from './modules/gate/dashboard';
import { getVisitorsMenu } from './modules/gate/visitors';
import { getLogsMenu } from './modules/gate/logs';
import { getComplexMenu } from './modules/gate/complex';
import { getParkingMenu } from './modules/gate/parking';
import { getVehicleMenu } from './modules/gate/vehicle';
import { getAccountsMenu } from './modules/gate/accounts';
import { getSettingsMenu as getGateSettingsMenu } from './modules/gate/settings';

/**
 * Returns vertical or horizontal menu data based on the specified mode.
 *
 * @param mode - The operating mode: `"society"` or `"gate"`
 * @returns An array of menu items relevant to the specified mode.
 *
 * @example
 * ```ts
 * const menu = getMenuByMode("society");
 * ```
 */
const getMenuByMode = (mode: 'society' | 'gate'): VerticalMenuDataType[] | HorizontalMenuDataType[] => {
  if (mode === 'society') {
    return [
      ...societyDashboard(),
      ...getHelpDeskMenu(),
      ...getIncomeMenu(),
      ...getExpanseMenu(),
      ...getVendorMenu(),
      ...getAccountMenu(),
      ...getReportMenu(),
      ...getDCOMenu(),
      ...getSettingMenu()
    ];
  }

  if (mode === 'gate') {
    return [
      ...gateDashboard(),
      ...getVisitorsMenu(),
      ...getComplexMenu(),
      ...getParkingMenu(),
      ...getVehicleMenu(),
      ...getAccountsMenu(),
      ...getLogsMenu(),
      ...getGateSettingsMenu()
    ];
  }

  return []; // fallback (optional if mode is tightly controlled)
};

export default getMenuByMode;
