import type { VerticalMenuDataType } from '@/types/menuTypes'

const complexMenu: VerticalMenuDataType[] = [
  {
    label: "Complex",
    icon: 'ri-building-2-line',
    children: [
      {
        label: 'Pending Approval',
        href: '/admin/gate/members/requests/list',
        pageType: 'table',
      },
      {
        label: 'Pending Change Request',
        href: '/admin/gate/members/change/requests',
        pageType: 'table',
      },
      {
        label: 'Add Building',
        href: '/admin/gate/building/add',
      },
      {
        label: 'Building & Parking Lots',
        href: '/admin/gate/buildings/list',
        pageType: 'table',
      },
      {
        label: 'Add Unit',
        href: '/admin/gate/units/add',
      },
      {
        label: 'Units',
        href: '/admin/gate/units/list',
        pageType: 'table',
      },
      {
        label: 'Add Gate',
        href: '/admin/gate/gates/add',
      },
      {
        label: 'Gates',
        href: '/admin/gate/gates/list',
        pageType: 'table',
      },
      {
        label: 'Gate Alarm Report',
        href: '/admin/gate/logs/pass-card-history',
      },
      {
        label: 'Add Member',
        href: '/admin/gate/visitors/members/add',
      },
      {
        label: 'Members',
        href: '/admin/gate/visitors/members/list',
        pageType: 'table',
      },
      {
        label: 'Messaging Templates',
        href: '/admin/gate/messaging/templates/list',
        pageType: 'table',
      }
    ]
  }
]

export const getComplexMenu = (): VerticalMenuDataType[] => complexMenu
