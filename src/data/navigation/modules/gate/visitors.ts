import type { VerticalMenuDataType } from '@/types/menuTypes'

const visitorsMenu: VerticalMenuDataType[] = [
  {
    label: "All Visitors",
    icon: 'ri-user-follow-line',
    children: [
      {
        label: 'Add Staff',
        href: '/admin/gate/staffs/addStaff',
      },
      {
        label: 'Staffs',
        href: '/admin/gate/visitors/staffs/list',
        pageType: 'table',
      },
      {
        label: 'Staff Attendance',
        href: '/admin/gate/visitors/staffs/attendance',
        pageType: 'table',
      },
      {
        label: 'Guest',
        href: '/admin/gate/visitors/guests/list',
        pageType: 'table',
      },
    ]
  }
]

export const getVisitorsMenu = (): VerticalMenuDataType[] => visitorsMenu
