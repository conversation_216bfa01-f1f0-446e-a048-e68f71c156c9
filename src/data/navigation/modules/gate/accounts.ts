import type { VerticalMenuDataType } from '@/types/menuTypes'

const accountsMenu: VerticalMenuDataType[] = [
  {
    label: "Accounts",
    icon: 'ri-user-settings-line',
    children: [
      {
        label: 'Add User',
        href: '/admin/gate/user/add',
      },
      {
        label: 'User',
        href: '/admin/gate/user/list',
        pageType: 'table',
      },
      {
        label: 'Add Role',
        href: '/admin/gate/roles/add',
      },
      {
        label: 'Role',
        href: '/admin/gate/user/roles/list',
        pageType: 'table',
      }
    ]
  }
]

export const getAccountsMenu = (): VerticalMenuDataType[] => accountsMenu
