import type { VerticalMenuDataType } from '@/types/menuTypes'

const logsMenu: VerticalMenuDataType[] = [
    {
        label: "Logs",
        icon: 'ri-file-list-3-line',
        children: [
            {
                label: 'Visitor Logs',
                href: '/admin/gate/logs/visits',
                pageType: 'table',
                children: [
                    {
                        label: 'Visitor Detail',
                        href: '/admin/gate/visitors/profile/[id]',
                        pageType: 'mix',
                        commonPageConfig: {
                            title: 'Visitor Detail',
                            buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
                        },
                        order: [
                            {
                                type: 'card',
                                mainGrid: { xs: 12, md: 8, lg: 12 },
                                headerKeyDisplay: [
                                    { key: 'name', label: '', isLink: true, isCheckbox: false },
                                    { key: 'status', showChip: false },
                                    { key: '()', label: '', showSubHeader: true },
                                ],
                                keyDisplay: [
                                    { key: 'mobile_number', label: 'Mobile Number', keyGrid: 3, valueGrid: 3 },
                                    { key: 'gender', label: 'Gender', keyGrid: 3, valueGrid: 3 },
                                    { key: 'coming_from', label: 'Coming from', keyGrid: 3, valueGrid: 3 },
                                    { key: 'id_proof', label: 'ID Proof', keyGrid: 3, valueGrid: 3 },
                                    { key: 'building', label: 'Building / Unit No', keyGrid: 3, valueGrid: 3 },
                                    { key: 'type', label: 'Type', keyGrid: 3, valueGrid: 3 },
                                    { key: 'in_time', label: 'In Time', keyGrid: 3, valueGrid: 3 },
                                    { key: 'out_time', label: 'Out Time', keyGrid: 3, valueGrid: 3 },
                                    { key: 'time_inside_complex', label: 'Time Inside Complex', keyGrid: 3, valueGrid: 3 },
                                    { key: 'last_visited', label: 'Last Visited', keyGrid: 3, valueGrid: 3 },
                                ]
                            }
                        ]
                    },
                ]
            },
            {
                label: 'Vehicle Logs',
                href: '/admin/gate/logs/vehicles',
                pageType: 'table',
            },
            {
                label: 'Pass & Cards Logs',
                href: '/admin/gate/logs/pass-card-history',
                pageType: 'table',
            },
        ]
    }
]

export const getLogsMenu = (): VerticalMenuDataType[] => logsMenu
