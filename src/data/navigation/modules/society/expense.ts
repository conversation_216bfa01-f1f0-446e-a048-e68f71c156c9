// @ts-ignore @ts-nocheck

import type { VerticalMenuDataType } from '@/types/menuTypes'

const expanseMenu: VerticalMenuDataType[] = [
  {
    label: 'Expenses',
    icon: 'ri-refund-line',
    children: [
      {
        label: 'Purchases/Expenses',
        href: '/admin/society/vendorbill/vendorBill',
        pageType: 'table',
        children: [
          {
            label: 'New Vendor Bill',
            href: '/admin/society/vendorbill/addVendorBill'
          },
          {
            label: "Payment Transaction",
            href: "/admin/society/vendorbill/viewPayments/[id]",
            pageType: "table"
          },
          {
            label: 'New Cash Purchase',
            href: '/admin/society/vendorbill/addBill'
          },
          {
            label: 'View Vendor Bill',
            href: '/admin/society/vendorbill/vendorBillDetails/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Vendor Bill Details',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 10,lg: 12 },

                apiURL: '/admin/vendorbill/vendorBillDetailsCard/[id]',
                headerKeyDisplay: [
                  { key: 'vendor_name', label: 'Vendor  Name :-', isLink: true, isCheckbox: false },
                  { key: 'payment_status', showChip: true, fallbackLabel: 'payment_status' }
                ],
                keyDisplay: [
                  { key: 'vendor_bill_id', label: 'Voucher Number', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_bill_type_purchase', label: 'Type of Purchase', keyGrid: 3, valueGrid: 3 },
                  { key: 'type_of_expense', label: 'Type of Expense', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_bill_num', label: 'Bill Number', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_bill_date', label: 'Bill Date', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_bill_amount', label: 'Bill Amount', keyGrid: 3, valueGrid: 3 },
                  { key: 'paid_amount', label: 'Paid Amount', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_bill_tds', label: 'TDS Deducted', keyGrid: 3, valueGrid: 3, showAsZero: true },
                  { key: 'due_amount', label: 'Due Amount', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_bill_due_date', label: 'Due Date', keyGrid: 3, valueGrid: 3 },
                  { key: 'advance_consumed', label: 'Advance Amount', keyGrid: 3, valueGrid: 3, showAsZero: true },
                  { key: 'is_billable', label: 'Is billable to all members?', keyGrid: 3, valueGrid: 3, showAsYesNo: true },
                  { key: 'is_rcm', label: 'Has RCM Applied?', keyGrid: 3, valueGrid: 3, showAsYesNo: true },
                  { key: 'vendor_phone_number', label: 'Attachment', keyGrid: 3, valueGrid: 3 }
                ]
              },
              {
                type: 'table',
                apiURL: '/admin/society/vendorbill/vendorBillDetails/[id]'
              },  
              {
                type: 'table',
                apiURL: '/admin/society/vendorbill/vendorBillDetails/[id]'
              }
            ]
          },
          {
            label: 'Vendor Bill Payment Details',
            href: '/admin/society/vendorbill/editVendorBill/[vendor_id]'
          },
          {
            label: 'Vendor Bill Payment',
            href: '/admin/society/vendorbill/payBill/[vendor_id]/[id]'
          }
        ]
      },
      {
        label: 'Miscellaneous',
        href: '/admin/society/otherexpense/expenses',
        pageType: 'table',
        children: [
          {
            label: 'Miscellaneous Details',
            href: '/admin/society/otherexpense/expenseDetails/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Miscellaneous Details',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 6, lg: 12 },
                headerKeyDisplay: [
                  { label: 'Miscellaneous Details', isLink: false, isCheckbox: false },
                ],
                keyDisplay: [
                  { key: 'other_expenses_item_name', label: 'Purchase Item Name', keyGrid: 3, valueGrid: 3 },
                  { key: 'other_expenses_amount', label: 'Amount', keyGrid: 3, valueGrid: 3 },
                  { key: 'other_expenses_purchase_date', label: 'Purchase Date', keyGrid: 3, valueGrid: 3 },
                  { key: 'other_expenses_item_desc', label: 'Item Description', keyGrid: 3, valueGrid: 3 }
                ]
              }
            ]
          },
          {
            label: 'New Miscellaneous',
            href: '/admin/society/otherexpense/addExpense'
          }
        ]

      },
      {
        label: 'Purchase/Work Orders',
        href: '/admin/society/purchaseform/list',
        pageType: 'mix',
        commonPageConfig: {
          title: 'Purchase/Work Orders',
          buttons: [{ label: 'New Purchase/work order', route: '/admin/society/purchaseform/add/', icon: 'ri-add-line' }]
        },
        order: [
          {
            type: 'card',
            renderButtonGroup: true,
            mainGrid: { xs: 12, md: 6, lg: 12 },
            headerKeyDisplay: [
              { key: 'purchase_form_po_number', label: '', isLink: true, isCheckbox: true },
              { key: 'status', showChip: true },
              { key: 'purchase_form_title', label: 'Title', showSubHeader: true },
              { showIcon: true, redirectUrl: '/admin/society/purchaseform/view/[id]', IconClassName: 'ri:eye-line' }
            ],
            keyDisplay: [
              { key: 'purchase_form_vendor_name', label: 'Vendor', keyGrid: 2, valueGrid: 4 },
              { key: 'purchase_form_amount', label: 'Amount', keyGrid: 3, valueGrid: 3 },
              { key: '', label: 'Approved / Unapproved By', keyGrid: 4, valueGrid: 2 },
              { key: '', label: '  Reviewed / Refused By', keyGrid: 3, valueGrid: 3 },
              { key: 'approved_by', label: 'Approved By', keyGrid: 3, valueGrid: 3 },
              { key: 'reviewed_by', label: 'Review by', keyGrid: 3, valueGrid: 3 },
              { key: 'approval_pending_by_name', label: 'Pending Approval', keyGrid: 3, valueGrid: 3 },
              { key: 'reviewed_pending_by_name', label: 'Pending Review', keyGrid: 3, valueGrid: 3 },
              { key: 'reject_by_name', label: 'Rejected', keyGrid: 3, valueGrid: 3 },
              { key: 'refused_by_name', label: 'Refused by', keyGrid: 3, valueGrid: 3 }
            ],
            showTable: true
          }
        ],
        children: [
          {
            label: 'Add Purchase/Work Order',
            href: '/admin/society/purchaseform/add'
          },
          {
            label: ' Purchase/Work Order',
            href: '/admin/society/purchaseform/view/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Purchase/Work Order Details',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }],
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 10 , lg: 12 },
                headerKeyDisplay: [
                  { key: 'purchase_form_title', label: '', isLink: true, isCheckbox: false },
                  { showIcon: true, redirectUrl: '/admin/society/purchaseform/downloadpo/[id]', IconClassName: 'ri:printer-line' }
                ],
                keyDisplay: [
                  { key: 'purchase_form_desc', label: 'Description', keyGrid: 2, valueGrid: 2 },
                  { key: 'purchase_form_vendor_name', label: 'Vendor Name', keyGrid: 2, valueGrid: 2 },
                  { key: 'purchase_form_po_number', label: 'PO/WO Number', keyGrid: 2, valueGrid: 2 },
                  { key: 'purchase_form_amount', label: 'Amount', keyGrid: 2, valueGrid: 2 },
                  { key: 'approved_by', label: 'Approved by', keyGrid: 2, valueGrid: 2 },
                  { key: 'approval_pending_by_name', label: 'Pending Approval', keyGrid: 2, valueGrid: 2 },
                  { key: 'purchase_form_reviewed_by', label: 'Rejected by', keyGrid: 2, valueGrid: 2 },
                  { key: 'attachment_link', label: 'Attached File', keyGrid: 2, valueGrid: 6, isLink: true }
                ]
              }
            ]
          },
          {
            label: 'Print Purchase/Work Order',
            href: '/admin/society/purchaseform/downloadpo/[id]',
            pageType: 'htmlContent',
            apiURL: "/admin/society/purchaseform/view/[id]",
            openInNewTab: true

          }
        ]
      },
      {
        label: 'Member Billable Purchases',
        href: '/admin/society/vendorbill/billableVendorBills',
        pageType: 'table',
        children: [
          {
            label: 'New Member Billable Purchase',
            href: '/admin/society/vendorbill/add_billable/[id]'
          }
        ]
      },
      {
        label: 'Payment Tracker',
        href: '/admin/society/vendorbill/expensePaymentTrackerList',
        pageType: 'table',
        children: [
          {
            label: "Edit Payment Tracker",
            href: "/admin/society/vendorbill/updatePaymentTracker/[id]",
          },
          {
            lable: "View Payment Tracker",
            href: '/admin/society/vendorbill/viewExpensePaymentVoucherReciept/[id]',
            pageType: 'PDF'
          },
        ]

      },
      {
        label: 'Vendor Advances',
        href: '/admin/society/vendorbill/vendorAdvances',
        pageType: 'table',
        children: [
          {
            label: 'Add Vendor Advance',
            href: '/admin/society/vendorbill/addVendorAdvances'
          },
          {
            label: 'View Vendor Transactions',
            href: '/admin/society/vendorbill/viewAllTransactionsVendors/[id]',
            pageType: 'table',
          },
          {
            label: 'New Vendor Advance',
            href: '/admin/society/vendorbill/addVendorAdvances/[id]'
          }
        ]
      }
    ]
  }
]

export const getExpanseMenu = (): VerticalMenuDataType[] => expanseMenu
