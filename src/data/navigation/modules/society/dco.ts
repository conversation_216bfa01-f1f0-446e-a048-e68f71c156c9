import type { VerticalMenuDataType } from '@/types/menuTypes'

const DCOMenu: VerticalMenuDataType[] = [
  {
    label: 'Configure',
    icon: 'ri-building-line',
    children: [
      {
        label: 'Society website',
        href: '/admin/society/socweb/socprofile',
        pageType: 'socprofile',
      },
      {
        label: 'Documents',
        href: '/admin/society/socweb/socprofile/documents',
        pageType: 'socprofile',
      },
      {
        label: 'Allotees',
        href: '/admin/society/member/list',
        pageType: 'table',
        "children": [
          {
            label: 'Allotees',
            href: '/admin/society/member/register',
          },
          {
            label: 'New Bulk Allottees ',
            href: '/admin/society/member/bulkAdd',
            pageType: 'table'
          },
          {
            label: 'Allottee Types',
            href: '/admin/society/membertype/details',
          },
          {
            label: 'Allottee Details',
            href: '/admin/society/member/viewDetails/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Allottee Details',
              buttons: [
                { label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' },
              ]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 8, lg: 12 },
                headerKeyDisplay: [

                  { key: 'full_name', label: 'Name :- ', isLink: true, isCheckbox: false },
                ],
                keyDisplay: [

                  { key: 'member_email_id', label: 'Email', keyGrid: 2, valueGrid: 2 },
                  { key: 'member_gender', label: 'Gender', keyGrid: 2, valueGrid: 2 },
                  { key: 'member_mobile_number', label: 'Mobile No', keyGrid: 2, valueGrid: 2 },
                  { key: 'intercom', label: 'Intercom', keyGrid: 2, valueGrid: 2 },
                  { key: 'member_dob', label: 'Date of Birth', keyGrid: 2, valueGrid: 2 },
                  { key: 'member_type_name', label: 'Member Type', keyGrid: 2, valueGrid: 2 },


                ]
              },
              {
                type: 'table',
              }
            ]
          },
          {
            label: 'Allottee Reports',
            href: '/admin/society/member/register/[id]',
          },
          {
            label: 'Revoke Allocation of Member ',
            href: '/admin/society/member/delete/[id]',
          },
          {
            label: 'Member Documents',
            href: '/admin/society/member/unitTransfer/[id]'
          }
        ]
      },
      {
        label: 'Vehicles Registration',
        href: '/admin/society/parking/list',
        pageType: 'table',
      },
      {
        label: 'Edit Vehicles Registration',
        href: '/admin/society/parking/registervehicle/[id]',
        pageType: 'table',
      },
      {
        label: 'Parking Allotments ',
        href: '/admin/society/parking-allotments/list',
        pageType: 'table',
        children: [
          {
            label: 'Parking Allotments ',
            href: '/admin/society/parking-allotments/add',
          },
          {
            label: 'Vehicles Registration',
            href: '/admin/society/parking/list',
            pageType: 'table',
            children: [
              {
                label: 'New / Edit Vehicles Registration',
                href: '/admin/society/parking/registervehicle'
              }
            ]
          },
          {
            label: 'New / Edit Allocate Parking',
            href: '/admin/society/parking-allotments/add/[id]',
          },
          {
            label: 'Revoke Allocation of Vehicle ',
            href: '/admin/society/parking-allotments/delete/[id]',
          }
        ]
      },
      {
        label: 'Management Committee',
        href: '/admin/society/committees/list',
        pageType: 'table',
        children: [
          {
            label: 'Management Committee',
            href: '/admin/society/committees/add',
          },
          {
            label: 'Form a committee',
            href: '/admin/society/committees/panel/committeeMembers/[id]',
            pageType: 'table',
          },
        ]
      },
      {
        label: 'Noc Forms',
        href: '/admin/society/noc/list',
        pageType: 'table',
        children: [
          {
            label: 'Noc Forms',
            href: '/admin/society/noc/add_noc',
          },
          {
            label: 'NOC',
            href: '/admin/society/noc/add_noc/[id]',
          }
        ]
      },
      {
        label: 'Noc Form Templates',
        href: '/admin/society/societies/nocForms',
        pageType: 'nocForm',
      },
      {
        label: 'Notices & Circulars',
        href: '/admin/society/notices/list',
        pageType: 'table',
        children: [
          {
            label: 'Notices & Circulars',
            href: '/admin/society/notices/add_notice',
          },
        ]
      },
      {
        label: 'Notice & Circular Templates',
        href: '/admin/society/notices/list_template',
        pageType: 'table',
        children: [
          {
            label: 'Notice & Circular Templates',
            href: '/admin/society/notices/templates',
          },
          {
            label: 'Notice & Circular Templates',
            href: '/admin/society/notices/templates/[id]',
          }
        ]
      },
      {
        label: 'Non Member Master',
        href: '/admin/society/income-details/nonmemberMaster',
        pageType: 'table',
        children: [
          {
            label: 'New Non Member Master',
            href: '/admin/society/income-details/addnonmemberMaster',
          },
          {
            label: 'Edit Non Member Master',
            href: '/admin/society/income-details/addnonmemberMaster/[id]',
          }
        ]
      },
      {
        label: 'Inventory',
        href: '/admin/society/inventory/listInventory',
        pageType: 'table',
        children: [
          {
            label: 'New Inventory',
            href: '/admin/society/inventory/addInventoryItem',
          },
          {
            label: 'Inventory Categories',
            href: '/admin/society/inventory/settings',
          }
        ]
      }
    ]
  }
]

export const getDCOMenu = (): VerticalMenuDataType[] => DCOMenu
