import type { VerticalMenuDataType } from '@/types/menuTypes'

const vendorMenu: VerticalMenuDataType[] = [
  {
    label: 'Vendors',
    icon: 'ri-user-settings-line',
    children: [
      {
        label: 'Vendors',
        href: '/admin/society/vendor/viewVendor',
        pageType: 'table',
        children: [
          {
            label: 'Vendor Details',
            href: '/admin/society/vendor/viewVendorDetails/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Vendor Details',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 10 ,lg: 12, },
                headerKeyDisplay: [
                  { key: 'vendor_name', label: 'Name :-', isLink: true, isCheckbox: false },
                  {
                    key: "vendor_is_company", showSubHeader: true, displayMap: {
                      1: "( Company )",
                      0: "( Individual )"
                    }
                  },
                  { key: 'status', showChip: true, },
                ],
                keyDisplay: [
                  { key: 'vendor_contact_number', label: 'Contact No', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_phone_number', label: 'Phone No', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_email', label: 'Email', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_credit_period', label: 'Credit Period', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_phone_number', label: 'Notes', keyGrid: 3, valueGrid: 3 }
                ]
              },
              {
                type: 'card',
                mainGrid: { xs: 12, md: 10,lg: 10 },
                keyDisplay: [
                  { key: 'vendor_service_regn', label: 'GST No', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_pan_num', label: 'PAN No', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_payee_name', label: 'Payee Name', keyGrid: 3, valueGrid: 3 },
                  { key: 'vendor_address', label: 'Billing Address', keyGrid: 3, valueGrid: 3 }
                ]
              }
            ]
          },
          {
            label: 'Edit Vendor Details',
            href: '/admin/society/vendor/editVendorDetails/[id]'
          },
          {
            label: 'Vendor Bills',
            href: '/admin/society/vendor/vendorBillList/[id]',
            pageType: 'table'
          },
          {
            label: 'Vendor Payments',
            href: '/admin/society/vendor/vendorPaymentList/[id]',
            pageType: 'table'
          },
          {
            label: 'New Vendor',
            href: '/admin/society/vendor/addVendor'
          },
          {
            label: 'Pay Bill',
            href: '/admin/society/vendorbill/payBill/[vendor_id]'
          },
        ]
      },
      {
        label: 'Vendors',
        href: '/admin/society/vendor/viewVendor',
        hideInNavbar: true,
        children: [

          {
            label: 'Payment Transactions',
            href: '/admin/society/vendorbill/viewPayments/[id]'
          },
          {
            label: 'Vendor Bills',
            href: '/admin/society/vendorbill/vendorBillDetails/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Vendor Bill Details',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 10,lg: 10 },
                headerKeyDisplay: [
                  { key: 'vendor_name', label: 'Vendor  Name :-', isLink: true, isCheckbox: false },
                  { key: 'payment_status', showChip: true, fallbackLabel: 'payment_status' }
                ],
                keyDisplay: [
                  { key: 'vendor_bill_id', label: 'Voucher Number', keyGrid: 2, valueGrid: 2 },
                  { key: 'vendor_bill_type_purchase', label: 'Type of Purchase', keyGrid: 2, valueGrid: 2 },
                  { key: 'type_of_expense', label: 'Type of Expense', keyGrid: 2, valueGrid: 2 },
                  { key: 'vendor_bill_num', label: 'Bill Number', keyGrid: 2, valueGrid: 2 },
                  { key: 'vendor_bill_date', label: 'Bill Date', keyGrid: 2, valueGrid: 2 },
                  { key: 'vendor_bill_amount', label: 'Bill Amount', keyGrid: 2, valueGrid: 2 },
                  { key: 'paid_amount', label: 'Paid Amount', keyGrid: 2, valueGrid: 2 },
                  { key: 'vendor_bill_tds', label: 'TDS Deducted', keyGrid: 2, valueGrid: 2, showAsZero: true },
                  { key: 'due_amount', label: 'Due Amount', keyGrid: 2, valueGrid: 2 },
                  { key: 'vendor_bill_due_date', label: 'Due Date', keyGrid: 2, valueGrid: 2 },
                  { key: 'advance_consumed', label: 'Advance Amount', keyGrid: 2, valueGrid: 2, showAsZero: true },
                  { key: 'is_billable', label: 'Is billable to all members?', keyGrid: 2, valueGrid: 2, showAsYesNo: true },
                  { key: 'is_rcm', label: 'Has RCM Applied?', keyGrid: 2, valueGrid: 2, showAsYesNo: true },
                  { key: 'vendor_phone_number', label: 'Attachment', keyGrid: 2, valueGrid: 2 }
                ]
              },
              {
                type: 'table',
              },
              {
                type: 'table',
                apiURL: '/admin/society/vendorbill/viewPayments/[id]'
              }
            ]
          },
        ]
      },
      {
        label: 'vendor Ageing Report',
        href: '/admin/society/vendor/vendorAging',
        pageType: 'table'
      },
      {
        label: 'Vendor Bill',
        hideInNavbar: true,
        children: [
          {
            label: 'Vendor Bills',
            href: '/admin/society/vendorbill/vendorBill'
          },
          {
            label: 'Add Vendor Bill',
            href: '/admin/society/vendorbill/addVendorBill'
          },
          {
            label: 'Billable Vendor Bill',
            href: '/admin/society/vendorbill/billableVendorBills'
          }
        ]
      }
    ]
  }
]

export const getVendorMenu = (): VerticalMenuDataType[] => vendorMenu
