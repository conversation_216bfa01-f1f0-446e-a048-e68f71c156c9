import type { VerticalMenuDataType } from '@/types/menuTypes'

const settingMenu: VerticalMenuDataType[] = [
  {
    label: 'Settings',
    icon: 'ri-settings-4-line',
    children: [
      {
        label: 'Access Control',
        children: [
          {
            label: 'Users',
            href: '/admin/society/users/list',
            pageType: 'table',
            children: [
              {
                label: 'User Details',
                href: '/admin/society/users/userDetails/[id]/[role]',
                pageType: 'mix',
                commonPageConfig: {
                  title: 'User Details',
                  buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
                },
                order: [
                  {
                    type: 'card',
                    mainGrid: { xs: 12, md: 12 },
                    href: '/admin/society/users/userDetails/[id]/[role]',
                    headerKeyDisplay: [
                      { key: '', label: 'User Details', isLink: true, isCheckbox: false },
                      { key: 'status', showChip: false },
                      { key: '()', label: '', showSubHeader: true },
                    ],
                    keyDisplay: [
                      { key: ['user_first_name', 'user_last_name'], label: 'Name', keyGrid: 1, valueGrid: 3 },
                      { key: 'user_name', label: 'Username', keyGrid: 1, valueGrid: 3 },
                      { key: 'user_mobile_number', label: 'Mobile No', keyGrid: 1, valueGrid: 3 },
                      { key: 'user_email_id', label: 'Email ID', keyGrid: 1, valueGrid: 3 },
                      { key: 'role', label: 'Role', keyGrid: 1, valueGrid: 3 },
                      { key: 'user_type', label: 'User Type', keyGrid: 1, valueGrid: 3 },
                      { key: 'user_lang_iso_639_3', label: 'Language Prefer', keyGrid: 1, valueGrid: 3 },
                      { key: 'user_source', label: 'Source', keyGrid: 1, valueGrid: 3 },
                      { key: 'user_gmt_time_zone', label: 'User Time Zone', keyGrid: 1, valueGrid: 3 },
                      { key: 'added_on', label: 'Added On', keyGrid: 1, valueGrid: 3 },
                      { key: 'modified_on', label: 'Last Modified', keyGrid: 1, valueGrid: 3 },
                      { key: 'status', label: 'Status', keyGrid: 1, valueGrid: 3, "showAsStatus": true }
                    ]
                  }
                ]
              }
            ]
          },
          {
            label: 'User Roles',
            href: '/admin/society/roles/listSocRoles',
            pageType: 'table',
            children: [
              {
                label: 'New Role',
                href: '/admin/society/roles/add'
              },
              {
                label: 'Edit Role',
                href: '/admin/society/roles/add/[id]'
              },
              {
                label: "User listing",
                href: "/admin/society/roles/viewUsers/[role_name]/[id]",
                pageType: 'table'
              },

            ]
          },
        ]
      },
      {
        label: 'Staffs',
        children: [
          {
            label: 'Staffs',
            href: '/admin/society/staffs/staffLists',
            pageType: 'table',
            children: [
              {
                label: 'Edit Staff',
                href: '/admin/society/staffs/editStaff/[id]'
              },
              {
                label: 'New Staff',
                href: '/admin/society/staffs/addStaff'
              },
              {
                label: 'Staff Details',
                href: '/admin/society/staffs/staffDetails/[id]',
                pageType: 'mix',
                commonPageConfig: {
                  title: 'Staff Details',
                  buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
                },
                order: [
                  {
                    type: 'card',
                    mainGrid: { xs: 12, md: 12 },
                    headerKeyDisplay: [
                      { key: 'vendor_name', label: 'Staff Details', isLink: true, isCheckbox: false },
                      { key: 'status', showChip: false },
                      { key: '()', label: '', showSubHeader: true },
                      { showIcon: false, redirectUrl: '/en/admin/society/purchaseform/view/[id]', icon: 'ri-eye-line' }
                    ],
                    keyDisplay: [
                      { key: 'staff_first_name', label: 'Name', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_badge_number', label: 'Badge No.', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_contact_number', label: 'Contact No.', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_email_id', label: 'Email', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_address_1', label: 'Address', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_gender', label: 'Gender', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_dob', label: 'Date of Birth', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_qualification', label: 'Qualification', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_skill', label: 'Skill', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_lang_iso_639_3', label: 'Language Spoken', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_rfid', label: 'RFID/Biometric', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_note', label: 'Note', keyGrid: 1, valueGrid: 3 },
                      { key: 'staff_proof', label: 'Addres Proof', keyGrid: 1, valueGrid: 3 }
                    ]
                  }
                ]
              }
            ]
          },
          {
            label: 'Staff Categories',
            href: '/admin/society/staffs/settings',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Staff Categories',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'form',
                gridProps: { xs: 12, sm: 12, md: 12 }
              },
              {
                type: 'table',
                apiURL: '/admin/society/staffs/settings',
              }
            ]
          }
        ]
      },
      {
        label: 'Complex Setup',
        children: [
          {
            label: 'Buildings',
            href: '/admin/society/building/list',
            pageType: 'table',
            children: [
              {
                label: 'New Building/Block',
                href: '/admin/society/building/add'
              },
              {
                label: 'Edit Building/Block',
                href: '/admin/society/building/edit/[id]'
              },
              {
                label: 'Delete Buildings',
                href: '/admin/society/building/deletedBuildingList',
                pageType: 'table'
              },
              {
                label: "Delete Bulding form",
                href: '/admin/society/building/delete/[id]'
              }]
          },
          {
            label: 'Unit Categories',
            href: '/admin/society/societies/listUnitType',
            pageType: 'table',
            children: [
              {
                label: 'New Unit Category',
                href: '/admin/society/societies/defineUnitType',
                pageType: 'mix',
                commonPageConfig: {
                  title: 'New Unit Category',
                  buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
                },
                order: [
                  {
                    type: 'form',
                    gridProps: { xs: 12, sm: 12, md: 12 }
                  },
                  {
                    type: 'helpNote',
                    gridProps: { xs: 12, sm: 12, md: 12 }
                  }
                ]
              },
              {
                label: 'Edit Unit Category',
                href: '/admin/society/societies/editUnitType/[id]'
              }
            ]
          },
          {
            label: 'Complex Units',
            href: '/admin/society/units/list',
            pageType: 'table',
            children: [
              {
                label: "New Bulk Unit Identification",
                href: "/admin/society/units/bulkUnitAdd",
                pageType: "bulk"
              },
              {
                label: 'New Unit',
                href: '/admin/society/units/add',
                pageType: 'mix',
                commonPageConfig: {
                  title: 'New Unit',
                  buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
                },
                order: [
                  {
                    type: 'form',

                    // gridProps: { xs: 12, sm: 12, md: 8 }
                  },
                  {
                    type: 'helpNote',

                    // gridProps: { xs: 12, sm: 12, md: 4 }
                  }
                ]

              },
              {
                label: "Edit Unit",
                href: "/admin/society/units/add/[id]",
                pageType: "mix",
                commonPageConfig: {
                  title: 'Edit Unit',
                  buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
                },
                order: [
                  {
                    type: 'form',
                    gridProps: { xs: 12, sm: 12, md: 8 }
                  },
                  {
                    type: 'helpNote',
                    gridProps: { xs: 12, sm: 12, md: 4 }
                  }
                ]
              },
              {
                label: "Delete Unit",
                href: "/admin/society/units/delete/[id]"
              }
            ]
          },
          {
            label: 'Gate Settings',
            href: 'https://gate.cubeonebiz.com/auto-login?username=917977679454&session_token=JDJ5JDEwJFNaZXhBRjVsNUd5SUlFQ1FqdVdnVHU0cGZBUHhaSC93ZE1JYklxelRvaGVKSDVlWlAuVWtD',
            target: '_blank'
          }
        ]
      },
      {
        label: 'Income Setup',
        children: [
          {
            label: 'Auto Invoicing Rules',
            href: '/admin/society/income-tracker-invoice-setting/invoicelisting',
            pageType: 'table',
            children: [
              {
                label: 'New Rule',
                href: '/admin/society/income-tracker-invoice-setting/add_rule',
                pageType: 'demo'
              },
              {
                label: "Show all Rule",
                href: "/admin/society/income-tracker-invoice-setting/unitrules/[id]",
                pageType: "table"
              },
              {
                label: 'Preview Rule',
                href: '/admin/society/income-tracker-invoice-setting/preview_income/[id]',
                pageType: 'previewRule'
              },
              {
                label: 'Edit Rule',
                href: '/admin/society/income-tracker-invoice-setting/add_rule/[id]',
                pageType: 'editNewRule'
              }
            ]
          },
          {
            label: 'Incidental Invoicing Rules',
            href: '/admin/society/income-tracker-setting/readcommonbilling/activebilling',
            pageType: 'table'
          },
          {
            label: 'General Settings',
            href: '/admin/society/income-tracker-setting/readGeneralSettings',
            pageType: "mix",
            commonPageConfig: {
              title: 'General Settings',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'form'
              },
              {
                type: 'card',
                gridProps: { xs: 4, sm: 12, md: 5, lg: 12 },
                mainGrid: { xs: 12, md: 12, lg: 12 },
                keyDisplay: [
                  { key: 'created_by', label: 'Created by', keyGrid: 3, valueGrid: 3 },
                  { key: 'created_date', label: 'Created Date', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_by', label: 'Updated by', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_date', label: 'Updated Date', keyGrid: 3, valueGrid: 3 }
                ]
              },
              {
                type: 'timeline',
              }
            ]
          },
          {
            label: 'Income Accounts',
            href: '/admin/society/income-tracker-setting/readIncomeAccounts',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Income Accounts',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'table',
                apiURL: '/admin/society/income-tracker-setting/readIncomeAccounts',
              },
              {
                type: 'card', showOnRight: true,
                apiURL: '/admin/society/settings/common_timestamp',
                mainGrid: { xs: 12, md: 12, lg: 12 },
                keyDisplay: [
                  { key: 'created_by', label: 'Created by', keyGrid: 3, valueGrid: 3 },
                  { key: 'created_date', label: 'Created Date', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_by', label: 'Updated by', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_date', label: 'Updated Date', keyGrid: 3, valueGrid: 3 }
                ],
              },
              {
                type: 'timeline',
              }

            ]
          },
          {
            label: 'Particular Settings ',
            href: '/admin/society/income-tracker-setting/readParticularSettings',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Particular Settings',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'table',
                apiURL: '/admin/society/income-tracker-setting/readParticularSettings',
              },
              {
                type: 'card', showOnRight: true,
                apiURL: '/admin/society/settings/common_timestamp',
                mainGrid: { xs: 12, md: 12, lg: 12 },
                keyDisplay: [
                  { key: 'created_by', label: 'Created by', keyGrid: 3, valueGrid: 3 },
                  { key: 'created_date', label: 'Created Date', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_by', label: 'Updated by', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_date', label: 'Updated Date', keyGrid: 3, valueGrid: 3 }
                ],
              },
              {
                type: 'timeline',
              },
              {
                type: 'helpNote',
                "contentType": "sequenceChange"
              }
            ]
          },
          {
            label: 'Payment Reminder',
            href: '/admin/society/income-tracker-setting/paymentreminder',
            pageType: 'table'
          }
        ]
      },
      {
        label: 'Income Setup',
        hideInNavbar: true,
        children: [
          {
            label: 'Unit Rule',
            href: '/admin/society/income-tracker-invoice-setting/unitrules/[id]',
            pageType: 'table'
          },
          {
            label: 'Rule',
            href: '/admin/society/income-tracker-invoice-setting/preview_income/[id]'
          },
          {
            label: 'Payment Gateways',
            href: '/admin/society/accounts/paymentGateway'
          }
        ]
      },
      {
        label: 'Expense Setup',
        children: [
          {
            label: 'Expense Accounts',
            href: '/admin/society/expensetracker/settingsApply',
            pageType: 'table'
          },
          {
            label: 'Expense Approval',
            href: '/admin/society/expensetracker/setExpenseRange',
            pageType: 'table'
          },
          {
            label: 'Expense GST Rates ',
            href: '/admin/society/expensetracker/expensetaxsetup',
            pageType: 'table'
          },
          {
            label: 'Expense Budgets',
            href: '/admin/society/expensetracker/expenseaccountsbudgets',
            pageType: 'table'
          },
          {
            label: 'Expense TDS Rates',
            href: '/admin/society/expensetracker/expensetdsrate',
            pageType: 'table',
            children: [
              {
                label: 'New TDS Rate',
                href: '/admin/society/expensetracker/addexpensetdsrate'
              },
              {
                label: 'Edit TDS Rate',
                href: '/admin/society/expensetracker/addexpensetdsrate/[id]'
              }
            ]
          }
        ]
      },
      {
        label: 'General',
        children: [
          {
            label: 'App Preferences',
            href: '/admin/society/societies/preferences'
          },
          {
            label: 'Auto Response Email            ',
            href: '/admin/society/societies/emailtemplates',
            pageType: 'table',
            children: [
              {
                label: 'Email Template Details',
                href: '/admin/society/societies/emailTemplateDetail/[id]',
                pageType: 'mix',
                commonPageConfig: {
                  title: 'Auto Response Email',
                  buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
                },
                order: [
                  {
                    type: 'card',
                    mainGrid: { xs: 12, md: 10 },
                    headerKeyDisplay: [
                      { key: '', label: 'Email Template Details', isLink: true, isCheckbox: false },
                      { key: 'is_active_status', showChip: true, },
                      { key: '()', label: '', showSubHeader: true },
                      { showIcon: false, redirectUrl: '/en/admin/society/purchaseform/view/[id]', icon: 'ri-eye-line' }
                    ],
                    keyDisplay: [
                      { key: 'purpose', label: 'Purpose', keyGrid: 2, valueGrid: 2 },
                      { key: 'subject', label: 'Subject', keyGrid: 2, valueGrid: 6 }
                    ],
                    renderHtmlBelow: true,
                    htmlContent: `/admin/society/societies/emailTemplateDetail/[id]`,
                    openInNewTab: false
                  }
                ]
              },
              {
                label: 'Auto Response Edit Email',
                href: '/admin/society/societies/emailtemplateedit/[id]',
              }
            ]
          },
          {
            label: 'Auto Response SMS            ',
            href: '/admin/society/societies/smstemplates',
            pageType: 'table'
          },
          {
            label: 'Email SMS Logs',
            href: '/admin/society/societies/viewEmailSmsLog',
            pageType: 'table'
          },
          {
            label: 'Send App Notification',
            href: '/admin/society/societies/appnotification',
            pageType: 'htmlContent',
          }
        ]
      },
    ]
  }
]

export const getSettingMenu = (): VerticalMenuDataType[] => settingMenu
