import type { VerticalMenuDataType } from '@/types/menuTypes'

const helpDeskMenu: VerticalMenuDataType[] = [
  {
    label: 'Helpdesk',
    icon: 'ri-questionnaire-line',
    children: [
      {
        label: 'Member Queries',
        href: '/admin/society/helpdesk/issues',
        pageType: 'table',
        children: [
          {
            label: 'Complaint Details',
            href: '/admin/society/helpdesk/view_issue/[id]',
            pageType: 'helpdesk',
          },
          {
            label: 'Edit Member Query',
            href: '/admin/society/helpdesk/edit_issue/[id]',
          },
        ]
      },
      {
        label: 'Help Topics',
        href: '/admin/society/helpdesktopics/help_topics',
        pageType: 'table',
        children: [
          {
            label: 'Help Topic Details',
            href: '/admin/society/helpdesktopics/add_helptopic'
          },
          {
            label: 'Edit Help Topic',
            href: '/admin/society/helpdesktopics/add_helptopic/[id]',
          },
          {
            label: 'View Help Topic',
            href: '/admin/society/helpdesktopics/view_helptopic/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Help Topic',
              buttons: [
                { label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' },
              ],

            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 8, lg: 10 },
                headerKeyDisplay: [
                  { key: 'help_topic', label: 'topic Name', isLink: true, isCheckbox: false },
                  { key: 'is_active', showChip: true },
                  { key: '()', label: '', showSubHeader: true },
                ],
                keyDisplay: [
                  { key: 'note', label: 'Description', keyGrid: 3, valueGrid: 9 },
                  { key: 'parent_topic', label: 'Parent Topic', keyGrid: 3, valueGrid: 3 },
                  { key: 'assignee_name', label: 'Auto Assign To', keyGrid: 3, valueGrid: 3 },
                  { key: 'priority', label: 'Priority', keyGrid: 3, valueGrid: 3 },
                  { key: 'created_by', label: 'Visiblity', keyGrid: 3, valueGrid: 3 }
                ]
              },
              {
                type: 'card',
                mainGrid: { xs: 12, md: 8, lg: 10 },
                keyDisplay: [
                  { key: 'created_by', label: 'Created by', keyGrid: 3, valueGrid: 3 },
                  { key: 'created_date', label: 'Created Date', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_by', label: 'Updated by', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_date', label: 'Updated Date', keyGrid: 3, valueGrid: 3 },
                ]
              }
            ]
          }
        ]
      },
      {
        label: 'Canned Responses',
        href: '/admin/society/helpdeskcannedresponse/cannedresponse',
        pageType: 'mix',
        commonPageConfig: {
          title: 'Canned Responses',
          buttons: [
            {
              label: 'Add Canned Response',
              redirectUrl: '/admin/society/helpdeskcannedresponse/add_cannedresponse',
              IconClassName: 'ri:add-line',
              color: 'primary',
            }
          ]
        },
        order: [
          {
            type: 'card',
            mainGrid: { xs: 12, md: 8, lg: 10 },
            headerKeyDisplay: [
              { key: 'canned_title', label: '', isLink: true, isCheckbox: false },
              { key: 'status', showChip: false },
              { key: '()', label: '', showSubHeader: true },
              { showIcon: true, redirectUrl: '/admin/society/helpdeskcannedresponse/view_cannedresponse/[id]', IconClassName: 'ri:eye-line' },
              { showIcon: true, redirectUrl: '/admin/society/helpdeskcannedresponse/add_cannedresponse/[id]', IconClassName: 'ri:edit-line' },
            ],

            keyDisplay: [
              { key: 'canned_response', label: 'Response :', keyGrid: 3, valueGrid: 9 },
            ]
          }
        ],
        children: [
          {
            label: 'Add Canned Response',
            href: '/admin/society/helpdeskcannedresponse/add_cannedresponse'
          },
          {
            label: 'Edit Canned Response',
            href: '/admin/society/helpdeskcannedresponse/add_cannedresponse/[id]',
          },
          {
            label: 'View Canned Response',
            href: '/admin/society/helpdeskcannedresponse/view_cannedresponse/[id]',
            pageType: 'mix',
            commonPageConfig: {

              title: 'Canned Response',
              buttons: [
                { label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' },
              ]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 8, lg: 10 },
                headerKeyDisplay: [
                  { key: 'canned_title', label: '', isLink: true, isCheckbox: false },
                  { key: 'status', showChip: true },
                  { key: '()', label: '', showSubHeader: true },
                ],
                keyDisplay: [
                  { key: 'canned_response', label: 'Response', keyGrid: 3, valueGrid: 3 },
                  { key: 'sms_response', label: 'SMS Response', keyGrid: 3, valueGrid: 3 },

                ]
              },
              {
                type: 'card',
                mainGrid: { xs: 12, md: 8, lg: 10 },
                keyDisplay: [
                  { key: 'created_by', label: 'Created by', keyGrid: 3, valueGrid: 3 },
                  { key: 'created_date', label: 'Created Date', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_by', label: 'Updated by', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_date', label: 'Updated Date', keyGrid: 3, valueGrid: 3 },
                ]
              }
            ]
          }
        ]
      },
      {
        label: 'Escalations',
        href: '/admin/society/helpdesktopics/list_escalations',
        pageType: 'table',
        children: [
          {
            label: 'Escalation Details',
            href: '/admin/society/helpdesktopics/view_escalation/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Escalation',
              buttons: [
                { label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' },
              ],
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 8, lg: 10 },
                headerKeyDisplay: [
                  { key: 'help_topic', label: 'Topic Name', isLink: true, isCheckbox: false },
                  { key: '()', label: '', showSubHeader: true },
                ],
                keyDisplay: [
                  { key: 'note', label: 'Description', keyGrid: 3, valueGrid: 9 },
                  { key: 'parent_topic', label: 'Parent Topic', keyGrid: 3, valueGrid: 3 },
                  { key: 'auto_assignee_name', label: 'Auto Assign To', keyGrid: 3, valueGrid: 3 },
                  { key: 'first_escalation_name', label: 'First Escalate To', keyGrid: 3, valueGrid: 3 },
                  { key: 'first_escalation_freq', label: 'First Delayed by', keyGrid: 3, valueGrid: 3 },
                  { key: 'second_escalation_name', label: 'Second Escalate To', keyGrid: 3, valueGrid: 3 },
                  { key: 'second_escalation_freq', label: 'Second Delayed by', keyGrid: 3, valueGrid: 3 },
                  { key: 'third_escalation_name', label: 'Third Escalate To', keyGrid: 3, valueGrid: 3 },
                  { key: 'third_escalation_freq', label: 'Third Delayed by', keyGrid: 3, valueGrid: 3 },
                  { key: 'escalation_from_date_type', label: 'Escalate From', keyGrid: 3, valueGrid: 3 },
                ]
              },
              {
                type: 'card',
                mainGrid: { xs: 12, md: 8, lg: 10 },
                keyDisplay: [
                  { key: 'created_by', label: 'Created by', keyGrid: 3, valueGrid: 3 },
                  { key: 'created_date', label: 'Created Date', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_by', label: 'Updated by', keyGrid: 3, valueGrid: 3 },
                  { key: 'updated_date', label: 'Updated Date', keyGrid: 3, valueGrid: 3 },
                ]
              }
            ]
          },
          {
            label: 'Edit Escalation',
            href: '/admin/society/helpdesktopics/add_escalation/[id]',
          },
          {
            label: 'Add Escalation',
            href: '/admin/society/helpdesktopics/add_escalation',
          }
        ]
      }
    ]
  }
]

export const getHelpDeskMenu = (): VerticalMenuDataType[] => helpDeskMenu
