import type { VerticalMenuDataType } from '@/types/menuTypes'

const reportMenu: VerticalMenuDataType[] = [
  {
    label: 'Reports',
    icon: 'ri-folder-download-line',
    children: [
      {
        label: 'TDS Receivable',
        href: '/admin/society/income-details/tdsreceivableReport',
        pageType: 'table'
      },
      {
        label: 'TDS Payable',
        href: '/admin/society/vendorbill/tdspayableReport',
        pageType: 'table'
      },
      {
        label: 'Members Receivable',
        href: '/admin/society/income-details/membersReceivableReport',
        pageType: 'table'
      },
      {
        label: 'Members Incidental Receivable',
        href: '/admin/society/income-details/incidentalReceivableReport',
        pageType: 'table'
      },
      {
        label: 'Member Invoice Detail',
        href: '/admin/society/income-details/membersInvoiceDetailReport',
        pageType: 'table'
      },
      {
        label: 'Member Incidental Invoice Detail',
        href: '/admin/society/income-details/incidentalInvoiceDetailReport',
        pageType: 'table'
      },
      {
        label: 'Member Unit Statement',
        href: '/admin/society/income-details/membersUnitStatementReport',
        pageType: 'table'
      },
      {
        label: 'Member Unit Ledger Statement',
        href: '/admin/society/accountsreporting/membersUnitAccountStatementReport',
        pageType: 'table'
      },
      {
        label: 'Non Member Receivable',
        href: '/admin/society/reports/nonMembersReceivable',
        pageType: 'table'
      },
      {
        label: 'Income Receipt Report',
        href: '/admin/society/income-details/memberReceiptReport',
        pageType: 'table'
      },
      {
        label: 'Expense Report',
        href: '/admin/society/reports/expenseReport',
        pageType: 'table'
      },
      {
        label: 'Expense Payment Report',
        href: '/admin/society/reports/expensePaymentReport',
        pageType: 'table'
      },
      {
        label: 'Expense Budget Report',
        href: '/admin/society/reports/expenseBudgetReport',
        pageType: 'table'
      },
      {
        label: 'GST Reports',
        href: '/admin/society/accounts/gstReports',
        pageType: 'table'
      },
      {
        label: 'Voucher Report',
        href: '/admin/society/transaction/voucherReport',
        pageType: 'table'
      },
      {
        label: 'Receipt Report',
        href: '/admin/society/income-details/receiptReport',
        pageType: 'table'
      },
      {
        label: 'Bank Reco Statement',
        href: '/admin/society/reports/bankRecoReport',
        pageType: 'table'
      },
      {
        label: 'Member',
        children: [
          {
            label: 'Member Contact Detail            ',
            href: '/admin/society/member/memberContactDetailReportPrint',
            pageType: 'table'
          },
          {
            label: 'Members Signature List            ',
            href: '/admin/society/member/memberSignatureListReport',
            pageType: 'table'
          },
          {
            label: 'Tenants Signature',
            href: '/admin/society/member/tenantsSignatureListReport',
            pageType: 'table'
          }
        ]
      },
      {
        label: 'Credit Accounts',
        children: [
          {
            label: 'Member Advance',
            href: '/admin/society/credit-accounts/memberCreditNote',
            pageType: 'table'
          },
          {
            label: 'Non Member Advances',
            href: '/admin/society/credit-accounts/nonmemberCreditNote',
            pageType: 'table'
          }
        ]
      },
      {
        label: 'Parking',
        children: [
          {
            label: 'Parking Allottment Detail',
            href: '/admin/society/parking-allotments/parkingAllottmentDetailReportPrint',
            pageType: 'table'
          },
          {
            label: 'Vehicle Registration Detail',
            href: '/admin/society/parking-allotments/allottedVehicleDetailReportPrint',
            pageType: 'table'
          },
          {
            label: 'Registered Vehicle Count Detail',
            href: '/admin/society/parking-allotments/allottedVehicleCountDetailReport',
            pageType: 'table'
          }
        ]
      },
      {
        label: 'help Desk',
        children: [
          {
            label: 'Complaints',
            href: '/admin/society/helpdesk/helpdeskReport',
            pageType: 'table'
          }
        ]
      },
      {
        label: 'Statutory',
        children: [
          {
            label: 'J Register',
            href: '/admin/society/member/jRegisterReportPrint',
            pageType: 'table',
            children: [
              {
                label: 'J Register',
                href: '/admin/society/member/iRegiste/[id]',
                pageType: 'table'
              },
            ]
          }
        ]
      }
    ]
  }
]

export const getReportMenu = (): VerticalMenuDataType[] => reportMenu
