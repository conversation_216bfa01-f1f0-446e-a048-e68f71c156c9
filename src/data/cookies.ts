import type { CookieSerializeOptions } from 'cookie';

export const allowedCookies = ["site-settings", "cookieConsent"]

export type CookieConsent = {
  accepted: boolean
  required: boolean
  theme: boolean
  analytics: boolean
}

// Default consent values
export const defaultConsent: CookieConsent = {
  accepted: false,
  required: true,
  theme: true,
  analytics: true,
}

const isSecure = (process.env.NEXTAUTH_URL || "localhost")?.startsWith("https")

export const cookieConfig : CookieSerializeOptions = {
  httpOnly: true,
  secure: isSecure,
  sameSite: "lax",

  // path: "/",
  maxAge: 60 * 60 * 24 * 7, // 7 days
  // domain: (process.env.COOKIE_SUBDOMAIN || "") + (process.env.COOKIE_DOMAIN || ""),
  domain: process.env.COOKIE_DOMAIN || "",
};
