import { memo } from 'react';
import type {SVGAttributes} from 'react'

const Logo = ({...props}: SVGAttributes<SVGElement>) => {
  return (
    <svg
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      width="30"
      height="30"
      viewBox='0 0 72 75'
      {...props}
    >
      <path
        d="M0 0 C2.37633591 1.12834512 4.43991642 2.24979045 6.640625 3.6484375 C7.24888123 4.02102692 7.85713745 4.39361633 8.48382568 4.77749634 C10.41380141 5.96436969 12.3315599 7.16959138 14.25 8.375 C15.55397399 9.17824279 16.85865531 9.98033852 18.1640625 10.78125 C27.61676401 16.61676401 27.61676401 16.61676401 30 19 C30.22440838 21.74985124 30.32814835 24.39604856 30.3359375 27.1484375 C30.3459227 28.36184311 30.3459227 28.36184311 30.35610962 29.59976196 C30.36624362 31.31290409 30.37092641 33.02608575 30.37060547 34.73925781 C30.3749718 37.35808647 30.4112619 39.97522053 30.44921875 42.59375 C30.45508819 44.25780673 30.45905673 45.92187148 30.4609375 47.5859375 C30.47530853 48.36834808 30.48967957 49.15075867 30.50448608 49.95687866 C30.475854 53.37234377 30.34477457 55.52731798 28.3034668 58.32592773 C25.82892623 60.12432993 23.35344154 61.70370338 20.68359375 63.19921875 C19.16668945 64.05290039 19.16668945 64.05290039 17.61914062 64.92382812 C16.56919922 65.50583984 15.51925781 66.08785156 14.4375 66.6875 C13.38369141 67.28111328 12.32988281 67.87472656 11.24414062 68.48632812 C0.21871098 74.65691 0.21871098 74.65691 -7 74 C-10.11109789 72.74394414 -12.93473296 71.09110241 -15.8125 69.375 C-16.60132568 68.91931641 -17.39015137 68.46363281 -18.20288086 67.99414062 C-20.47665757 66.6760092 -22.73839335 65.33886281 -25 64 C-26.66289062 63.04480469 -26.66289062 63.04480469 -28.359375 62.0703125 C-34.85017821 58.29964358 -34.85017821 58.29964358 -36 56 C-36.35484644 50.7936642 -36.44173736 45.58770181 -36.51489258 40.37060547 C-36.5465075 38.63032587 -36.5942375 36.89026089 -36.65844727 35.15087891 C-37.17829715 20.86925896 -37.17829715 20.86925896 -34.5703125 16.73193359 C-32.46700817 15.10875471 -30.42020531 14.09242206 -28 13 C-26.19609458 11.87203458 -24.40227369 10.72783056 -22.6171875 9.5703125 C-20.70613854 8.41686731 -18.79202364 7.26848778 -16.875 6.125 C-15.43060547 5.23554688 -15.43060547 5.23554688 -13.95703125 4.328125 C-13.01988281 3.76867187 -12.08273438 3.20921875 -11.1171875 2.6328125 C-10.27784668 2.12637207 -9.43850586 1.61993164 -8.57373047 1.09814453 C-5.38690169 -0.26159326 -3.44259353 -0.43663751 0 0 Z "
        fill="red"
        transform="translate(42,2)"
      />
      <path
        d="M0 0 C0.33 0 0.66 0 1 0 C1.02464705 5.67830078 1.04283462 11.35657069 1.05493164 17.03491211 C1.05997256 18.96818585 1.06680519 20.90145576 1.07543945 22.8347168 C1.08751565 25.60726872 1.09323057 28.37977041 1.09765625 31.15234375 C1.10281754 32.02213364 1.10797882 32.89192352 1.11329651 33.78807068 C1.11349185 35.85905814 1.06208168 37.92994324 1 40 C0 41 0 41 -3.5625 41.0625 C-4.696875 41.041875 -5.83125 41.02125 -7 41 C-8.42336877 38.15326245 -8.00758837 35.85384653 -7.87890625 32.6796875 C-7.83056641 31.42285156 -7.78222656 30.16601562 -7.73242188 28.87109375 C-7.67634766 27.55238281 -7.62027344 26.23367187 -7.5625 24.875 C-7.50927587 23.53517562 -7.45653549 22.19533192 -7.40429688 20.85546875 C-7.27429992 17.57009113 -7.13932314 14.28499322 -7 11 C-7.99 11 -8.98 11 -10 11 C-8.85455923 7.56367769 -7.7288492 6.66528415 -5 4.375 C-4.278125 3.76398437 -3.55625 3.15296875 -2.8125 2.5234375 C-2.214375 2.02070312 -1.61625 1.51796875 -1 1 C-0.67 0.67 -0.34 0.34 0 0 Z "
        fill="#FEFDFD"
        transform="translate(42,35)"
      />
    </svg>
  );
};

export default memo(Logo);
