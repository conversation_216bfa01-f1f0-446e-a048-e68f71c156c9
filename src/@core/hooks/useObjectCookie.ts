import { useMemo } from 'react';

import { useCookie } from 'react-use';

export const useObjectCookie = <T>(key: string, fallback?: T | null): [T, (newVal: T) => void] => {
  const [valStr, updateCookie] = useCookie(key);

  const value = useMemo<T>(() => {
    try {
      if (valStr) {
        return JSON.parse(valStr);
      } else if (fallback !== undefined) {
        updateCookie(JSON.stringify(fallback)); // Write fallback to cookie

        return fallback;
      }
    } catch (error) {
      console.warn('Failed to parse cookie value:', error);

      return fallback as T;
    }


    return null as T;
  }, [valStr, fallback, updateCookie]);

  const updateValue = (newVal: T) => {
    updateCookie(JSON.stringify(newVal));
  };

  return [value, updateValue];
};
