// Helper function to concatenate keys
export const concatenateKeys = (item, keys) => {
    if (!keys || !Array.isArray(keys)) return '';
    
    return keys.map((key) => item[key] || '').filter(Boolean).join(' ');
};

const STATUS_MAP: Record<string, string> = {
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    0: 'Inactive',
    1: 'Active',
};

export const renderValue = (
    item: Record<string, any>,
    key: string,
    options: {
        showAsZero?: boolean;
        showAsYesNo?: boolean;
        showAsStatus?: boolean;
        displayMap?: Record<string | number, string>;
        showAsChip?: boolean;
    } = {}
): string | JSX.Element => {
    // If key is empty, return '-'
    if (!key) {
        return '-';
    }

    // Check if the key exists in the item object
    if (!(key in item)) {
        return 'N/A';
    }

    const value = item[key];

    // Handle null, undefined, or empty string values
    if (value === null || value === undefined || value === '') {
        return 'N/A';
    }

    // Display value based on options
    if (options.showAsStatus) {
        return STATUS_MAP[value] ?? '-';
    }

    if (options.showAsYesNo) {
        return value === 0 ? 'No' : 'Yes';
    }

    if (options.displayMap && options.displayMap[value] !== undefined) {
        return options.displayMap[value];
    }

    if (options.showAsZero && value === 0) {
        return '0.00';
    }

    if (options.showAsChip) {
        return value === 1 ? (
            <span style={{ color: 'green' }}>Active</span>
        ) : (
            <span style={{ color: 'red' }}>Inactive</span>
        );
    }

    // Default return for any other value
    return value;
};