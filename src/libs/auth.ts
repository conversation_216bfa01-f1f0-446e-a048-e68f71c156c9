import { create<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'crypto'

import type { AuthOptions } from 'next-auth'
import type { JWT } from 'next-auth/jwt'
import KeycloakProvider from 'next-auth/providers/keycloak'
import axios from 'axios'

// import { isAxiosError } from 'axios'

import { jwtDecrypt, EncryptJWT } from 'jose'

import { flushCookies, getIdTokenCookie, updateIdTokenCookie } from '@/app/server/actions'

// Define an interface for the expected refresh token response.
// interface RefreshTokenResponse {
//   access_token?: string;
//   refresh_token?: string;
//   error?: any;
//   [key: string]: any;
// }

// async function requestRefreshOfAccessToken(token: JWT): Promise<RefreshTokenResponse> {
//   try {
//     if (typeof token.refreshToken !== 'string') {
//       throw new Error('Invalid refresh token');
//     }

//     const { KEYCLOAK_ISSUER, <PERSON><PERSON><PERSON><PERSON><PERSON>K_CLIENT_ID, KEY<PERSON><PERSON><PERSON>_CLIENT_SECRET } = process.env;

//     if (!KEYCLOAK_ISSUER || !KEYCLOAK_CLIENT_ID || !KEYCLOAK_CLIENT_SECRET) {
//       throw new Error('Missing required environment variables');
//     }

//     const params = new URLSearchParams({
//       client_id: KEYCLOAK_CLIENT_ID,
//       client_secret: KEYCLOAK_CLIENT_SECRET,
//       grant_type: 'refresh_token',
//       refresh_token: token.refreshToken,
//     });

//     const response = await axios.post(
//       `${KEYCLOAK_ISSUER}/protocol/openid-connect/token`,
//       params,
//       {
//         headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
//         withCredentials: true,
//       }
//     );

//     return response.data;
//   } catch (error) {
//     if (isAxiosError(error)) {
//       if (error.response) {
//         console.warn('Error Response:', error.response.data);
//         console.warn('Error Status:', error.response.status);
//         return { error: error.response.data || 'Refresh AccessToken Error' };
//       } else if (error.request) {
//         console.warn('No response received:', error.request);
//         return { error: 'No response from server' };
//       } else {
//         console.warn('Error setting up request:', error.message);
//         return { error: error.message };
//       }
//     } else {
//       console.warn('Unexpected Error:', error);
//       return { error: 'Unexpected Error occurred' };
//     }
//   }
// }

export const decode: any['decode'] = async ({ token, secret }): Promise<JWT | null> => {
  try {
    const secretKey = createSecretKey(Buffer.from(secret, 'base64'));

    // Use the proper decryption options.
    const { payload } = await jwtDecrypt(token, secretKey, {
      keyManagementAlgorithms: ['A256KW'],
      contentEncryptionAlgorithms: ['A256GCM'],
    });

    return payload as JWT;
  } catch (err) {
    console.warn(err);

    return null;
  }
}

async function doFinalSignoutHandshake(): Promise<void> {
  try {
    const idToken: string = await getIdTokenCookie();
    const params = new URLSearchParams();

    params.append('id_token_hint', idToken);

    const { status, statusText, data } = await axios.get(
      `${process.env.KEYCLOAK_ISSUER}/protocol/openid-connect/logout?${params.toString()}`
    );

    if (status !== 200) {
      console.warn("Error logging out", { status, statusText, data });
    } else {
      console.log("Logged out successfully", { status, statusText, data });
    }

    await flushCookies();

  } catch (error) {
    console.warn(error);
  }
}

export const authOptions: AuthOptions = {
  providers: [
    KeycloakProvider({
      clientId: process.env.KEYCLOAK_CLIENT_ID || '',
      clientSecret: process.env.KEYCLOAK_CLIENT_SECRET || '',
      issuer: process.env.KEYCLOAK_ISSUER || ''
    })
  ],
  pages: {
    signIn: '/'
  },
  secret: process.env.NEXTAUTH_SECRET || '',
  session: {
    strategy: 'jwt'
  },
  cookies: {
    sessionToken: {
      name: process.env.AUTH_COOKIE_NAME || 'x-access-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.COOKIE_DOMAIN || ''
      }
    },
  },
  jwt: {
    async encode({ token, secret, maxAge }): Promise<string> {
      try {
        if (typeof secret !== "string") {
          throw new Error("Secret must be a string.");
        }

        const secretKey = createSecretKey(new Uint8Array(Buffer.from(secret, 'base64')));
        const now = Math.floor(Date.now() / 1000);

        const payload = {
          exp: now + (maxAge || 60 * 60),
          ...token,
        };

        return await new EncryptJWT(payload)
          .setProtectedHeader({ alg: 'A256KW', enc: 'A256GCM' })
          .setIssuedAt()
          .setExpirationTime('2h')
          .setNotBefore('0s')
          .encrypt(secretKey);
      } catch (err) {
        console.warn(err, "Error encoding JWT");

        return "";
      }
    },
    decode,
  },

  callbacks: {
    async jwt({ token, account, profile }): Promise<JWT> {
      try {
        // console.log("jwt callback", { token, account, profile }); 
        if (profile) {
          // Explicitly type-cast profile to any if its properties are not defined in its type.
          token.old_gate_user_id = (profile as any).old_gate_user_id;
          token.old_sso_user_id = (profile as any).old_sso_user_id;

          if (account?.id_token) {
            await updateIdTokenCookie(account.id_token);
          }
        }

        return token;
      } catch (err) {
        console.warn(err);
        
        return token;
      }
    }
  },
  events: {
    signOut: async () => await doFinalSignoutHandshake()
  }
};
