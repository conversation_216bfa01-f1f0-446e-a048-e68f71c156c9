 
import { useCallback, useEffect, useRef, useState } from "react";

import { Database } from "@/data/db";

// Utility function to wrap an IDBRequest in a promise
const promisifyRequest = <T>(request: IDBRequest<T>): Promise<T> =>
  new Promise((resolve, reject) => {
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });

export interface UseIndexedDBResult<T = any> {
  getValue: (tableName: string, id: IDBValidKey) => Promise<T | undefined>;
  getAllValue: (tableName: string) => Promise<T[]>;
  putValue: (tableName: string, value: T, id: IDBValidKey) => Promise<IDBValidKey>;
  putBulkValue: (tableName: string, values: { id: IDBValidKey; value: any }[]) => Promise<T[]>;
  putKeyValuePairs: (tableName: string, data: { [key: string]: any }) => Promise<void>;
  updateValue: (tableName: string, id: IDBValidKey, newItem: Partial<T>) => Promise<T>;
  deleteValue: (tableName: string, id: IDBValidKey) => Promise<IDBValidKey>;
  deleteAll: (tableName: string) => Promise<void>;
  isDBConnecting: boolean;
  error: Error | null;
}

export const useIndexedDB = (
  databaseName: string,
  tableNames: string[]
): UseIndexedDBResult => {
  const [db, setDB] = useState<IDBDatabase | null>(null);
  const [isDBConnecting, setIsDBConnecting] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const dbClosedRef = useRef<boolean>(false);

  useEffect(() => {
    dbClosedRef.current = false;

    if (!("indexedDB" in window)) {
      setError(new Error("IndexedDB is not supported in this environment."));
      setIsDBConnecting(false);

      return;
    }

    let activeDB: IDBDatabase | null = null;
    const request = indexedDB.open(databaseName, Database.version);

    // onupgradeneeded creates object stores if they don't already exist.
    request.onupgradeneeded = () => {
      const database = request.result;

      Database.tables.forEach((tableName) => {
        if (!database.objectStoreNames.contains(tableName)) {
          // Create object store without a keyPath to allow external keys.
          database.createObjectStore(tableName);
        }
      });
    };

    request.onsuccess = () => {
      activeDB = request.result;


      // If the component unmounted before the DB was ready, close the connection.
      if (dbClosedRef.current) {
        activeDB.close();
      } else {
        setDB(activeDB);
        setIsDBConnecting(false);
      }
    };

    request.onerror = () => {
      setError(request.error);
      console.warn("Error initializing IndexedDB:", request.error);
      setIsDBConnecting(false);
    };

    // Cleanup on unmount.
    return () => {
      dbClosedRef.current = true;

      if (activeDB) {
        activeDB.close();
      }
    };
  }, [databaseName, tableNames]);

  // getStore is memoized to ensure stable reference.
  const getStore = useCallback(
    (tableName: string, mode: IDBTransactionMode) => {
      if (dbClosedRef.current || !db) {
        throw new Error("Database is not initialized or has been closed.");
      }


      return db.transaction(tableName, mode).objectStore(tableName);
    },
    [db]
  );

  const getValue = useCallback(
    async (tableName: string, id: IDBValidKey): Promise<any> => {
      try {
        const store = getStore(tableName, "readonly");
        const request = store.get(id);


        return await promisifyRequest(request);
      } catch (err) {
        throw err;
      }
    },
    [getStore]
  );

  const getAllValue = useCallback(
    async (tableName: string): Promise<any[]> => {
      try {
        const store = getStore(tableName, "readonly");
        const request = store.getAll();


        return await promisifyRequest(request);
      } catch (err) {
        throw err;
      }
    },
    [getStore]
  );

  const putValue = useCallback(
    async (tableName: string, value: any, key: IDBValidKey): Promise<IDBValidKey> => {
      const store = getStore(tableName, "readwrite");


      return await promisifyRequest(store.put(value, key));
    },
    [getStore]
  );

  const putBulkValue = useCallback(
    async (
      tableName: string,
      values: { id: IDBValidKey; value: any }[]
    ): Promise<any[]> => {
      try {
        console.log({ tableName, values })
        const store = getStore(tableName, "readwrite");

        const promises = values.map(async ({ id, ...value }) => {
          return await promisifyRequest(store.put(value, id));
        });

        await Promise.all(promises);

        return await getAllValue(tableName);
      } catch (err) {
        throw new Error("Bulk insert failed: " + err);
      }
    },
    [getStore, getAllValue]
  );

  const putKeyValuePairs = useCallback(
    async (tableName: string, data: Record<string, any>): Promise<void> => {
      const store = getStore(tableName, "readwrite");


      // Store each key/value pair separately.
      const promises = Object.entries(data).map(([key, value]) =>
        promisifyRequest(store.put(value, key))
      );

      await Promise.all(promises);
    },
    [getStore]
  );

  const updateValue = useCallback(
    async (tableName: string, key: IDBValidKey, newValue: Partial<any>): Promise<any> => {
      const store = getStore(tableName, "readwrite");


      // This overwrites the existing value.
      return await promisifyRequest(store.put(newValue, key));
    },
    [getStore]
  );

  const deleteValue = useCallback(
    async (tableName: string, id: IDBValidKey): Promise<IDBValidKey> => {
      try {
        const store = getStore(tableName, "readwrite");
        const deleteRequest = store.delete(id);

        await promisifyRequest(deleteRequest);

        return id;
      } catch (err) {
        throw err;
      }
    },
    [getStore]
  );

  const deleteAll = useCallback(
    async (tableName: string): Promise<void> => {
      try {
        const store = getStore(tableName, "readwrite");
        const clearRequest = store.clear();

        await promisifyRequest(clearRequest);
      } catch (err) {
        throw err;
      }
    },
    [getStore]
  );

  return {
    getValue,
    getAllValue,
    putValue,
    putBulkValue,
    updateValue,
    putKeyValuePairs,
    deleteValue,
    deleteAll,
    isDBConnecting,
    error,
  };
};
