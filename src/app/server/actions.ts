"use server"

import { cookies } from "next/headers";

import { allowedCookies, cookieConfig } from "@/data/cookies";

export async function updateCompanyIdCookie(companyId: number) {
  try {
    (await cookies()).set("company_id", companyId.toString(), cookieConfig);

    return true;
  } catch (error) {
    console.log(error, "error");

    return false;
  }
}

export async function updateIdTokenCookie(idToken: string) {
  try {
    (await cookies()).set(
      "id_token",
      idToken,
      { ...cookieConfig, domain: process.env.COOKIE_DOMAIN }
    );

    return true;
  } catch (error) {
    console.log(error);

    return false;
  }
}

export async function getIdTokenCookie() {
  try {
    return (await cookies()).get("id_token")?.value;
  } catch (error) {
    console.log(error);
  }
}

export async function updateCompanyNameCookie(companyName: string) {
  try {
    (await cookies()).set("company_name", companyName, cookieConfig);

    return true;
  } catch (error) {
    console.log(error);

    return false;
  }
}

export async function getCompanyCookie() {
  try {
    const name = (await cookies()).get("company_name")?.value;
    const id = (await cookies()).get("company_id")?.value;


    return { name, id };
  } catch (error) {
    console.log(error);
  }
}

export async function updateToggleModeCookie(mode: "society" | "gate") {
  try {
    (await cookies()).set("toggle_mode", mode, cookieConfig);

    return true;
  } catch (error) {
    console.log(error, "error setting toggle_mode");

    return false;
  }
}

export async function getToggleModeCookie() {
  try {
    return (await cookies()).get("toggle_mode")?.value as
      | "society"
      | "gate"
      | undefined;
  } catch (error) {
    console.log(error);
  }
}

export async function removeCompanyIdCookie() {
  try {
    (await cookies()).set("company_id", "", { ...cookieConfig, maxAge: 0 });
    (await cookies()).set("company_name", "", { ...cookieConfig, maxAge: 0 });
    (await cookies()).set(
      "id_token",
      "",
      { ...cookieConfig, maxAge: 0, domain: process.env.COOKIE_DOMAIN }
    );

    // also clear toggle_mode
    (await cookies()).set("toggle_mode", "", { ...cookieConfig, maxAge: 0 });

    return true;
  } catch (error) {
    console.log(error);

    return false;
  }
}

export async function flushCookies(allCookies = []) {
  try {
    if (!allCookies.length) return false;
    allCookies.forEach(async (cookie) => {
      if (!allowedCookies.includes(cookie.name)) {
        console.log("Deleting cookie", cookie.name);
        (await cookies()).delete(cookie.name);
      }
    });

    return true;
  } catch (error) {
    console.log(error);

    return false;
  }
}
