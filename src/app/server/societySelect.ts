import { cookies } from 'next/headers';

// lib/companyService.ts
import axios from 'axios';

import type { CompanyType } from '@/components/layout/shared/CompanyDropdown';

export interface CompaniesResponse {
  companies: CompanyType[];
  status?: string;
  message?: string;
}

export async function getCompanies(): Promise<CompaniesResponse> {
  const cookieName = process.env.AUTH_COOKIE_NAME || 'x-access-token';
  const token = (await cookies()).get(cookieName)?.value;

  if (!token) {
    return {
      companies: [],
      status: 'error',
      message: 'No token found',
    };
  }

  // console.log(cookies().getAll(), "All", process.env.NODE_ENV)

  const response = await axios.get("/company-list", {
    baseURL: process.env.API_URL,
    validateStatus: () => true,
    headers: {
      [cookieName]: token
    },
    withCredentials: true,
  });

  const companies: CompanyType[] = Object.values(response.data?.data || {}) || [];

  return {
    companies,
    status: response.data?.status,
    message: response.data?.message,
  };
}
