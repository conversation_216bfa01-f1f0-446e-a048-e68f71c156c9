'use server'

import { cookies } from 'next/headers'

import { defaultConsent } from '@/data/cookies'
import type { CookieConsent } from '@/data/cookies'

const cookieConsentName = 'cookieConsent'

// Server Action to save cookie consent securely
export async function saveConsent(newConsent: Partial<CookieConsent>) {
  const store = await cookies()

  // Get existing consent from cookies or use default if not found
  const existingConsent = store.get(cookieConsentName)
  const parsedConsent = existingConsent ? JSON.parse(existingConsent.value) : defaultConsent

  // Merge existing consent with new values
  const updatedConsent = { ...parsedConsent, ...newConsent }

  // Set the cookie with HttpOnly and Secure flags
  store.set({
    name: 'cookieConsent',
    value: JSON.stringify(updatedConsent),
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    maxAge: 60 * 60 * 24 * 180, // 180 days (6 months)
  })

  return updatedConsent
}

export async function getConsent() {
  // Get existing consent from cookies or use default if not found
  const existingConsent = (await cookies()).get(cookieConsentName)
  
  return existingConsent ? JSON.parse(existingConsent.value) : defaultConsent
}
