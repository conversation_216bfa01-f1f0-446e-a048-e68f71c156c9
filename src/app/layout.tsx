// Third-party Imports
import 'react-perfect-scrollbar/dist/css/styles.css'

// MUI Imports
import InitColorSchemeScript from '@mui/material/InitColorSchemeScript'

import type { ChildrenType } from '@core/types'

// Style Imports
import './globals.css'

// Generated Icon CSS Imports
import '@assets/iconify-icons/generated-icons.css'

import { getSystemMode } from '@/@core/utils/serverHelpers'

export const viewport = {
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  width: "device-width",
  height: "device-height",
  userScalable: false,
  themeColor: "#EB3D47",
}

export const metadata = {
  title: "OneSociety",
  applicationName: "OneSociety",
  icons: {
    icon: [
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
    ],
    apple: { url: "/apple-touch-icon.png", sizes: "180x180" },
    other: [
      {
        rel: "mask-icon",
        url: "/safari-pinned-tab.svg",
        color: "#5bbad5"
      }
    ]
  },
  manifest: "/manifest.webmanifest",
  appleWebApp: {
    title: "OneSociety",
    statusBarStyle: "black-translucent"
  },
  msapplication: {
    TileColor: "#ffc40d"
  },
  charset: "utf-8"
}


const RootLayout = async ({
  children
}: ChildrenType) => {
  const systemMode = await getSystemMode();

  return (
    <html id='__next' suppressHydrationWarning={true} dir="ltr">
      <body className='flex is-full min-bs-full flex-auto flex-col'>
        <InitColorSchemeScript attribute='data' defaultMode={systemMode} />
        {children}
      </body>
    </html>
  )
}

export default RootLayout