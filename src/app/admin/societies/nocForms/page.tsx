import React from 'react';

import Link from 'next/link';

import type { Metadata } from 'next';

import { Box, Breadcrumbs, Typography } from '@mui/material';
import { Icon } from '@iconify/react/dist/iconify.js';

import NocFormsClient from '@/components/nocForms/NocFormsClient';

export const metadata: Metadata = {
  title: 'NOC Forms - Society App',
  description: 'NOC Forms for Society Management',
};

const Page = () => {
  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto' }}>
      <Box sx={{ mb: 3, px: 3, pt: 2 }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link href="/admin/dashboard" passHref>
            <Typography color="inherit" sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none', cursor: 'pointer' }}>
              <Icon icon="ri-home-line" style={{ marginRight: '4px' }} />
              Home
            </Typography>
          </Link>
          <Link href="/admin/society/noc/list" passHref>
            <Typography color="inherit" sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none', cursor: 'pointer' }}>
              <Icon icon="ri-file-list-line" style={{ marginRight: '4px' }} />
              NOC
            </Typography>
          </Link>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
            <Icon icon="ri-file-text-line" style={{ marginRight: '4px' }} />
            NOC Forms
          </Typography>
        </Breadcrumbs>
      </Box>
      <NocFormsClient />
    </Box>
  );
};

export default Page;
