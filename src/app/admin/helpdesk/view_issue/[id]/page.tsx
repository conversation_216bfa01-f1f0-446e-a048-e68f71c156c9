import React from 'react';

import Link from 'next/link';

import type { Metadata } from 'next';

import { Box, Breadcrumbs, Typography } from '@mui/material';
import { Icon } from '@iconify/react/dist/iconify.js';

import ComplaintDetailsClient from '@/components/helpdesk/ComplaintDetailsClient';

export const metadata: Metadata = {
  title: 'Complaint Details - Society App',
  description: 'View and manage complaint details',
};

const ComplaintDetailsPage = ({ params }: { params: { id: string } }) => {
  
  console.log(params.id);

  return (
    <>
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link href="/admin/dashboard" passHref>
            <Typography 
              component="span" 
              color="text.secondary" 
              sx={{ 
                display: 'flex', 
                alignItems: 'center',
                '&:hover': { color: 'primary.main' }
              }}
            >
              <Icon icon="ri-home-line" style={{ marginRight: '4px', fontSize: '18px' }} />
              Home
            </Typography>
          </Link>
          <Link href="/admin/society/helpdesk/issues" passHref>
            <Typography 
              component="span" 
              color="text.secondary" 
              sx={{ 
                display: 'flex', 
                alignItems: 'center',
                '&:hover': { color: 'primary.main' }
              }}
            >
              <Icon icon="ri-questionnaire-line" style={{ marginRight: '4px', fontSize: '18px' }} />
              Complaint Issues
            </Typography>
          </Link>
          <Typography 
            color="text.primary" 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              fontWeight: 500
            }}
          >
            <Icon icon="ri-ticket-line" style={{ marginRight: '4px', fontSize: '18px' }} />
            Complaint Details
          </Typography>
        </Breadcrumbs>
      </Box>
      
      <ComplaintDetailsClient />
    </>
  );
};

export default ComplaintDetailsPage;
