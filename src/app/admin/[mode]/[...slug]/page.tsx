// @ts-nocheck


import type { Metadata } from 'next';

import verticalMenuData from "@/data/navigation/verticalMenuData";

// import Link from 'next/link';
import PageTypeScreen from '@/views/PageTypeScreen';

import NotFound from '@/views/NotFound';

// import { getMarkdownHTMLFromPath } from '@/utils/markdownToHTML';

import PageHeader<PERSON>ithHelp from '@/views/pageheader/PageHeaderWithHelp';
import { deduplicateVerticalMenu, getPaths, findItemWithBreadcrumb } from '@/utils/menu';

// export const dynamic = "force-dynamic"; // Mark this as dynamic to ensure server-side generation for uncached pages
// export const dynamic = 'force-static';

export async function generateStaticParams() {
  const societyData = verticalMenuData('society');
  const gateData = verticalMenuData('gate');

  const allMenus = [...societyData, ...gateData];
  const uniqueMenus = deduplicateVerticalMenu(allMenus);
  const paths = getPaths(uniqueMenus);

  return paths;
}

// Generate metadata dynamically
export const generateMetadata = async ({ params }): Promise<Metadata> => {
  const para = await params
  const slugs = para?.slug
  const adminMode = para?.mode

  const slug: string[] = ["admin", adminMode, ...(slugs || [])];
  const data = verticalMenuData(adminMode);
  const { pageData } = findItemWithBreadcrumb(data, slug);

  if (!pageData) {
    return {
      title: "404 | Page Not Found",
    };
  }

  return {
    title: String(pageData?.label) || "404 | Page Not Found",
    description: String(pageData?.description || pageData?.label) || "404 | Page Not Found",
  };
};

// Page Component
const Page = async ({ params }) => {
  const para = await params

  const slugs = para?.slug
  const adminMode = para?.mode

  const slug: string[] = ["admin", adminMode, ...(slugs || [])];
  const data = verticalMenuData(adminMode);
  const { pageData, breadcrumbs } = findItemWithBreadcrumb(data, slug)

  if (!pageData) return <NotFound mode="system" />;

  // const lastItem = breadcrumbs?.[breadcrumbs.length - 1]?.href
  // const markdown = await getMarkdownHTMLFromPath(lastItem)

  const sanitizedSlug = slug?.map((s) => {
    return s === decodeURIComponent(s) ? encodeURIComponent(s) : s;
  });

  const pathname = sanitizedSlug ? `/${sanitizedSlug.join('/')}` : '/';

  return (
    <>
      <PageHeaderWithHelp items={breadcrumbs} helpContent={null} />
      <PageTypeScreen pageData={pageData} pathname={pathname} />
    </>
  );
};

export default Page;
