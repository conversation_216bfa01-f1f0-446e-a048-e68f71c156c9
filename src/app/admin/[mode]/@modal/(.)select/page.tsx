import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { Alert } from '@mui/material';

import Modal from '@/views/form/components/Modal';
import { getCompanies } from '@/app/server/societySelect';
import { cookieConfig } from '@/data/cookies';
import CompanyDropdown from '@/components/layout/shared/CompanyDropdown';

// import { auth, update } from '@/app/api/auth/[...nextauth]/route';
// import { getSession } from 'next-auth/react';

export default async function SelectModal() {
  const { companies, status, message } = await getCompanies();

  // console.log({ companies, status, message })
  if (!companies || !Array.isArray(companies) || companies.length === 0) {
    redirect('/api/flush');
  }

  // If there's only one valid company, set the cookies and redirect
  if (companies.length === 1 && companies[0].company_id && !isNaN(companies[0].company_id)) {
    const store = await cookies()
    
    store.set("company_id", companies[0].company_id.toString(), cookieConfig);
    store.set("company_name", companies[0].company_name, cookieConfig);
    redirect('/admin/dashboard');
  }

  return <Modal title='Select Company' screenFrom='popup' footer={null}>
    {
      status === "error" ? <Alert severity='error' icon={<i className="line-md-downloading-loop" />}>
        {message || 'Error fetching company list!'}
      </Alert> : <CompanyDropdown companies={companies as any} />
    }
  </Modal>
}
