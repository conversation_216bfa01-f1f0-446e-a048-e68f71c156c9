"use server"

import Builder from '@/views/form/builder/server';

export default async function FormModal({
  params,
  searchParams
}) {
  const para = await params;
  const query = await searchParams;

  const form = para?.form
  const mode = para?.mode
  
  if (!form?.length) return <div>Invalid form</div>;
  
  const lastElement = form?.slice(-1)[0];
  const lastElementInt = isNaN(Number(lastElement)) ? null : Number(lastElement)
  const href = lastElementInt === null ? form : form.slice(0, -1);
  
  if (lastElementInt !== null) {
    if (!isNaN(lastElementInt)) {
      href.push("[id]")
    } else href.push(lastElement)
  }

  return <Builder href={[mode,...href]?.join("/")} params={{ id: lastElementInt, ...query }} screenFrom='popup'/>
}
