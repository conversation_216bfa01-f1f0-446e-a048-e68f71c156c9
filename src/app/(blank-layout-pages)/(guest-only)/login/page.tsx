import { redirect } from 'next/navigation';

import { getServerSession } from 'next-auth';

import { authOptions } from '@/libs/auth';

import Login from '@/components/login';
import GuestOnlyRoute from '@/hocs/GuestOnlyRoute';

const signinErrors: Record<string, string> = {
  default: 'Unable to sign in.',
  signin: 'Try signing in with a different account.',
  oauthsignin: 'Try signing in with a different account.',
  oauthcallbackerror: 'Try signing in with a different account.',
  oauthcreateaccount: 'Try signing in with a different account.',
  emailcreateaccount: 'Try signing in with a different account.',
  callback: 'Try signing in with a different account.',
  oauthaccountnotlinked:
    'To confirm your identity, sign in with the same account you used originally.',
  sessionrequired: 'Please sign in to access this page.',
};

type Props = {
  searchParams: Promise<{
    redirectTo?: string;
    error?: string;
  }>;
};

export default async function Signin({ searchParams }: Props) {
  const session = await getServerSession(authOptions);
  const resolvedParams = await searchParams; // ✅ Await it here

  console.log(session, resolvedParams)

  const callbackUrl = resolvedParams?.redirectTo || '/';
  const errorKey = resolvedParams?.error?.toLowerCase() || 'default';
  const errorMessage = signinErrors[errorKey];

  if (session) {
    redirect(callbackUrl);
  }

  return (
    <GuestOnlyRoute>
      <div className="flex flex-col space-y-3 justify-center items-center h-screen">
        {errorMessage && <div className="text-red-600">{errorMessage}</div>}
        <Login />
      </div>
    </GuestOnlyRoute>
  );
}
