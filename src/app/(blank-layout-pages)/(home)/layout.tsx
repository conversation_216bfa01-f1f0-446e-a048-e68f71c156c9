// MUI Imports
import Button from '@mui/material/Button'

// Third-party Imports
import 'react-perfect-scrollbar/dist/css/styles.css'

// Type Imports
import type { ChildrenType } from '@core/types'

// Context Imports
import { IntersectionProvider } from '@/contexts/intersectionContext'

// Component Imports
import FrontLayout from '@components/layout/front-pages'
import ScrollToTop from '@core/components/scroll-to-top'

// Style Imports
import '@/app/globals.css'

// Generated Icon CSS Imports
import '@assets/iconify-icons/generated-icons.css'
import Customizer from '@/@core/components/customizer'

export const metadata = {
  title: 'OneSociety - Society Management System',
  description: 'OneSociety is a society management system that helps you manage your society with ease.'
}

const Layout = ({ children }: ChildrenType) => {

  return (
    <IntersectionProvider>
      <FrontLayout>
        {children}
        <ScrollToTop className='mui-fixed'>
          <Button
            variant='contained'
            className='is-10 bs-10 rounded-full p-0 min-is-0 flex items-center justify-center'
          >
            <i className='ri-arrow-up-line' />
          </Button>
        </ScrollToTop>
        <Customizer />
      </FrontLayout>
    </IntersectionProvider>
  )
}

export default Layout
