import React from 'react';

import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { Alert } from '@mui/material';

import { getCompanies } from '@/app/server/societySelect';
import CompanyDropdown from '@/components/layout/shared/CompanyDropdown';
import { cookieConfig } from '@/data/cookies';

const Page = async () => {
  const { companies, status, message } = await getCompanies();

  // Redirect to /api/flush if no companies are available
  if (!companies || !Array.isArray(companies) || companies.length === 0) {
    redirect('/api/flush');
  }

  // If there's only one valid company, set cookies and redirect to dashboard
  if (companies.length === 1 && companies[0].company_id && !isNaN(companies[0].company_id)) {
    const cookieStore = await cookies();

    cookieStore.set("company_id", companies[0].company_id.toString(), cookieConfig);
    cookieStore.set("company_name", companies[0].company_name, cookieConfig);
    cookieStore.set("toggle_mode", "society", cookieConfig);

    redirect(`/admin/${"society"}/dashboard`);
  }

  // Show dropdown to select company
  return status === "error" ? (
    <Alert severity="error" icon={<i className="line-md-downloading-loop" />}>
      {message || 'Error fetching company list!'}
    </Alert>
  ) : (
    <CompanyDropdown companies={companies} isPopup={false} />
  );
};

export default Page;
