import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'


//   title = "June 2019"
//   id = "6-2019"
const generateMonths = (start, end) => {
    const startDate = new Date(start);
    const endDate = new Date(end);

    // We won't include "All Months" in this array; 
    // we handle that as a separate default or your choice
    const generatedMonths = [];

    const current = new Date(startDate);

    while (current <= endDate) {
        const year = current.getFullYear();

        // Months in JS are 0-based, so +1 to get normal month
        const monthIndex = current.getMonth() + 1;

        const title = current.toLocaleString('default', {
            month: 'long',
            year: 'numeric',
        });

        const id = `${monthIndex}-${year}`; // e.g. "6-2019"

        generatedMonths.push({ title, id });

        // Move to next month
        current.setMonth(current.getMonth() + 1);
    }

    return generatedMonths
};

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const start = searchParams.get('start')
  const end = searchParams.get('end')

  // Basic validation
  if (!start || !end) {
    return NextResponse.json(
      { error: 'Both `start` and `end` query parameters are required.', status: 400, data: [] },
      { status: 400 }
    )
  }

  return NextResponse.json({ status: 'success', data: generateMonths(start, end) }, { status: 200 })
}

