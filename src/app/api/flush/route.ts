// app/api/flush-cookies/route.ts
import { NextResponse } from 'next/server';

import { cookieConfig } from '@/data/cookies';

export async function GET(request: Request) {
  const response = NextResponse.redirect(new URL('/', request.url));

  try {
    response.cookies.set(process.env.AUTH_COOKIE_NAME, '', { ...cookieConfig, maxAge: 0, domain: process.env.COOKIE_DOMAIN });
    response.cookies.set('company_id', '', { ...cookieConfig, maxAge: 0 });
    response.cookies.set('company_name', '', { ...cookieConfig, maxAge: 0 });

    return response;
  } catch (err) {
    console.log(err)

    return response;
  }
}
