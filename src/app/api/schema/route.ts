import { readdir, readFile } from "fs/promises";
import { join, parse, sep } from "path";

import type { Fact, Rule } from "json-rules-engine";

import generateSchema from "@/utils/generateSchema";

type apiResponseTypes = {
  schema: {
    flow: any; // Updated to support only flow
    actions: any[];
    rules: Array<Rule>;
    facts?: Array<Fact>;
    engine?: any;
  };
  meta: {
    layout?: "tabs" | "steps" | "horizontal" | "vertical";
    title: string;
    data_source: string;
    titleInCenter?: boolean;
    method?: string;
  };
};

async function readFiles(directory: string): Promise<string[]> {
  const files: string[] = [];

  async function read(directory: string) {
    const entries = await readdir(directory, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = join(directory, entry.name);

      if (entry.isDirectory()) {
        await read(fullPath);
      } else if (entry.isFile() && entry.name.endsWith(".json")) {
        files.push(fullPath);
      }
    }
  }

  await read(directory);

  return files;
}

function extractFilePathParams(filePath: string, schemaDirectory: string) {
  const { dir, name } = parse(filePath);
  const schemaDirParts = schemaDirectory.split(sep);
  const relativeDir = dir.split(sep).slice(schemaDirParts.length).join(sep);

  const dirParts = relativeDir.split(sep).filter(Boolean);

  if (dirParts.length === 0) return null;

  if (name === "index") {
    return {
      params: { slug: dirParts },
    };
  }

  return {
    params: { slug: [...dirParts, name].filter(Boolean) },
  };
}

export const getPaths = async (): Promise<{ paths: any; fallback: boolean }> => {
  const schemaDirectory = join(process.cwd(), "src/schema");
  const files = await readFiles(schemaDirectory);

  const params = files
    .map((filePath) => extractFilePathParams(filePath, schemaDirectory))
    .filter(Boolean);

  return { paths: params, fallback: false };
};

export const getSchema = async (schema_path: string[] = []) => {
  try {
    const dirName = process.env.NODE_ENV === "development" ? "schema" : "modified";
    const withoutAdminPath = schema_path[0] === "admin" ? schema_path.slice(1) : schema_path;
    const basePath = join(process.cwd(), "src", "data", dirName, ...withoutAdminPath);

    const schema = await readFile(join(basePath, "index.json"), "utf-8").catch(async () => {
      return await readFile(`${basePath}.json`, "utf-8").catch(() => null);
    });

    if (!schema) return null;

    return JSON.parse(schema) as apiResponseTypes;
  } catch (err) {
    console.log("Error in getSchema", err);

    return null;
  }
};

export const getFormattedSchema = async (schema_path: string[] = [], params: any) => {
  try {
    const result = await getSchema(schema_path);

    // console.log("result", result);

    if (!result) return null;

    if (!result?.schema?.flow) return result;

    // result.schema.flow.children["__loadingFields"] = {
    //   hidden: true,
    //   type: "array",
    //   default: ["vendor_gst"]
    // }

    if (params?.id || result.meta?.data_source || result?.meta?.method?.toLowerCase() === "delete") {
      for (const [key, value] of Object.entries(params)) {
        result.schema.flow.children[key] = {
          hidden: true,
          type: ["number", "string"],
          required: true,
          ...result.schema.flow.children[key],
          ...(value && { default: isNaN(value as any) ? value : Number(value) })
        }
      }
    }

    const [schema, uiSchema] = generateSchema(result.schema.flow?.children);

    result.schema.flow.children = {
      ...schema,
      allowUndefinedFacts: true,
      title: result.schema.flow?.title || "",
      required: [...(result.schema.flow?.required || []), ...schema.required],
    };

    result.schema.flow.uiSchema = {
      ...uiSchema,
      "ui:order": result.schema.flow?.order || ["*"],
    };

    return result;
  } catch (err) {
    console.log(err, "error?");

    return null;
  }
};

