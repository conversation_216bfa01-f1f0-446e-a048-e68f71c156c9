import { NextResponse } from 'next/server';

/**
 * GET handler for the /admin/member/bulkAdd API route
 * Returns a mock response with allottee data and schema for a table UI
 */
export async function GET() {
  // Mock response data based on bulkAdd.json schema
  const responseData = {
    "status": "success",
    "status_code": "200",
    "message": "Allottees listed successfully",
    "data": [
      {
        id: 1
      },
      {
        "id": 2,
        "soc_id": 8454,
        "soc_building_name": {
          "id": 2,
          "soc_building_name": "Tower B",
          "floor_array": ["1", "2", "3", "4"]
        },
        "soc_building_floor": "3",
        "unit_flat_number": "302",
        "name": "<PERSON>",
        "mobile_no": "+919876543211",
        "email": "<EMAIL>",
        "effective_date": "2023-02-15"
      }
    ],
    "meta": {
      "pagination": {
        "total": 2
      },
      "schema": {
        "table": {
          "tableTitle": "Bulk Allottees",
          "fields": ["*"],
          "columns": [
            {
              "title": "Building",
              "key": "soc_building_name",
              "editable": true,
              "type": "dropdown",
              "apiPath": "/admin/building/list",
              "label": "soc_building_name",
            },
            {
              "title": "Floor",
              "key": "soc_building_floor",
              "editable": true,
              "type": "select",
              "dependent": "soc_building_name"
            },
            {
              "title": "Unit Number",
              "key": "unit_flat_number",
              "editable": true
            },
            {
              "title": "Unit Category",
              "key": "fk_unit_category_id",
              "editable": true,
              "type": "dropdown",
              "apiPath": "/admin/societies/listUnitType",
              "label": "type"
            },
            {
              "title": "Name",
              "key": "name",
              "editable": true
            },
            {
              "title": "Mobile No",
              "key": "mobile_no",
              "editable": true
            },
            {
              "title": "Email",
              "key": "email",
              "editable": true,
              "format": "email"
            },
            {
              "title": "Allottee Type",
              "key": "allottee_type",
              "editable": true,
              "type": "dropdown",
              "apiPath": "/admin/societies/listUnitType",
              "label": "type"
            },
            {
              "title": "Effective Date",
              "key": "effective_date",
              "editable": true,
              "type": "date"
            }
          ]
        }
      }
    },
    "pointer": []
  };

  // Return the response with status 200
  return NextResponse.json(responseData, { status: 200 });
}
