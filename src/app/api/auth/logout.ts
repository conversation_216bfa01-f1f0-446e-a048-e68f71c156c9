import type { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const redirectUri = encodeURIComponent(process.env.NEXTAUTH_URL!);
  const logoutUrl = `${process.env.KEYCLOAK_ISSUER}/protocol/openid-connect/logout?post_logout_redirect_uri=${redirectUri}`;

  // Clear cookies
  res.setHeader('Set-Cookie', [
    'company_id=deleted; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT',
    'NEXT_LOCALE=deleted; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT',
  ]);

  // Redirect to Keycloak logout
  res.redirect(logoutUrl);
}
