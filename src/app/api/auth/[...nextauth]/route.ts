import NextAuth from "next-auth/next";

import { authOptions } from "@/libs/auth";

// import type { JWT } from 'next-auth/jwt';

// function requestRefreshOfAccessToken(token: JWT) {
//   return fetch(`${process.env.<PERSON><PERSON><PERSON><PERSON>OAK_ISSUER}/protocol/openid-connect/token`, {
//     headers: { "Content-Type": "application/x-www-form-urlencoded" },
//     body: new URLSearchParams({
//       client_id: process.env.KEYCLOAK_CLIENT_ID!,
//       client_secret: process.env.KEYCLOAK_CLIENT_SECRET!,
//       grant_type: "refresh_token",
//       refresh_token: token.refreshToken!,
//     }),
//     method: "POST",
//     cache: "no-store"
//   });
// }

// export const { handlers: { GET, POST }, auth, update } = NextAuth(authOptions);
const handler = NextAuth(authOptions);

export { handler as GET, handler as POST, }

// export { handler as GET, handler as POST, authOptions }

// export default handler;
