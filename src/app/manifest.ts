import { type MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: "OneSociety",
    short_name: "OneSociety",
    description: "Society Management System",
    icons: [
      {
        src: "/android-chrome-192x192.png",
        sizes: "192x192",
        type: "image/png"
      },
      {
        src: "/android-chrome-256x256.png",
        sizes: "256x256",
        type: "image/png"
      }
    ],
    theme_color: "#EB3D47",
    background_color: "#ffffff",
    display: "standalone",
    scope: "/",
    start_url: "/",
    orientation: "portrait",
    lang: "en",
    dir: "ltr",
    related_applications: [
      {
        platform: "web",
        url: "https://society.cubeone.in/"
      },
      {
        platform: "play",
        url: "https://play.google.com/store/apps/details?id=com.cubeone.app",
        id: "com.cubeone.app"
      }
    ],
    prefer_related_applications: false,
    screenshots: [
      {
        src: "/images/illustrations/auth/v2-login-dark-border.png",
        sizes: "682x664",
        type: "image/png",
        label: "Login screen",
        form_factor: "wide"
      },
      {
        src: "/images/illustrations/auth/v2-login-dark.png",
        sizes: "696x673",
        type: "image/png",
        label: "Register screen",
        form_factor: "wide"
      }
    ],
    categories: ["productivity", "utilities", "finance", "events"],
    shortcuts: [
      {
        name: "New Post",
        url: "/admin/new-post",
        description: "Create a new post",
      },
      {
        name: "Profile",
        url: "/admin/profile",
        description: "View your profile"
      }
    ],
    share_target: {
      action: "/share-target",
      method: "POST",
      enctype: "application/x-www-form-urlencoded",
      params: {
        title: "title",
        text: "text",
        url: "url"
      }
    }
  }
}
