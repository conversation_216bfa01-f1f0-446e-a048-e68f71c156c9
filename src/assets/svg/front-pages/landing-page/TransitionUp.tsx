// React Imports
import type { SVGAttributes } from 'react'

const TransitionUp = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='42' height='43' viewBox='0 0 42 43' fill='none' {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.44041 7.41429C9.45077 6.40394 10.8211 5.83633 12.25 5.83633H29.75C31.1788 5.83633 32.5491 6.40394 33.5595 7.41429C34.5698 8.42464 35.1375 9.79498 35.1375 11.2238V14.7238C35.1375 15.6144 35.8594 16.3363 36.75 16.3363C37.6405 16.3363 38.3624 15.6144 38.3624 14.7238V11.2238C38.3624 8.93965 37.4551 6.74903 35.8399 5.13387C34.2247 3.51871 32.0341 2.61133 29.75 2.61133H12.25C9.96577 2.61133 7.77515 3.51871 6.15999 5.13387C4.54484 6.74903 3.63745 8.93965 3.63745 11.2238V14.7238C3.63745 15.6144 4.35939 16.3363 5.24995 16.3363C6.14051 16.3363 6.86245 15.6144 6.86245 14.7238V11.2238C6.86245 9.79497 7.43006 8.42464 8.44041 7.41429ZM22.1402 10.0836C21.5104 9.4539 20.4895 9.4539 19.8597 10.0836L14.6097 15.3336C13.98 15.9633 13.98 16.9843 14.6097 17.614C15.2395 18.2438 16.2604 18.2438 16.8902 17.614L19.3875 15.1167V23.6113H10.5C8.6799 23.6113 6.9344 24.3343 5.64743 25.6213C4.36046 26.9083 3.63745 28.6538 3.63745 30.4738V33.9738C3.63745 35.7939 4.36046 37.5394 5.64743 38.8264C6.9344 40.1133 8.6799 40.8363 10.5 40.8363H31.5C33.32 40.8363 35.0655 40.1133 36.3525 38.8264C37.6394 37.5394 38.3624 35.7939 38.3624 33.9738V30.4738C38.3624 28.6538 37.6394 26.9083 36.3525 25.6213C35.0655 24.3343 33.32 23.6113 31.5 23.6113H22.6124V15.1167L25.1097 17.614C25.7395 18.2438 26.7604 18.2438 27.3902 17.614C28.0199 16.9843 28.0199 15.9633 27.3902 15.3336L22.1402 10.0836ZM21 26.8363H10.5C9.53523 26.8363 8.61001 27.2196 7.92785 27.9017C7.24569 28.5839 6.86245 29.5091 6.86245 30.4738V33.9738C6.86245 34.9386 7.24569 35.8638 7.92785 36.5459C8.61001 37.2281 9.53523 37.6113 10.5 37.6113H31.5C32.4647 37.6113 33.3899 37.2281 34.0721 36.5459C34.7542 35.8638 35.1375 34.9386 35.1375 33.9738V30.4738C35.1375 29.5091 34.7542 28.5839 34.0721 27.9017C33.3899 27.2196 32.4647 26.8363 31.5 26.8363H21Z'
        fill='var(--mui-palette-primary-main)'
      />
    </svg>
  )
}

export default TransitionUp
