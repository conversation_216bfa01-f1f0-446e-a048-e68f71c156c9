// React Imports
import type { SVGAttributes } from 'react'

const Continental = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg width='155' height='29' viewBox='0 0 155 29' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <g id='Logo-3' clipPath='url(#clip0_425_86429)'>
        <path
          id='Vector'
          d='M154.539 22.127H133.728V21.4208H154.539V22.127ZM144.17 18.1725L146.144 16.2658L146.921 17.0426L144.804 18.808C144.663 19.0199 144.522 19.373 144.381 19.5848H142.829C143.078 18.9595 143.558 18.4539 144.17 18.1725ZM137.538 6.16768C137.468 6.16768 135.915 6.59137 135.915 6.59137C135.553 6.73524 135.2 6.9003 134.857 7.08569C134.505 7.29754 134.152 7.65061 134.152 7.8625L135.563 8.21558C135.756 7.9864 135.968 7.77381 136.198 7.57998L136.55 7.43877L137.679 6.23829C137.75 6.16768 137.75 6.09706 137.503 6.16768H137.538ZM147.061 9.98098C146.203 9.04172 145.402 8.05129 144.663 7.01507C145.194 5.69494 145.388 4.26287 145.227 2.84871C144.94 2.32808 144.609 1.83209 144.24 1.36577C144.099 1.15391 143.464 0.447754 143.182 0.447754C143.111 0.447754 143.04 0.447754 143.04 0.51837L143.111 1.29515C142.194 1.86008 141.136 2.56624 140.219 3.27241C140.201 3.5568 140.224 3.84232 140.289 4.1198C140.438 4.23998 140.604 4.33536 140.783 4.40227C141.411 4.25052 142.047 4.13266 142.688 4.04918L141.489 5.8146L140.077 6.80322L138.385 6.23829C138.243 6.23829 138.173 6.23829 138.103 6.30891L136.833 7.65061C136.789 7.68421 136.754 7.72705 136.73 7.77591C136.705 7.82477 136.692 7.87846 136.691 7.93306L136.621 9.41601V9.91035C136.598 10.4921 136.771 11.0645 137.115 11.5345L137.891 10.3341C137.731 10.0709 137.612 9.78526 137.538 9.48664V8.21558L138.314 7.65061L140.43 9.13356C140.751 9.73325 141.129 10.3007 141.559 10.8283C142.5 11.6513 143.515 12.3842 144.592 13.0174C144.683 13.5709 144.825 14.1146 145.016 14.6417L147.555 17.2545L146.074 19.1611C145.789 19.3117 145.547 19.5342 145.374 19.8067C145.201 20.0791 145.102 20.3921 145.087 20.7146H146.85L147.061 19.726L149.178 17.3957C149.319 17.2545 149.319 17.1838 149.249 17.0426L148.684 15.0654C149.22 14.4551 149.542 13.6864 149.601 12.8763C149.601 10.9696 147.696 10.4753 147.061 9.94563V9.98098ZM150.236 13.0881C150.106 12.6287 149.941 12.1801 149.742 11.7464C149.742 11.7464 149.813 11.6758 150.024 11.7464C150.236 11.817 151.294 12.5937 151.294 12.5937C151.858 13.3705 152.211 16.7601 152.211 17.9606C151.593 17.7019 151.021 17.3441 150.519 16.9014L150.236 13.0881ZM96.1985 12.3113C96.1985 11.0402 95.775 10.0515 94.7173 10.0515C93.6589 10.0515 93.1647 11.0402 93.1647 12.3113V20.7146H94.4352V22.0563H87.3099V20.7146H88.6504V9.13356H87.3099V7.82715H92.8826V10.0163H92.9533C93.2669 9.28329 93.7877 8.65847 94.4527 8.21915C95.117 7.77983 95.8961 7.54533 96.692 7.5447C97.5712 7.49192 98.4378 7.76527 99.128 8.31253C99.8175 8.85972 100.282 9.64232 100.431 10.5106C100.569 11.2798 100.639 12.0595 100.643 12.8409V20.7499H102.336V22.0917H96.1278L96.1985 12.276V12.3113ZM67.3456 12.276C67.3456 11.0049 66.9223 10.0163 65.8641 10.0163C64.8059 10.0163 64.3121 11.0049 64.3121 12.276V20.6794H65.5819V22.0211H58.4568V20.6794H59.7972V9.13356H58.4568V7.82715H64.0299V10.0163H64.1005C64.4138 9.28329 64.935 8.65847 65.5996 8.21915C66.2642 7.77983 67.0429 7.54533 67.8393 7.5447C68.7179 7.49192 69.5851 7.76527 70.2749 8.31253C70.9649 8.85972 71.429 9.64232 71.5781 10.5106C71.7153 11.2798 71.786 12.0595 71.7902 12.8409V20.7499H73.4828V22.0917H67.275L67.3456 12.276ZM108.473 9.09828V19.6201C108.463 19.7433 108.479 19.8675 108.521 19.9838C108.563 20.1 108.631 20.2056 108.718 20.2931C108.806 20.3806 108.911 20.4479 109.027 20.4902C109.144 20.5326 109.268 20.5489 109.391 20.5381C109.678 20.5308 109.963 20.4833 110.237 20.3968V21.668C109.388 22.1013 108.437 22.2965 107.486 22.2329C105.299 22.2329 103.959 21.1736 103.959 18.8433V9.13356H102.477V8.21558C104.17 6.87384 106.146 5.32028 107.697 4.04918H108.544V7.82715H110.696V9.09828H108.473ZM19.516 0.447754C19.516 0.447754 22.9726 3.90795 24.5247 5.67337V7.01507C22.519 5.17817 19.9063 4.14717 17.188 4.1198C10.7683 4.1198 6.74728 8.9923 6.74728 14.9947C6.74728 20.9971 10.5567 25.799 17.188 25.799C18.5717 25.8106 19.9432 25.5377 21.2173 24.9972C22.4914 24.4566 23.6411 23.66 24.5952 22.6566V23.9277C22.62 26.6817 18.9516 28.4471 14.3661 28.4471C6.18292 28.5178 0.539307 22.586 0.539307 15.03C0.552166 13.2293 0.920651 11.449 1.62356 9.79135C2.32646 8.13375 3.3499 6.63164 4.63499 5.37151C5.92007 4.11139 7.44143 3.11809 9.11151 2.4488C10.7816 1.77951 12.5674 1.44744 14.3661 1.47169C16.117 1.47709 17.8556 1.76318 19.516 2.31908V0.447754ZM85.1931 18.3137C84.8151 18.7623 84.3377 19.1168 83.7987 19.3496C83.2604 19.5825 82.6752 19.6871 82.0893 19.6554C80.8195 19.6554 78.7741 18.9492 78.7034 15.6303H86.3222C86.3222 15.3478 86.3929 14.8535 86.3929 14.571C86.3929 10.5459 84.7703 7.57998 80.4667 7.57998C76.1638 7.57998 73.977 10.899 73.977 14.9947C73.977 19.0904 76.1638 22.3388 80.4667 22.3388C81.6196 22.4005 82.7648 22.118 83.7574 21.5271C84.7493 20.9361 85.5438 20.0635 86.0401 19.0199L85.1931 18.3137ZM78.6327 14.2179C78.6327 10.4753 78.9148 9.06293 80.1846 9.06293C81.4544 9.06293 81.7365 10.4046 81.8072 14.2179H78.6327ZM15.777 14.9241C15.777 10.5459 16.2003 8.63922 17.6817 8.63922C19.1632 8.63922 19.5865 10.5459 19.5865 14.9241C19.5865 19.3023 19.1632 21.209 17.6817 21.209C16.2003 21.209 15.777 19.3023 15.777 14.9241ZM10.4862 14.9947C10.4862 19.3023 13.0963 22.8331 17.6817 22.8331C22.2672 22.8331 24.8774 19.3023 24.8774 14.9947C24.8774 10.6871 22.2672 7.121 17.6817 7.121C13.0963 7.121 10.4862 10.6518 10.4862 14.9594V14.9947ZM34.8948 12.276C34.8948 11.0049 34.4715 10.0163 33.4133 10.0163C32.3551 10.0163 31.8614 11.0049 31.8614 12.276V20.6794H33.0959V22.0211H26.0061V20.6794H27.3464V9.13356H26.0061V7.82715H31.5791V10.0163H31.6497C31.963 9.28329 32.4843 8.65847 33.1489 8.21915C33.8135 7.77983 34.5922 7.54533 35.3886 7.5447C36.2672 7.49192 37.1343 7.76527 37.8242 8.31253C38.514 8.85972 38.9781 9.64232 39.1275 10.5106C39.2648 11.2798 39.3355 12.0595 39.3391 12.8409V20.7499H41.0322V22.0917H34.8243V12.3113L34.8948 12.276ZM51.0496 4.01388C51.0496 5.28497 52.1783 6.13237 53.8009 6.13237C55.4234 6.13237 56.5521 5.28497 56.5521 4.01388C56.5521 2.74279 55.4234 1.89539 53.8009 1.89539C52.1783 1.89539 51.0496 2.8134 51.0496 4.01388ZM47.1697 9.09828V19.6201C47.1589 19.7433 47.1752 19.8675 47.2175 19.9838C47.2598 20.1 47.327 20.2056 47.4144 20.2931C47.5017 20.3806 47.6072 20.4479 47.7234 20.4902C47.8396 20.5326 47.9636 20.5489 48.0867 20.5381C48.3741 20.5308 48.6591 20.4833 48.9332 20.3968V21.668C48.0839 22.1013 47.1332 22.2965 46.182 22.2329C43.9951 22.2329 42.6547 21.1736 42.6547 18.8433V9.13356H41.1733V8.21558C42.8664 6.87384 44.8417 5.32028 46.3936 4.04918H47.2049V7.82715H48.9685V9.09828H47.1697ZM130.413 20.7499H132.176V22.0917H125.898V3.90795H124.346V2.56624H130.448V20.7853L130.413 20.7499ZM118.35 15.03C118.071 14.809 117.716 14.7075 117.362 14.7476C116.022 14.7476 115.74 16.0893 115.74 17.431C115.74 18.7727 116.163 19.8319 117.292 19.8319C117.489 19.8511 117.688 19.8225 117.872 19.7488C118.056 19.6751 118.22 19.5583 118.35 19.4082V15.03ZM118.491 20.3968C118.086 21.0169 117.531 21.5237 116.877 21.8696C116.223 22.2155 115.491 22.3892 114.752 22.3741C112.424 22.3741 111.013 20.3968 111.013 17.9959C111.013 15.1713 113.059 13.3352 116.304 13.3352C116.997 13.3105 117.689 13.4061 118.35 13.6177V12.6997C118.379 12.349 118.333 11.9959 118.212 11.6653C118.092 11.3346 117.901 11.0343 117.652 10.7854C117.403 10.5365 117.103 10.345 116.773 10.2244C116.443 10.1037 116.09 10.0568 115.74 10.0869C115.103 10.0882 114.475 10.2428 113.911 10.5378C113.346 10.8327 112.86 11.2594 112.495 11.7817L111.718 10.7931C112.311 9.8211 113.139 9.01407 114.126 8.44637C115.112 7.87874 116.225 7.56864 117.362 7.5447C120.819 7.5447 122.864 9.59255 122.864 12.6997V20.7499H124.346V22.0917H118.561L118.491 20.3968ZM50.2736 22.0917V20.7499H51.5434V9.13356H50.2736V7.82715H56.0583V20.7499H57.3987V22.0917H50.2736Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath id='clip0_425_86429'>
          <rect width='154' height='28' fill='white' transform='translate(0.539307 0.447754)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default Continental
