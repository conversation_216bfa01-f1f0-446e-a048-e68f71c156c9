export type FormDataTypes = {
  [key: string]: any
}

export type FormBuilderProps = {
  uniqueKey?: string
  schema?: apiResponseTypes | null
  layout?: "horizontal" | "vertical" | "tabs" | "steps" | "accordion"
  itemData?: {
    [key: string]: string
  } | null
  cb?: () => void,
  href?: string
}

export type apiResponseTypes = {
  schema: {
    steps: any[]
    actions: any[]
    rules: any[]
  }
  meta: {
    layout?: "tabs" | "steps" | "horizontal" | "vertical"
    title: string
    data_source: string
    titleInCenter?: boolean
  }
}

import type { RJSFSchema, UiSchema } from "@rjsf/utils"

export type SchemaType = RJSFSchema

export interface Schema {
  [key: string]: SchemaType
}

export type UISchema = UiSchema

type depMethod = "oneOf" | "anyOf" | "allOf"

export interface Dependencies {
  [key: string]: {
    [key in depMethod]?: {
      properties?: {
        [key: string]: {
          enum?: string[]
          contains?: {
            enum: string[]
          }
        }
      }
    }[]
  }
}

export type generateSchemaHelperType = (apiResponse: any, schema: Schema, uiSchema: UISchema) => void
export type generateSchemaReturnType = {
  required: string[]
  type: "object"
  properties: Schema
  dependencies: Dependencies
}

export type rule = {
  conditions?: {
    [key: string]: {
      [key: string]: any
    }
  }
  order?: number
  event?: {
    type: string
    classNames?: string
    params?: {
      field?: string | string[]
    }
  }
}

export type generateSchemaType = (apiResponse: any, parentUiSchema?: UISchema) => [generateSchemaReturnType, UISchema, rule[]]

export type inputFormat =
  | "date"
  | "time"
  | "date-time"
  | "email"
  | "url"
  | "textarea"
  | "data-url"
export type inputTypesType = {
  [key: string]: any | any[]
}
export type inputFormatsType = {
  [key: string]: inputFormat
}
export type WidgetType =
  | "CameraWidget"
  | "MapWidget"
  | "DateWidget"
  | "TimeWidget"
  | "DateRangeWidget"
  | "RichTextWidget"
  | "AutocompleteWidget"
  | "checkboxes"
  | "radio"
  | "select"
  | "boolean"
  | "TextWidget"
  | "ImageWidget"
  | "ColorWidget"
  | "TableWidget"
  | "FloorWidget"
  | "textarea"

export type WidgetMapType = {
  [key: string]: WidgetType
}


