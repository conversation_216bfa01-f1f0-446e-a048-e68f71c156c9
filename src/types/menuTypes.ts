// React Imports
import type { ReactNode } from 'react'

// MUI Imports
import type { ChipProps } from '@mui/material/Chip'

// Type Imports
import type {
  SubMenuProps as VerticalSubMenuProps,
  MenuItemProps as VerticalMenuItemProps,
  MenuSectionProps as VerticalMenuSectionProps
} from '@menu/vertical-menu'
import type {
  SubMenuProps as HorizontalSubMenuProps,
  MenuItemProps as HorizontalMenuItemProps
} from '@menu/horizontal-menu'
import type { MenuItemExactMatchUrlProps } from '@menu/types'

// Define the type for components that can appear in the order
export type ComponentConfig =
  | { type: 'table'; id?: string; title?: string; apiURL?: string }
  | {
    type: 'card',
    showOnRight?: boolean,
    renderButtonGroup?: boolean,
    mainGrid?: {xs?:number, md?: number , lg?: number, sm?: number},
    headerKeyDisplay?: {[key: string]: any}[],
    keyDisplay?: { key: string; label: string; keyGrid: number; valueGrid: number }[]
  }
  | { type: 'form' }
  | { type: 'dashboard' }
  | { type: 'mix'; order: ComponentConfig[] }
  | { type: 'htmlContent'; apiURL: string }
  | { type: 'timeline'}
  | { type: 'helpNote' }
  
export type MenuItemData = {
  pagination: boolean
  label: string
  excludeLang?: boolean
  icon?: string
  prefix?: ReactNode | ChipProps
  suffix?: ReactNode | ChipProps
  hideInNavbar?: boolean
  href?: string
  children?: never
  pageType?: 'form' | 'table' | 'dashboard' | 'mix' | 'htmlContent' | 'bulk' | 'newRule' | 'previewRule' | 'demo' | 'editNewRule' | 'timeline' | 'accordion' | 'PDF' | 'helpNote' | 'nocForm' | 'helpdesk' | 'socprofile'
  parent?: VerticalSubMenuDataType | VerticalSectionDataType
  order?: ComponentConfig[]
  commonPageConfig?: any
  id?: string
  context?: string
  openInNewTab?: boolean
  apiURL?: string
}

// Vertical Menu Data
export type VerticalMenuItemDataType = Omit<
  VerticalMenuItemProps,
  'children' | 'exactMatch' | 'activeUrl' | 'icon' | 'prefix' | 'suffix'
> &
  MenuItemExactMatchUrlProps &
  MenuItemData

export type VerticalSubMenuDataType = Omit<VerticalSubMenuProps, 'children' | 'icon' | 'prefix' | 'suffix'> & {
  children?: VerticalMenuDataType[]
  icon?: string
  prefix?: ReactNode | ChipProps
  suffix?: ReactNode | ChipProps
  hideInNavbar?: boolean
  parent?: any
  commonPageConfig?: any
}

export type VerticalSectionDataType = Omit<VerticalMenuSectionProps, 'children'> & {
  isSection: boolean
  children: VerticalMenuDataType[]
  hideInNavbar?: boolean
  parent: any
}

export type VerticalMenuDataType = VerticalMenuItemDataType | VerticalSubMenuDataType | VerticalSectionDataType

// Horizontal Menu Data
export type HorizontalMenuItemDataType = Omit<
  HorizontalMenuItemProps,
  'children' | 'exactMatch' | 'activeUrl' | 'icon' | 'prefix' | 'suffix'
> &
  MenuItemExactMatchUrlProps & {
    label: ReactNode
    excludeLang?: boolean
    icon?: string
    prefix?: ReactNode | ChipProps
    suffix?: ReactNode | ChipProps
  }

export type HorizontalSubMenuDataType = Omit<HorizontalSubMenuProps, 'children' | 'icon' | 'prefix' | 'suffix'> & {
  children: HorizontalMenuDataType[]
  icon?: string
  prefix?: ReactNode | ChipProps
  suffix?: ReactNode | ChipProps
}

export type HorizontalMenuDataType = HorizontalMenuItemDataType | HorizontalSubMenuDataType
