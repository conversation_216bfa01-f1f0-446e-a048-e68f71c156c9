import type { FormDataTypes } from "@/types/form"

export const injectFieldsInTitle = (title: string, formData: FormDataTypes | null | FormDataTypes[]) => {
  if (!title) return title
  const regex = /{{(.*?)}}/g
  const matches = title.match(regex)

  if (!matches || !formData) return title

  let newTitle = title

  matches.forEach(match => {
    const key = match.replace("{{", "").replace("}}", "")

    // Ensure formData is actually an array before calling .reduce()
    if (Array.isArray(formData)) {
      // Let TypeScript know it’s an array of FormDataTypes
      const arr = formData as FormDataTypes[]


      // Use the generic <number> to make the accumulator a number
      const count = arr.reduce<number>((acc, curr) => {
        return acc + Number(curr[key])
      }, 0)

      // If you need to replace with a string, call .toString()
      newTitle = newTitle.replace(match, count.toString())
    } else {
      // formData is a single object
      newTitle = newTitle.replace(match, String(formData[key]))
    }
  })

  return newTitle
}
