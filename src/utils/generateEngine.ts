import axios from 'axios';
import { Engine } from 'json-rules-engine';
import type {
  RuleProperties,
  Event,
  TopLevelCondition,
  NestedCondition,
  AllConditions,
  AnyConditions,
  NotConditions,
  ConditionReference,
  ConditionProperties,
  Fact
} from 'json-rules-engine';

import { replacePlaceholders } from '@/utils/replacePlaceholders';
import { evaluateExpression } from './evaluate';
import { updateNestedValue } from './object';

// Instantiate a QueryClient to be used for all dynamic fact fetches.
import { queryClient } from "@/contexts/queryClientProvider"

const isDynamic = (value) => !!value?.dynamic || !!value?.method;

interface Rule extends RuleProperties {
  events?: Event[] | Event;
}

// Type Guards for Conditions
const isAllConditions = (condition: NestedCondition): condition is AllConditions => {
  return (condition as AllConditions).all !== undefined;
};

const isAnyConditions = (condition: NestedCondition): condition is AnyConditions => {
  return (condition as AnyConditions).any !== undefined;
};

const isNotConditions = (condition: NestedCondition): condition is NotConditions => {
  return (condition as NotConditions).not !== undefined;
};

const isConditionReference = (condition: NestedCondition): condition is ConditionReference => {
  return (condition as ConditionReference).condition !== undefined;
};

const isConditionProperties = (condition: NestedCondition): condition is ConditionProperties => {
  return (condition as ConditionProperties).fact !== undefined;
};

// Normalize Conditions
const normalizeConditions = (condition: NestedCondition): TopLevelCondition => {
  if (isAllConditions(condition)) {
    return {
      ...condition,
      all: condition.all.map(normalizeConditions),
    };
  }

  if (isAnyConditions(condition)) {
    return {
      ...condition,
      any: condition.any.map(normalizeConditions),
    };
  }

  if (isNotConditions(condition)) {
    return {
      ...condition,
      not: normalizeConditions(condition.not),
    };
  }

  if (isConditionReference(condition)) {
    return condition;
  }

  if (isConditionProperties(condition)) {
    return {
      ...condition,
      value: condition.hasOwnProperty('value') ? condition.value : null,
    };
  }

  throw new Error(`Unknown condition type: ${JSON.stringify(condition)}`);
};

// Normalize Rules
const normalizeRules = (rules: RuleProperties[]): RuleProperties[] => {
  return rules.flatMap((rule) => {
    const normalizedConditions = normalizeConditions(rule.conditions);

    if (Array.isArray(rule.event)) {
      return rule.event.map((event) => {
        const { priority, ...rest } = event;


        return ({
          ...rule,
          conditions: normalizedConditions,
          event: rest,
          ...(priority ? { priority } : {}),
        });
      });
    } else {
      return {
        ...rule,
        conditions: normalizedConditions,
      };
    }
  });
};

// Utility to Get Nested Value
function getNestedValue(obj: any, path: string) {
  if (!obj || !path) return obj;

  return path?.split('.')?.reduce((acc, key) => acc?.[key], obj);
}

const addParams = (data, defaultParams) => {
  if (!defaultParams || !Array.isArray(defaultParams)) return data;

  // Clone the data to ensure immutability
  const updatedData = { ...data };

  defaultParams.forEach((param) => {
    const path = Array.isArray(param.path) ? param.path : [param.path];

    if (!path) return; // Skip if path is not provided

    const params = param.params;
    const value = getNestedValue(updatedData, path);

    if (!value) return; // Skip if value is not found

    if (Array.isArray(value)) {
      // If the value is an array, update each object in the array
      const updatedArray = value.map((item) => ({
        ...item,
        ...params,
      }));


      // Assign the updated array back to the correct path
      updateNestedValue(updatedData, path, updatedArray);
    } else {
      // If the value is not an array, update it directly
      const updatedValue = { ...value, ...params };

      updateNestedValue(updatedData, path, updatedValue);
    }
  });

  return updatedData;
}

// Custom Fact Type
type CustomFact = {
  fact: string;
  dependsOn?: string;
  dynamic?: boolean;
  method?: string;
  path: string;
};

type GetSchema = (
  facts: Array<Fact & CustomFact & any>,
  rules: Rule[]
) => Engine;

// Generate Engine
const generateEngine: GetSchema = (facts = [], rules = []) => {
  const engine = new Engine([], {
    allowUndefinedFacts: true,
    replaceFactsInEventParams: true,
  });

  try {
    // Operators
    engine.addOperator('tableChanged', (factValue, params) => {
      if (!params || typeof params !== 'string') return false;
      const previousValue = engine.getFact(params);

      console.log(factValue, params, previousValue)

      return JSON.stringify(previousValue) !== JSON.stringify(factValue);
    });
    engine.addOperator('truthy', (factValue) => !!factValue);
    engine.addOperator('falsy', (factValue) => !factValue);
    engine.addOperator('isNumber', (factValue) => typeof factValue === 'number');
    engine.addOperator('exists', (factValue) => factValue !== undefined);
    engine.addOperator('inRange', (factValue, minMax = []) => {
      if (
        !Array.isArray(minMax) ||
        minMax.length !== 2 ||
        minMax.some((val) => Number.isNaN(Number(val))) ||
        minMax[0] > minMax[1]
      ) {
        return false;
      }

      return factValue >= minMax[0] && factValue <= minMax[1];
    });
    engine.addOperator('filterBy', (arrayOfObjects, filter) => {
      console.log(arrayOfObjects, filter)

      if (!Array.isArray(arrayOfObjects) || typeof filter !== 'object' || filter === null) {
        return false; // Invalid input
      }

      // Get filtered array
      const filtered = arrayOfObjects.filter(obj => {
        return Object.entries(filter).every(([key, value]) => obj[key] === value);
      });

      return filtered.length > 0; // or return filtered itself if chaining
    });

    // Process Facts
    try {
      facts.forEach(async (fact) => {
        if (!fact?.fact) return;

        if (fact?.onMount) {
          const data = await queryClient.fetchQuery({
            queryKey: [fact?.fact], // Query key based on fact and its dependency value
            queryFn: async () => {
              const res = await axios({
                method: fact?.method || 'GET',
                url: fact?.path,

                // url: replacePlaceholders(fact?.path, { id: value, ...formData }),
                baseURL: process.env.NEXT_PUBLIC_API_URL,
                withCredentials: true,
                validateStatus: () => true,
              });

              let data = null;

              if (fact?.returns === "all") {
                data = res?.data;
              } else {
                data = fact?.returns
                  ? getNestedValue(res?.data?.data, fact.returns)
                  : res?.data?.data;
              }

              if (fact?.defaultParams) {
                data = addParams(data, fact.defaultParams);
              }

              // Retain the side-effect of adding loading fields
              // almanac.addFact("__loadingFields", ["is_billable"]);

              return data || {};
            },
            staleTime: 2 * 60 * 1000
          }
          );

          engine.addFact(fact.fact, data);
        } else if (!isDynamic(fact)) {
          if (fact?.type === 'rowCalculation') {
            engine.addFact(fact.fact, async (params, almanac) => {
              const arr: any[] = await almanac.factValue(fact.dependsOn, {}, fact.xpath);

              if (!arr) return [];

              return arr.map((item) => {
                const result = evaluateExpression(fact.expression, { ...params, ...item });

                return { ...item, [fact.fact]: result };
              });
            });
          } else {
            engine.addFact(fact.fact, fact.value ?? null);
          }
        } else {
          // Using TanStack Query to fetch dynamic facts
          engine.addFact(
            fact.fact,
            async (_, almanac) => {
              const value = await almanac.factValue(fact?.dependsOn, {}, fact?.xpath);

              // Extract keys from fact.params or default to an empty array
              const keys = Object.keys(fact?.params || {});

              // Map each key to a promise that resolves with an object containing the key and the fetched value.
              const promises = keys.map(async key => {
                const paramsKey = fact?.params?.[key]?.fact || fact?.params?.[key];
                const paramsPath = fact?.params?.[key]?.path || fact?.params?.[key]?.xpath;

                if (!paramsKey || typeof paramsKey !== "string") {
                  return { key, value: null };
                }

                const value = await almanac.factValue(
                  paramsKey,
                  {},
                  paramsPath
                );

                return { key, value };
              })

              // Wait for all promises to resolve
              const keyValuePairs = await Promise.all(promises);

              // Build the formData object from the array of key/value pairs
              const formData = keyValuePairs.reduce((acc, { key, value }) => {
                acc[key] = value;

                return acc;
              }, {});

              if (!value && fact?.dependsOn) {
                return {};
              }

              // Check if the fact has a condition to allow on certain values only
              if (fact?.allowOn) {
                const isNotAllowed = Object?.keys(fact?.allowOn)?.some((key) => {
                  const factValue = fact?.allowOn?.[key];

                  if (Array.isArray(factValue)) {
                    // Fail if the current formData value is NOT included in factValue array
                    return !factValue?.includes(formData?.[key]);
                  } else if (typeof factValue === "object") {
                    // will implement later
                    // For now, assuming objects don't cause a failure
                    return false;
                  } else if (typeof factValue === "string") {
                    // Fail if the current formData value does not match the string exactly
                    return factValue !== formData?.[key];
                  }

                  // If factValue doesn't match any expected type, consider it a failure
                  return true;
                })

                if (isNotAllowed) return {};
              }

              try {
                const data = await queryClient.fetchQuery({
                  queryKey: [fact?.fact, value, formData], // Query key based on fact and its dependency value
                  queryFn: async () => {
                    const res = await axios({
                      method: fact?.method || 'GET',
                      baseURL: fact?.baseURL || process.env.NEXT_PUBLIC_API_URL,
                      url: replacePlaceholders(fact?.path, { id: value, ...formData }),
                      withCredentials: true,
                      validateStatus: () => true,
                    });

                    let data = null;

                    if (fact?.returns === "all") {
                      data = res?.data;
                    } else {
                      data = fact?.returns
                        ? getNestedValue(res?.data?.data, fact.returns)
                        : res?.data?.data;
                    }

                    if (fact?.defaultParams) {
                      data = addParams(data, fact.defaultParams);
                    }

                    // Retain the side-effect of adding loading fields
                    // almanac.addFact("__loadingFields", ["is_billable"]);

                    return data || {};
                  },
                  staleTime: 2 * 60 * 1000
                }
                );

                // const hashed = await hash(formData);

                // console.log("Hashed Data", hashed, `${fact.fact}_hash`);
                // almanac.addFact(`${fact.fact}_hash`, hashed);

                return data;
              } catch (err: any) {
                console.warn('API Error:', err.message);

                return {};
              }
            },
            { cache: false }
          );
        }
      });
    } catch (err) {
      console.warn('Error processing facts:', err);
    }

    // Normalize and Add Rules
    try {
      const normalizedRules = normalizeRules(rules);

      normalizedRules.forEach((rule) => {
        engine.addRule(rule);
      });
    } catch (err) {
      console.warn('Error processing rules:', err);
    }
  } catch (err) {
    console.warn('Error generating engine:', err);
  } finally {
    return engine;
  }
};

export default generateEngine;
