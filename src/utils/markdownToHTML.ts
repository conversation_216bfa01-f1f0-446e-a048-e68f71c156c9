import { readFile } from 'fs/promises';
import path from 'path';

import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMdx from 'remark-mdx';
import remarkRehype from 'remark-rehype';
import rehypeStringify from 'rehype-stringify';

const SCHEMA_DIR = path.resolve(process.cwd(), 'src', 'data', 'content');

// Create a reusable MDX-to-HTML processor
const mdxProcessor = unified()
  .use(remarkParse)
  .use(remarkMdx)
  .use(remarkRehype)
  .use(rehypeStringify)

/**
 * Reads and returns the raw MDX source for a given file path, ensuring the path
 * is within the configured SCHEMA_DIR and handling both direct and index-based
 * MDX files.
 *
 * @param filePath - The relative path (from SCHEMA_DIR) to the MDX file. May include a leading 'admin/'.
 * @returns The MDX source as a string.
 * @throws If `filePath` is empty, invalid, or attempts path traversal, or if no matching file is found.
 *
 * @example
 * ```ts
 * const source = await resolveAndReadSource('posts/intro');
 * console.log(source.slice(0, 100));
 * ```
 */
async function resolveAndReadSource(filePath: string): Promise<string> {
  if (typeof filePath !== 'string' || !filePath.trim()) {
    throw new Error('filePath must be a non-empty string');
  }

  const segments = filePath.split('/').filter(Boolean);
  
  if (segments[0] === 'admin') segments.shift();
  
  if (segments.length === 0) {
    throw new Error(`Invalid filePath after stripping 'admin': "${filePath}"`);
  }

  const cleanPath = segments.join('/');
  const directPath = path.join(SCHEMA_DIR, `${cleanPath}.mdx`);
  const indexPath = path.join(SCHEMA_DIR, cleanPath, 'index.mdx');

  for (const p of [directPath, indexPath]) {
    const resolved = path.resolve(p);
    
    if (!resolved.startsWith(SCHEMA_DIR + path.sep)) {
      throw new Error(`Attempted path traversal: ${filePath}`);
    }
  }

  try {
    return await readFile(indexPath, 'utf8');
  } catch (indexErr) {
    try {
      return await readFile(directPath, 'utf8');
    } catch (directErr) {
      throw new Error(`${indexErr?.message || directErr?.message}, Unable to read MDX file for "${filePath}`)
    }
  }
}

/**
 * Compiles an MDX file to HTML and returns the resulting markup.
 *
 * @param filePath - The relative path (from SCHEMA_DIR) to the MDX file.
 * @returns A promise resolving to the compiled HTML string.
 *
 * @example
 * ```ts
 * const html = await getMdxContentFromPath('posts/intro');
 * console.log(html.includes('<h1>')); // true
 * ```
 */
export async function getMdxContentFromPath(filePath: string): Promise<string> {
  try{
    const source = await resolveAndReadSource(filePath);
    const processed = await mdxProcessor.process(source)

    return String(processed);
  } catch (error) {
    console.error(`Error processing MDX file "${filePath}":`, error);
   
    return '';
  }
}
