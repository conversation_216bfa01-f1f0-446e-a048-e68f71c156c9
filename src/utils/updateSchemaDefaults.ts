type Schema = {
  type: string;
  properties?: Record<string, Schema>;
  default?: any;
  title?: string;
  enum?: { const: any; title: string }[];
  children?: Schema; // Handling the `children` property
  key: string
};

type Data = Record<string, any>;

const getType = (schemaPart: Schema): string | string[] => {
  if (!Array.isArray(schemaPart.type)) {
    return schemaPart.type;
  }

  const type = schemaPart.type?.filter((t) => t !== "null") as string[];

  if (type.length === 1) {
    return type[0];
  }

  return type;
}

const atRootLevel = (schema: Schema): boolean => {
  return schema.hasOwnProperty("allowUndefinedFacts");
}

function updateSchemaDefaults(schema: Schema, data: Data): Schema {
  const updateDefaults = (schemaPart: Schema): Schema => {
    const type = getType(schemaPart);

    if (schemaPart?.key && data.hasOwnProperty(schemaPart.key)) {
      if (type === "boolean") {
        schemaPart.default = Boolean(data[schemaPart.key]) ?? false;
      } else {
        schemaPart.default = data[schemaPart.key];
      }
    }

    // Handle children recursively
    if (schemaPart.children) {
      schemaPart.children = updateDefaults(schemaPart.children);
    }

    // Handle properties recursively
    if (schemaPart.properties) {
      Object.entries(schemaPart.properties).forEach(([key, subSchema]) => {
        schemaPart.properties![key] = updateDefaults({ ...subSchema, key });
      });

      if (atRootLevel(schemaPart)) {
        if (schemaPart.properties.id) {
          schemaPart.properties.id.default = data.id;
        }
      } else {
        if (schemaPart.properties.id) {
          schemaPart.properties.id.default = data[schemaPart.key];
        }
      }
    }

    return schemaPart;
  };

  return updateDefaults(schema);
}

export default updateSchemaDefaults;
