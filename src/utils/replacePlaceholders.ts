/**
 * Replaces placeholders in a URL with actual values from a given data object.
 *
 * - Supports both `:param` and `[param]` placeholder formats.
 * - Handles mailto: and tel: prefixes properly.
 * - Works with both path and query parameters.
 * - Supports nested object paths in placeholders (e.g., `:user.id`).
 * - Also supports query parameters written as keys only (e.g., `/search?hello&bye`)
 *   by replacing them with the corresponding value from data if available.
 *
 * @param {string} href - The URL template containing placeholders.
 * @param {Record<string, any>} data - An object with values to replace placeholders.
 * @returns {string} The formatted URL with placeholders replaced.
 *
 * @example
 * replacePlaceholders("/admin/purchaseform/view/:id", { id: "1" });
 * // Returns "/admin/purchaseform/view/1"
 *
 * @example
 * replacePlaceholders("/user/:userId/profile", { userId: "42" });
 * // Returns "/user/42/profile"
 *
 * @example
 * replacePlaceholders("/admin/purchaseform/view/[id]", { id: "1" });
 * // Returns "/admin/purchaseform/view/1"
 *
 * @example
 * replacePlaceholders("/search?query=:query", { query: "books" });
 * // Returns "/search?query=books"
 *
 * @example
 * replacePlaceholders("mailto::email", { email: "<EMAIL>" });
 * // Returns "mailto:<EMAIL>"
 *
 * @example
 * replacePlaceholders("/search?query=:query2&hello&bye", { query2: "books", hello: 2, bye: 3 });
 * // Returns "/search?query=books&hello=2&bye=3"
 *
 * @remarks
 * Note: If an error occurs or invalid inputs are provided, the function logs the error and returns an empty string.
 */
export function replacePlaceholders(href: string = "", data: Record<string, any> = {}): string {
  try {
    if (!href || typeof href !== "string") {
      throw new TypeError("href should be a valid string!");
    }

    // Helper function to safely get nested values from data
    const getNestedValue = (key: string, obj: Record<string, any>): any => {
      return key.split(".").reduce((result, prop) => (result ? result[prop] : undefined), obj);
    };

    // 1. Detect and preserve special prefixes like mailto: and tel:
    let prefix = "";

    if (href.startsWith("mailto:")) {
      prefix = "mailto:";
    } else if (href.startsWith("tel:")) {
      prefix = "tel:";
    }

    // 2. Remove the prefix temporarily for easier processing
    let urlWithoutPrefix = prefix ? href.slice(prefix.length) : href;

    // 3. If the entire URL (without prefix) is a key in data and doesn't contain placeholders, replace it directly
    if (!urlWithoutPrefix.includes(":") && !urlWithoutPrefix.includes("[")) {
      const directValue = getNestedValue(urlWithoutPrefix, data);

      if (directValue !== undefined) {
        urlWithoutPrefix = String(directValue);
      }
    } else {
      // (A) Replace path placeholders (supports both `:id` and `[id]` formats)
      const pathPlaceholderRegex = /[:[](\w+(?:\.\w+)?)[\]]?/g;

      urlWithoutPrefix = urlWithoutPrefix.replace(pathPlaceholderRegex, (_match, key: string) => {
        const value = getNestedValue(key, data);

        return value !== undefined ? String(value) : "";
      });

      // (B) Replace query placeholders (e.g., `?foo=:someKey`)
      const queryRegex = /([?&])([^=&#]+)=([^&#]*)/g;

      urlWithoutPrefix = urlWithoutPrefix.replace(queryRegex, (match: string, leadingChar: string, paramKey: string, paramValue: string) => {
        if (paramValue.startsWith(":")) {
          const key = paramValue.slice(1);
          const value = getNestedValue(key, data);

          return `${leadingChar}${paramKey}=${value !== undefined ? encodeURIComponent(String(value)) : ""}`;
        }

        return match;
      });
    }

    // 4. Additional step: Process query parameters that are standalone keys (e.g., "&hello&bye")
    const queryIndex = urlWithoutPrefix.indexOf("?");

    if (queryIndex !== -1) {
      const baseUrl = urlWithoutPrefix.substring(0, queryIndex);
      const queryString = urlWithoutPrefix.substring(queryIndex + 1);

      const queryParams = queryString.split("&").map(param => {
        // If the parameter already contains '=', leave it as is.
        if (param.includes("=")) {
          return param;
        }

        // If not, try to find its value in the data using getNestedValue.

        const value = getNestedValue(param, data);

        return value !== undefined ? `${param}=${encodeURIComponent(String(value))}` : param;
      });

      urlWithoutPrefix = baseUrl + "?" + queryParams.join("&");
    }

    // 5. Post-process the URL to remove redundant slashes:
    // Remove redundant leading slashes (but keep one)

    urlWithoutPrefix = urlWithoutPrefix.replace(/^\/{2,}/, '/');

    // Collapse multiple slashes elsewhere (excluding protocol slashes)
    urlWithoutPrefix = urlWithoutPrefix.replace(/([^:])\/{2,}/g, '$1/');

    // 6. Reattach the prefix and return the final URL
    return prefix + urlWithoutPrefix;
  } catch (err) {
    console.warn(err?.message);

    return "";
  }
}

/**
 * Replaces placeholders in a URL string with corresponding values from the params slug array.
 * Placeholders in the URL are denoted by square brackets (e.g., [id], [role]). The function extracts all such
 * placeholders and replaces them with values from the slug array. The replacement starts at an index calculated
 * by subtracting the number of placeholders from the slug array's length.
 *
 * @param {string} href - The URL string containing placeholders.
 * @param {{ slug: string[] }} params - An object containing a slug array with values to replace the placeholders.
 * @returns {string} - The updated URL with placeholders replaced by corresponding slug values.
 *
 * @example
 * // Example 1: Basic replacement
 * const url = replacePlaceholdersHref("/user/[id]/profile", { slug: ["123"] });
 * console.log(url); // Outputs: "/user/123/profile"
 *
 * @example
 * // Example 2: Multiple placeholders replacement
 * const url2 = replacePlaceholdersHref("/[lang]/user/[id]", { slug: ["en", "456"] });
 * console.log(url2); // Outputs: "/en/user/456"
 *
 * @example
 * // Example 3: Insufficient slug values (with warning)
 * const url3 = replacePlaceholdersHref("/[role]/dashboard", { slug: [] });
 * // Console warning: Missing value for placeholder [role]
 * console.log(url3); // Outputs: "/dashboard"
 *
 * @remarks
 * Note: If there are insufficient slug values, the function will log a warning and replace the placeholder with an empty string.
 * Consider handling this scenario more strictly (e.g., by throwing an error) based on your application's requirements.
 */
export function replacePlaceholdersHref(href: string, params: { slug: string[] }): string {
  try {
    // Validate inputs
    if (typeof href !== "string") {
      console.warn("Invalid href: expected a string.");

      return "";
    }

    if (!params || !Array.isArray(params.slug)) {
      console.warn("Invalid params: expected an object with a 'slug' array property.");

      return "";
    }

    // Ensure all slug values are strings
    if (!params.slug.every(value => typeof value === "string")) {
      console.warn("Invalid slug array: all values should be strings.");

      return "";
    }

    const { slug } = params;
    const placeholderRegex = /\[(\w+)]/g;

    // Extract placeholders (e.g., [id], [role]) from href
    const placeholders = href.match(placeholderRegex);
    
    if (!placeholders) return href; // No placeholders to replace, return href as is

    // Calculate starting index for replacement values in the slug array.
    let slugIndex = slug.length - placeholders.length;

    if (slugIndex < 0) {
      console.warn("Insufficient slug values to replace all placeholders.");
      slugIndex = 0;
    }

    let updatedURL = href.replace(placeholderRegex, (_, placeholder) => {
      if (slugIndex < 0 || slugIndex >= slug.length) {
        console.warn(`Missing value for placeholder [${placeholder}].`);

        return "";
      }

      const value = slug[slugIndex];

      slugIndex++;

      return value;
    });

    // Remove redundant leading slashes if any (e.g., "//dashboard" becomes "/dashboard")
    updatedURL = updatedURL.replace(/^\/{2,}/, '/');

    // Also collapse multiple slashes in the URL (except after a protocol, e.g., "http://")
    updatedURL = updatedURL.replace(/([^:])\/{2,}/g, '$1/');

    return updatedURL;
  } catch (error) {
    console.warn("Error in replacePlaceholdersHref:", error);

    return "";
  }
}

