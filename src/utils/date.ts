/**
 * @file parseDateString.ts
 * 
 * Demonstrates a robust parser that:
 *  1) Parses a base date/time from the input (if present).
 *  2) Looks for "special tokens" like lastQuarter, startOf('quarter'), endOf('quarter'), etc.
 *  3) Applies each special token in sequence.
 *  4) Parses and applies leftover offsets like "+ 2 days" or "-1 month".
 *  5) Optionally sets the final date to startOf('day') if required.
 * 
 * Uses Dayjs + QuarterOfYear plugin. 
 */

import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";

dayjs.extend(quarterOfYear);

/**
 * A discriminated union that ensures we either call Dayj<PERSON>'s
 * quarter-based overload (QUnitType) or standard ManipulateType.
 */
type UnitInfo =
  | { type: "quarter"; value: "quarter" }  // QUnitType
  | { type: "manipulate"; value: dayjs.ManipulateType };

/**
 * parseManipulateUnit()
 * 
 * Given a string like "days", "week", "quarter", or "q",
 * returns either { type: 'quarter', value: 'quarter' }
 * or { type: 'manipulate', value: <ManipulateType> }.
 * 
 * This avoids TypeScript errors when calling .add() or .subtract().
 * 
 * @param rawUnit - A string describing the time unit (e.g. "days", "q", "month")
 * @returns A UnitInfo object indicating which overload to use.
 */
function parseManipulateUnit(rawUnit: string): UnitInfo {
  let unit = rawUnit.toLowerCase().trim();

  // Remove trailing 's' if present (days -> day, months -> month)
  if (unit.endsWith("s")) {
    unit = unit.slice(0, -1);
  }

  // Quarter-based units are a special QUnitType
  if (unit === "quarter" || unit === "q") {
    return { type: "quarter", value: "quarter" };
  }

  // Otherwise, map to dayjs.ManipulateType
  switch (unit) {
    case "y":
    case "year":
      return { type: "manipulate", value: "year" };
    case "m":
    case "month":
      return { type: "manipulate", value: "month" };
    case "w":
    case "week":
      return { type: "manipulate", value: "week" };
    case "d":
    case "day":
      return { type: "manipulate", value: "day" };
    case "h":
    case "hour":
      return { type: "manipulate", value: "hour" };
    case "min":
    case "minute":
      return { type: "manipulate", value: "minute" };
    case "s":
    case "second":
      return { type: "manipulate", value: "second" };
    case "ms":
    case "millisecond":
      return { type: "manipulate", value: "millisecond" };
    default:
      // Fallback or throw an error for unknown units
      return { type: "manipulate", value: "day" };
  }
}

/**
 * applyOffsets()
 * 
 * Scans a string for offset patterns like "+ 2 days" or "-1 quarter"
 * and applies them sequentially to the given base date.
 * 
 * @param base - The Dayjs date to modify
 * @param leftover - The string that may contain offset patterns
 * @returns A new Dayjs instance with all offsets applied
 */
function applyOffsets(base: Dayjs, leftover: string): Dayjs {
  // Regex captures sign (+/-), numeric amount, and a recognized unit
  const OFFSET_PATTERN =
    /([+\-])\s*(\d+)\s*(year|years|y|month|months|m|week|weeks|w|day|days|d|hour|hours|h|minute|minutes|min|second|seconds|s|millisecond|milliseconds|ms|quarter|quarters|q)/gi;

  let match: RegExpExecArray | null;

  while ((match = OFFSET_PATTERN.exec(leftover)) !== null) {
    const [, sign, rawAmount, rawUnit] = match;
    const amount = parseInt(rawAmount, 10) || 0;

    const unitInfo = parseManipulateUnit(rawUnit);

    if (sign === "+") {
      if (unitInfo.type === "quarter") {
        // Overload requiring QUnitType
        base = base.add(amount, unitInfo.value);
      } else {
        // Overload requiring ManipulateType
        base = base.add(amount, unitInfo.value);
      }
    } else {
      if (unitInfo.type === "quarter") {
        base = base.subtract(amount, unitInfo.value);
      } else {
        base = base.subtract(amount, unitInfo.value);
      }
    }
  }

  return base;
}

/**
 * applySpecialTokens()
 * 
 * Identifies and applies tokens like:
 *   - lastQuarter        => subtract(1, 'quarter')
 *   - startOf('quarter') => base.startOf('quarter')
 *   - endOf('month')     => base.endOf('month')
 *   - etc.
 * 
 * @param base - The Dayjs date to modify
 * @param input - The string that may contain special tokens
 * @returns An object { base, leftover } where
 *   - base is the updated Dayjs object
 *   - leftover is the string with those tokens removed
 */
function applySpecialTokens(base: Dayjs, input: string): { base: Dayjs; leftover: string } {
  // Regex to match tokens like:
  // lastQuarter
  // startOf('year'), endOf('quarter'), startOf('month'), etc.
  const TOKEN_PATTERN = /(lastQuarter|startOf\('(\w+)'\)|endOf\('(\w+)'\))/g;

  let match: RegExpExecArray | null;

  // We'll store them in an array so we apply them *in order*.
  const tokens: string[] = [];

  // 1) Collect all tokens in order
  while ((match = TOKEN_PATTERN.exec(input)) !== null) {
    tokens.push(match[0]);
  }

  // 2) Remove them from the input (so leftover can be used for offsets)
  let leftover = input;

  tokens.forEach((token) => {
    leftover = leftover.replace(token, "").trim();
  });

  // 3) Apply each token in the sequence found
  tokens.forEach((token) => {
    if (token === "lastQuarter") {
      // Subtract 1 quarter from the current base date
      base = base.subtract(1, "quarter");
    } else if (token.startsWith("startOf")) {
      // e.g. "startOf('quarter')"
      const inside = token.match(/startOf\('(\w+)'\)/);

      if (inside && inside[1]) {
        // e.g. "quarter", "month", "year", "week", "day"
        base = base.startOf(inside[1] as dayjs.OpUnitType);
      }
    } else if (token.startsWith("endOf")) {
      // e.g. "endOf('quarter')"
      const inside = token.match(/endOf\('(\w+)'\)/);

      if (inside && inside[1]) {
        base = base.endOf(inside[1] as dayjs.OpUnitType);
      }
    }
  });

  return { base, leftover };
}

/**
 * parseDateString()
 * 
 * Orchestrates the entire process:
 *  1) Parse a base date/time from the input (if any).
 *  2) Apply special tokens like "lastQuarter" or "startOf('quarter')".
 *  3) Parse and apply leftover offsets like "+ 1 month - 1 day".
 *  4) Optionally set to startOf('day') at the end if requested.
 * 
 * @param input - A string that may contain a date/time,
 *                special tokens, and offset instructions.
 * @param startOfDay - If true, the final date is forced to 00:00:00 of that day.
 * @returns A Dayjs object representing the resulting date/time.
 * 
 * @example
 *   parseDateString("2025-03-15 + 2 weeks - 1 quarter", false)
 *   parseDateString("lastQuarter endOf('quarter') + 1 month - 1 day", true)
 */
export function parseDateString(
  input: string | null | undefined,
  startOfDay: boolean = false
): Dayjs {
  // 1) If input is empty, default to now
  if (!input || !input.trim()) {
    const fallback = dayjs();

    
return startOfDay ? fallback.startOf("day") : fallback;
  }

  // 2) Attempt to parse a leading date/time from input (ISO or partial)
  let base = dayjs();
  const dateRegex = /^\d{4}-\d{2}-\d{2}(\D\d{2}:\d{2}:\d{2})?/; 
  const match = input.match(dateRegex);

  if (match) {
    const dateStr = match[0];
    const parsed = dayjs(dateStr);

    if (parsed.isValid()) {
      base = parsed;

      // Remove that portion from input
      input = input.slice(dateStr.length).trim();
    }
  }

  // 3) Identify & apply special tokens (lastQuarter, startOf('...'), endOf('...'))
  const { base: afterTokens, leftover } = applySpecialTokens(base, input);

  // 4) Apply offsets like "+1 day", "-2 weeks" from leftover
  const finalBase = applyOffsets(afterTokens, leftover);

  // 5) If requested, set startOf('day')
  return startOfDay ? finalBase.startOf("day") : finalBase;
}

// /* ======================================================================
//    EXAMPLES: Showcasing the parser's capabilities
//    ====================================================================== */

// /**
//  * Example 1:
//  *   A known ISO date, plus some standard offsets (no special tokens).
//  */
// const example1 = parseDateString("2025-03-15 + 2 weeks - 1 quarter", false);
// console.log("Example #1:", example1.format()); 
// // => "Example #1: <resulting date>"

// /**
//  * Example 2:
//  *   No base date => "now", then "lastQuarter", "endOf('quarter')", +1 month, -1 day
//  */
// const example2 = parseDateString("lastQuarter endOf('quarter') + 1 month - 1 day", false);
// console.log("Example #2:", example2.format()); 
// // => "Example #2: <resulting date>"

// /**
//  * Example 3:
//  *   Partial ISO + special tokens + offset. Then set startOfDay to true.
//  */
// const example3 = parseDateString("2026-01-01T10:00:00 lastQuarter startOf('month') + 3 days", true);
// console.log("Example #3:", example3.format()); 
// // => "Example #3: <resulting date at 00:00:00>"

// /**
//  * Example 4:
//  *   Just a special token "lastQuarter" with no date => from "now".
//  *   Also 'endOf('quarter')'.
//  */
// const example4 = parseDateString("lastQuarter endOf('quarter')", false);
// console.log("Example #4:", example4.format()); 
// // => "Example #4: <the final day/time of the previous quarter>"

// /**
//  * Example 5:
//  *   Empty or null => fallback to 'now'.
//  */
// const example5 = parseDateString(null, false);
// console.log("Example #5:", example5.format()); 
// // => "Example #5: <today's date/time>"
