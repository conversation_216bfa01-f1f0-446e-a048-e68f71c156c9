/**
 * Updates a nested value inside an object based on a given path.
 * It supports both object properties and array indices. Additionally,
 * it supports using a wildcard `"*"` in the path to update a property
 * for every object in an array.
 *
 * When the path includes a `"*"`, it indicates that at that level the property is an array,
 * and the provided `newValue` should also be an array. The function will update the nested
 * property (specified in the remaining path) for each object in that array with the corresponding
 * value from `newValue`.
 *
 * @param {object} obj - The target object to update. Must be a non-null object.
 * @param {string[]} path - An array representing the path to the property.
 *    If the path includes a `"*"`, the corresponding property is expected to be an array.
 *    The path must not be empty.
 * @param {any} newValue - The new value to set at the target path.
 *    When using a wildcard, this should be an array of values, one for each element in the target array.
 *    If `newValue` is `undefined`, the property will be deleted.
 *
 * @throws {Error} If `obj` is not a valid object, the path is empty, or if an expected array or object is missing in the path.
 *
 * @example
 * // Example 1: Updating a nested object field
 * const data1 = { user: { profile: { name: "<PERSON>" } } };
 * updateNestedValue(data1, ["user", "profile", "name"], "Bob");
 * console.log(data1); // { user: { profile: { name: "Bob" } } }
 *
 * @example
 * // Example 2: Updating an array element
 * const data2 = { items: [{ value: 10 }] };
 * updateNestedValue(data2, ["items", "0", "value"], 20);
 * console.log(data2); // { items: [{ value: 20 }] }
 *
 * @example
 * // Example 3: Using wildcard "*" in the path to update all elements of an array
 * const data3 = { particulars: [
 *   { particular_total: 5 },
 *   { particular_total: 7 },
 *   { particular_total: 9 }
 * ] };
 * updateNestedValue(data3, ["particulars", "*", "particular_total"], [10, 20, 30]);
 * console.log(data3);
 * // {
 * //   particulars: [
 * //     { particular_total: 10 },
 * //     { particular_total: 20 },
 * //     { particular_total: 30 }
 * //   ]
 * // }
 */
export function updateNestedValue(obj: any, path: string[], newValue: any): void {  
  try {
    // console.log(`updateNestedValue called with obj: ${JSON.stringify(obj, null, 10)}, path: ${path}, newValue: ${newValue}`);
    
    // Validate target object.
    if (obj === null || typeof obj !== "object") {
      throw new Error("Invalid target object provided. It must be a non-null object.");
    }

    // Validate that path is a non-empty array.
    if (!Array.isArray(path) || path.length === 0) {
      throw new Error("The path must be a non-empty array of strings.");
    }

    // Check if the path contains a wildcard "*"
    const wildcardIndex = path.indexOf("*");

    if (wildcardIndex !== -1) {
      // When a wildcard is used, we expect newValue to be an array.
      if (!Array.isArray(newValue)) {
        throw new Error(`Expected newValue to be an array when using wildcard "*" in path.`);
      }


      // Split the path into two parts:
      // 1. prePath: the path leading up to the wildcard.
      // 2. postPath: the path after the wildcard.
      const prePath = path.slice(0, wildcardIndex);
      const postPath = path.slice(wildcardIndex + 1);

      // Traverse the object until we reach the level with the wildcard.
      let currentLevel = obj;

      for (const key of prePath) {
        const isArrayIndex = !isNaN(Number(key));
        const parsedKey: string | number = isArrayIndex ? Number(key) : key;

        if (currentLevel[parsedKey] === undefined) {
          // Initialize as array or object depending on whether the key is numeric.
          currentLevel[parsedKey] = isArrayIndex ? [] : {};
        }

        currentLevel = currentLevel[parsedKey];
      }

      // Ensure the current level is an array.
      if (!Array.isArray(currentLevel)) {
        throw new Error(`Expected an array at ${prePath.join('.')}`);
      }

      // Iterate over the array. For each element, update the nested value using the corresponding
      // value from the newValue array. If newValue doesn't have a corresponding element, skip the update.
      for (let i = 0; i < currentLevel.length; i++) {
        if (i >= newValue.length) continue;
        updateNestedValue(currentLevel[i], postPath, newValue[i]);
      }

      return;
    }

    // Original logic (without wildcard) for traversing the object.
    let currentLevel = obj;

    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i];
      const isArrayIndex = !isNaN(Number(key));
      const parsedKey: string | number = isArrayIndex ? Number(key) : key;
      
      if (isArrayIndex) {
        if (!Array.isArray(currentLevel)) {
          throw new Error(`Expected an array at ${path.slice(0, i + 1).join('.')}`);
        }

        if (currentLevel[parsedKey] === undefined) {
          // Ensure the array index exists.
          currentLevel[parsedKey] = {};
        }
      } else {
        if (currentLevel[parsedKey] === undefined) {
          currentLevel[parsedKey] = {};
        }
      }

      currentLevel = currentLevel[parsedKey];
    }

    // Update the final property.
    const lastKey = path[path.length - 1];
    const isLastKeyArrayIndex = !isNaN(Number(lastKey));
    const parsedLastKey: string | number = isLastKeyArrayIndex ? Number(lastKey) : lastKey;

    if (newValue === undefined) {
      delete currentLevel[parsedLastKey];
    } else {
      currentLevel[parsedLastKey] = newValue;
    }
  } catch (error) {
    console.warn("Error in updateNestedValue:", error);
    throw error;
  }
}


/**
 * Recursively searches an object or array for a nested property that meets the following criteria:
 * - The object has a `fact` property that is one of the specified `factNames`.
 * - If such an object also has a `params.affects` property (which is either a string or an array of strings),
 *   that value is returned; otherwise, the `fact` value is returned.
 *
 * If no object meeting these criteria is found, the function returns null.
 *
 * @param obj - The object or array to search.
 * @param factNames - An array of fact names to look for.
 * @returns The matching `params.affects` value (if present and valid), or the `fact` value, or null.
 *
 * @example
 * // Example with `params.affects` as a string:
 * const obj1 = {
 *   fact: 'purchaseformlist',
 *   params: { affects: 'someValue' }
 * };
 * // Returns "someValue" because the fact is in factNames and params.affects exists.
 * console.log(findMatchingFact(obj1, ['purchaseformlist'])); // "someValue"
 *
 * @example
 * // Example without `params.affects`:
 * const obj2 = {
 *   fact: 'purchaseformlist'
 * };
 * // Returns "purchaseformlist" because no affects value is available.
 * console.log(findMatchingFact(obj2, ['purchaseformlist'])); // "purchaseformlist"
 *
 * @example
 * // Example with nested objects:
 * const obj3 = {
 *   event: {
 *     params: {
 *       details: {
 *         fact: 'vendor_id',
 *         params: { affects: ['affect1', 'affect2'] }
 *       }
 *     }
 *   }
 * };
 * // Returns ["affect1", "affect2"] because vendor_id is in factNames.
 * console.log(findMatchingFact(obj3, ['vendor_id'])); // ["affect1", "affect2"]
 */
export function findMatchingFact(
  obj: unknown,
  factNames: string[]
): string | string[] | null {
  if (Array.isArray(obj)) {
    for (const item of obj) {
      const found = findMatchingFact(item, factNames);

      if (found !== null) {
        return found;
      }
    }
  } else if (obj !== null && typeof obj === 'object') {
    const record = obj as Record<string, any>;


    // Check if this object has a `fact` property that is in factNames.
    if (typeof record.fact === 'string' && factNames.includes(record.fact)) {

      // If a valid `params.affects` exists, return that.
      if (record.params && typeof record.params === 'object') {
        const affects = record.params.affects;

        if (
          typeof affects === 'string' ||
          (Array.isArray(affects) && affects.every((item) => typeof item === 'string'))
        ) {
          return affects;
        }
      }


      // Otherwise, return the fact value.
      return record.fact;
    }


    // Otherwise, continue searching in all properties.
    for (const key in record) {
      if (Object.prototype.hasOwnProperty.call(record, key)) {
        const found = findMatchingFact(record[key], factNames);

        if (found !== null) {
          return found;
        }
      }
    }
  }


  return null;
}

/**
 * Recursively transforms raw formData into a flattened payload suitable for API consumption.
 * - Converts nested objects with `id` properties into their IDs.
 * - Flattens objects when uiSchema defines a layout.
 * - Converts arrays of objects into arrays of simplified objects (extracting `id` where available).
 * - Preserves primitive values and nulls.
 * - Avoids injecting schema defaults — uses only what the user has input.
 * - Applies widget/field mapping logic from RJSF conventions (e.g., `typehead`, `TableWidget`).
 * - Coerces primitive values based on declared ui:options.type.
 * - Logs warnings for unexpected types or unknown widgets.
 *
 * @param formData - The original form state data.
 * @param uiSchema - UI Schema definition from RJSF (optional).
 * @returns A new object containing only primitive values or IDs, structured for backend APIs.
 */
export function processFormData(
  formData: Record<string, any>,
  uiSchema: Record<string, any> = {}
): Record<string, any> {
  const result: Record<string, any> = {};

  function isTypeheadField(uiField: string | undefined): boolean {
    return uiField === 'typehead';
  }

  function isCustomWidget(widget: string | undefined): boolean {
    return [
      'TableWidget',
      'ImageWidget',
      'TimeWidget',
      'CameraWidget',
    ].includes(widget ?? '');
  }

  function coerceValueByType(value: any, type?: string): any {
    if (value === null) return null;

    if (type === 'boolean') {
      if (value === true || value === 'true') return true;
      if (value === false || value === 'false') return false;
    }

    if (type === 'number' || type === 'integer') {
      const parsed = parseFloat(value);


      return isNaN(parsed) ? value : parsed;
    }

    if (type === 'string') {
      return String(value);
    }


    return value;
  }

  function traverse(
    data: any,
    uiSchemaNode: Record<string, any>,
    parent: Record<string, any>
  ) {
    if (data === null) return;
    if (typeof data !== 'object') return;

    Object.entries(data).forEach(([key, value]) => {
      const uiNode = uiSchemaNode?.[key] || {};
      const uiOptions = uiNode['ui:options'] || {};
      const uiField = uiNode['ui:field'];
      const uiWidget = uiNode['ui:widget'];
      const fieldType = uiOptions?.type;

      try {
        if (value === null) {
          parent[key] = null;

          return;
        }

        // Flatten layout containers (e.g., group rows/columns)
        if (uiOptions?.layout && typeof value === 'object' && !Array.isArray(value)) {
          traverse(value, uiNode, parent);

          return;
        }

        // Handle typehead (dropdown/autocomplete)
        if (isTypeheadField(uiField)) {
          parent[key] = Array.isArray(value)
            ? value.map((v: any) => v?.id ?? v)
            : value?.id ?? value;

          return;
        }

        // Handle file/image/table/camera widgets
        if (isCustomWidget(uiWidget)) {
          if (uiWidget === 'TableWidget') {
            parent[key] = (Array.isArray(value) ? value : [])?.filter((v: any) => v?.id && v?.selected).map((v: any) => v?.id);
          } else {
            parent[key] = value;
          }

          return;
        }

        // Unknown widget warning
        if (uiWidget && !isCustomWidget(uiWidget)) {
          console.warn(`Unknown widget for key "${key}":`, uiWidget);
        }

        // Handle arrays
        if (Array.isArray(value)) {
          parent[key] = value.map(item => {
            if (item && typeof item === 'object') {
              if ('id' in item) return item.id;
              const nested: Record<string, any> = {};

              traverse(item, uiNode?.items || {}, nested);

              return nested;
            }


            return coerceValueByType(item, fieldType);
          });

          return;
        }

        // Handle nested objects
        if (value && typeof value === 'object') {
          if ('id' in value) {
            parent[key] = value.id;
          } else {
            parent[key] = {};
            traverse(value, uiNode, parent[key]);
          }


          return;
        }

        const coerceValue = coerceValueByType(value, fieldType);

        console.log(`coerced value for ${key} is ${coerceValue} with type of ${typeof coerceValue}`)

        // Primitive value with type-based coercion
        parent[key] = coerceValue;
      } catch (err) {
        console.warn(`Failed to process key: ${key}`, err);
        parent[key] = value;
      }
    });
  }

  traverse(formData, uiSchema, result);

  return result;
}

/**
 * Checks if an object is empty (has no own properties).
 * 
 * @param obj - The object to check.
 * @returns True if the object is empty and is a plain object, false otherwise.
 */
export const isEmptyObject = (obj: any): boolean => {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
};

/**
 * Generates a SHA-256 or FNV-1a fallback hash of the input value.
 * 
 * This function normalizes the input (string, object, or bytes) and computes a stable hash.
 * 
 * - If browser `crypto.subtle.digest` is available, it uses SHA-256.
 * - Otherwise, it falls back to a FNV-1a 32-bit hash.
 * 
 * @param value - The input to hash. Accepts:
 *   - `string`
 *   - `Uint8Array`
 *   - `ArrayBuffer`
 *   - Plain `object` (which will be JSON.stringified deterministically)
 * @returns A promise that resolves to the hexadecimal hash string.
 * 
 * @example
 * ```typescript
 * const hashValue = await hash({ name: "John Doe", age: 30 });
 * console.log(hashValue); // e.g., '********************************'
 * ```
 */
export const hash = async (value: string | Uint8Array | ArrayBuffer | object): Promise<string> => {
  const toBytes = (v: string | Uint8Array | ArrayBuffer | object): Uint8Array => {
    if (v instanceof Uint8Array) return v;
    if (v instanceof ArrayBuffer) return new Uint8Array(v);
    if (typeof v === 'string') return new TextEncoder().encode(v);
    if (typeof v === 'object' && v !== null)
      return new TextEncoder().encode(JSON.stringify(v));
    
    throw new TypeError('hash() accepts string, Uint8Array, ArrayBuffer, or plain object');
  };

  const bytes = toBytes(value);

  /* SHA-256 */
  if (typeof crypto !== "undefined" && crypto.subtle?.digest) {
    try {
      const buf = await crypto.subtle.digest('SHA-256', bytes);
      
      return [...new Uint8Array(buf)]
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
    } catch (err) {
      console.error('SHA-256 failed; falling back to FNV-1a', err);
    }
  }

  /* FNV-1a fallback */
  let h = 0x811c9dc5;

  for (let i = 0; i < bytes.length; i++) {
    h ^= bytes[i];
    h = (h >>> 0) * 0x01000193;
  }

  return (h >>> 0).toString(16).padStart(8, '0');
};
