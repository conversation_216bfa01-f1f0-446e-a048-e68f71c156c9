import { evaluate } from 'mathjs';

/**
 * Recursively replaces all `null` and `undefined` values in an object or array with `0`.
 * This function is type-safe and preserves other values like booleans, strings, and nested objects.
 *
 * @param input - The input value, which can be an object, array, or primitive.
 * @returns A new object, array, or primitive with `null` and `undefined` values replaced by `0`.
 *
 * @example
 * const input = {
 *   vendor_id: null,
 *   vendor_bill_type_purchase: 'credit',
 *   particulars: [{ particular_total: 0 }],
 *   apply_round_off: true,
 *   is_billable: false,
 *   amount: [null, 1, undefined],
 * };
 *
 * const result = replaceNullAndUndefined(input);
 * console.log(result);
 * // {
 * //   vendor_id: 0,
 * //   vendor_bill_type_purchase: 'credit',
 * //   particulars: [{ particular_total: 0 }],
 * //   apply_round_off: true,
 * //   is_billable: false,
 * //   amount: [0, 1, 0],
 * // }
 */
export function replaceNullAndUndefined<T>(input: T): T {
  if (Array.isArray(input)) {
    return input.map((item) =>
      item === null || item === undefined ? 0 : replaceNullAndUndefined(item)
    ) as T;
  }

  if (typeof input === 'object' && input !== null) {
    const result: Record<string, unknown> = {};

    for (const [key, value] of Object.entries(input)) {
      result[key] =
        value === null || value === undefined ? 0 : replaceNullAndUndefined(value);
    }


    return result as T;
  }

  return input;
}

/**
 * Evaluates a math.js expression using data from formData.
 * @param {string} expression - The math.js expression to evaluate.
 * @param {Record<string, any>} formData - The data to use as scope in the expression.
 * @returns {any} - The result of the evaluation.
 */
export function evaluateExpression(expression, formData) {
  // Evaluate the expression using math.js
  try {
    const modified = replaceNullAndUndefined(formData);

    return evaluate(expression, modified);
  } catch (error) {
    console.warn('Error evaluating expression:', error);

    // throw error;
  }
}
