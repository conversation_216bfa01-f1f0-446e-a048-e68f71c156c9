// evaluateExpression.ts

import { evaluate } from 'mathjs';

/**
 * Replaces placeholders like `:someField->someKey` with the actual data
 * from formData. For example:
 *    :purchase_order_items->item_cost
 * becomes
 *    [10, 20, 30]
 * if purchase_order_items = [
 *   { item_cost: 10 },
 *   { item_cost: 20 },
 *   { item_cost: 30 }
 * ] in formData.
 */
function replacePlaceholders(expression: string, formData: Record<string, any>): string {
  // Regex Explanation:
  // :([a-zA-Z0-9_]+)  => captures the formData key after ':'
  // (->([a-zA-Z0-9_]+))? => optionally captures '->someKey'
  const regex = /:([a-zA-Z0-9_]+)(->([a-zA-Z0-9_]+))?/g;

  return expression.replace(regex, (_, variableName, _fullArrow, subKey) => {
    const value = formData[variableName];

    // If the formData doesn't have this field, replace with 0 by default
    if (typeof value === 'undefined') {
      return '0';
    }

    // If we have `:someVar->someKey` and `someVar` is an array of objects,
    // replace with `[obj[ 'someKey' ], ...]`
    if (subKey && Array.isArray(value)) {
      // Extract just the subKey from each item
      const arr = value.map((item) => item?.[subKey] ?? 0);

      
return JSON.stringify(arr); // e.g. [10,20,30]
    }

    // Otherwise, if no subKey, just use the raw value
    // If it's an array, we convert to JSON; if it's a scalar, just return it
    if (Array.isArray(value)) {
      return JSON.stringify(value);
    }

    // If it's a scalar (number/string), just return it.
    // If it's string text, might need quotes for mathjs. 
    // But typically we assume numeric or array usage.
    if (typeof value === 'string' && !isNaN(Number(value))) {
      return value; // e.g. "42"
    }

    // Fallback if it’s a plain string or object
    return JSON.stringify(value);
  });
}

/**
 * Evaluate the expression using math.js, after replacing placeholders.
 */
export function evaluateExpression(
  expression: string,
  formData: Record<string, any>
): any {
  try {
    const replaced = replacePlaceholders(expression, formData);

    // e.g. "sum( [10,1,2], [1,1,2] )"

    const result = evaluate(replaced);

    
return result;
  } catch (error) {
    console.warn('Error evaluating expression:', error);
    
return null;
  }
}

// {
//   "conditions": {
//     "all": [
//       {
//         "fact": "purchase_order_items",
//         "operator": "truthy",
//         "value": true
//       }
//     ]
//   },
//   "event": [
//     {
//       "type": "calculate",
//       "params": {
//         "costs": {
//           "fact": "purchase_order_items",
//           "path": "$..item_cost"
//         },
//         "quantities": {
//           "fact": "purchase_order_items",
//           "path": "$..item_quantity"
//         },
//         "__field": "purchase_form_amount",
//         "__exp": "{{ sum(dotMultiply(costs, quantities)) }}"
//       }
//     }
//   ]
// }