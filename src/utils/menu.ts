import type { MenuItemData, VerticalMenuDataType } from '@/types/menuTypes';

/**
 * Deduplicates nested vertical menu items by their `href` value.
 *
 * This is used to ensure no duplicate routes are generated when combining
 * menu structures across multiple modes (e.g., society + gate).
 *
 * Only the first occurrence of each unique `href` is retained.
 *
 * @param menuItems - Full vertical menu structure (can include nested children).
 * @returns A new menu array with duplicate `href` entries removed.
 *
 * @example
 * ```ts
 * const uniqueMenus = deduplicateVerticalMenu([...societyData, ...gateData]);
 * ```
 */
export function deduplicateVerticalMenu(menuItems: (VerticalMenuDataType)[]): VerticalMenuDataType[] {
  const seen = new Set<string>();

  const deduplicate = (items: VerticalMenuDataType[]): VerticalMenuDataType[] => {
    const result: VerticalMenuDataType[] = [];

    for (const item of items) {
      let dedupedChildren: VerticalMenuDataType['children'] | undefined;

      if (item.children?.length) {
        dedupedChildren = item.children.filter(child => {
          if (!child.href) return true;
          if (seen.has(child.href)) return false;
          seen.add(child.href);

          return true;
        });
      }

      const hasHref = !!item.href;
      const isUnique = !hasHref || (hasHref && !seen.has(item.href!));

      if (isUnique) {
        if (item.href) seen.add(item.href);

        result.push({
          ...item,
          ...(dedupedChildren ? { children: dedupedChildren } : {}),
        });
      }
    }

    return result;
  };

  return deduplicate(menuItems);
}

// /**
//  * Returns true if the last two path segments are Next-style dynamic segments.
//  */
// function isDynamicRoute(href: string): boolean {
//   const segments = href.split('/').filter(Boolean);
//   const len = segments.length;

//   // only inspect the last two segments
//   for (let i = Math.max(0, len - 3); i < len; i++) {
//     const seg = segments[i];

//     if (seg.startsWith('[') && seg.endsWith(']')) {
//       return true;
//     }
//   }

//   return false;
// }

/**
 * Recursively flattens a nested `VerticalMenuDataType[]` menu structure
 * into an array of dynamic route parameters for static site generation.
 *
 * Skips paths containing `[id]` and non-root-relative URLs.
 *
 * @param items - The vertical menu items to flatten.
 * @returns An array of `{ slug: string[] }` route objects.
 *
 * @example
 * ```ts
 * getPaths(menuData)
 * // => [ { slug: ['admin', 'gate', 'users', 'list'] }, ... ]
 * ```
 */
export const getPaths = (
  items: (VerticalMenuDataType & { href?: string })[]
): Array<{ mode: string; slug: string[] }> => {
  const paths: Array<{ mode: string; slug: string[] }> = [];

  for (const item of items) {
    if (item.children?.length) {
      paths.push(...getPaths(item.children));
    }

    if (
      item.href &&
      item.href.startsWith('/admin/')

      // && !isDynamicRoute(item.href)   // skip if any of the last two segments is dynamic
    ) {
      const [, mode, ...rest] = item.href.split('/').filter(Boolean);

      if (mode === 'society' || mode === 'gate') {
        paths.push({ mode, slug: rest });
      }
    }
  }

  return paths;
};


type FindResult = {
  pageData: VerticalMenuDataType | null;
  breadcrumbs: MenuItemData[];
};

export function findItemWithBreadcrumb(
  items: VerticalMenuDataType[],
  slug: string[]
): FindResult {
  const isDynamic = (seg: string) =>
    seg.startsWith('[') && seg.endsWith(']');

  const dfs = (
    nodes: VerticalMenuDataType[],
    trail: MenuItemData[]
  ): FindResult => {
    for (const node of nodes) {
      // build this level’s crumb entry
      const entry: MenuItemData = {
        label: String(node.label),
        href: String(node?.href),
        icon: String(node.icon),
      };

      const newTrail = [...trail, entry];

      // 1) direct match on this node’s href?
      if (node.href) {
        const segs = node.href.split('/').filter(Boolean);

        if (
          segs.length === slug.length &&
          segs.every((s, i) => (isDynamic(s) ? true : s === slug[i]))
        ) {
          return { pageData: node, breadcrumbs: newTrail };
        }
      }

      // 2) recurse into children unconditionally
      if (node.children) {
        const result = dfs(node.children, newTrail);

        if (result.pageData) {
          return result;
        }
      }
    }

    // no match anywhere under these nodes
    return { pageData: null, breadcrumbs: [] };
  };

  return dfs(items, []);
}
