export const ensurePrefix = (str: string, prefix: string) => (str.startsWith(prefix) ? str : `${prefix}${str}`)

/**
 * Escapes special characters in a string for safe usage in a RegExp.
 *
 * @param input - The string to escape.
 * @returns The escaped string.
 *
 * @example
 * escapeRegExp("a+b(c)*"); // "a\\+b\\(c\\)\\*"
 */
export function escapeRegExp(input: string): string {
  try {
    return input.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  } catch (error) {
    console.warn("Error escaping RegExp string:", error);

    return input;
  }
}

/**
 * A generic schema type representing any object.
 */
export type UISchema = {
  [key: string]: any;
};

/**
 * Finds the path of a given field within a uiSchema object.
 *
 * @param fieldName - The name of the field to find.
 * @param uiSchema - The uiSchema object to search through.
 * @param path - The accumulated path for nested fields (default is an empty string).
 * @returns The dot-separated path to the field if found; otherwise, returns null.
 *
 * @example
 * const uiSchema = {
 *   properties: {
 *     name: { type: "string" },
 *     details: {
 *       properties: {
 *         age: { type: "number" }
 *       }
 *     }
 *   }
 * };
 * findFieldPathInUISchema("age", uiSchema);
 * // Returns "properties.details.properties.age"
 */
export function findFieldPathInUISchema(fieldName: string, uiSchema: UISchema, path: string = ''): string | null {
  try {
    if (!uiSchema || typeof uiSchema !== 'object') return null;

    // Check if the field exists directly at this level.
    if (uiSchema[fieldName]) {
      return path ? `${path}.${fieldName}` : fieldName;
    }

    // Handle array types with 'items'
    if (uiSchema.items) {
      // If no current path, use "items" as the prefix; otherwise, append ".items"
      const newPath = path ? `${path}.items` : "items";
      const result = findFieldPathInUISchema(fieldName, uiSchema.items, newPath);

      if (result) return result;
    }

    // Recursively search in nested objects.
    for (const key in uiSchema) {
      if (typeof uiSchema[key] === 'object') {
        const newPath = path ? `${path}.${key}` : key;
        const result = findFieldPathInUISchema(fieldName, uiSchema[key], newPath);

        if (result) return result;
      }
    }

    return null;
  } catch (error) {
    console.warn("Error finding field path in uiSchema:", error);

    return null;
  }
}

/**
 * Extracts dynamic parameters from a URL based on a URL template.
 *
 * The function compares the segments of the given pathname and the href template.
 * Dynamic segments in the href should be enclosed in square brackets (e.g., [vendor_id]).
 * If the pathname has extra segments at the beginning, the function aligns the href segments
 * from the right. If static segments (non-dynamic) do not match, an error is thrown.
 *
 * @param pathname - The current URL path.
 * @param href - The URL template containing dynamic segments.
 * @returns A record mapping parameter names to their corresponding values.
 *
 * @example
 * const pathname = "/vendor/1234/details";
 * const href = "/[vendor_id]/details";
 * extractParamsFromUrl(pathname, href);
 * // Returns { vendor_id: "1234" }
 *
 * @example
 * const pathname = "/admin/user/5678/profile";
 * const href = "/admin/[user_id]/profile";
 * extractParamsFromUrl(pathname, href);
 * // Returns { user_id: "5678" }
 */
export const extractParamsFromUrl = (pathname: string, href: string): Record<string, string> | null => {
  try {
    if (!pathname || !href) return null;

    // Remove '/admin' prefix if present.
    const cleanedHref = href.startsWith('/admin') ? href.replace('/admin', '') : href;
    const cleanedPathname = pathname.startsWith('/admin') ? pathname.replace('/admin', '') : pathname;

    // Split into segments.
    const pathnameSegments: string[] = cleanedPathname.split('/').filter(Boolean);
    const hrefSegments: string[] = cleanedHref.split('/').filter(Boolean);

    if (pathnameSegments.length < hrefSegments.length) {
      throw new Error("Mismatch between pathname and href segments");
    }

    // Align segments from the right.
    const offset = pathnameSegments.length - hrefSegments.length;
    const params: Record<string, string> = {};

    hrefSegments.forEach((segment, index) => {
      const actualSegment = pathnameSegments[index + offset];

      if (segment.startsWith('[') && segment.endsWith(']')) {
        const paramName = segment.slice(1, -1);

        params[paramName] = actualSegment;
      } else if (segment !== actualSegment) {
        throw new Error("Mismatch between pathname and href segments");
      }
    });

    // console.log(params)

    return params;
  } catch (error) {
    console.warn("Error extracting params from URL:", error);
    throw error;
  }
};

/**
 * Returns the initials from the provided string.
 *
 * Splits the string on whitespace and concatenates the first character of each word.
 *
 * @param input - The string to extract initials from.
 * @returns A string containing the initials.
 *
 * @example
 * getInitials("John Doe"); // "JD"
 */
export const getInitials = (input: string): string => {
  try {
    if (typeof input !== 'string') {
      throw new Error("Invalid input for getInitials: expected a string");
    }


    return input
      .trim()
      .split(/\s+/)
      .reduce((response, word) => response + word.charAt(0), "");
  } catch (error) {
    console.warn("Error in getInitials:", error);

    return "";
  }
};

/**
 * Extracts a path from a string by removing the "root" prefix and the specified field name.
 *
 * The function removes the "root_" prefix (if present) and then removes all occurrences
 * of the field name (preceded by either the start of the string or an underscore).
 * The remaining string is split on underscores and empty segments are filtered out.
 *
 * @param fullString - The original string containing the path.
 * @param fieldName - The field name to remove.
 * @returns An array of the extracted path segments.
 *
 * @example
 * extractPathWithUnderscores("root_particulars_0_particular_gst", "particular_gst");
 * // Returns ["particulars", "0"]
 */
export function extractPathWithUnderscores(fullString: string, fieldName: string): string[] {
  try {
    if (typeof fullString !== "string" || typeof fieldName !== "string") {
      throw new Error("Invalid input: both fullString and fieldName must be strings");
    }

    let cleanedString = fullString.startsWith("root_") ? fullString.slice(5) : fullString;
    const escapedFieldName = escapeRegExp(fieldName);

    // Remove occurrences where fieldName is at the start or preceded by an underscore.
    const regex = new RegExp(`(^|_)${escapedFieldName}`, 'g');

    cleanedString = cleanedString.replace(regex, "");

    return cleanedString.split('_').filter(Boolean);
  } catch (error) {
    console.warn("Error in extractPathWithUnderscores:", error);

    return [];
  }
}

/**
 * Options for truncating a string in the middle.
 */
export type TruncateMiddleOptions = {

  /**
   * Number of characters to keep from the start of the string.
   * @default 6
   */
  startLength?: number;

  /**
   * Number of characters to keep from the end of the string.
   * @default 6
   */
  endLength?: number;

  /**
   * String used as the separator in the middle.
   * @default '...'
   */
  delimiter?: string;
};

/**
 * Truncates a string in the middle by keeping a specified number of characters at the start and end.
 *
 * If the string's length is less than or equal to startLength + endLength, the original string is returned.
 * Otherwise, the middle portion is replaced by the delimiter. Additionally, if the end substring starts with
 * a non-alphanumeric character, it is trimmed.
 * @deprecated Use `ellipsis(str, { position: 'middle', ...opts })` instead.
 * @param str - The string to truncate.
 * @param options - Configuration options for truncation.
 * @returns The truncated string.
 *
 * @example
 * truncateMiddle("abcdefghijklmnopqrstuvwxyz");
 * // Returns "abcdef...uvwxyz"
 */
export const truncateMiddle = (
  str: string,
  options: TruncateMiddleOptions = {}
): string => {
  try {
    if (!str || typeof str !== 'string') return "";
    const { startLength = 6, endLength = 6, delimiter = '...' } = options;

    if (str.length <= startLength + endLength) {
      return str;
    }

    const start = str.substring(0, startLength);
    let end = str.substring(str.length - endLength);


    // If the end substring starts with a non-alphanumeric character, trim it.
    if (end && !/^[a-z0-9]/i.test(end)) {
      end = end.substring(1);
    }


    return `${start}${delimiter}${end}`;
  } catch (error) {
    console.warn("Error in truncateMiddle:", error);

    return "";
  }
};

/**
 * Represents a text segment with highlighting metadata.
 */
export interface HighlightedText {
  text: string;
  highlight: boolean;
}

/**
 * Represents a range of indices [start, end] for matched text.
 */
export type MatchRange = [number, number];

/**
 * Finds all occurrences of a query within a given text.
 *
 * The search is case-insensitive by default. When caseSensitive is true, the search is case-sensitive.
 * All non-overlapping occurrences are returned as index pairs.
 *
 * @param text - The text to search.
 * @param query - The substring to find.
 * @param caseSensitive - Whether the search is case-sensitive (default false).
 * @returns An array of [start, end] index pairs for each match.
 *
 * @example
 * match("Some example text", "ex");
 * // Returns [[5, 7]]
 */
export function match(text: string, query: string, caseSensitive: boolean = false): MatchRange[] {
  try {
    if (!text || !query) return [];
    const sourceText = caseSensitive ? text : text.toLowerCase();
    const searchQuery = caseSensitive ? query : query.toLowerCase();
    const matches: MatchRange[] = [];
    let startIndex = sourceText.indexOf(searchQuery, 0);

    while (startIndex !== -1) {
      matches.push([startIndex, startIndex + searchQuery.length]);
      startIndex = sourceText.indexOf(searchQuery, startIndex + searchQuery.length);
    }


    return matches;
  } catch (error) {
    console.warn("Error in match function:", error);

    return [];
  }
}

/**
 * Parses text into segments based on provided match ranges.
 *
 * Each segment is an object with the text and a highlight flag indicating whether
 * that segment is highlighted (i.e. part of a match).
 *
 * @param text - The text to parse.
 * @param matches - An array of index ranges indicating matches.
 * @returns An array of text segments with highlighting metadata.
 *
 * @example
 * const text = "Some example text example";
 * const ranges = [[5, 7], [14, 16], [19, 21]];
 * parse(text, ranges);
 * // Returns segments with appropriate highlight flags.
 */
export function parse(text: string, matches: MatchRange[]): HighlightedText[] {
  try {
    if (!matches || matches.length === 0) return [{ text, highlight: false }];
    const parts: HighlightedText[] = [];
    let lastIndex = 0;

    for (const [start, end] of matches) {
      if (lastIndex < start) {
        parts.push({ text: text.slice(lastIndex, start), highlight: false });
      }

      parts.push({ text: text.slice(start, end), highlight: true });
      lastIndex = end;
    }

    if (lastIndex < text.length) {
      parts.push({ text: text.slice(lastIndex), highlight: false });
    }


    return parts;
  } catch (error) {
    console.warn("Error in parse function:", error);

    return [{ text, highlight: false }];
  }
}

export interface EllipsisOptions {

  /** Total maximum length of the returned string, including delimiter */
  maxLength?: number;

  /** Where to place the ellipsis: 'end' | 'start' | 'middle' */
  position?: 'end' | 'start' | 'middle';

  /** The string to use as the ellipsis */
  delimiter?: string;

  /** When using 'middle', how many chars to keep at the start */
  startLength?: number;

  /** When using 'middle', how many chars to keep at the end */
  endLength?: number;
}

/**
 * Truncates a string by inserting an ellipsis delimiter at the start, middle, or end,
 * ensuring the result never exceeds {@link EllipsisOptions.maxLength}.
 *
 * @param str - The input string to truncate.
 * @param options - Configuration for how to apply the ellipsis.
 * @param options.maxLength - Maximum length of the returned string (including delimiter).
 *    Must be an integer ≥ 1 + delimiter.length (default: 30).
 * @param options.position - Where to place the delimiter:
 *    - `"end"` (default) keeps the first characters,
 *    - `"start"` keeps the last characters,
 *    - `"middle"` splits between start/end.
 * @param options.delimiter - The string to indicate truncation (default: `'...'`).
 * @param options.startLength - For `"middle"`, number of characters to keep at the start.
 *    Must be a non-negative integer. Default: `Math.floor((maxLength - delimiter.length)/2)`.
 * @param options.endLength - For `"middle"`, number of characters to keep at the end.
 *    Must be a non-negative integer. Default: `Math.ceil((maxLength - delimiter.length)/2)`.
 *
 * @returns The original string if its length ≤ maxLength; otherwise, a truncated version.
 *
 * @throws {TypeError} When `str` isn’t a string, or any numeric option is not a safe integer,
 *                      or if `maxLength` is too small to fit the delimiter.
 *
 * @example
 * // Default: truncate at end
 * ellipsis("abcdefghijklmnopqrstuvwxyz", { maxLength: 10 });
 * // → "abcdefg..."
 *
 * @example
 * // Ellipsis at start
 * ellipsis("Hello, world!", { maxLength: 8, position: "start" });
 * // → "...o, world!"
 *
 * @example
 * // Ellipsis in middle, custom edge lengths
 * ellipsis("1234567890ABCDEFG", {
 *   maxLength: 12,
 *   position: "middle",
 *   delimiter: "--",
 *   startLength: 4,
 *   endLength: 4,
 * });
 * // → "1234--CDEF"
 */
export function ellipsis(
  str: string,
  options: {
    maxLength?: number;
    position?: "start" | "middle" | "end";
    delimiter?: string;
    startLength?: number;
    endLength?: number;
  } = {}
): string {
  if (typeof str !== "string") {
    throw new TypeError(`ellipsis: expected 'str' to be string, got ${typeof str}`);
  }

  const {
    maxLength = 30,
    position = "end",
    delimiter = "...",
    startLength,
    endLength,
  } = options;

  // Validate maxLength
  if (
    typeof maxLength !== "number" ||
    !Number.isSafeInteger(maxLength) ||
    maxLength < delimiter.length + 1
  ) {
    throw new TypeError(
      `ellipsis: 'maxLength' must be a safe integer ≥ ${delimiter.length + 1}, got ${maxLength}`
    );
  }

  // No truncation needed
  if (str.length <= maxLength) {
    return str;
  }

  // Validate delimiter
  if (typeof delimiter !== "string") {
    throw new TypeError(`ellipsis: 'delimiter' must be a string, got ${typeof delimiter}`);
  }

  const avail = maxLength - delimiter.length;

  switch (position) {
    case "start": {
      // Keep the last `avail` chars
      return delimiter + str.slice(-avail);
    }

    case "middle": {
      // Compute or validate edge lengths
      const left =
        startLength !== undefined
          ? startLength
          : Math.floor(avail / 2);

      const right =
        endLength !== undefined
          ? endLength
          : Math.ceil(avail / 2);

      if (
        !Number.isSafeInteger(left) ||
        !Number.isSafeInteger(right) ||
        left < 0 ||
        right < 0 ||
        left + right > avail
      ) {
        throw new TypeError(
          `ellipsis: for 'middle', 'startLength' and 'endLength' must be non-negative integers ` +
          `whose sum ≤ ${avail} (got ${left} + ${right})`
        );
      }

      return str.slice(0, left) + delimiter + str.slice(-right);
    }

    case "end": {
      // Keep the first `avail` chars
      return str.slice(0, avail) + delimiter;
    }

    default:
      // Protect against invalid position strings
      throw new TypeError(
        `ellipsis: 'position' must be 'start', 'middle', or 'end' (got "${position}")`
      );
  }
}

/**
 * Removes the mode segment (`society` or `gate`) from a Next.js admin route path.
 *
 * This is useful when you want to normalize admin URLs for shared layouts or logic
 * without needing to care whether the current path is under `society` or `gate`.
 *
 * Only removes the segment if it appears directly after `/admin`.
 *
 * @param path - The URL pathname (e.g., from `usePathname()`).
 * @returns The cleaned path with the mode segment removed.
 *
 * @example
 * ```ts
 * stripAdminScopedPath("/admin/society/income-details/incomemember");
 * // => "/admin/income-details/incomemember"
 * ```
 *
 * @example
 * ```ts
 * stripAdminScopedPath("/admin/gate/settings");
 * // => "/admin/settings"
 * ```
 *
 * @example
 * ```ts
 * stripAdminScopedPath("/admin/dashboard");
 * // => "/admin/dashboard"
 * ```
 */
export function stripAdminScopedPath(path: string): string {
  const segments = path.split('/').filter(Boolean);

  if (segments[0] === 'admin' && (segments[1] === 'society' || segments[1] === 'gate')) {
    segments.splice(1, 1); // remove the mode segment
  }

  return '/' + segments.join('/');
}

/**
 * Detects the active admin mode (`"society"` or `"gate"`) from a given path.
 *
 * Useful for determining context-sensitive logic (e.g., rendering mode-specific navigation).
 * Only returns a mode if it appears immediately after `/admin`.
 *
 * @param path - The URL pathname (e.g., from `usePathname()`).
 * @returns The detected mode: `"society"`, `"gate"`, or `null` if not found.
 *
 * @example
 * ```ts
 * getAdminModeFromPath("/admin/society/income-details/incomemember");
 * // => "society"
 * ```
 *
 * @example
 * ```ts
 * getAdminModeFromPath("/admin/gate/settings");
 * // => "gate"
 * ```
 *
 * @example
 * ```ts
 * getAdminModeFromPath("/admin/dashboard");
 * // => null
 * ```
 */
export function getAdminModeFromPath(path: string): 'society' | 'gate' | null {
  const segments = path.split('/').filter(Boolean);

  if (segments[0] === 'admin' && (segments[1] === 'society' || segments[1] === 'gate')) {
    return segments[1] as 'society' | 'gate';
  }

  return null;
}

/**
 * Ensures the correct admin mode (`society` or `gate`) is present in the path.
 * If `null` is provided as the mode, any existing mode will be removed.
 *
 * @param path - The URL path to modify (e.g., `/admin/settings`, `/admin/gate/...`)
 * @param desiredMode - `'society'`, `'gate'`, or `null` to remove the mode
 * @returns A new path with the correct mode inserted, replaced, or removed
 *
 * @example
 * ```ts
 * ensureAdminModeInPath('/admin/settings', 'gate'); // '/admin/gate/settings'
 * ensureAdminModeInPath('/admin/gate/settings', 'society'); // '/admin/society/settings'
 * ensureAdminModeInPath('/admin/gate/settings', null); // '/admin/settings'
 * ```
 */
export function ensureAdminModeInPath(
  path: string,
  desiredMode: 'society' | 'gate' | null
): string {
  if(!path) return path;
  const segments = path?.split('/').filter(Boolean);

  if (segments[0] !== 'admin') return path;

  const currentMode = getAdminModeFromPath(path);

  if (desiredMode === null) {
    // Remove mode if present
    if (currentMode) {
      segments.splice(1, 1);
    }
  } else {
    // Add or replace mode
    if (currentMode) {
      segments[1] = desiredMode;
    } else {
      segments.splice(1, 0, desiredMode);
    }
  }

  return '/' + segments.join('/');
}


/**
 * Options for formatting currency or numbers.
 * Extends the built-in Intl.NumberFormatOptions with an optional locale override.
 */
export interface FormatCurrencyOptions extends Intl.NumberFormatOptions {

  /**
   * BCP 47 language tag for locale formatting.
   * @default "en-IN"
   */
  locale?: string;
}

/**
 * Formats a number or numeric string using the Intl.NumberFormat API.
 *
 * @remarks
 * You can pass any valid `Intl.NumberFormatOptions` (like `notation`, `compactDisplay`,
 * `useGrouping`, `minimumFractionDigits`, etc.) to customize the output. By default,
 * this function:
 * - Uses the `"en-IN"` locale,
 * - Formats in `"currency"` style with `"INR"`,
 * - Shows no decimal places (`minimumFractionDigits: 0`),
 * - Allows up to 2 decimal places (`maximumFractionDigits: 2`).
 *
 * @typeParam T
 * If you pass a number directly, the return type is still `string` (formatted).
 *
 * @param amount
 * A number or string representing the amount to format. Strings may include commas
 * (they will be stripped before parsing). Invalid numbers will format to `"NaN"`.
 *
 * @param opts
 * Custom formatting options. Any `Intl.NumberFormatOptions` field may be provided,
 * plus an optional `locale` override.
 *
 * @returns
 * A formatted string according to the specified locale and options.
 *
 * @example
 * ```ts
 * formatCurrency(7855);
 * // → "₹ 7,855"
 *
 * formatCurrency("7855", { currency: "USD", locale: "en-US" });
 * // → "$7,855.00"
 *
 * formatCurrency("50000", {
 *   notation: "compact",
 *   compactDisplay: "short",
 * });
 * // → "₹ 50K"
 *
 * formatCurrency("50000", {
 *   style: "decimal",
 *   notation: "compact",
 *   compactDisplay: "short",
 * });
 * // → "50K"
 * ```
 */
export function formatCurrency(
  amount: number | string,
  opts: FormatCurrencyOptions = {}
): string {
  // Destructure with smart defaults
  const {
    locale = "en-IN",
    style = "currency",
    currency = "INR",
    minimumFractionDigits = 0,
    maximumFractionDigits = 2,
    ...restOptions
  } = opts;

  // Normalize input: strip commas if it's a string
  const num =
    typeof amount === "number"
      ? amount
      : parseFloat(String(amount).replace(/,/g, ""));

  // Build the Intl options object
  const formatOptions: Intl.NumberFormatOptions = {
    style,
    currency,
    minimumFractionDigits,
    maximumFractionDigits,
    ...restOptions,
  };

  return new Intl.NumberFormat(locale, formatOptions).format(num);
}
