const openPDF = async (pdfUrl) => {
    try {
      const response = await fetch(pdfUrl, { mode: 'cors' }); // Ensure CORS handling

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
  
      // Try to open in new tab, fallback to download if blocked
      const newTab = window.open(blobUrl, '_blank');

      if (!newTab || newTab.closed || typeof newTab.closed === 'undefined') {
        // If blocked, force download
        const a = document.createElement('a');

        a.href = blobUrl;
        a.download = 'document.pdf';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    } catch (error) {
      console.warn('Error opening PDF:', error);
    }
  };
  

export default openPDF;
