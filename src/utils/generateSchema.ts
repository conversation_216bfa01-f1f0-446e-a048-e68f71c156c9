import type {
  Dependencies,
  Schema,
  SchemaType,
  UISchema,
  WidgetMapType,
  generateSchemaHelperType,
  generateSchemaType,
  inputFormatsType,
  inputTypesType,
  rule
} from '@/types/form'

type labelItem = string | { "field": string, "key": string }

const inputTypes: inputTypesType = {
  email: 'string',
  number: 'number',
  integer: 'integer',
  dropdown: 'integer',
  autocomplete: 'integer',
  radio: ['string', 'number', 'boolean'],
  select: ['string', 'number', 'boolean'],
  boolean: 'boolean',
  null: 'null',
  array: 'array'
}

const inputFormats: inputFormatsType = {
  'datetime': 'date-time',
  date: 'date',
  time: 'time',
  uri: 'url',
  email: 'email',
  file: 'url'
}

const widgetMap: WidgetMapType = {
  date: 'TimeWidget',
  time: 'TimeWidget',
  datetime: 'TimeWidget',
  daterange: 'DateRangeWidget',

  camera: "CameraWidget",

  checkbox: 'checkboxes',
  select: 'select',
  radio: 'radio',
  textarea: "textarea",

  file: 'ImageWidget',
  table: 'TableWidget',
}

const fieldMap = {
  dropdown: 'typehead',
  autocomplete: 'typehead',
  array: 'ArrayFieldTemplate'
}

const formatEnums = (enums = []) => {
  return enums.map(item => ({ const: item?.const ?? item, title: item?.title ?? item }))
}

const handleCommonProperties = (key: string, value: any): SchemaType => {
  return {
    title: value?.title,
    type: inputTypes[value?.type] || (Array.isArray(value?.type) ? value?.type : 'string'),
    ...((inputTypes[value?.type] === 'string') && { minLength: value?.minLength || 0, maxLength: value?.maxLength || 100 }),
    ...(value?.default !== undefined && { default: value.default }),
    ...(value?.description && { description: value.description }),
    ...(value?.pattern && { pattern: value.pattern || '.*' }),
    ...(value?.enum && { oneOf: formatEnums(value.enum) }),
    ...(value?.multipleOf && { multipleOf: value.multipleOf || 1 })
  }
}

const handleWidgetOptions = (key: string, value: any, uiSchema: UISchema) => {  
  uiSchema[key] = {
    ...uiSchema[key],
    ...(value?.help && { 'ui:help': value?.help }),
    ...(value?.placeholder && { 'ui:placeholder': value?.placeholder }),
    'ui:options': {
      key,
      ...uiSchema[key]?.['ui:options'],
      ...(value?.limitTags && { limitTags: Number.isNaN(Number(value.limitTags)) ? 1 : Number(value.limitTags) }),
      ...(value?.multiple && { multiple: value.multiple }),
      ...(value?.startAdornment && {
        startAdornment: Array.isArray(value.startAdornment) ? value.startAdornment : [value.startAdornment]
      }),
      ...((Object.hasOwn(value, 'hideLabel') || value?.type === "null") && {hideLabel: Object.hasOwn(value, 'hideLabel') ? value?.hideLabel : value?.type === "null"}),
      ...(value?.layout && { layout: value.layout }),
      ...(value?.endAdornment && {
        endAdornment: Array.isArray(value.endAdornment) ? value.endAdornment : [value.endAdornment]
      }),
      ...(value?.apiPath && { apiPath: value.apiPath }),
      ...(value?.labelKeys && { labelKeys: value.labelKeys }),
      ...(value?.minLength && { minLength: value.minLength }),
      ...(value?.onMount && { onMount: value.onMount }),
      ...(value?.type === 'dropdown' && { onMount: true }),
      ...(value?.multiple && { multiple: value.multiple }),
      ...(value?.type === 'textarea' && { rows: 3 }),
      ...(['radio', 'checkbox', 'boolean'].includes(value?.type) && { inline: value?.inline ?? true, label: false }),
      ...(value?.defaultParams && { defaultParams: value.defaultParams }),
      ...(value?.dependent && { dependent: value.dependent }),
      ...(value?.update && { update: value.update }),
      ...(value?.options && {
        api: {
          path: value?.options?.path || '',
          key: value?.options?.key || 'id',
          value: value?.options?.value || 'title'
        },
      }),
      type: value?.type || 'string'
    },
    ...(value?.tooltip && typeof value.tooltip === 'string' && { 'ui:help': value.tooltip }),
    ...(value?.enableMD && { 'ui:enableMarkdownInDescription': true })
  }
}

const handleFormat = (value: any, common: SchemaType) => {
  if (inputFormats[value?.format]) {
    common.format = inputFormats[value?.format] || 'text'
  }
}

const handleWidgetMap = (key: string, value: any, uiSchema: UISchema) => {
  uiSchema[key] = {
    ...uiSchema[key],
    ...((value?.startAdornment || value?.endAdornment) && { 'ui:field': 'adornment' }),
    ...(value?.type in widgetMap && { 'ui:widget': widgetMap[value.type] }),
    ...(value?.format in widgetMap && { 'ui:widget': widgetMap[value.format] }),
    ...(value?.type in fieldMap && { 'ui:field': fieldMap[value.type] }),
    ...(value?.hidden && { 'ui:widget': 'hidden' })
  }
}

const handleOneOf = (key: string, value: any, schema: Schema, uiSchema: UISchema, dependencies: Dependencies) => {
  if (!value?.children) return

  const oneOf = []
  const enums = schema[key]?.enum || schema[key]?.oneOf || []

  for (const enumKey in enums) {
    const enumValue: any = enums?.[enumKey]
    let enumSchema = {}
    const enumRequired = new Set<string>()
    const enumUiSchema: UISchema = {}

    const constKey =
      enumValue && typeof enumValue === 'object' && Object(enumValue).hasOwnProperty('const') ? enumValue?.const : false

    if (value?.children?.[String(constKey)]) {
      const [childSchema, childUiSchema] = generateSchema(value.children[String(constKey)])

      enumSchema = childSchema.properties
      childSchema?.required?.forEach(enumRequired.add, enumRequired)
      enumUiSchema[key] = childUiSchema
    }

    let toPush = {}

    if (value?.type === 'checkbox') {
      toPush = {
        ...toPush,
        contains: {
          enum: [enumValue?.const ?? enumValue]
        }
      }
    } else {
      toPush = {
        ...toPush,
        const: enumValue?.const ?? enumValue
      }
    }

    oneOf.push({
      properties: {
        [key]: {
          ...toPush
        },
        ...(enumSchema || {})
      },
      required: [...enumRequired]
    })

    if (Object.keys(enumUiSchema || {}).length > 0) {
      Object.entries(enumUiSchema[key]).forEach(([field, value]) => {
        uiSchema[field] = {
          ...uiSchema[field],
          ...(typeof value === 'object' ? value : {})
        }
      })
    }

    dependencies[key] = {
      oneOf
    }
  }
}

const handleTable = (key: string, value: any, schema: Schema, uiSchema: UISchema) => {
  schema[key] = {
    ...schema[key],
    type: 'array',

    items: {
      type: 'object'
    },

    // ...(value?.dependent && {
    //   items: {
    //     type: 'object', properties: (value?.dependent || [])?.reduce((accu: any, item: any) => ({
    //       ...accu,
    //       [item?.field]: { type: ['null', 'string', 'number', 'array', 'object'] }
    //     }), { id: { type: ['string', 'number'] } })
    //   }
    // })
  }

  uiSchema[key] = {
    ...uiSchema[key],
    'ui:options': {
      ...uiSchema[key]?.['ui:options'],

      // dependent: Array.isArray(value?.dependent) ? value?.dependent : [],
      // hasDependent: Boolean(value?.dependent) ?? null,
      
      selectable: typeof value?.selectable === "boolean" ? value?.selectable : true,
      tableSchema: {}
    }
  }
}

const handleFile = (key: string, value: any, schema: Schema, uiSchema: UISchema) => {
  schema[key] = {
    ...schema[key],
    type: value?.required ? 'string' : ['null', 'string'],
    ...(value?.multiple && { type: 'array', items: { type: 'string' } }),
  }

  uiSchema[key] = {
    ...uiSchema[key],
    'ui:options': {
      ...uiSchema[key]?.['ui:options'],
      multiple: !!value?.multiple,
      accept: value?.accept || 'image/*',
      maxSize: value?.maxSize ?? 1000000,
      maxFiles: value?.maxFiles ?? 1,
      minFiles: value?.minFiles ?? 1,
      apiPath: value?.apiPath || '',
      filePreview: value?.preview || true
    }
  }
}

const handleNumberOrInteger = (key: string, value: any, schema: Schema) => {
  schema[key] = {
    ...schema[key],
    maximum: value?.max ?? Number.MAX_SAFE_INTEGER,
    minimum: value?.min ?? 0
  }
}

const handleArray = (key: string, value: any, schema: Schema, uiSchema: UISchema) => {
  uiSchema[key] = {
    ...uiSchema[key],
    'ui:options': {
      ...uiSchema[key]?.['ui:options'],
      addable: value?.addable ?? true,
      orderable: value?.orderable ?? false,
      removable: value?.removable ?? true,
      copyable: value?.copyable ?? false,
      layout: value?.layout ?? "minimal",
    },
  }

  if (!value?.children) {
    //? For string array

    delete uiSchema[key]["ui:field"]
    schema[key] = {
      type: 'array',
      title: value?.title ?? '',
      default: value?.default,
      items: { type: value?.dataType || 'string' },
      minItems: value?.minItems ?? 1,
      uniqueItems: value?.uniqueItems ?? true
    }

    uiSchema[key] = {
      ...uiSchema[key],
      'ui:options': {
        ...uiSchema[key]?.['ui:options'],

        // label: false,
      },
      items: {
        ...uiSchema[key]?.items,
        'ui:options': {
          ...uiSchema[key]?.items?.['ui:options'],
          label: value?.title || ""
        }
      }
    }
  } else {
    const passToChildren = ["endAdornment", "startAdornment"]

    const uiSchemaToBePassed = passToChildren?.reduce((accu, current) => {
      if(uiSchema?.[key]?.["ui:options"]?.[current]){
        accu[current] = uiSchema[key]["ui:options"][current]
      }

      return accu
    }, {})

    const [objectSchema, objectUiSchema] = generateSchema(value?.children || {}, {
      "ui:options": uiSchemaToBePassed
    })

    schema[key] = {
      ...schema[key],
      type: 'array',
      title: value.title,
      default: value?.default ?? [],
      items: objectSchema,
      minItems: value?.minItems ?? 1,

      // uniqueItems: value?.uniqueItems ?? false,
      // properties: objectSchema?.properties,
      // required: [...(value.required || []), ...objectSchema ? objectSchema.required : []],
    }

    uiSchema[key] = {
      ...uiSchema?.[key],

      // ...(value?.useTable && { 'ui:field': 'compositeArray' }),
      // ...objectUiSchema,
      items: {
        ...uiSchema?.[key]?.["items"],
        ...objectUiSchema,
        "ui:options": {
          ...objectUiSchema?.["ui:options"],
          layout: value?.layout ?? "minimal", // Pass parent layout to items
        }
      }
    }
  }
}

const handleGroup = (key: string, value: any, schema: Schema, uiSchema: UISchema) => {
  const [objectSchema, objectUiSchema] = generateSchema(value?.children || {})

  schema[key] = {
    type: 'object',
    title: value.title,
    properties: objectSchema?.properties,
    required: [
      ...(Array.isArray(value.required) ? value.required : []),
      ...(Array.isArray(objectSchema?.required) ? objectSchema.required : [])
    ]
  }

  uiSchema[key] = {
    ...uiSchema[key],
    ...objectUiSchema,
  }
}

const handleDateOrTime = (key: string, value: any, schema: Schema, uiSchema: UISchema) => {
  schema[key] = {
    ...schema[key],
    type: value?.required ? 'string' : ['null', 'string'],
    format: value?.type === 'datetime' ? 'date-time' : value?.type
  }

  const handleFormats = (type: 'date' | 'time' | 'datetime' = 'date') => {
    const format = {
      date: 'YYYY-MM-DD',
      time: 'HH:mm',
      datetime: 'YYYY-MM-DD HH:mm'
    }

    const muiFormat = {
      date: 'DD/MM/YYYY',
      time: 'HH:mm',
      datetime: 'DD/MM/YYYY HH:mm'
    }

    const views = {
      date: ['year', 'month', 'day'],
      time: ['hours', 'minutes'],
      datetime: ['day', 'month', 'year', 'hours', 'minutes']
    }

    return {
      format: value?.format && !(value.format in format)
        ? value.format
        : format[type], // supports custom format
      muiFormat: value?.muiFormat || muiFormat[type],
      views: value?.views || views[type],
      openTo: value?.openTo || (type === 'time' ? 'hours' : 'day'),
      maxDateTime: value?.maxDateTime || '+100 year',
      minDateTime: value?.minDateTime || '-100 year'
    }
  }

  uiSchema[key] = {
    ...uiSchema[key],
    'ui:options': {
      ...uiSchema[key]?.['ui:options'],
      ...handleFormats(value?.type || value?.format)
    }
  }
}

const handleAdornments = (key: string, value: any, schema: Schema, uiSchema: UISchema) => {
  schema[key] = {
    ...schema[key],
    type: value?.required ? 'string' : ['null', 'string'],
    
    // required: !!value?.required || false
  }

  uiSchema[key] = {
    ...uiSchema[key],
    'ui:options': {
      ...uiSchema[key]?.['ui:options'],
      multipleAdornments: Object.keys(schema?.[key]?.properties || {}).length > 1
    }
  }
}

const handleDateRange = (key: string, value: any, schema: Schema, uiSchema: UISchema) => {  
  schema[key] = {
    ...schema[key],
    type: 'array',
    items: {
      type: 'string',
      format: 'date'
    },
    minItems: value?.required ? 2 : 0,
    maxItems: 2,
  }

  uiSchema[key] = {
    ...uiSchema[key],
    'ui:options': {
      ...uiSchema[key]?.['ui:options'],
      minDate: value?.minDate,
      maxDate: value?.maxDate,
      format: value?.format || 'YYYY-MM-DD'
    }
  }
}

export const generateSchema: generateSchemaType = (apiResponse, parentUiSchema = {}) => {
  const schema: Schema = {}
  const uiSchema: UISchema = parentUiSchema
  const dependencies: Dependencies = {}
  const disabled = new Set<string>()
  const required = new Set<string>()
  const rules: rule[] = []

  const generateSchemaHelper: generateSchemaHelperType = (apiResponse, schema, uiSchema) => {
    for (const key in apiResponse) {
      const value = apiResponse[key]
      const common = handleCommonProperties(key, value)

      handleWidgetOptions(key, value, uiSchema)
      handleFormat(value, common)
      handleWidgetMap(key, value, uiSchema)

      schema[key] = common

      if (disabled.has(key)) {
        disabled.delete(key)
      }

      if (value?.required) {
        if (typeof value.required === 'boolean') required.add(key)
        else if (Array.isArray(value.required)) {
          value?.required?.forEach(required.add, required)
        }
      }

      if (!required.has(key) && value?.type !== "null") {
        schema[key] = {
          ...schema[key],
          ...(Array.isArray(schema[key]?.type) ? { type: [...schema[key]?.type, 'null'] } : { type: [schema[key]?.type, 'null'] }),
        }
      }

      const generateOneOfOn = ['radio', 'checkbox', 'select', 'boolean', 'autocomplete', 'dropdown']

      if (generateOneOfOn.includes(value?.type)) {
        handleOneOf(key, value, schema, uiSchema, dependencies)

        if (value?.type === 'checkbox') {
          schema[key] = {
            ...schema[key],
            type: 'array',
            items: {
              type: ['string', 'number'],
              enum: schema[key]?.oneOf?.map((item: any) => item?.const)
            },
            uniqueItems: true
          }
          delete schema[key].oneOf
        }

        if (['autocomplete', 'dropdown'].includes(value?.type)) {
          const safeDependent = value?.dependent ? Array.isArray(value.dependent) ? value.dependent : [value.dependent] : []
          const safeLabelKeys = value?.labelKeys ? Array.isArray(value.labelKeys) ? value.labelKeys : [value.labelKeys] : []

          const properties = [
            ...safeDependent,
            { field: 'id' },
            ...(safeLabelKeys?.map((item: labelItem) =>
              ({ field: typeof item === 'string' ? item : item.field })
            ) || [])
          ].reduce((accu: any, { field }) => ({
            ...accu,
            [field]: { type: field === 'id' ? ['string', 'number'] : ['null', 'string', 'number', 'array', 'object'] },
          }), {});

          const extraType = required.has(key) ? [] : ['null'];

          schema[key] = {
            ...schema[key],
            ...(value?.multiple
              ? {
                type: ['array', ...extraType],
                minItems: 1,
                items: {
                  type: 'object',
                  properties,

                  // required: value?.required ? ['id'] : []
                }
              }
              : {
                type: ['object', ...extraType],
                properties,
                required: value?.required ? ['id'] : []
              }),
          }
        }
      } else if ((!value?.type || value.type === 'group') && Object.hasOwn(value, 'children')) {
        handleGroup(key, value, schema, uiSchema)
      } else if (value.type === 'array') {
        handleArray(key, value, schema, uiSchema)
      } else if (value.type === 'table') {
        handleTable(key, value, schema, uiSchema)
      } else if (value.type === 'file') {
        handleFile(key, value, schema, uiSchema)
      } else if (value.type === 'number' || value.type === 'integer') {
        handleNumberOrInteger(key, value, schema)
      } else if (['date', 'time', 'datetime'].includes(value.format || value.type)) {
        handleDateOrTime(key, value, schema, uiSchema)
      } else if(value.type === 'daterange') {
        handleDateRange(key, value, schema, uiSchema)
      } else if (value?.startAdornment || value?.endAdornment) {
        handleAdornments(key, value, schema, uiSchema)
      }

      if (value?.disabled || disabled.has(key)) {
        uiSchema[key] = {
          ...uiSchema[key],
          'ui:disabled': true
        }
      }
    }
  }

  generateSchemaHelper(apiResponse, schema, uiSchema)

  return [{ type: 'object', properties: schema, dependencies, required: [...required] }, uiSchema, rules]
}

export default generateSchema
