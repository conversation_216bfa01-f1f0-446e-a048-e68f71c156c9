import { getFormattedSchema } from "@/app/api/schema/route"
import BuilderClient from "./client"

// import { getRelavantPageData } from "@/utils/menu"

type BuilderProps = {
  href: string
  isFormScreen?: boolean
  params?: Record<string, any>
  showInModal?: boolean
  screenFrom?: "form" | "mix" | "popup"
}

const Builder = async ({ href = "", params, screenFrom = "form" }: BuilderProps) => {
  const response = await getFormattedSchema(href?.split("/")?.filter(Boolean), params)

  return <BuilderClient response={response || {}} api={response?.meta?.data_source ?? false} params={params} screenFrom={screenFrom} />
}

export default Builder
