"use client";

import { useState } from "react";

import dynamic from "next/dynamic";

import { usePathname } from "next/navigation";

import axios from "axios";
import { Skeleton } from "@mui/material";

import { useEffectOnce } from "react-use";

import updateSchemaDefaults from "@/utils/updateSchemaDefaults";
import { replacePlaceholders } from "@/utils/replacePlaceholders";
import { stripAdminScopedPath } from "@/utils/string";

// import Form from "@/views/form/components/form";

// Dynamic import of the Form component
const Form = dynamic(() => import("@/views/form/components/form"), {
  ssr: false,
  loading: () => <Skeleton variant="rectangular" width="100%" height={500} />,
});

type FormProps = {
  response: any;
  api: string | boolean;
  params: any;
  screenFrom: "form" | "mix" | "popup";
};

const BuilderClient = ({ response, api, params = {}, screenFrom = "form" }: FormProps) => {
  const [loading, setLoading] = useState(!!api);
  const pathname = usePathname();
  const asPath = stripAdminScopedPath(pathname);

  // console.log("BuilderClient", response, api, params);

  useEffectOnce(() => {
    if (api) {
      if (typeof api === "string") {
        const fetchData = async () => {
          try {
            const path = replacePlaceholders(typeof api === "string" ? "/admin/" + api : asPath, params);

            const apiResponse = await axios.get(path, {
              withCredentials: true,
              baseURL: process.env.NEXT_PUBLIC_API_URL,
              validateStatus: () => true
            });

            updateSchemaDefaults(response?.schema?.flow, { ...params, ...apiResponse?.data?.data });
          } catch (error) {
            console.warn("Error fetching API data:", error);
          } finally {
            setLoading(false);
          }
        };

        fetchData();
      } else {
        updateSchemaDefaults(response?.schema?.flow, params);
        setLoading(false);
      }
    }
  });

  if (loading) {
    return <Skeleton variant="rectangular" width="100%" height={500} />;
  }

  return <Form response={response} screenFrom={screenFrom} />;
};

export default BuilderClient;
