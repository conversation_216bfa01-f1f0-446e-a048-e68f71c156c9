import type { ButtonPropsColorOverrides, ButtonPropsVariantOverrides } from '@mui/material/Button';
import type { OverridableStringUnion } from '@mui/types';

export type ActionType =
  | "submit"
  | "submit_and_new"
  | "reset"
  | "cancel"
  | "download"
  | "view";

export type Action = {
  title: string;
  type: ActionType;
  api_path?: string;
  mode ?: "society" | "gate";
  method?: "GET" | "POST" | "PUT" | "DELETE";
  icon?: string;
  redirect?: string;
  refetch?: boolean;
  default_params?: Array<string | Record<string, any>>;
  color?: OverridableStringUnion<
    'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning',
    ButtonPropsColorOverrides
  >;
  variant?: OverridableStringUnion<
    'text' | 'outlined' | 'contained',
    ButtonPropsVariantOverrides
  >;
};
