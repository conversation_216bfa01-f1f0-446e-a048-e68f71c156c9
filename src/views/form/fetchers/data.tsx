import axios from "axios"

async function getAlbums(data_source: string, id: string) {
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/${data_source}/${id}`)

  
return res.json()
}

export const preload = async (data_source: string, id: string) => {
  const data = await getAlbums(data_source, id)

  
return data
}

const DataFetcher = async ({ data_source = "", id = 0 }) => {

  const response = await axios.get(
    `${process.env.NEXT_PUBLIC_API_URL}/admin/${data_source}/${id}`
    , {
      validateStatus: () => true,
      withCredentials: true,
    }
  )

  if (response.status === 200 && Object.keys(response?.data?.data || {}).length) {
    return { form_type: "put", ...response.data.data, paramId: id, id }
  } else {
    return { form_type: "post", paramId: id, id }
  }
}

export default DataFetcher
