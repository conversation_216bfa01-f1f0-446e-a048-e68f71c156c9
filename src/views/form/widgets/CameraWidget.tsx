import { useState, useRef, useEffect } from "react";

import { Button, Typography, Dialog, Paper, IconButton } from "@mui/material";
import RefreshIcon from "@mui/icons-material/Refresh";
import DeleteIcon from "@mui/icons-material/Delete";
import axios from "axios";

const CameraWidget = ({ value, onChange, label }: any) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [mediaStream, setMediaStream] = useState<MediaStream | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [open, setOpen] = useState(false);
  const [facingMode, setFacingMode] = useState<"user" | "environment">("user");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open) {
      startWebcam(facingMode);
    } else {
      stopWebcam();
    }

    return () => stopWebcam(); // Ensure webcam stops on unmount
  }, [open]);

  // Sync `capturedImage` with `value` prop
  useEffect(() => {
    setCapturedImage(value || null);
  }, [value]);

  const startWebcam = async (mode: "user" | "environment" = "user") => {
    stopWebcam(); // Stop any existing media stream before starting a new one

    try {
      setError(null);

      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: mode },
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        await videoRef.current.play();
      }

      setMediaStream(stream);
      setFacingMode(mode);
    } catch (err) {
      console.warn("Error accessing webcam:", err);
      setError("Unable to access camera. Please check permissions or device settings.");
    }
  };

  const stopWebcam = () => {
    if (mediaStream) {
      mediaStream.getTracks().forEach((track) => track.stop());
      setMediaStream(null);
    }
  };

  const uploadImage = async (file: File): Promise<string | null> => {
    try {
      const formData = new FormData();

      formData.append("files[]", file);

      const response = await axios.post(
        `/admin/file-upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
          withCredentials: true,
          baseURL: process.env.NEXT_PUBLIC_API_URL,
        }
      );

      const uploadedFileData = response.data.data[0]; // Assuming server returns S3 URL in data array

      
return uploadedFileData || null;
    } catch (error) {
      console.warn("Error uploading image:", error);
      
return null;
    }
  };

  const captureImage = async () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext("2d");

      if (context && video.videoWidth && video.videoHeight) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        canvas.toBlob(async (blob) => {
          if (blob) {
            const file = new File([blob], "captured-image.jpg", { type: "image/jpeg" });

            const uploadedImageUrl = await uploadImage(file);

            if (uploadedImageUrl) {
              setCapturedImage(uploadedImageUrl); // Update local state for preview

              if (onChange) {
                onChange(uploadedImageUrl); // Notify parent component
              }
            }

            setOpen(false); // Close the dialog
            stopWebcam(); // Stop the webcam
          }
        }, "image/jpeg");
      }
    }
  };

  const handleDelete = () => {
    setCapturedImage(null);

    if (onChange) {
      onChange(null); // Notify parent component about the reset
    }
  };

  return (
    <div style={{ textAlign: "left", margin: "20px 0" }}>
      <Typography variant="subtitle1" gutterBottom>
        {label || "Capture Image"}
      </Typography>
      <Paper
        variant="outlined"
        sx={{
          padding: "16px",
          border: "2px dashed #ccc",
          textAlign: "center",
          cursor: capturedImage ? "default" : "pointer",
          backgroundColor: "#fafafa",
          "&:hover": {
            borderColor: capturedImage ? "#ccc" : "#000",
          },
        }}
        onClick={() => !capturedImage && setOpen(true)}
      >
        {!capturedImage ? (
          <Typography variant="body1" color="textSecondary">
            Click to open camera and capture an image
          </Typography>
        ) : (
          <div>
            <img
              src={capturedImage}
              alt="Captured"
              style={{
                width: "100px",
                height: "100px",
                objectFit: "cover",
                borderRadius: 8,
                marginBottom: 8,
              }}
            />
            <Typography variant="body2" color="textSecondary">
              Captured Image
            </Typography>
            <div style={{ marginTop: 8, display: "flex", justifyContent: "center", gap: 8 }}>
              <IconButton color="primary" onClick={() => setOpen(true)}>
                <RefreshIcon />
              </IconButton>
              <IconButton color="error" onClick={handleDelete}>
                <DeleteIcon />
              </IconButton>
            </div>
          </div>
        )}
      </Paper>

      <Dialog open={open} onClose={() => setOpen(false)} fullWidth maxWidth="sm">
        <div style={{ padding: 20 }}>
          <Typography variant="h6">Camera</Typography>
          <Paper
            variant="outlined"
            sx={{
              position: "relative",
              width: "100%",
              height: 300,
              margin: "20px 0",
              border: "2px dashed #ccc",
              borderRadius: 8,
              overflow: "hidden",
              backgroundColor: "#fafafa",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {error ? (
              <Typography color="error">{error}</Typography>
            ) : (
              <video
                ref={videoRef}
                style={{ width: "100%", height: "100%", objectFit: "cover" }}
                autoPlay
                playsInline
                muted
              />
            )}
          </Paper>
          <div style={{ display: "flex", justifyContent: "center", gap: 10 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={() => startWebcam(facingMode === "user" ? "environment" : "user")}
              disabled={!mediaStream}
            >
              Switch Camera
            </Button>
            <Button variant="contained" color="primary" onClick={captureImage}>
              Capture
            </Button>
            <Button variant="contained" color="success" onClick={() => setOpen(false)}>
              Close
            </Button>
          </div>
          <canvas ref={canvasRef} style={{ display: "none" }} />
        </div>
      </Dialog>
    </div>
  );
};

export default CameraWidget;
