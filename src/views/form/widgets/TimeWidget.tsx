import { useEffect, useMemo, useState } from "react";

import type { Dayjs } from "dayjs";
import dayjs from "dayjs";
import type { RJSFSchema, WidgetProps } from "@rjsf/utils";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { renderTimeViewClock } from "@mui/x-date-pickers/timeViewRenderers";
import type { DateOrTimeView } from "@mui/x-date-pickers";

import { parseDateString } from "@/utils/date";
import { useIndexedDB } from "@/hooks/useIndexedDB";
import { tableNames } from "@/data/db";

interface CustomTimeFieldProps extends WidgetProps<any, RJSFSchema, any> {
  id: string;
  label: string;
  onChange: (value: string | undefined) => void;
  onBlur: (id: string, value: boolean) => void;
  disabled?: boolean;
  uiSchema?: {
    "ui:options"?: {
      maxDateTime?: string;
      minDateTime?: string;
      views?: DateOrTimeView[];
      format?: string;
      muiFormat?: string;
      openTo?: DateOrTimeView;
      defaultDateTime?: string;
    };
  };
}

// Escapes special regex characters in a string
function escapeRegex(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

function replaceFinancialPlaceholders(
  input: string,
  financialData: Record<string, string>
): string {
  // 1. Gather only those keys that have non-empty values.
  const keys = Object.keys(financialData).filter(
    (key) => financialData[key]
  );

  // 2. If no replacements are needed, return the original input immediately.
  if (!keys.length || !input) return input;

  // 3. Build a single RegExp to match any of the placeholder keys.
  //    We escape each key to handle special regex characters (e.g., "." or "+").
  const pattern = new RegExp(
    keys.map((key) => escapeRegex(key)).join("|"),
    "g"
  );

  // 4. Replace each match with the corresponding value from financialData.
  return input.replace(pattern, (matched) => financialData[matched] || matched);
}

const TimeWidget = ({
  label,
  onChange,
  value = "",
  disabled = false,
  uiSchema,
  rawErrors,
}: CustomTimeFieldProps) => {
  const [open, setOpen] = useState(false); // Manage the open state
  const tables = useMemo(() => [tableNames.financialYears], []);
  const { getAllValue, isDBConnecting } = useIndexedDB("soc-db", tables);

  const [financialData, setFinancialData] = useState({
    financialYearStart: "",
    financialYearEnd: "",
    currentFinancialStart: "",
    currentFinancialEnd: "",
  });

  useEffect(() => {
    if (isDBConnecting) return;

    const fetchFinancialYears = async () => {
      const financialYears = await getAllValue(tableNames.financialYears);
      const currentYear = financialYears.find((year) => !year.closed);

      setFinancialData({
        financialYearStart: financialYears[0]?.fy_start_date || "",
        financialYearEnd:
          financialYears[financialYears.length - 1]?.fy_end_date || "",
        currentFinancialStart: currentYear?.fy_start_date || "",
        currentFinancialEnd: currentYear?.fy_end_date || "",
      });
    };

    fetchFinancialYears();
  }, [getAllValue, isDBConnecting]);

  useEffect(() => {
    if (uiSchema?.["ui:options"]?.defaultDateTime)
      onChange(uiSchema?.["ui:options"]?.defaultDateTime);
  }, [uiSchema?.["ui:options"]?.defaultDateTime]);

  const setMaxDate = (): Dayjs => {
    let max: string | undefined = uiSchema?.["ui:options"]?.maxDateTime;

    if (max && typeof max === "string") {
      max = replaceFinancialPlaceholders(max, financialData);
    }

    
return parseDateString(max || "+1 years", true);
  };

  const setMinDate = (): Dayjs => {
    let min: string | undefined = uiSchema?.["ui:options"]?.minDateTime;

    if (min && typeof min === "string") {
      min = replaceFinancialPlaceholders(min, financialData);
    }

    
return parseDateString(min || "-1 years", false);
  };

  const handleTimeChange = (date: Dayjs | null) => {
    const format = uiSchema?.["ui:options"]?.format || "HH:mm:ss";

    if (date) {
      onChange(dayjs(date).format(format));
    } else {
      onChange(undefined);
    }

    setOpen(false); // Close picker after selection
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DateTimePicker
        label={label}
        loading={isDBConnecting}
        viewRenderers={{
          hours: renderTimeViewClock,
          minutes: renderTimeViewClock,
          seconds: renderTimeViewClock,
        }}
        disabled={disabled}
        onChange={handleTimeChange}
        format={uiSchema?.["ui:options"]?.muiFormat}
        value={value ? dayjs(value) : null}
        views={uiSchema?.["ui:options"]?.views || ["hours", "minutes", "seconds"]}
        minDateTime={setMinDate()}
        maxDateTime={setMaxDate()}
        open={open} // Controlled open state
        onOpen={() => setOpen(true)} // Open picker
        onClose={() => setOpen(false)} // Close picker
        slotProps={{
          textField: {
            onClick: () => setOpen(true), // Open picker on input click
            error: !!rawErrors?.length,
          },
        }}
      />
    </LocalizationProvider>
  );
};

export default TimeWidget;
