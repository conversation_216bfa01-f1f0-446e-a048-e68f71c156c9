import { useEffect, useState } from "react";

import type { WidgetProps } from "@rjsf/utils";

import RenderTable from "@/views/table/components/RenderTable";
import { isEmptyObject } from "@/utils/object";

interface TableWidgetProps extends WidgetProps {
  options: {
    apiPath: string;
    changeOn: string; // Example: "vendor_id.id" or "vendor_name"
    onMount?: boolean;
    factKey?: string;
    dependent?: { field: string, key?: string }[]
    selectable?: boolean;
  };
}

const TableWidget = (props: TableWidgetProps) => {
  const { options, formContext } = props;

  const { engine } = formContext; // Access the engine instance

  const [result, setResult] = useState(null);

  useEffect(() => {
    const fetchSequenceValue = async () => {
      if (!options?.factKey) return null;

      engine.on('success', async (_, almanac) => {
        try {
          const value = await almanac.factValue(options.factKey);

          if (!value || isEmptyObject(value) || value?.status_code >= 400) {
            console.warn("No fact value found for key:", options.factKey);
            setResult(null);
          } else {
            if (typeof value === "object") {
              const isDifferent = JSON.stringify(value) !== JSON.stringify(result?.data);

              if (!isDifferent) return;
              console.log("Fact value changed:", value, result);
              setResult({ data: value, status: value?.status_code || 200 });
            }
          }
        } catch (error) {
          console.error("Error fetching fact value:", error);
          
          // setResult(error);
        }
      });
    }

    // Call the async function and update state when resolved.
    fetchSequenceValue()

    // Optionally, you could add a cleanup function to remove the listener if needed.

  }, [result, options.factKey]);

  if (!result) {
    return <></>;
  }

  return <RenderTable factResult={result} />

  // const handleChange = (value: any[]) => {
  //   if (!!options?.dependent) {
  //     const matching = schema?.data
  //       ?.filter((row) => value.includes(row.id))
  //       ?.map((row) =>
  //         options?.dependent?.reduce(
  //           (acc, dep) => {
  //             const fieldName = dep.field;
  //             const fieldKey = dep.key || dep.field;

  //             acc[fieldName] = row[fieldKey];

  //             return acc;
  //           },
  //           { id: row?.id } // Initialize with the id directly
  //         )
  //       ) || [];

  //     props.onChange(matching);
  //   } else {
  //     props.onChange(value);
  //   }
  // };

  // return <RJSFTable schema={schema} handleChange={handleChange} selectable={!!options?.selectable} />
};

export default TableWidget;
