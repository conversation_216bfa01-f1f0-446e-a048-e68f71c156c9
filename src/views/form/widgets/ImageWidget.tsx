import { useState, useEffect, useRef } from "react";
import type { ChangeE<PERSON>, DragEvent, ClipboardEvent } from "react";

import axios from "axios";
import IconButton from "@mui/material/IconButton";
import DeleteIcon from "@mui/icons-material/Delete";
import {
  Typography,
  List,
  ListItem,
  ListItemText,
  Paper,
  ListItemAvatar,
  Avatar,
  LinearProgress,
  Box,
  CircularProgress,
  Backdrop,
} from "@mui/material";
import UploadIcon from "@mui/icons-material/UploadFile";
import { alpha } from "@mui/material/styles";

import { truncateMiddle } from "@/utils/string";

interface FileWidgetOptions {
  multiple?: boolean;
  accept?: string;
}

interface FileWidgetProps {
  id: string;
  readonly: boolean;
  disabled: boolean;
  value: string | string[] | null;
  onChange: (value: string | string[]) => void;
  options?: FileWidgetOptions;
}

const FileWidget: React.FC<FileWidgetProps> = ({ id, readonly, disabled, value, onChange, options }) => {
  const [files, setFiles] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const dropRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (value) {
      const initialFiles = Array.isArray(value) ? value : [value];

      setFiles(initialFiles.filter((file): file is string => typeof file === "string"));
    } else {
      setFiles([]);
    }
  }, [value]);

  const handleFiles = async (selectedFiles: FileList | DataTransferItemList | null): Promise<void> => {
    if (!selectedFiles) return;

    const uploadedFilePaths: string[] = [];

    const uploadFile = async (file: File): Promise<void> => {
      const formData = new FormData();

      formData.append("files[]", file);

      try {
        setIsUploading(true);

        const upload = await axios.post(`/admin/file-upload`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
          withCredentials: true,
          baseURL: process.env.NEXT_PUBLIC_API_URL,
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / (progressEvent.total || 1)
            );

            setUploadProgress(percentCompleted);
          },
        });

        const uploadedFileData: string = upload.data.data[0];

        if (uploadedFileData) {
          uploadedFilePaths.push(uploadedFileData);
        }
      } catch (error) {
        console.warn("File upload error:", error);
      } finally {
        setUploadProgress(null);
        setIsUploading(false);
      }
    };

    if (selectedFiles instanceof FileList) {
      for (const file of Array.from(selectedFiles)) {
        await uploadFile(file);
      }
    } else if (selectedFiles instanceof DataTransferItemList) {
      for (const item of Array.from(selectedFiles)) {
        const file = item.getAsFile();

        if (file) {
          await uploadFile(file);
        }
      }
    }

    const updatedFiles: string[] = options?.multiple
      ? [...files, ...uploadedFilePaths]
      : [uploadedFilePaths[0]];

    setFiles(updatedFiles);
    onChange(options?.multiple ? updatedFiles : updatedFiles[0] || "");
  };

  const onFileChange = (event: ChangeEvent<HTMLInputElement>): void => {
    handleFiles(event.target.files);
  };

  const onDrop = (event: DragEvent<HTMLDivElement>): void => {
    event.preventDefault();
    handleFiles(event.dataTransfer.files);
  };

  const onPaste = (event: ClipboardEvent<HTMLDivElement>): void => {
    const clipboardItems = event.clipboardData.items;

    handleFiles(clipboardItems);
  };

  const onRemoveFile = (index: number): void => {
    const updatedFiles = [...files];
    
    updatedFiles.splice(index, 1);
    setFiles(updatedFiles);
    onChange(options?.multiple ? updatedFiles : updatedFiles[0] || "");
  };

  return (
    <div className="form-group">
      <Paper
        ref={dropRef}
        variant="outlined"
        sx={(theme) => ({
          position: "relative",
          padding: theme.spacing(2),
          border: `2px dashed ${
            readonly || disabled
              ? theme.palette.action.disabled
              : theme.palette.mode === "dark"
              ? theme.palette.grey[700]
              : theme.palette.grey[400]
          }`,
          textAlign: "center",
          cursor: readonly || disabled ? "not-allowed" : "pointer",
          backgroundColor:
            readonly || disabled
              ? theme.palette.action.disabledBackground
              : theme.palette.background.paper,
          "&:hover": {
            borderColor:
              readonly || disabled
                ? theme.palette.action.disabled
                : theme.palette.primary.main,
          },
          overflow: "hidden",
        })}
        onClick={() =>
          !readonly && !disabled && document.getElementById(id)?.click()
        }
        onDrop={onDrop}
        onDragOver={(event) => event.preventDefault()}
        onPaste={onPaste}
      >
        <input
          type="file"
          id={id}
          style={{ display: "none" }}
          disabled={readonly || disabled || isUploading}
          onChange={onFileChange}
          multiple={!!options?.multiple}
          accept={typeof options?.accept === "string" ? options.accept : undefined}
        />
        <UploadIcon fontSize="large" color="action" />
        <Typography variant="body1" color="textSecondary">
          Drag and drop files here, click to upload, or paste from clipboard
        </Typography>
        <Typography variant="caption" color="textSecondary">
          {options?.multiple
            ? "You can upload multiple files"
            : "Only one file can be uploaded"}
        </Typography>

        {/* Progress Bar */}
        {uploadProgress !== null && (
          <Box
            sx={(theme) => ({
              position: "absolute",
              bottom: 0,
              left: 0,
              width: "100%",
              zIndex: theme.zIndex.tooltip,
            })}
          >
            <LinearProgress variant="determinate" value={uploadProgress} />
          </Box>
        )}

        {/* Spinner Overlay */}
        {isUploading && (
          <Backdrop
            open={isUploading}
            sx={(theme) => ({
              position: "absolute",
              top: 0,
              left: 0,
              zIndex: theme.zIndex.modal,
              backgroundColor: alpha(
                theme.palette.mode === "dark"
                  ? theme.palette.common.black
                  : theme.palette.common.white,
                0.8
              ),
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              borderRadius: "inherit",
              height: "100%",
              width: "100%",
            })}
          >
            <CircularProgress />
          </Backdrop>
        )}
      </Paper>

      {files.length > 0 && (
        <List>
          {files.map((file: string, index: number) => (
            <ListItem
              key={index}
              sx={(theme) => ({
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: theme.shape.borderRadius * 2,
                marginBottom: theme.spacing(1),
                padding: theme.spacing(1, 2),
                backgroundColor: theme.palette.background.paper,
                display: "flex",
                alignItems: "center",
                gap: theme.spacing(2),
              })}
              secondaryAction={
                <IconButton
                  edge="end"
                  aria-label="delete"
                  onClick={() => onRemoveFile(index)}
                >
                  <DeleteIcon />
                </IconButton>
              }
            >
              <ListItemAvatar>
                <Avatar alt={file} src={file} />
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography
                    component="a"
                    href={file}
                    target="_blank"
                    rel="noopener noreferrer"
                    sx={{
                      display: "inline-block",
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      maxWidth: "100%",
                    }}
                  >
                    {truncateMiddle(file, { startLength: 15, endLength: 15 })}
                  </Typography>
                }
                sx={(theme) => ({
                  marginLeft: theme.spacing(2),
                })}
              />
            </ListItem>
          ))}
        </List>
      )}
    </div>
  );
};

export default FileWidget;
