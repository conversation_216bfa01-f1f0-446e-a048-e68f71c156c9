import React, { useMemo } from "react";

import type { WidgetProps } from "@rjsf/utils";

import RenderTable from "@/views/table/components/RenderTable";

interface TableWidgetProps extends WidgetProps {
  options: {
    tableSchema: {
      pagination?: { total: number };
      schema: {
        table: {
          columns: Array<{ key: string; title: string; width?: number }>;
          [key: string]: any;
        };
      };
    };
    selectable?: boolean;
    dependent?: { field: string; key?: string }[];
  };
}

const TableWidget = ({ value, onChange, options }: TableWidgetProps) => {
  // console.log(value, "options");
  
  // Memoize rows (current form value)
  const rows = useMemo(() => (Array.isArray(value) ? value : []), [value]);

  // Memoize the full table schema (all table-related settings)
  const schema:any = useMemo(
    () => options?.tableSchema ?? {},
    [options?.tableSchema]
  );

  // Handle selection / dependent mapping
  const onSelectionChange = (selectedIds: any[]) => {
    const updated = rows.map((row) => {
      const selected = selectedIds.includes(String(row.id));
    
      return {
        ...row,
        selected,
      };
    });

    onChange(updated);
  };

  return (
    <RenderTable
      rows={rows}
      schema={schema}
      {...(!!options.selectable && { onSelectionChange, selectedFromForm: value })}
    />
  );
};

export default TableWidget;
