import React, { useState } from "react"

import { SimpleTreeView } from "@mui/x-tree-view/SimpleTreeView"
import { TreeItem2 } from "@mui/x-tree-view/TreeItem2"

interface LedgerAccount {
  id: string
  label: string
  children?: LedgerAccount[]
}

const TreeSelect: React.FC = () => {
  const [options] = useState<LedgerAccount[]>([])
  const [expanded, setExpanded] = useState<string[]>([])
  const [selectedOption, setSelectedOption] = useState<LedgerAccount[]>([])

  const handleExpandClick = (event: React.MouseEvent, nodeId: string) => {
    setExpanded(prev => (prev.includes(nodeId) ? prev.filter(id => id !== nodeId) : [...prev, nodeId]))
  }

  const handleOptionClick = (event: React.MouseEvent, option: LedgerAccount) => {
    event.stopPropagation()
    event.preventDefault()

    if (option?.children) {
      handleExpandClick(event, option.id)
    } else {
      // Handle selection logic here
      setSelectedOption([option])
    }
  }

  const renderTree = (nodes: LedgerAccount) => (
    <TreeItem2
      key={nodes.id}
      itemId={nodes.id}
      onClick={event => handleOptionClick(event, nodes)}
      label={nodes.label}
      sx={{ width: "100%" }}
    >
      {nodes?.children ? nodes.children.map(node => renderTree(node)) : null}
    </TreeItem2>
  )

  return (
    <SimpleTreeView
      expandedItems={expanded}
      selectedItems={selectedOption.map(item => item.id)}
      expansionTrigger="iconContainer"
      checkboxSelection={false}
      sx={{
        width: "100%",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
        borderBottom: "1px solid #ddd",
        padding: "5px 0"
      }}
    >
      {options.map(option => renderTree(option))}
    </SimpleTreeView>
  )
}

export default TreeSelect
