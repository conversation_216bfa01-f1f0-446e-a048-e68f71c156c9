"@ts-nocheck";

"use client";

import { useEffect, useState } from "react";

import { MaterialReactTable } from "material-react-table";

import { GenerateColumnsForMRT } from "../../table/components/GenerateColumns";
import RenderRowActions from "@/views/table/actions/row/RowActions";

// import { initialState, reducer } from "./tableSchemaState";

const Table = ({
  schema,
  handleChange,
  selectable = false
}) => {
  const [rowSelection, setRowSelection] = useState({});
  const [rows, setRows] = useState(() => schema?.data || []);
  const [columns, setColumns] = useState([]);
  const [loading, setLoading] = useState(true);

  // console.log(schema)

  useEffect(() => {
    async function fetchData() {
      const rows = schema?.data

      setRows(rows || []);

      // console.log(rows, value, value?.reduce((acc, id) => ({ ...acc, [id?.id ?? id]: true }), {}) || {})
      // setRowSelection(value?.reduce((acc, id) => ({ ...acc, [id?.id ?? id]: true }), {}) || {});
      if(selectable) setRowSelection(rows?.reduce((acc, row) => ({ ...acc, [row.id]: true }), {}) || {});
      const table = schema?.meta?.schema?.table || {}

      if (!table?.columns) return setLoading(false);
      const columns = GenerateColumnsForMRT(table, () => { })

      setColumns(columns);
      setLoading(false);
    }

    fetchData();
  }, [schema?.data]);

  useEffect(() => {
    if (typeof rows[0]?.id === 'string') {
      handleChange(Object.keys(rowSelection));
    } else if (typeof rows[0]?.id === 'number') {
      handleChange(Object.keys(rowSelection).map((id) => parseInt(id)));
    }
  }, [rowSelection, rows, handleChange]);

  return (
    <MaterialReactTable

      // ********* Data and Columns *********
      data={rows}
      columns={columns || []}
      getRowId={(row) => row.id}
      enableRowActions={!schema?.rowActions?.length}

      renderRowActions={({ row, table }) => (
              <RenderRowActions
                row={row?.original}
                actions={schema?.meta?.schema?.table?.columns?.filter((col: any) => col?.actions)?.map((col: any) => col?.actions)?.flat() || []}
                table={table}
              />
            )}

      // ********* Sorting, Filtering, and Pagination *********
      enableSorting={false}
      enablePagination={false}
      state={{
        isLoading: loading,
        rowSelection,
        columnOrder: ['mrt-row-numbers', 'mrt-row-select', 'mrt-row-expand', 'mrt-row-pin', ...(schema?.columns?.map((col) => col?.id || col?.accessorKey) || []), 'mrt-row-actions']
      }}
      initialState={{
        density: "compact",
        columnPinning: {
          left: ['mrt-row-select']
        },
      }}
      rowCount={rows?.length}

      // ********* Selection *********
      {...(selectable && {
        enableRowSelection: selectable,
        onRowSelectionChange: setRowSelection,
      })}

      // ********* Grouping and Sub Rows *********
      // enableGrouping
      getSubRows={(row) => row?.rows || row?.children || []}
      enableExpanding={rows.some((row) => row?.rows || row?.children)}

      // ********* UI Customization *********
      muiTablePaperProps={{
        elevation: 1,
        style: {
          width: '100%',
          overflowX: 'hidden',
          maxWidth: '100%',
          overflow: 'hidden',
        }
      }}
      muiTableBodyRowProps={() => ({
        // onClick: () => {
        //   console.log(row);
        //   // setRowSelection((prev) => ({
        //   //   ...prev,
        //   //   [row.id]: !prev[row.id], // Toggle row selection
        //   // }));
        // },
        // selected: !!row?.selected,
        // sx: { cursor: 'pointer' },
        sx: () => ({
          cursor: 'pointer',
        }),
      })}

      // muiTablePaperProps={{ style: { width: width || '100%', overflowX: 'auto' }, elevation: 1 }}
      mrtTheme={(theme) => ({
        draggingBorderColor: theme.palette.secondary.main,
      })}
      enableStickyHeader

    // enableColumnPinning
    // layoutMode="grid"
    />
  );
};

export default Table;