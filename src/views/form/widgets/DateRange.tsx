// RJSFDateRangeWidget.tsx
import React, { useCallback } from 'react';

import type { WidgetProps } from '@rjsf/utils';

import EnhancedRangePicker from '@/views/table/CustomFilters/DateRangePicker';

interface DateRangeWidgetProps extends WidgetProps {
    options: {
        minDate?: string;
        maxDate?: string;
    };
}

export default function DateRangeWidget({
  value,
  label,
  required,
  disabled,
  readonly,
  onChange,
  rawErrors,
  options,
}: DateRangeWidgetProps) {
  // ensure we have a [start, end] tuple
  const [start, end] = Array.isArray(value)
    ? [value[0] as string | null || null, value[1] as string | null || null]
    : [null, null];

  const handleChange = useCallback(
    (newRange: [string | null, string | null]) => {
      // RJSF array field: directly call onChange with the tuple
      onChange(newRange);
    },
    [onChange]
  );

  return (
    <EnhancedRangePicker
      value={[start, end]}
      onChange={handleChange}
      label={label + (required ? ' *' : '')}
      disabled={disabled || readonly}
      minDate={options?.minDate}
      maxDate={options?.maxDate}
      size='medium'
      error={!!rawErrors?.length}
    />
  );
}
