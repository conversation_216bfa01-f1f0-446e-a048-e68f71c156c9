import { updateNestedValue } from "@/utils/object";

type Schema = Record<string, any>;
type Params = Record<string, any>;

/**
 * Replaces (merges in) the uiSchema properties with the provided configuration.
 *
 * @param uiParams - The configuration object with properties to replace.
 * @param uiSchema - The UI schema object to modify.
 */
const uiSchemaReplace=(uiParams: Params,_schema,  uiSchema: Schema): void => {
  console.log("uiSchemaReplace", uiParams, uiSchema);
  
  Object.keys(uiParams).forEach((path) => {
    const value = uiParams[path];

    updateNestedValue(uiSchema, path.split("."), value);
  });
}

export default uiSchemaReplace;
