import { updateNestedValue } from "@/utils/object";

export type Params = {
  fields: string[];
};

export type Schema = Record<string, any>;
export type UISchema = Record<string, any>;

const nestedRemove = (params: Params, schema: Schema, uiSchema: UISchema): void => {
  if (!params) return;

  params.fields.forEach((field: string) => {
    // Replace .*. with .items.properties. for schema and split into path segments
    const schemaPath = field.replace('.*.', '.items.properties.')?.split('.');

    updateNestedValue(schema, ["properties", ...schemaPath], undefined);

    // Replace .*. with .items. for uiSchema and split into path segments
    const uiSchemaPath = field.replace('.*.', '.items.')?.split('.');

    updateNestedValue(uiSchema, uiSchemaPath, undefined);
  });
};

export default nestedRemove;
