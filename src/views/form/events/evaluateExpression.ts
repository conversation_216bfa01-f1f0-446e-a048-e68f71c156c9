// calculateFormData.ts

import { evaluateExpression } from "@/utils/evaluate";
import { findFieldPathInUISchema } from "@/utils/string";
import { updateNestedValue } from "@/utils/object";

export type Params = Record<string, any>;
export type Schema = Record<string, any>;
export type UISchema = Record<string, any>;
export type FormData = Record<string, any>;

/**
 * Calculates a value based on the provided parameters and updates the formData accordingly.
 *
 * @param params - The parameters object which must include `__field` and `__exp`.
 * @param _schema - The schema (unused in this function but kept for signature compatibility).
 * @param uiSchema - The UI schema object.
 * @param formData - The formData object to update.
 */
const calculateExpression = (
  params: Params,
  _schema: Schema,
  uiSchema: UISchema,
  formData: FormData
): void => {
  const field = params?.__field;

  if (!params || !field || !params.__exp) return;

  const cleanExpression = params.__exp;
  const result = evaluateExpression(cleanExpression, { ...formData, ...params });
  const path = findFieldPathInUISchema(field, uiSchema);

  // console.log(params, result)
  
  // If no valid path is found and the result is an array for fields matching a specific pattern,
  // set the nested value directly using the field path split by "."
  if (!path && Array.isArray(result) && field.includes(".*.")) {
    updateNestedValue(formData, field.split("."), result);

    return;
  }

  // Attempt to convert the result to a number
  const toNumber = Number(result);
  const pathParts = path.split(".");

  // Check if conversion results in NaN or if the original result is null
  if (isNaN(toNumber) || result === null) {
    updateNestedValue(formData, pathParts, result);
  } else {
    // If the number is not an integer, round to two decimals
    if (!Number.isInteger(toNumber)) {
      updateNestedValue(formData, pathParts, parseFloat(toNumber.toFixed(2)));
    } else {
      updateNestedValue(formData, pathParts, toNumber);
    }
  }
}

export default calculateExpression