import { updateNestedValue } from "@/utils/object";

export type Params = Record<string, any>;
export type Schema = Record<string, any>;
export type UISchema = Record<string, any>;
export type FormData = Record<string, any>;

const descriptionAsTooltip = (
  params: Params,
  _schema: Schema,
  uiSchema: UISchema,
  formData: FormData
): void => {
  try {
    if (!params || !params.__title || !params.__field) {
      console.warn("Missing required parameters for descriptionAsTooltip.");

      return;
    }

    // Compute the title without mutating params.__title.
    // Replace placeholders of the form {{ key }} using a regex callback.
    // Here, we prefer params first, then formData; if neither has the key, we default to 0.
    const computedTitle = params.__title.replace(/{{(.*?)}}/g, (_match, keyMatch) => {
      const key = keyMatch.trim();

      const replacement = params.hasOwnProperty(key)
        ? (params[key] || 0)
        : (formData.hasOwnProperty(key) ? formData[key] : 0);


      return String(replacement);
    });

    // Determine if the computed title uses custom syntax (like [[...]] or currency(...)).
    const usesCustomSyntax = /(?:\[\[(.*?)\]\]|currency\((.*?)\))/g.test(computedTitle);
    const uiProperties = ['ui:help', 'ui:description'];

    // Update the uiSchema accordingly.
    updateNestedValue(uiSchema, [...params.__field, uiProperties[Number(usesCustomSyntax)]], undefined);
    updateNestedValue(uiSchema, [...params.__field, uiProperties[Number(!usesCustomSyntax)]], computedTitle);
  } catch (error) {
    console.warn("Error in descriptionAsTooltip:", error);
  }
};

export default descriptionAsTooltip;

