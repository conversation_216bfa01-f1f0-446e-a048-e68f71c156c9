import { updateNestedValue } from "@/utils/object";

type Schema = Record<string, any>;
type Params = Record<string, any>;

/**
 * Overrides the schema properties with the provided configuration.
 *
 * @param schema - The JSON schema object to modify.
 * @param params - The configuration object with properties to override.
 */
const schemaOverride=(params: Params, schema: Schema): void =>{  
  // console.log("schemaOverride", params, schema);
  
  Object.keys(params).forEach((path) => {
    const value = params[path];

    // Ensure updates happen within the `properties` section or relevant nested paths
    if (path.startsWith("properties.") || path.includes(".")) {
      // mergePropertyByPath(schema.properties, path.replace(/^properties\./, ""), value);
      updateNestedValue(schema.properties, path.replace(/^properties\./, "").split("."), value);
    }
  });
}

export default schemaOverride;
