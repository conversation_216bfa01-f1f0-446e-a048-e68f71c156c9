// calculateFormData.ts

import { parseDateString } from "@/utils/date";
import { updateNestedValue } from "@/utils/object";

export type Params = Record<string, any>;
export type Schema = Record<string, any>;
export type UISchema = Record<string, any>;
export type FormData = Record<string, any>;

/**
 * Calculates a value based on the provided parameters and updates the formData accordingly.
 *
 * @param params - The parameters object which must include `__field` and `__exp`.
 * @param _schema - The schema (unused in this function but kept for signature compatibility).
 * @param uiSchema - The UI schema object.
 * @param formData - The formData object to update.
 */
const updateDate = (
  params: Params,
  _schema: Schema,
  _uiSchema: UISchema,
  formData: FormData
): void => {
  const field = params?.__field;
  const isMax = params?.__max || true;
  let cleanExpression = params.__exp;

  if (!params || !field || !cleanExpression) return;

  const matches = cleanExpression.match(/{{(.*?)}}/g);

  if (matches) {
    matches.forEach((match: string) => {
      const key = match.replace(/{{|}}/g, '');

      if (
        !Object.hasOwnProperty.call(formData, key) &&
        !Object.hasOwnProperty.call(params, key)
      ) {
        return;
      }

      cleanExpression = cleanExpression.replace(match, formData[key] ?? params[key]);
    });
  }

  const result = parseDateString(cleanExpression, isMax);

  updateNestedValue(formData, field.split("."), result.format('YYYY-MM-DD'));
}

export default updateDate
