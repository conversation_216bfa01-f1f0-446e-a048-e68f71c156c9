// updateFormData.ts

import { updateNestedValue } from "@/utils/object";

export type Params = Record<string, any>;
export type Schema = Record<string, any>;
export type UISchema = Record<string, any>;
export type FormData = Record<string, any>;

/**
 * Updates the formData based on the provided params and uiSchema.
 *
 * @param params - The parameters containing fields to update.
 * @param _schema - The schema (unused in this function, kept for signature compatibility).
 * @param uiSchema - The UI schema object that defines widget options.
 * @param formData - The formData object to update.
 */
const updateFormData = (
  params: Params,
  _schema: Schema,
  uiSchema: UISchema,
  formData: FormData
): void => {
  if (!params) return;

  Object.entries(params).forEach(([field, value]) => {
    if (Array.isArray(value) && !uiSchema[field]?.["ui:options"]?.multiple && !["table", "array"].includes(String(uiSchema[field]?.["ui:options"]?.type))) {
      // If the value is an array but the widget doesn't support multiple selections,
      // join the array values into a comma-separated string.

      // console.log("in IF for ", field, "with value ", value, " and type", uiSchema[field]?.["ui:options"]?.type);
      
      formData[field] = value?.filter(Boolean).map((item) => String(item)).join(",");
    } else {
      // Coerce the value based on the type defined in the UI schema.
      const typeMapping: { [key: string]: any } = {
        boolean: Boolean(value),
        number: Number(value) || 0,
        integer: Number(value) || 0,
      };

      const uiType: string | undefined = uiSchema[field]?.["ui:options"]?.type;

      if (uiType && uiType in typeMapping) {
        // console.log("in IF for ", field, "with value ", value, " and type", uiType);
        updateNestedValue(formData, field.split("."), typeMapping[uiType]);
      } else {
        // console.log("in ELSE for ", field, "with value ", value, " and type", uiType);
        updateNestedValue(formData, field.split("."), value);
      }
    }
  });
}

export default updateFormData;