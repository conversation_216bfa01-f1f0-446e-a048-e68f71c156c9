"use client";

import type { ReactNode} from "react";
import { memo, useState } from "react";

import Divider from "@mui/material/Divider";
import Accordion from "@mui/material/Accordion";
import Typography from "@mui/material/Typography";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import { Grid2 } from "@mui/material";
import { Icon } from "@iconify/react/dist/iconify.js";

type AccordionLayoutProps = {
  title: string;
  children: ReactNode;
  isError?: boolean;
  beforeTitle?: ReactNode;
};

const AccordionLayout = ({ title, children, isError = false, beforeTitle = <></> }: AccordionLayoutProps) => {
  const [selected, setSelected] = useState(false);

  const handleAccordionChange = () => {
    setSelected((prev) => !prev);
  };

  return (
    <Accordion expanded={isError || selected} onChange={handleAccordionChange} >
      <AccordionSummary
        expandIcon={<Icon icon="ri-arrow-down-s-line" />
        }
      >
        {beforeTitle}
        <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
          {title}
        </Typography>
      </AccordionSummary>
      <Divider />
      <AccordionDetails>
        <Grid2
          container
          spacing={3}
          direction="column"
          sx={{
            mt: 2,
            "& .required-asterisk": {
              color: "crimson", // Force asterisk to be red
            },
          }}
        >
          {children}
        </Grid2>
      </AccordionDetails>
    </Accordion>
  );
};

export default memo(AccordionLayout);
