import type { ReactNode } from "react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Divider } from "@mui/material"

interface CardLayoutProps {
  children: ReactNode
  title: string
  subheader?: string
  inCenter?: boolean
}

const CardLayout = ({ children, title, subheader, inCenter = false }: CardLayoutProps): JSX.Element => {
  return (
    <Card sx={{ border: 0, boxShadow: 0 }}>
      <CardHeader
        title={title}
        sx={{ p: theme => `${theme.spacing(5, 0, 0)} !important`, textAlign: inCenter ? "center" : "left" }}
        {...(subheader && { subheader })}
      />
      {children && (
        <CardContent>
          {!inCenter && title && <Divider />}
          {children}
        </CardContent>
      )}
    </Card>
  )
}

export default CardLayout
