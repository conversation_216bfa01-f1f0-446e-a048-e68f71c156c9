import React, { memo } from "react";

import type { ArrayFieldTemplateProps } from "@rjsf/utils";
import { Paper, IconButton, Tooltip } from "@mui/material";
import Grid2 from "@mui/material/Grid2";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import { ArrowDownwardRounded, ArrowUpwardRounded, ContentCopyRounded } from "@mui/icons-material";

const checkDefaultRow = (defaultValues: any[], formData: any) =>
  defaultValues.some(
    (defaultValue: any) =>
      JSON.stringify(defaultValue) === JSON.stringify(formData)
  );

function ArrayFieldTemplate(props: ArrayFieldTemplateProps) {
  const defaultValues = props.schema.default || [];

  return (
    <Grid2 container spacing={2}>
      {props.items.map((element, index) => (
        <Grid2 key={index} size={12}>
          <Paper elevation={0}>
            <Grid2 container spacing={2} alignItems="center">
              {/* Main form fields (single row layout) */}
              <Grid2 size={11} >
                {element.children}
              </Grid2>
              {/* Icon buttons (right area) */}
              <Grid2
                size={1}
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                {/* Add icon on the first item */}
                {element.canAdd && index === 0 && (
                  <Tooltip title="Add New Row" arrow placement="top">
                    <IconButton
                      color="primary"
                      onClick={props.onAddClick}
                      sx={{ mr: 1 }}
                    >
                      <AddIcon />
                    </IconButton>
                  </Tooltip>
                )}

                {element.hasMoveDown && (
                  <Tooltip title="Move Down" arrow placement="top">
                    <IconButton
                      color="primary"
                      onClick={element.onReorderClick(
                        index,
                        index + 1
                      )}
                      disabled={index === props.items.length - 1}
                    >
                      <ArrowDownwardRounded />
                    </IconButton>
                  </Tooltip>
                )}

                {element.hasMoveUp && (
                  <Tooltip title="Move Up" arrow placement="top">
                    <IconButton
                      color="primary"
                      onClick={element.onReorderClick(
                        index,
                        index - 1
                      )}
                      disabled={index === 0}
                    >
                      <ArrowUpwardRounded />
                    </IconButton>
                  </Tooltip>
                )}

                {element.hasCopy && (
                  <Tooltip title="Copy This Row" arrow placement="top">
                    <IconButton
                      color="primary"
                      onClick={element.onCopyIndexClick(index)}
                    >
                      <ContentCopyRounded />
                    </IconButton>
                  </Tooltip>
                )}

                {/* Close (remove) icon on subsequent items */}
                {element.hasRemove && index !== 0 && (
                  <Tooltip title="Remove This Row" arrow placement="top">
                    <IconButton
                      color="error"
                      onClick={element.onDropIndexClick(index)}
                      disabled={checkDefaultRow(
                        defaultValues,
                        element.children.props.formData
                      )}
                    >
                      <CloseIcon />
                    </IconButton>
                  </Tooltip>
                )}
              </Grid2>
            </Grid2>
          </Paper>
        </Grid2>
      ))}
    </Grid2>
  );
}

export default memo(ArrayFieldTemplate);
