import type { ReactNode, SyntheticEvent} from "react";
import { memo, useState } from "react"

import Tabs from "@mui/material/Tabs"
import Tab from "@mui/material/Tab"
import Box from "@mui/material/Box"
import TabPanel from "@mui/lab/TabPanel"
import TabContext from '@mui/lab/TabContext';

type TabsLayoutProps = {
  titles: string[]
  children: ReactNode[]
}

const TabsLayout = ({ children, titles }: TabsLayoutProps) => {
  const [value, setValue] = useState("0")

  const handleChange = (_event: SyntheticEvent, newValue: string) => {
    setValue(newValue)
  }

  return (
    <TabContext value={value}>
      <Box sx={{ width: "100%" }}>
        <Tabs value={value} onChange={handleChange} variant='scrollable' scrollButtons='auto'>
          {titles.map((item, index: number) => (
            <Tab key={item || `Step ${index + 1}`} label={item || `Step ${index + 1}`} value={`${index}`} />
          ))}
        </Tabs>

        <Box>
          {children?.map((child, index: number) => (
            <TabPanel key={index} value={`${index}`}>
              {child}
            </TabPanel>
          ))}
        </Box>
      </Box>
    </TabContext>
  )
}

export default memo(TabsLayout)
