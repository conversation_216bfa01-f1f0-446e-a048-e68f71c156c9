import { Fragment, memo } from "react";

import type { ObjectFieldTemplatePropertyType, UIOptionsType } from "@rjsf/utils";

import { Paper } from "@mui/material";
import type { GridBaseProps } from "@mui/material";

import Grid2 from "@mui/material/Grid2";

import Expandable from "./Accordion";
import Tabs from "./Tabs";
import Adornment from "../components/Adornment";

type LayoutWrapperProps = {
  layout?: "tabs" | "accordion" | "minimal" | "group" | "tabular"
  properties: ObjectFieldTemplatePropertyType[];
  title: string,
  isError?: boolean;
  options: UIOptionsType
};

const LayoutChildren = ({ properties, useGrid = false, options = {} }: { properties: ObjectFieldTemplatePropertyType[], useGrid?: boolean, options?: GridBaseProps}) => {
  const Component = useGrid ? Grid2 : Fragment;

  return properties.map((prop: ObjectFieldTemplatePropertyType) => (
    <Component
      key={prop.content.key}
      {...(useGrid ? { xs: 12, ...options } : {})}
    >
      {prop.content}
    </Component>
  ));
};

const LayoutWrapper = ({ properties, title, isError = false, options }: LayoutWrapperProps) => {
  const layout = options?.layout ?? "group"

  if (layout === "tabs") {
    return (
      <Tabs titles={Object.values(properties)?.map((item) => item?.name)}>
        {LayoutChildren({ properties })}
      </Tabs>
    );
  }

  if (layout === "accordion") {
    const adornments = {
      ...(options?.startAdornment && Array.isArray(options?.startAdornment) && {
        startAdornment: (
          <>
            {options?.startAdornment?.filter(item => item?.hidden !== true)?.map((adornment, index) => (
              <Adornment
                key={index}
                adornment={adornment}
                position="start"
              />
            ))}
          </>
        ),
      }),
    };

    return (
      <Expandable
        title={title}
        isError={isError}
        beforeTitle={
          adornments?.startAdornment
        }
      >
      <LayoutChildren properties={properties} />
      </Expandable>
    );
  }

  if (layout === "minimal") {
    return (
      <Grid2 container my={3} direction="column" spacing={3}>
        <LayoutChildren properties={properties} useGrid={true} />
      </Grid2>
    );
  }

  if (layout === "tabular") {
    const clamped = Math.floor(12 / Math.min(properties?.length ?? 1, 12))
    
    return (
      <Grid2 container spacing={2} direction="row">
        <LayoutChildren properties={properties} useGrid={true} options={{size: {lg: clamped }}} />
      </Grid2>
    );
  }

  return (
    <Paper
      style={{
        padding: 10,
        width: "100%",        
      }}
      variant="elevation"
      elevation={0}
    >
      <LayoutChildren properties={properties} />
    </Paper>
  );
};

export default memo(LayoutWrapper);
