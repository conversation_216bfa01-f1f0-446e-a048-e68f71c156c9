// import useMediaQuery from "@/@menu/hooks/useMediaQuery"
import { memo } from "react"

import { getUiOptions } from "@rjsf/utils"
import type { FieldTemplateProps, FormContextType, RJSFSchema, StrictRJSFSchema } from "@rjsf/utils"
import { useMedia } from "react-use"

import NullFieldWrapper from "./fieldWrapper/NullFieldWrapper"
import InputFieldWrapper from "./fieldWrapper/InputFieldWrapper"
import MobileFieldWrapper from "./fieldWrapper/MobileFieldWrapper"

function TitleFieldTemplate<T = any, S extends StrictRJSFSchema = RJSFSchema, F extends FormContextType = any>(
  props: FieldTemplateProps<T, S, F>
) {
  const { label, children, required = false, schema, uiSchema } = props

  const options = getUiOptions<T, S, F>(uiSchema);
  const field: string = String(uiSchema?.["ui:field"]) || ''
  const widget: string = String(uiSchema?.["ui:widget"]) || ''
  const isMobileScreen = useMedia('(max-width: 600px)', false)

  // console.log(options, "ops")

  if (
    isMobileScreen ||

    // Check for hideLabel option
    (!!options?.hideLabel) ||

    (field === "ArrayFieldTemplate") ||

    // Check for TableWidget
    (widget === "TableWidget") ||

    // Check for an object type schema when field is not "typehead"
    (schema?.type === "object" && field !== "typehead") ||

    // Check for an array of strings schema
    (schema?.type === "array" && schema?.items?.type === "string" && widget !== "DateRangeWidget")
  ) {
    return <MobileFieldWrapper>
      {children}
    </MobileFieldWrapper>
  }

  if (schema?.type === "null") {
    return <NullFieldWrapper hideLabel={!!options?.hideLabel} title={label}>
      {children}
    </NullFieldWrapper>
  }

  return (
    <InputFieldWrapper label={label} required={required}>
      {children}
    </InputFieldWrapper>
  )
}

export default memo(TitleFieldTemplate)
