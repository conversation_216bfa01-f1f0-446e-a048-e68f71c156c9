import { memo } from "react";

import type { FormContextType, ObjectFieldTemplateProps, RJSFSchema, StrictRJSFSchema } from "@rjsf/utils";
import { getTemplate, getUiOptions, titleId, descriptionId } from "@rjsf/utils";

// import Grid from '@mui/material/Grid2'

import LayoutWrapper from "../layout/Layout";
import { injectFieldsInTitle } from "@/utils/injectFieldsInTitle";

function ObjectFieldTemplate<T = any, S extends StrictRJSFSchema = RJSFSchema, F extends FormContextType = any>(
  props: ObjectFieldTemplateProps<T, S, F>
) {
  const { registry, properties, title, description, uiSchema, required, schema, idSchema, formData } = props;
  const options = getUiOptions<T, S, F>(uiSchema);
  const TitleFieldTemplate = getTemplate<'TitleFieldTemplate', T, S, F>('TitleFieldTemplate', registry, options);
  const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, options)

  const injectedTitle = injectFieldsInTitle(title, formData);
  const injectedDescription = injectFieldsInTitle(description, formData);
  
  return (
    <fieldset id={idSchema.$id}>
      {(title && options?.layout !== "accordion") && options?.layout !== "tabular" && (
        <TitleFieldTemplate
          id={titleId<T>(idSchema)}
          title={injectedTitle}
          required={required}
          schema={schema}
          uiSchema={uiSchema}
          registry={registry}
        />
      )}

      {description && options?.layout !== "tabular" && (
        <DescriptionFieldTemplate
          id={descriptionId(idSchema)}
          description={injectedDescription}
          schema={schema}
          uiSchema={uiSchema}
          registry={registry}
        />
      )}

      {/* <Grid container direction="column" spacing={2}> */}
        <LayoutWrapper options={options} properties={properties} title={title} isError={!!Object.keys(props?.errorSchema || {})?.length} />
      {/* </Grid> */}
    </fieldset>
  );
}

export default memo(ObjectFieldTemplate)
