import { Grid2, Typography } from "@mui/material";

const InputFieldWrapper = ({ label, children, required }) => (
  <Grid2 container mb={2}>
    <Grid2 size={3}>
      <Typography>{label} {required && <Typography component="span" color="crimson" className="required-asterisk">*</Typography>}</Typography>
    </Grid2>
    <Grid2 size={9}>
      {children}
    </Grid2>
  </Grid2>
)

export default InputFieldWrapper;
