import { Grid2, Typography } from "@mui/material";

const NullFieldWrapper = ({ children, hideLabel=true, title="" }) => (
  <Grid2 container size={12} mb={2} sx={{ textAlign: 'center' }}>
    {(title && !hideLabel) && <Grid2 size={12}>
      <Typography>{title}</Typography>
    </Grid2>}

    {/* {children && <Grid2 size={12}> */}
      {children}
    {/* </Grid2>} */}
  </Grid2>
)

export default NullFieldWrapper;