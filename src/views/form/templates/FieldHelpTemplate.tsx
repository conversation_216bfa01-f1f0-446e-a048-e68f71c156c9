import React from "react";

import Typography from "@mui/material/Typography";
import Grid2 from "@mui/material/Grid2"; // Stable MUI Grid2
import type { FieldHelpProps, FormContextType, RJSFSchema, StrictRJSFSchema } from "@rjsf/utils";
import { helpId } from "@rjsf/utils";

import { formatCurrency } from "@/utils/string";

/**
 * Defines the style attributes for text.
 */
type StyleAttributes = {
  color?: string;
  bg?: string;
};

/**
 * StyledText component wraps its children in a Typography component with optional color and background styles.
 *
 * @param props - Component props.
 * @param props.children - The content to display.
 * @param props.color - Optional text color.
 * @param props.bg - Optional background color.
 *
 * @returns A styled Typography element.
 *
 * @example
 * <StyledText color="red" bg="yellow">Styled Content</StyledText>
 */
function StyledText({ children, color, bg }: { children: React.ReactNode; color?: string; bg?: string }): JSX.Element {
  return (
    <Typography
      component="span"
      variant="caption"
      sx={{
        color: color || "inherit",
        backgroundColor: bg || "transparent",
        padding: bg ? "2px 4px" : "0",
      }}
    >
      {children}
    </Typography>
  );
}

/**
 * Resolves a style value. If the value can be converted to a number, it returns "green" for positive numbers
 * and "red" for zero or negative numbers. Otherwise, it returns the original value.
 *
 * @param value - The string value to resolve.
 * @returns A string representing the resolved style value.
 *
 * @example
 * resolveStyleValue("10") // returns "green"
 * resolveStyleValue("-5") // returns "red"
 */
const resolveStyleValue = (value: string): string => {
  const numericValue = Number(value);

  if (!isNaN(numericValue)) {
    return numericValue > 0 ? "green" : "red";
  }

  
return value;
};

/**
 * Parses the input text for markdown-like formatting and returns an array of strings or JSX elements.
 *
 * Supported syntaxes:
 * - **bold text**
 * - _italic text_
 * - currency(number), e.g. currency(-7855)
 * - Styled text blocks defined using [[color:red]], [[bg:yellow]], or combined like [[color:red;bg:yellow]].
 *
 * @param text - The input string containing the formatting.
 * @returns An array of strings and/or JSX elements representing the parsed output.
 *
 * @example
 * const parsed = parseText("This is **bold** and _italic_ text with currency(7855) and [[color:green]]styled text[[color]]");
 */
function parseText(text: string): (string | JSX.Element)[] {
  // Regular expression using named capture groups for clarity.
  // The global flag is set so that matchAll returns all matches.
  const markupRegex =
    /(?<bold>\*\*(?<boldText>.+?)\*\*)|(?<italic>_(?<italicText>.+?)_)|(?<currency>currency\s*\((?<currencyAmount>-?\d+(?:\.\d+)?)\))|(?<openTag>\[\[(?<styleAttr>[^\]]+)\]\])(?<styledContent>.+?)(?=\[\[|\*\*|_|$)|(?<closeTag>\[\[(?<closeType>color|bg)\]\])/g;

  const parts: (string | JSX.Element)[] = [];
  let lastIndex = 0;

  // Stack to handle nested style contexts.
  const styleStack: StyleAttributes[] = [{}];

  try {
    const matches = Array.from(text.matchAll(markupRegex));

    for (const match of matches) {
      const index = match.index ?? 0;


      // Append any plain text before the current match.
      if (index > lastIndex) {
        parts.push(text.slice(lastIndex, index));
      }

      const { groups } = match;

      if (!groups) {
        continue;
      }

      if (groups.bold && groups.boldText) {
        // Render bold text.
        parts.push(<Typography component="strong" key={index}>{groups.boldText}</Typography>);
      } else if (groups.italic && groups.italicText) {
        // Render italic text.
        parts.push(<Typography component="em" key={index}>{groups.italicText}</Typography>);
      } else if (groups.currency && groups.currencyAmount) {
        // Format currency.
        const formatted = formatCurrency(groups.currencyAmount);

        parts.push(formatted);
      } else if (groups.openTag && groups.styleAttr && groups.styledContent) {
        // Parse style attributes from the open tag.
        const newStyles = { ...styleStack[styleStack.length - 1] };

        groups.styleAttr.split(";").forEach((attr) => {
          const [key, value] = attr.split(":").map((s) => s.trim());

          if (key && value) {
            const resolvedValue = resolveStyleValue(value);

            if (key === "color") newStyles.color = resolvedValue;
            if (key === "bg") newStyles.bg = resolvedValue;
          }
        });

        // Push the new style context.
        styleStack.push(newStyles);

        // Recursively parse the content within the styled tag.
        const parsedContent = parseText(groups.styledContent.trim());

        parts.push(
          <StyledText key={index} color={newStyles.color} bg={newStyles.bg}>
            {parsedContent}
          </StyledText>
        );
      } else if (groups.closeTag && groups.closeType) {
        // Close a style block by popping the style stack.
        if (styleStack.length > 1) {
          styleStack.pop();
        } else {
          console.warn("Unmatched closing tag encountered in parseText:", groups.closeTag);
        }
      }

      lastIndex = index + match[0].length;
    }
  } catch (error) {
    console.warn("Error parsing text:", error);

    // In case of error, return the original text as a single element.
    return [text];
  }

  // Append any remaining plain text after the last match.
  if (lastIndex < text.length) {
    parts.push(text.slice(lastIndex));
  }

  return parts;
}

/**
 * FieldHelpTemplate renders formatted help text for form fields.
 *
 * It takes a string containing markdown-like syntax (bold, italic, currency formatting,
 * and styled text blocks) and transforms it into a collection of React elements.
 *
 * @template T - The type of the form data.
 * @template S - The schema type.
 * @template F - The form context type.
 *
 * @param props - FieldHelpProps containing help text and metadata.
 * @param props.help - The help text to format.
 * @param props.idSchema - The id schema used to generate a unique id for the help text.
 *
 * @returns A Grid2 container with the formatted help text, or null if no help text is provided.
 *
 * @example
 * <FieldHelpTemplate help="This is **bold** help text with currency(-7855) and [[color:green]]styled text[[color]]" idSchema={{ $id: 'my-help' }} />
 */
function FieldHelpTemplate<
  T = any,
  S extends StrictRJSFSchema = RJSFSchema,
  F extends FormContextType = any
>(props: FieldHelpProps<T, S, F>): JSX.Element | null {
  const { help, idSchema } = props;

  // Return null if help text is missing or is not a string.
  if (!help || typeof help !== "string") {
    return null;
  }

  const parsedContent = parseText(help);

  return (
    <Grid2 container size={12} id={helpId<T>(idSchema)} className="field-help">
      <Typography variant="caption" color="textSecondary">
        {parsedContent}
      </Typography>
    </Grid2>
  );
}

export default FieldHelpTemplate;
