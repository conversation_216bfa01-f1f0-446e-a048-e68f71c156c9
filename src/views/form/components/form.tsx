"use client";

import type { ReactNode } from "react";
import { useRef, useMemo, useEffect, useState } from "react";

import { useRouter } from "next/navigation";

import { Box, Button, Typography, Divider } from "@mui/material";
import validator from "@rjsf/validator-ajv8";
import Form from "@rjsf/mui";
import applyRules from "rjsf-conditionals";

import { ArrowBackOutlined } from "@mui/icons-material";

import ObjectFieldTemplate from "../templates/ObjectField";
import generateEngine from "@/utils/generateEngine";
import { ActionBuilder } from "./Actions";
import TypeaheadField from "../fields/Typeahead";
import TitleFieldTemplate from "@views/form/templates/TitleField";
import AdornmentField from "@views/form/fields/AdornmentField";
import TimeWidget from "../widgets/TimeWidget";
import ImageWidget from "../widgets/ImageWidget";
import TableWidget from "../widgets/TableWidgetV2";
import CameraWidget from "../widgets/CameraWidget";
import DateRangeWidget from "../widgets/DateRange";

import GroupLayout from "../layout/Group";
import FieldHelpTemplate from "../templates/FieldHelpTemplate";
import uiSchemaReplace from "../events/uiSchemaReplace";
import schemaOverride from "../events/schemaOverride";
import updateFormData from "../events/updateFormData";
import calculateExpression from "../events/evaluateExpression";
import descriptionAsTooltip from "../events/descriptionAsTooltip";
import nestedRemove from "../events/nestedRemove";
import updateDate from "../events/updateDate";

import Modal from "./Modal"; // Import our updated Modal component
import { injectFieldsInTitle } from "@/utils/injectFieldsInTitle";


const templates = {
  ButtonTemplates: { SubmitButton: () => <></> },
  WrapIfAdditionalTemplate: TitleFieldTemplate,
  ObjectFieldTemplate,
  ArrayFieldTemplate: GroupLayout,
  FieldHelpTemplate,
};

const widgets = {
  CameraWidget,
  TableWidget,
  TimeWidget,
  ImageWidget,
  DateRangeWidget,
};

const fields = {
  typehead: TypeaheadField,
  adornment: AdornmentField,
};

const extraActions = {
  tooltip: descriptionAsTooltip,
  calculate: calculateExpression,
  update: updateFormData,
  schemaOverride,
  uiSchemaReplace,
  nestedRemove,
  updateDate,
};

const transformErrors = (errors: any) => {
  const filteredErrors = errors.filter((error: any) => {
    if (error.name === "const" || error.name === "oneOf") {
      return false;
    }

    return true;
  });


  return filteredErrors.map((error: any) => {
    if (error.name === "required") {
      error.message = "This field is required. Please provide a value.";
    } else if (error.name === "enum") {
      error.message = "Please select a valid option from the list.";
    } else if (error.name === "pattern") {
      error.message =
        "The input format is incorrect. Please follow the required format.";
    } else if (error.name === "type") {
      error.message =
        "The value entered is invalid. Please check and try again.";
    } else if (error.name === "minimum") {
      const minValue = error.params.limit;

      error.message = `The value entered is less than the minimum value of ${minValue}. Please check and try again.`;
    } else if (error.name === "maximum") {
      const maxValue = error.params.limit;

      error.message = `The value entered is more than the maximum value of ${maxValue}. Please check and try again.`;
    } else if (error.name === "uniqueItems") {
      if (!Number.isNaN(error?.params?.i) && !Number.isNaN(error?.params?.j)) {
        error.message = `Duplicates Found! The value entered at row ${error.params.j + 1} is already present at row ${error.params.i + 1}. Please check and try again.`;
      } else {
        error.message =
          "Duplicates Found! The value entered is already present. Please check and try again.";
      }
    }

    return error;
  });
};

type FormPageProps = {
  response: any;
  screenFrom?: "form" | "mix" | "popup";
};

type FormPageType = (props: FormPageProps) => ReactNode; 

const FormPage:FormPageType = ({ response, screenFrom = "form" }) => {
  const router = useRouter();
  const formRef = useRef<any>(null);

  const engine = useMemo(
    () => generateEngine(response?.schema?.facts || [], response?.schema?.rules || []),
    [response]
  );

  const [FormWithConditionals, setFormWithConditionals] = useState<any>(null);

  useEffect(() => {
    const formWithRules = applyRules(
      response?.schema?.flow?.children || {},
      response?.schema?.flow?.uiSchema || {},
      [],
      engine,
      extraActions
    )(Form);

    setFormWithConditionals(() => formWithRules);
  }, [response, engine]);

  const [injectedTitle, setInjectedTitle] = useState(response?.meta?.title);

  useEffect(() => {
    if (FormWithConditionals && formRef.current && formRef.current?.state?.formData) {
      const newTitle = injectFieldsInTitle(response?.meta?.title, formRef?.current?.state?.formData);

      setInjectedTitle(newTitle);
    }

    // The dependency array is empty so this runs only once after the first render.
  }, [FormWithConditionals, response?.meta?.title]);

  function customValidate(_formData: any, errors: any, uiSchema: any) {
    Object.keys(uiSchema).forEach((key) => {
      if (uiSchema[key]?.["ui:options"]?.error) {
        errors[key].addError(uiSchema[key]["ui:options"]["error"]);
      }
    });

    return errors;
  }

  // If isPopup is true, render the form inside our Modal component.
  if (screenFrom === "popup") {
    return (
      <Modal
        title={injectedTitle || response?.meta?.title || "Form"}
        footer={
          response?.schema?.actions && (
            <ActionBuilder actions={response?.schema?.actions} parentForm={formRef} />
          )
        }
        screenFrom={screenFrom}
      >
        {FormWithConditionals && (
          <FormWithConditionals
            widgets={widgets}
            fields={fields}
            validator={validator}
            templates={templates}
            formContext={{ formRef, engine }}
            showErrorList={true}
            focusOnFirstError={true}
            ref={formRef}
            noHtml5Validate={true}
            transformErrors={transformErrors}
            customValidate={customValidate}
          />
        )}
      </Modal>
    );
  }

  // Otherwise, render a full-page layout with a similar header, scrollable content, and footer.
  return (
    <Box sx={{ display: "flex", flexDirection: "column", maxHeight: "100vh" }}>
      <Box
        sx={{
          p: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          flexShrink: 0,
        }}
      >
        <Typography variant="h6">{injectedTitle || response?.meta?.title || "Form"}</Typography>
        {screenFrom === "form" && (
          <Button
            variant="contained"
            color="secondary"
            onClick={() => router.back()}
            sx={{ textTransform: "none" }}
            startIcon={<ArrowBackOutlined />}
          >
            Back
          </Button>
        )}
      </Box>
      <Divider />
      {/* <ScrollWrapper> */}
        {FormWithConditionals && (
          <FormWithConditionals
            widgets={widgets}
            fields={fields}
            validator={validator}
            templates={templates}
            formContext={{ formRef, engine }}
            showErrorList={true}
            focusOnFirstError={true}
            ref={formRef}
            noHtml5Validate={true}
            transformErrors={transformErrors}
            customValidate={customValidate}
          />
        )}
      {/* </ScrollWrapper> */}

      {response?.schema?.actions && (
        <>
          <ActionBuilder actions={response?.schema?.actions} parentForm={formRef} />
        </>
      )}
    </Box>
  );
};

export default FormPage;
