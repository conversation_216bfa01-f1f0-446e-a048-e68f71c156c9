import React from "react";

import { useRouter } from "next/navigation";

import { InputAdornment, Button, IconButton, Tooltip } from "@mui/material";
import { Icon } from "@iconify/react";

import { ensureAdminModeInPath } from "@/utils/string";

type AdornmentType =
  | string
  | {
    mode?: "society" | "gate";
    showButton?: boolean;
    redirect?: string;
    form?: string; // Form key for redirecting to /form/[formKey]
    icon?: string;
    title?: string;
    update?: { key: string; value: string };
  }
  | number
  | null;

interface AdornmentProps {
  position?: "start" | "end";
  adornment?: AdornmentType;
  updateKey?: (key: string, value: any) => void;
}

const Adornment: React.FC<AdornmentProps> = ({ adornment, position = "start", updateKey }) => {
  const { push } = useRouter();

  const onClick = async () => {
    if (!adornment) return;
    if (typeof adornment !== "object") return;

    if (adornment?.redirect && push) push(ensureAdminModeInPath(adornment.redirect, adornment.mode || "society"));

    // Redirect to the form popup route if a form key is provided
    if (adornment?.form && push) push(`/admin/${adornment.mode || "society"}/form/${adornment.form}`);

    if (adornment?.update && updateKey) updateKey(adornment?.update?.key, adornment?.update?.value);
  };

  if (!adornment) return null;

  if (typeof adornment === "string" || typeof adornment === "number") {
    return <InputAdornment position={position}>{adornment}</InputAdornment>;
  }

  if (typeof adornment === "object") {
    return (
      <InputAdornment position={position} sx={(theme) => ({
        color: `${theme.palette.text.secondary} !important`,
        "& svg": {
          fill: `${theme.palette.text.secondary} !important`,
        },
      })}>
        <Tooltip title={adornment.title || ""}>
          {adornment?.showButton ? (
            <Button onClick={onClick} color="primary" variant="outlined" startIcon={adornment?.icon ? <Icon icon={adornment.icon} /> : <></>}>
              {adornment.title}
            </Button>
          ) : (
            <IconButton onClick={onClick}>
              <Icon icon={adornment.icon} />
            </IconButton>
          )}
        </Tooltip>
      </InputAdornment>
    );
  }

  
return null;
};

export default Adornment;

{/* <Button
          variant="outlined"
          color={adornment?.color || "primary"}
          onClick={onClick}
          {...(adornment?.icon && { startIcon: <Icon icon={adornment.icon} /> })}
        >
          {adornment?.title}
        </Button>
      </InputAdornment> */}