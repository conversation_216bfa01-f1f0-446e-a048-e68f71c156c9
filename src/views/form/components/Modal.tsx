"use client";

import { useId } from "react";
import type { ReactNode } from "react"

import { usePathname, useRouter } from "next/navigation";

import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import SwipeableDrawer from "@mui/material/SwipeableDrawer";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import Button from "@mui/material/Button";
import { useTheme, useMediaQuery } from "@mui/material";
import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";
import { styled } from "@mui/material/styles";

// Define a nonzero value for the bleeding area.
// This value determines how much of the header (puller, title, close icon) bleeds above the main drawer area.
const drawerBleeding = 24;

const Puller = styled("div")(({ theme }) => ({
  width: 30,
  height: 6,
  backgroundColor: theme.palette.grey[300],
  borderRadius: 3,
  position: "absolute",
  top: 8,
  left: "calc(50% - 15px)",
}));

interface ModalProps {
  children: ReactNode;
  title: string; // Title is required.
  footer?: ReactNode; // Optional footer content.
  screenFrom?: "form" | "mix" | "popup"; // Determines the screen type.
}

function Modal({
  children,
  title,
  footer,
  screenFrom = "form",
}: ModalProps) {
  const { back } = useRouter();
  const pathname = usePathname();
  const theme = useTheme();
  const id = useId()
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // This openState logic remains unchanged.
  const openState = pathname.includes("/form") || pathname.includes("/select");

  function handleClose(event?: React.MouseEvent<HTMLElement>) {
    if (event) event.stopPropagation();
    if (screenFrom === "form") return;
    back();
  }

  // Render the header action: show a "Back" button if isFormScreen is true; otherwise, show a close icon.
  const renderHeaderAction = () => {
    if (screenFrom === "form") {
      return (
        <Button
          variant="contained"
          color="secondary"
          onClick={handleClose}
          sx={{ textTransform: "none" }}
        >
          Back
        </Button>
      );
    }

    if (screenFrom === "popup") {
      return (
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{ color: theme.palette.grey[500] }}
        >
          <CloseIcon />
        </IconButton>
      );
    }
  }

  if (isMobile) {
    return (
      <SwipeableDrawer
        anchor="bottom"
        open={openState}
        onClose={handleClose}
        onOpen={() => { }}
        slotProps={{
          paper: {
            sx: {
              height: "100%",
              maxHeight: "98vh",
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8,
              overflow: "hidden", // prevent the header (bleeding area) from being visible
            },
          }
        }}
      >
        {/* Header area with puller */}
        <Box
          sx={{
            position: "absolute",
            top: -drawerBleeding,
            left: 0,
            right: 0,
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
            backgroundColor: theme.palette.background.paper,
            paddingTop: `${drawerBleeding / 2}px`,
            zIndex: 1,
          }}
        >
          <Puller />
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              px: 2,
              pt: 1,
            }}
          >
            <div dangerouslySetInnerHTML={{ __html: title }} />
            {renderHeaderAction()}
          </Box>
        </Box>
        {/* Main content container */}
        <Box sx={{ px: 2, pb: 2, height: "100%" }}>
          {children}
        </Box>
        {footer && (
          <Box sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
            {footer}
          </Box>
        )}
      </SwipeableDrawer>
    );
  }

  return (
    <Dialog
      open={openState}
      onClose={handleClose}
      fullWidth
      maxWidth="md"
      slotProps={{
        paper: {
          sx: {
            borderRadius: "8px",
            overflow: "hidden",
            boxShadow: theme.shadows[5], // using theme shadow for consistency
            position: "relative",
          },
        }
      }}
      aria-labelledby={id}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          p: 2,
        }}
      >
        <DialogTitle sx={{ p: 0 }} id={id}>
          <div dangerouslySetInnerHTML={{ __html: title }} />
        </DialogTitle>
        {renderHeaderAction()}
      </Box>
      <Divider />
      <DialogContent>{children}</DialogContent>
      {footer && <DialogActions>{footer}</DialogActions>}
    </Dialog>
  );
}

export default Modal;
