import { useState } from "react"

import { usePathname, useRouter } from "next/navigation"

import axios from "axios"
import { Box } from "@mui/material"
import LoadingButton from "@mui/lab/LoadingButton"
import { toast } from "react-toastify"

import { Icon } from "@iconify/react/dist/iconify.js"

import { replacePlaceholders } from "@/utils/replacePlaceholders"
import { queryClient } from "@/contexts/queryClientProvider"
import { validateColor, validateVariant } from "@/views/table/Actions"
import { processFormData } from "@/utils/object"
import type { Action } from "../types/action"
import { ensureAdminModeInPath } from "@/utils/string"

const ACTION_CONFIG = {
  submit: { color: "success", variant: "contained", message: "Submitting…" },
  submit_and_new: { color: "success", variant: "contained", message: "Submitting…" },
  reset: { color: "secondary", variant: "outlined", message: "Resetting…" },
  cancel: { color: "error", variant: "outlined", message: "Cancelling…" },
  download: { message: "Downloading…" },
  view: { message: "Opening…" },
}

/**
 * Simple toast wrapper for async operations
 *
 * @param type - The action type from ACTION_CONFIG
 * @param cb - The async callback function to execute
 * @returns The result of the callback function
 */
async function withToast(type: string, cb: () => Promise<any>) {
  const id = toast.loading(ACTION_CONFIG[type].message)
  const autoCloseTime = (process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000) as number

  try {
    // Execute the callback function with validateStatus: true to prevent axios from throwing on non-2xx status
    const result = await cb()

    // Check if the response indicates an error before showing success toast
    const hasError =
      result?.data?.status === 'error' ||
      result?.status >= 400 ||
      (result?.data?.meta?.errors && Object.keys(result?.data?.meta?.errors || {}).length > 0)

    if (!hasError) {
      // Only show success toast if there's no error
      toast.update(id, {
        render: "Success",
        type: "success",
        isLoading: false,
        autoClose: autoCloseTime
      })
    } else {
      // For errors, just close the loading toast - detailed error handling will be done by the caller
      toast.dismiss(id)
    }

    return result
  } catch (err: any) {
    // Log the error for debugging
    console.log("Error in withToast", err)

    // Show a generic error message
    const errorMessage = err?.message || 'An unexpected error occurred'

    toast.update(id, {
      render: errorMessage,
      type: "error",
      isLoading: false,
      autoClose: autoCloseTime
    })

    // Return a structured error response
    return {
      error: true,
      message: errorMessage,
      originalError: err
    }
  }
}

function buildUrl(path: string, data: any) {
  let url = replacePlaceholders(path, data)

  if (!url.includes(":id") && data.id) url += `/${data.id}`

  return `${process.env.NEXT_PUBLIC_API_URL}${url}`
}

/**
 * Merge default parameters with form data
 *
 * @param params - Default parameters array
 * @param formData - Form data object
 * @returns Merged parameters object
 */
function mergeDefaultParams(params: Array<string | Record<string, any>> = [], formData: any) {
  return params.reduce((acc: Record<string, any>, p) => {
    const key = typeof p === "object" ? Object.keys(p)[0] : p;
    const value = typeof p === "object" ? formData[Object.keys(p)[0]] : formData[p];

    return { ...acc, [key]: value };
  }, {});
}

export const ActionBuilder = ({ actions, parentForm }: { actions: any[], parentForm?: any }) => {
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  /**
   * Handle API error responses by extracting detailed error messages
   * and updating the form state and UI
   *
   * @param data - The API response data containing error information
   */
  const handleErrorResponse = (data: any) => {
    console.log('Processing API error response:', data)

    // Only proceed if there are errors to process
    if (!data.meta?.errors || Object.keys(data.meta.errors).length === 0) {
      // If no detailed errors, show a generic error message
      toast.error(data.message || 'An error occurred');

      return;
    }

    // Extract detailed error messages from meta.errors
    const errors = Object.entries(data.meta.errors).map(([key, value]) => ({
      property: `.${key}`,
      name: "server",
      message: Array.isArray(value) ? value[0] : value
    }))

    console.log('Extracted error details:', errors, parentForm?.current)

    // Update form state with error information
    if (parentForm?.current) {
      parentForm.current.setState({ errors });
      
      // parentForm.current?.updater?.enqueueSetState
    }

    // Display individual error messages with a slight delay to prevent overlap
    errors.forEach((error) => {
      toast.error(error.message);
    });
  }

  /**
   * Handle actions after form submission
   *
   * @param action - The action configuration
   * @param formData - The form data
   */
  const handleAfterSubmit = (action: Action, formData: any) => {
    const { type, redirect, refetch, mode = "society" } = action;

    if (type === "submit_and_new" || type === "reset") {
      parentForm?.current?.reset?.();
    }

    if (redirect) {
      router.push(replacePlaceholders(ensureAdminModeInPath(redirect, mode), formData));
    } else if (type === "cancel" && pathname && pathname.includes("/form/")) {
      // Only back if no explicit redirect is provided
      router.back();
    }

    if (refetch) {
      setTimeout(() => queryClient.refetchQueries(), 500);
    }
  };

  /**
   * Handle form actions (submit, reset, cancel, download, view)
   *
   * @param action - The action configuration
   */
  const handleAction = async (action: Action) => {
    setLoading(true)

    const raw = parentForm?.current?.state.formData || {}
    const formData = processFormData(raw, parentForm.current.state.uiSchema)

    // For debugging purposes
    console.log(raw, formData)

    // Handle non-API actions immediately
    if (action.type === "cancel" || action.type === "reset" || (action.type === "submit" && !action?.api_path)) {
      handleAfterSubmit(action, formData);
      setLoading(false);

      return;
    }

    // Validate form before proceeding
    if (!parentForm?.current?.validateForm?.()) {
      setLoading(false);

      return;
    }

    // Merge default parameters with form data
    // Use type assertion to avoid TypeScript error
    const defaultParams = mergeDefaultParams(
      (action.default_params || []) as Array<string | Record<string, any>>,
      formData
    )

    try {
      if (action.type === "download" || action.type === "view") {
        // Handle download/view actions
        let tab: Window | null = null

        if (action.type === "view") {
          tab = window.open(`${process.env.NEXT_PUBLIC_BASE_URL}/pdf.html`, '_blank');
          if (!tab) throw new Error("Please allow pop-ups")
        }

        // Make API request with toast notification and validateStatus: true
        const result = await withToast(action.type, () =>
          axios.get(buildUrl(action.api_path || '', formData), {
            withCredentials: true,
            validateStatus: () => true, // Accept all status codes and handle them manually
          })
        )

        // Check if the request resulted in an error (network error, etc.)
        if (result.error) {
          // Error already handled by withToast
          return;
        }

        // Check if the API response indicates an error
        if (result?.data?.status === 'error' || result?.status >= 400) {
          // Handle API-level errors in the response
          if (result?.data?.meta?.errors && Object.keys(result.data.meta.errors).length > 0) {
            // Use handleErrorResponse to display detailed error messages
            handleErrorResponse(result.data);
          } else {
            // If no detailed errors, show the generic error message
            toast.error(result?.data?.message || `Error: ${result?.status} ${result?.statusText}`);
          }

          return;
        }

        // Extract data from the response
        const { data } = result;

        // Get the PDF from the URL
        const pdf = await axios.get(data?.data?.url, { responseType: "blob" })

        const fileBlob = new Blob([pdf.data], {
          type: action.type === 'download'
            ? 'application/octet-stream'
            : 'application/pdf',
        });

        const blobUrl = URL.createObjectURL(fileBlob)

        if (action.type === "download") {
          // Handle download action
          const a = document.createElement("a");

          a.href = blobUrl;
          a.download = `${action?.title || "file"}.pdf`;
          a.click();
        } else {
          // Handle view action
          const pdfContainer = tab!.document.getElementById('pdfContainer');

          if (!pdfContainer) throw new Error("PDF container not found")
          pdfContainer.innerHTML = `<object data="${blobUrl}" type="application/pdf" style="width:100%; height:100%;"></object>`;
          const pdfObject = tab!.document.querySelector('object');

          pdfObject?.addEventListener('load', () => {
            // Update the document title when the PDF is loaded.
            tab!.document.title = 'Your PDF is ready';

            const overlay = tab!.document.getElementById('spinnerOverlay');

            if (overlay) {
              overlay.style.display = 'none';
            }
          });
          handleAfterSubmit(action, formData);
        }
      } else {
        // Handle form submission actions
        const method = action?.method || (!!formData.id ? "PUT" : "POST")

        // Make API request with toast notification and validateStatus: true to prevent axios from throwing on non-2xx status
        const result = await withToast(action.type, () =>
          axios({
            url: buildUrl(`/admin/${action.api_path || ''}`, formData),
            method,
            data: { ...(formData as Record<string, any>), ...(defaultParams as Record<string, any>) },
            withCredentials: true,
            validateStatus: () => true, // Accept all status codes and handle them manually
          })
        )

        // Check if the request resulted in an error (network error, etc.)
        if (result.error) {
          // Error already handled by withToast
          return;
        }

        // Check if the API response indicates an error
        if (result?.data?.status === 'error') {
          // Handle API-level errors in the response
          if (result?.data?.meta?.errors && Object.keys(result.data.meta.errors).length > 0) {
            // Use handleErrorResponse to display detailed error messages and update form state
            handleErrorResponse(result.data);
          } else {
            // If no detailed errors, show the generic error message
            toast.error(result?.data?.message || `Error: ${result?.status} ${result?.statusText}`);
          }

          return;
        }

        // Handle successful response
        if (result?.data?.status === "success" || (result?.status >= 200 && result?.status < 300)) {
          handleAfterSubmit(action, formData);
        }
      }
    } catch (err: any) {
      // This catch block should only handle unexpected errors not caught by withToast
      console.log("Unexpected error in Action.tsx", err);
      toast.error(err?.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }

  return (
    <Box display="flex" justifyContent="end" my={2} mb={2}>
      {actions.slice().sort((a, b) => {
        const priorityOrder: Record<string, number> = {
          submit: 0,
          submit_and_new: 1,
          view: 2,
          download: 3,
          reset: 4,
          cancel: 5,
        };

        const aPriority = priorityOrder[a.type] ?? Number.MAX_SAFE_INTEGER;
        const bPriority = priorityOrder[b.type] ?? Number.MAX_SAFE_INTEGER;

        return aPriority - bPriority;
      }).map(a => (
        <LoadingButton
          key={a.title}
          sx={{ mr: 1 }}
          variant={(a?.variant ? validateVariant(a.variant) : ACTION_CONFIG[a.type]?.variant) || "outlined"}
          color={(a?.color ? validateColor(a.color) : ACTION_CONFIG[a.type]?.color) || 'primary'}
          {...((a?.icon && typeof a?.icon === 'string') && { startIcon: <Icon icon={a.icon} /> })}
          loading={loading}
          onClick={e => { e.preventDefault(); handleAction(a) }}
        >
          {a.title}
        </LoadingButton>
      ))}
    </Box>
  )
}
