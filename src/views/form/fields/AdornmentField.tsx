import { FormControl, TextField } from "@mui/material";
import { descriptionId, getTemplate, getUiOptions } from '@rjsf/utils'
import type { FormContextType, RJSFSchema, StrictRJSFSchema } from '@rjsf/utils'

import Adornment from "../components/Adornment";

function AdornmentField<T = any, S extends StrictRJSFSchema = RJSFSchema, F extends FormContextType = any>(props) {
  const { onChange, disabled, label, schema, required, formData, uiSchema, registry, idSchema, rawErrors, formContext } = props;

  // console.log(formData)

  const description = uiSchema?.['ui:description'] || schema?.description || "";
  const options = getUiOptions<T, S, F>(uiSchema);
  const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, options)

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    let value: number | string = event.target.value;
    const isValidNumber = !isNaN(parseFloat(value)) && isFinite(value as any);

    if (options?.type === "number" && isValidNumber) {
      value = parseFloat(value) || 0;
    }

    onChange(value);
  };

  const updateKey = (key: string, value: any = false) => {
    if (!key) return;

    const state = formContext?.formRef?.current?.state?.formData;

    formContext?.formRef?.current?.onChange({ ...state, [key]: value });

    // formContext.updateKey(key, value);
    // if (typeof formData === "object") {
    //   onChange({ ...formData, [key]: value });
    // } else {
    //   onChange({ value: formData, [key]: value });
    // }
  }

  // console.log({value}, formData)

  const adornments = {
    ...(options?.startAdornment && Array.isArray(options?.startAdornment) && {
      startAdornment: (
        <>
          {options?.startAdornment?.filter(item => item?.hidden !== true)?.map((adornment, index) => (
            <Adornment
              key={index}
              adornment={adornment}
              position="start"
              updateKey={updateKey}
            />
          ))}
        </>
      ),
    }),
    ...(options?.endAdornment && Array.isArray(options?.endAdornment) && {
      endAdornment: (
        <>
          {options?.endAdornment?.filter(item => item?.hidden !== true)?.map((adornment, index) => (
            <Adornment
              key={index}
              adornment={adornment}
              position="end"
              updateKey={updateKey}
            />
          ))}
        </>
      ),
    }),
  };

  return (
    <FormControl fullWidth={true} required={required}>
      <TextField
        label={label}
        id={`input_${label}`}
        InputProps={adornments}
        value={formData?.value || formData}
        onChange={handleChange}
        disabled={disabled}
        defaultValue={schema?.default ?? ""}
        placeholder={uiSchema?.['ui:placeholder'] || ""}
        required={required}
        error={!!rawErrors?.length}
        helperText={<DescriptionFieldTemplate
          id={descriptionId(idSchema)}
          description={description}
          schema={schema}
          uiSchema={uiSchema}
          registry={registry}
        />}
        type={["string", "number"].includes(String(options?.type) || "string")
          ? (options?.type === "number" ? "number" : "text")
          : "text"}
      />
    </FormControl>
  );
}

;

export default AdornmentField;
