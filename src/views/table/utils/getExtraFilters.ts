// import { TableSchema } from "../types/tableTypes";

export const getExtraFilters = (table: any): any[] => {
  const extraFilters = table?.extraFilters || [];
  
  if (Array.isArray(extraFilters)) return extraFilters;

  return Object.entries(extraFilters)
    .map(([key, value]) => {
      if (typeof value === "object") {
        return { key, ...value };
      }
      
      if (typeof value === "string") {
        return { key, title: value };
      }

      return null;
    })
    .filter(Boolean);
};
