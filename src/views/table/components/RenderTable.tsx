'use client';

import React, { useState, useCallback } from 'react';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import { MaterialReactTable } from 'material-react-table';
import { Alert, Box, darken, lighten } from '@mui/material';

import type {
  MRT_ColumnFiltersState,
  MRT_PaginationState,
  MRT_RowSelectionState,
  MRT_SortingState,
} from 'material-react-table';

import useTableSchema from '../hooks/useTableSchema';
import useTableHandlers, { type TableHandlers } from '../hooks/useTableHandlers';
import { getPerTableBindings, type PerTableBindings } from '../hooks/getPerTableBindings';

import TopToolbarCustomActions from '../TopToolbarCustomActions';
import RenderRowActions from '../actions/row/RowActions';
import type { ApiResponse } from '../types/tableTypes';
import { stripAdminScopedPath } from '@/utils/string';
import { Loader } from '@/components/Loader';

interface RenderTableProps {
  isGate?: boolean;
  customURL?: string | null;
  rows?: ApiResponse['data']['data'];
  schema?: ApiResponse['data']['meta'] | null;
  onSelectionChange?: (selectedRows: any[]) => void;
}

const RenderTable: React.FC<RenderTableProps> = ({
  isGate = false,
  customURL = null,
  rows = [],
  schema: defaultSchema = null,
  onSelectionChange,
}) => {
  // URL / router
  const pathName = usePathname();
  const asPath = stripAdminScopedPath(pathName);
  const searchParams = useSearchParams();
  const { replace } = useRouter();

  // Controlled state
  const [columnFilters, setColumnFilters] = useState<MRT_ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');

  const [extraFilters, setExtraFilters] = useState<Record<string, unknown>>(
    () => Object.fromEntries(searchParams?.entries()) || {}
  );

  const [sorting, setSorting] = useState<MRT_SortingState>([]);

  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [rowSelection, setRowSelection] = useState<MRT_RowSelectionState>(() =>
    rows.reduce((acc, row: any) => {
      acc[String(row.id)] = true;

      return acc;
    }, {} as MRT_RowSelectionState)
  );

  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // Tabs
  const [currentTab, setCurrentTab] = useState<string | null>(
    searchParams?.get('current_tab') || null
  );

  // Query key
  const queryKey = [
    pagination,
    sorting,
    columnFilters,
    globalFilter,
    currentTab,
    extraFilters,
    asPath,
    defaultSchema,
    rows,
  ] as const;

  // Fetch schema & data
  const { data: schema, isRefetching, isPending, refetch, isError } = useTableSchema({
    customURL,
    isGate,
    currentTab,
    asPath,
    globalFilter,
    columnFilters,
    sorting,
    extraFilters,
    pagination,
    schema: defaultSchema,
    rows,
  });

  // Save handlers hook
  const handlers: TableHandlers = useTableHandlers({
    queryKey,
    asPath,
    isGate,
    currentTab,
    customURL,
    refetch,
    schema: schema || null,
    setIsEditing,
  });

  // Tab change
  const onTabChange = useCallback(
    (_: React.SyntheticEvent, newValue: string) => {
      const params = new URLSearchParams();

      params.set('current_tab', newValue);
      setCurrentTab(newValue);
      setColumnFilters([]);
      setGlobalFilter('');
      setExtraFilters({});
      setSorting([]);
      setPagination((p) => ({ ...p, pageIndex: 0 }));
      replace(`?${params.toString()}`);
    },
    [replace]
  );

  if (isError) return <Alert severity="error">
    {schema?.message || 'Error fetching data'} </Alert>

  if (!schema) return <Loader />

  return (
    <Box
      sx={{
        display: 'grid',
        gridTemplateColumns: {
          xs: 'auto',
          lg:
            schema.tableDirection === 'column'
              ? 'auto'
              : schema.columns.map(() => '1fr').join(' '),
        },
        gap: '2rem',
        overflow: 'auto',
      }}
    >
      {schema.columns.map((_, idx) => {
        // Bind per-table data + handlers via pure function
        const {
          rows: dataRows,
          editedRows,
          columns,
          setRows,
          updateEditedRows,
          handleSave,
          handleReset,
          handlePage,
          handleCell,
          handleRowDrag,
          handleEditRowSave
        }: PerTableBindings = getPerTableBindings(
          idx,
          schema,
          handlers,
          setRowSelection,
          setPagination,
          setIsSaving
        );

        return (
          <MaterialReactTable
            key={idx}
            data={
              schema.isEditable && schema.editMode !== 'row'
                ? editedRows
                : dataRows
            }
            columns={columns}
            displayColumnDefOptions={{
              'mrt-row-select': { size: 30, grow: false },
              'mrt-row-expand': { size: 50, grow: false },
              'mrt-row-actions': {
                grow: false,
                size: schema?.rowActions?.[idx]?.length ?
                  schema?.rowActions?.[idx]?.length <= 3
                    ? schema?.rowActions?.[idx]?.length * 40
                    : Math.ceil(schema?.rowActions?.[idx]?.length / 2) * 40 : 0
              },
            }}
            getRowId={(r) => r?.id}
            enableEditing={schema.isEditable}
            editDisplayMode={schema.editMode}
            createDisplayMode="row"

            // Cell edits
            onEditingCellChange={handleCell}
            onEditingRowSave={handleEditRowSave}

            // Row ordering & dragging
            enableRowOrdering={!!schema.orderable}
            autoResetPageIndex={!!schema.orderable}
            muiRowDragHandleProps={(meta) => {
              const tableInst = meta?.table;

              if (!tableInst) return {};

              return {
                onDragEnd: () => {
                  const { draggingRow, hoveredRow } = tableInst.getState();

                  if (draggingRow && hoveredRow) {
                    handleRowDrag(
                      draggingRow,
                      hoveredRow,
                      schema.orderable as string
                    );
                  }
                },
              };
            }}

            // Pagination / Filtering / Sorting
            manualPagination
            manualFiltering
            filterFromLeafRows
            paginationDisplayMode="pages"
            onPaginationChange={handlePage}
            muiPaginationProps={{
              variant: 'outlined',
              color: 'primary',
              showRowsPerPage: true,
              rowsPerPageOptions: [5, 10, 20, 50],
            }}

            onSortingChange={setSorting}
            onGlobalFilterChange={setGlobalFilter}
            onColumnFiltersChange={(f) => {
              setPagination((p) => ({ ...p, pageIndex: 0 }));
              setColumnFilters(f);
            }}

            // Table state
            state={{
              isSaving,
              isLoading: isPending,
              pagination,
              columnFilters,
              globalFilter,
              sorting,
              rowSelection,
              showProgressBars: isRefetching,
              columnOrder: [
                'mrt-row-numbers',
                'mrt-row-select',
                'mrt-row-expand',
                'mrt-row-pin',
                'mrt-row-drag',
                ...(columns?.map((col) => col?.id || col?.accessorKey) || []),
                'mrt-row-actions'
              ]
            }}
            initialState={{
              density: 'compact',
              showGlobalFilter: true,
              showColumnFilters: false,
              expanded: true,
            }}


            // Full toolbar override
            renderTopToolbar={({ table }) => (
              <TopToolbarCustomActions
                table={table}
                tableIndex={idx}
                schema={schema}
                setRows={setRows}
                setEditedRows={updateEditedRows}
                setIsEditing={setIsEditing}
                handleSaveChanges={handleSave}
                handleResetChanges={handleReset}
                rowSelection={rowSelection}
                isEditing={isEditing}
                loading={isPending || isRefetching || isSaving}
                refetch={refetch}
                isGate={isGate}
                extraFilters={extraFilters}
                setExtraFilters={setExtraFilters}
                handleTabChange={onTabChange}
                currentTab={currentTab}
                handleResetFilters={() => {
                  setColumnFilters([]);
                  setGlobalFilter('');
                  setExtraFilters({});
                  setSorting([]);
                  setPagination((p) => ({ ...p, pageIndex: 0 }));
                }}
                columnFilters={columnFilters}
                totalTables={schema.totalTables}
              />
            )}

            // Row actions
            enableRowActions={!!schema.rowActions?.[idx]?.length}
            renderRowActions={({ row, table }) => (
              <RenderRowActions
                row={row.original}
                actions={schema.rowActions?.[idx] || []}
                setEditedRows={updateEditedRows}
                setIsEditing={setIsEditing}
                table={table}
                refetch={refetch}
              />
            )}

            // Selection
            enableRowSelection={schema.multiSelect}
            {...(schema?.enableSelectOn && {
              enableRowSelection: (row: any): boolean =>
                row?.original?.[schema?.enableSelectOn?.key] === schema?.enableSelectOn?.value
            })}
            onRowSelectionChange={(u) => {
              const sel = typeof u === 'function' ? u(rowSelection) : u;

              setRowSelection(sel);
              onSelectionChange?.(Object.keys(sel));
            }}

            enableExpanding={schema?.rows?.[idx]?.some((row: any) => row?.rows || row?.children)}
            getSubRows={(row: any) => row?.rows || row?.children || []}

            // Misc
            rowCount={schema.total}
            manualSorting
            muiTableBodyProps={{
              sx: (theme) => ({
                '& tr:nth-of-type(odd) > td': {
                  backgroundColor: darken(theme.palette.background.paper, 0.04),
                },
                '& tr:nth-of-type(even) > td': {
                  backgroundColor: lighten(theme.palette.background.paper, 0.04),
                },
                '& tr:hover > td': {
                  backgroundColor: 'rgba(255,164,0,0.05)',
                },
              }),
            }}

            // enableBottomToolbar={
            //   schema.totalTables > 1
            //     ? idx + 1 === schema.totalTables
            //     : true
            // }
            enableStickyHeader
            layoutMode="grid"
          />
        );
      })}
    </Box>
  );
};

export default RenderTable;
