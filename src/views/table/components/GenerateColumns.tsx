
import Image from 'next/image';
import NextLink from 'next/link';

import Link from '@mui/material/Link';


import axios from 'axios';
import { Box, Checkbox, Chip, darken, lighten, MenuItem, Switch, Typography } from '@mui/material';
import { toast } from 'react-toastify';

import { replacePlaceholders } from '@/utils/replacePlaceholders';
import NestedOrSelect, { getItemById } from '../NestedOrSelectColumn';
import { ensureAdminModeInPath } from '@/utils/string';

// import { DependentSelectEdit } from './DependentSelect';

const getWithCurrency = ({ amount = 0, currency = null }) => {
  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,

    ...(currency && { style: 'currency', currency, minimumFractionDigits: 2 }),
  });

  return formatter.format(amount);
}

export const GenerateColumnsForMRT = (
  { columns = [], fields = [], select_by = {}, filter_by = {} },
  refetch,
) => {
  // Handle the case when columns is not provided or is empty

  if (!columns || (Array.isArray(columns) && columns.length === 0)) {
    console.warn('No columns provided to GenerateColumnsForMRT');

    return [];
  }

  // Check if we have a multi-dimensional array of columns (multiple tables)
  // This handles both [[col1, col2], [col3, col4]] and [col1, col2, col3] formats
  const isMultiTable = Array.isArray(columns) && columns.length > 0 && Array.isArray(columns[0]);

  // Convert to a standard format: array of column arrays
  // If it's already a multi-dimensional array, use it as is
  // If it's a single array, wrap it in another array to make it multi-dimensional
  const columnsArray = isMultiTable ? columns : [columns];

  // Process each table's columns
  return columnsArray.map((columnGroup) => {
    // Handle empty or invalid column groups

    if (!Array.isArray(columnGroup) || columnGroup.length === 0) {
      console.warn('Invalid column group:', columnGroup);

      return [];
    }

    // Add a wrapper to capture and log the result
    const processedColumns = (() => {

    return columnGroup.reduce((acc, item) => {
    const {
      key,
      title,
      type,
      sortable = false,
      filterable = false,
      options = {},
      editable = false,
      parentKey,
      childrenKey,
      min = 0,
      max = Number.MAX_SAFE_INTEGER,
      required = true,
      width = 0,
      ...rest
    } = item;

    // If fields is not specified or contains '*', include all columns
    // Otherwise, only include columns whose keys are in the fields array
    if (fields && fields.length > 0 && fields[0] !== '*' && (!key || !fields.includes(key))) {
      console.log(`Skipping column with key "${key}" because it's not in fields:`, fields);

      return acc;
    }

    const defaultMap = {
      string: '',
      number: 0,
      boolean: false,
      tree: null,
      select: null,
      colored: null,
      switch: false,
      checkbox: false,
      date: null,
      dropdown: null,
      image: null,
      link: null,
      actions: null,
    };

    const common = {
      accessorKey: key,
      id: key,
      header: title || key,
      Header: () => <Typography
        key={key}
        variant='subtitle2'
        sx={(theme) => ({
          fontWeight: 'bold',
          color: theme.palette.text.primary,
          whiteSpace: "normal",  // Allows wrapping
          wordBreak: "break-word", // Breaks words if needed
          overflowWrap: "break-word",
        })}
      >
        {title || key}
      </Typography>,
      meta: {
        type,
        default: item?.default ?? defaultMap[type] ?? '',
        ...(item?.dependsOn && { dependsOn: item.dependsOn }),
      },
      minSize: 100,

      size: 100 / (columnGroup?.length || 1),
      grow: true,

      ...(width && { size: width, grow: false }),
      enableColumnActions: false,
      enableSorting: sortable,

      // Check if key exists in select_by or filter_by

      enableColumnFilter: filterable ||
        (key && select_by && typeof select_by === 'object' && key in select_by) ||
        (key && filter_by && typeof filter_by === 'object' && key in filter_by),
      enableEditing: editable,

      // grow: true,
      ...(key && filter_by && typeof filter_by === 'object' && key in filter_by &&
          filter_by[key]?.options && Object.entries(filter_by[key]?.options)?.length && {
        filterSelectOptions: Object.entries(filter_by[key]?.options)?.map(([value, label]) => ({
          label,
          value,
        })),
        filterVariant: 'multi-select',
      }),
      muiTableHeadCellProps: {
        align: 'center',
      },
      muiTableBodyCellProps: ({ row }) => ({
        sx: ({ palette, direction }) => {
          const isDarkMode = palette.mode === "dark";

          const adjustColor = (color, factor) =>
            isDarkMode ? darken(color, factor) : lighten(color, factor);

          // Extract properties for clarity
          const { _error, _action } = row?.original || {};

          const getBackgroundColor = () => {
            if (_error?.[key]) {
              return adjustColor(palette.error.light, 0.6);
            }

            switch (_action) {
              case "delete":
                return adjustColor(palette.error.main, 0.6);
              case "edit":
                return adjustColor(palette.info.main, 0.6);
              case "add":
                return adjustColor(palette.success.main, 0.6);
              default:
                return row?.index % 2 === 0
                  ? adjustColor(palette.action.hover, 0.5)
                  : "transparent";
            }
          };

          // Utility for text alignment based on direction
          const getAlignment = (rtl = true) => (rtl ? "right" : "left");

          return {
            borderRight: `1px solid ${palette.divider}`,
            color: palette.text.primary,
            backgroundColor: getBackgroundColor(),
            textAlign: getAlignment(direction === "rtl"),
            align: getAlignment(direction === "rtl"),
            ...(type === "number" && {
              textAlign: getAlignment(direction !== "rtl"),
              align: getAlignment(direction !== "rtl"),
            }),
            whiteSpace: "normal", // Allows wrapping
            wordBreak: "break-word", // Breaks words if needed
            overflowWrap: "break-word",
          };
        },
      })

    };

    const commonEditProps = ({ row, table, cell }) => ({
      ...(table?.options?.editDisplayMode !== 'row' && { value: cell.getValue() }),

      // value: cell.getEditingValue() ?? '',

      error: !!row.original?._error?.[key],
      helperText: row.original?._error?.[key],
      required,
      ...(table && cell && {
        onBlur: () => {
          if (required && (row.original?.[key] === null || row.original?.[key] === undefined)) {
            table.setEditingCell(cell.row.id, '_error', { ...row.original?._error, [key]: 'This field is required' });
          } else {
            table.setEditingCell(cell.row.id, '_error', { ...row.original?._error, [key]: null });
          }
        },
      }),

      // ...(table && cell && row.original?._error && row.original?._error[key] && {
      //   onFocus: () => {
      //     table.setEditingCell(cell.row.id, '_error', { [key]: null });
      //   },
      // }),
      ...(table && cell && {
        onChange: (e) => {
          table.setEditingCell(cell.row.id, cell.column.id, e.target.value)
        },
      }),
    });

    switch (type) {
      case 'colored':
        const colorMap = {
          success: 'green',
          error: 'tomato',
          warning: 'orange',
          info: 'dodgerblue',
        };

        return [
          ...acc,
          {
            ...common,
            Header: () => (
              <>
                {Object.entries(options || rest?.colored).map(([key, value]: any) => {
                  return (
                    <Typography
                      key={key}
                      variant='caption'
                      color={value?.color || "inherit"}
                      sx={{ fontWeight: 'bold' }}
                    >
                      {value?.title || key}
                    </Typography>
                  )
                })}
              </>
            ),
            Cell: ({ row }) => {
              const isNumber = Object.values(options || rest?.colored).some((value: any) => {
                return value?.type && value?.type === 'number'
              })

              return <Typography align={isNumber ? 'right' : 'left'}>
                {Object.entries(options || rest?.colored).map(([key, value]: any) => {
                  return (
                    <Typography
                      key={key}
                      variant="caption"
                      color={colorMap?.[value?.color] || "inherit"}
                      component='span'
                    >
                      {value?.type === 'number' ? getWithCurrency({ amount: row?.original?.[key], currency: value?.currency }) : (value?.value ?? row?.original?.[key])}
                    </Typography>
                  )
                })}
              </Typography>
            },
          },
        ];

      case 'chip':
        const maxLabelLength = Math.max(...Object.values(options).map(({ title }) => title?.length));
        const maxWidth = `${maxLabelLength * 15}px`; // Adjust multiplier for font size and padding

        return [
          ...acc,
          {
            ...common,
            Cell: ({ row }) => (
              row.original[key] !== undefined && row.original[key] !== null && row.original[key] !== "" ? (
                <Chip
                  label={options[row.original[key]]?.title || row.original[key]?.toString()}
                  color={options[row.original[key]]?.color || 'default'}
                  sx={{
                    width: maxWidth, // Apply fixed width
                    textAlign: 'center', // Center text
                    fontSize: '0.7rem', // Consistent font size
                    overflow: 'hidden', // Prevent overflow
                    whiteSpace: 'nowrap', // Prevent text wrapping
                    textOverflow: 'ellipsis', // Handle overflow gracefully
                  }}
                  variant='filled'
                />
              ) : null
            ),
            editVariant: 'select',
            editSelectOptions: Object.keys(options).map((key) => ({
              label: options[key].title,
              value: key,
            })),
            muiEditTextFieldProps: ({ table, cell, row }) => ({
              select: true,
              ...commonEditProps({ row, table, cell }),
            })
          },
        ];

      case 'image':
        return [
          ...acc,
          {
            ...common,
            enableSorting: false,
            enableFiltering: false,
            Cell: ({ row }) => (
              row.original[key] ? (
                <Image src={row.original[key]} alt={key} width={50} height={50} loading="lazy" />
              ) : null
            ),
          },
        ];

      case 'link':
        return [
          ...acc,
          {
            ...common,
            Cell: ({ row }) => {
              if (item?.hide_on) {
                for (const [hide_key, value] of Object.entries(item?.hide_on)) {
                  if (Array.isArray(value) && value.includes(row.original[hide_key])) {
                    return row.original[key]
                  } else if (row.original[hide_key] === value) {
                    return row.original[key]
                  }
                }
              }

              const url = replacePlaceholders(rest?.form ? ensureAdminModeInPath(`/admin/form/${rest?.form}`, "society") : ensureAdminModeInPath(rest?.href, rest?.mode || "society"), row?.original);

              if (!url) return <Typography color='info'>
                {row.original[key]}
              </Typography>

              return (
                <Link
                  href={url}
                  component={NextLink}
                  color="info.dark"
                  underline="hover"
                >
                  {row.original[key]}
                </Link>
              );
            },
            Edit: ({ cell, row, column, table }) => {

              const handleChange = (event) => {
                table.options.onCellEdit?.({
                  row,
                  column,
                  value: event.target.value, // Pass the updated
                });
              }

              return (
                <input
                  value={cell.value}
                  onChange={handleChange}
                />
              )
            },
          },
        ];

      case 'actions':
        return [
          ...acc,
          {
            actions: item?.actions,
          },
        ];

      case 'switch':
        return [
          ...acc,
          {
            ...common,

            // size: 100,
            Cell: ({ row }) => (
              <Switch
                checked={!!row.original[key]}
                onChange={async () => {
                  const newValue = !row.original[key];
                  const toastId = toast.loading('Updating...');

                  try {
                    const response = await axios
                      .patch(
                        replacePlaceholders(rest?.api?.url, row?.original),
                        { status: newValue ? 1 : 0 },
                        {
                          baseURL: process.env.NEXT_PUBLIC_API_URL,
                          withCredentials: true,
                        }
                      )

                    const isSuccess = response?.data?.status_code === 200;

                    toast.update(toastId, {
                      type: isSuccess ? 'success' : 'error',
                      render: response?.data?.message || (isSuccess ? 'Updated successfully' : 'Failed to update'),
                      isLoading: false,
                      autoClose: (process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000) as number
                    });

                    if (isSuccess) refetch();
                  } catch (err) {
                    toast.update(toastId, {
                      type: 'error',
                      render: err?.response?.data?.message || 'Failed to update',
                      isLoading: false,
                      autoClose: (process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000) as number
                    });
                  }
                }}
                color={row.original[key] ? 'success' : 'error'}
              />
            ),
            editVariant: 'checkbox',
            filterVariant: 'checkbox',
            filterSelectOptions: Object.keys(options).map((key) => ({
              label: key === '1' ? 'Active' : 'Inactive',
              value: key,
            })),
            editSelectOptions: Object.keys(options).map((key) => ({
              label: key === '1' ? 'Active' : 'Inactive',
              value: key,
            })),
            muiEditTextFieldProps: ({ cell, table, row }) => ({
              select: true,
              ...commonEditProps({ row, table, cell }),
              onChange: (e) => {
                table.setEditingCell(cell.row.id, cell.column.id, !!e.target.value)
              }
            }),
          },
        ];

      case 'chip_with_string':
        return [
          ...acc,
          {
            ...common,
            Cell: ({ row }) => {
              return (
                <Box display="flex" alignItems="center" gap={1}>
                  <Typography>{row.original[key]}</Typography>
                  {row.original?.is_rcm === 1 && (
                    <Chip
                      label="RCM"
                      color="info"
                      size="small"
                    />
                  )}
                </Box>
              );
            },
          },
        ];

      case 'checkbox':
        return [
          ...acc,
          {
            ...common,
            filterVariant: 'checkbox',
            size: 50,
            grow: false,

            // editSelectOptions:
            Cell: ({ cell }) => (
              <Checkbox
                checked={!!cell.getValue()}
                title={cell.getValue() ? "Active" : "Inactive"}
                disabled // Makes the checkbox non-clickable
              />
            ),

            // use Checkbox for edit
            Edit: ({ cell, table }) => {
              const handleChange = (event) => {
                table.setEditingCell(cell.row.id, cell.column.id, event.target.checked);
              };

              return (
                <Checkbox
                  checked={!!cell.getValue()} // Checkbox state based on cell value
                  onChange={handleChange}
                  title={cell.getValue() ? "Active" : "Inactive"}
                />
              );
            },
          },
        ];

      case 'tree':
        return [
          ...acc,
          {
            ...common,
            Cell: ({ row }) => (
              <Typography>
                {getItemById(options, row.original[key])?.label}
              </Typography>
            ),
            Edit: ({ cell, table }) => {
              const currentValue = cell.getValue() ?? null;

              return (
                <NestedOrSelect
                  data={options}
                  value={currentValue}
                  label="Pick an item"
                  table={table}
                  cell={cell}
                  isRequired={required}
                />
              );
            },
          },
        ];

      case 'select':
      case 'dropdown':
        return [
          ...acc,
          {
            ...common,
            Cell: ({ row }) => (
              <Typography>
                {options?.find((option) => option.value === row.original?.[key])?.label}
              </Typography>
            ),
            editVariant: 'select',
            editSelectOptions: options,
            muiEditTextFieldProps: ({ cell, table, row }) => ({
              select: true,
              onChange: (e) => {
                table.setEditingCell(cell.row.id, cell.column.id, e.target.value)

                if (childrenKey) {
                  const validChilColumnOptions = table.getAllColumns().find(column => column.id === childrenKey)?.columnDef?.editSelectOptions

                  if (Array.isArray(validChilColumnOptions) && validChilColumnOptions?.length) {
                    const validChilColumn = validChilColumnOptions.filter(option => option._parent === e.target.value)

                    if (validChilColumn?.length) {
                      table.setEditingCell(cell.row.id, childrenKey, validChilColumn[0].value)
                    } else {
                      table.setEditingCell(cell.row.id, childrenKey, null)
                    }
                  }
                }
              },
              children: options.map((option) => {
                const shouldDisable = !!parentKey && !!option?._parent && row?.original?.[parentKey] !== option?._parent;

                return (
                  <MenuItem
                    key={option.value}
                    value={option.value}
                    disabled={shouldDisable}
                  >
                    {option.label}
                  </MenuItem>
                );
              }),
              ...commonEditProps({ row, table, cell }),
            }),
            filterVariant: 'multi-select',
            filterSelectOptions: options,
          },
        ];

      case 'number':
        return [
          ...acc,
          {
            ...common,
            accessorFn: (row) => {

              const formatter = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 0,
                ...(item?.currency && { style: 'currency', currency: item.currency, minimumFractionDigits: 2 }),
                maximumFractionDigits: 2,
              });

              const formattedValue = formatter.format(row?.[key] ?? 0);

              return formattedValue
            },
            muiEditTextFieldProps: ({ cell, table, row }) => ({
              type: 'number',
              min,
              max,
              ...commonEditProps({ row, table, cell }),
              onChange: (e) => {
                const value = e.target.value;

                // 1) Handle empty or just '-' (allow user to type negative sign)
                //    Store it as-is so the user can keep typing
                if (value === '' || value === '-') {
                  // If required, show error if it's empty (or just '-')
                  if (required && (value === '' || value === '-')) {
                    table.setEditingCell(cell.row.id, '_error', { ...row.original?._error, [key]: 'This field is required' });
                  } else {
                    table.setEditingCell(cell.row.id, '_error', { ...row.original?._error, [key]: null });
                  }

                  table.setEditingCell(cell.row.id, cell.column.id, value);

                  return;
                }

                // 2) Convert to Number, check if valid
                const num = Number(value);

                if (isNaN(num)) {
                  // It's not a valid number, e.g. user typed something weird like "--" or "5-"
                  table.setEditingCell(cell.row.id, '_error', {
                    ...row.original?._error,
                    [key]: 'Invalid number'
                  });
                  table.setEditingCell(cell.row.id, cell.column.id, value); // still store the raw input
                } else {
                  // 3) Valid number, check min/max/required
                  if (required && value === '') {
                    // Typically won't get here if we're already skipping empty above,
                    // but just in case
                    table.setEditingCell(cell.row.id, '_error', { ...row.original?._error, [key]: 'This field is required' });
                  } else if (num < min) {
                    table.setEditingCell(cell.row.id, '_error', { ...row.original?._error, [key]: `Minimum value is ${min}` });
                  } else if (num > max) {
                    table.setEditingCell(cell.row.id, '_error', { ...row.original?._error, [key]: `Maximum value is ${max}` });
                  } else {
                    // no errors
                    table.setEditingCell(cell.row.id, '_error', { ...row.original?._error, [key]: null });
                  }

                  // 4) Store the numeric value in the table state if it’s fully valid
                  //    (some folks prefer storing the string, but numeric is fine if you
                  //    also want automatic numeric behavior).
                  table.setEditingCell(cell.row.id, cell.column.id, num);
                }
              },
            }),
            ...(item?.footer && {
              Footer: () => {
                return (
                  <Typography variant='h6' sx={{ fontWeight: "bold" }} color='primary' align='right'>
                    {typeof item?.footer === "string" ? item?.footer : "Total"}
                  </Typography>
                );
              }
            }),
            ...(item?.aggregation && {
              Footer: ({ table }) => {
                const rows = table.getCoreRowModel().rows;

                const total = rows.reduce((acc, row) => {
                  // row.original will be the original data object
                  const amount = row.original?.[key] || 0;

                  return acc + amount;
                }, 0);

                const formattedTotal = getWithCurrency({ amount: total, currency: item.currency || null });

                return (
                  <Typography variant='h6' sx={{ fontWeight: "bold" }} color='primary' align='right'>
                    {typeof item?.aggregation === "string" ? item?.aggregation : "Total"}: {formattedTotal}
                  </Typography>
                );
              }
            }),
          },
        ];

      case 'date':
        return [
          ...acc,
          {
            ...common,
            muiEditTextFieldProps: ({ row, table, cell }) => ({
              ...commonEditProps({ row, table, cell }),
              type: 'date',
            }),
          },
        ];

      // case 'dependentSelect':
      //   return [
      //     ...acc,
      //     {
      //       ...common,
      //       Cell: ({ row }) => (
      //         <Typography>
      //           {options?.find((option) => option.value === row.original?.id)?.label}
      //         </Typography>
      //       ),
      //       editVariant: 'custom',
      //       Edit: DependentSelectEdit
      //     },
      //   ];

      default:
        return [
          ...acc,
          {
            ...common,
            muiEditTextFieldProps: ({ cell, table, row }) => ({
              minLength: min,
              maxLength: max,
              ...commonEditProps({ row, table, cell }),
            }),
            ...((options && Object.keys(options)?.length) && {
              Cell: ({ row }) => (
                row.original[key] !== undefined && row.original[key] !== null && row.original[key] !== "" ? (
                  options[row.original[key]]?.title || row.original[key]?.toString()
                ) : null
              ),
            })
          },
        ];
    }
  }, []);
    })();

    return processedColumns;
  });
};
