import axios from "axios";

type param =
  | string
  | number
  | boolean
  | null
  | undefined
  | param[]
  | { [key: string]: param };
type Params = { [key: string]: param };

const tableAxios = axios.create({
  validateStatus: (status) => status <= 500,
  ...axios.defaults,
  withCredentials: true,
  withXSRFToken: true,
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    ...axios.defaults.headers,
  },
  paramsSerializer: (params: Params) => {
    const { filters, sort, ...rest } = params;

    const filterParams = Object.entries(filters || {}).map(([key, value]) => {
      if (Array.isArray(value)) {
        return `filters[${key}]=${value.join(",")}`;
      }

      return `filters[${key}]=${value}`;
    });

    const sortParams = Object.entries(sort || {}).map(([key, value]) => {
      return `sort[${key}]=${value}`;
    });

    return [
      ...filterParams,
      ...sortParams,
      ...Object.entries(rest).map(([key, value]) => `${key}=${value}`),
    ].join("&");
  },
});

/** 
 * Updated Item interface:
 * - `value` is the unique key for each item (instead of `id`).
 */
export interface Item {
  value: string; // unique key
  label?: string;
  rows?: Item[];
  children?: Item[];
  _parent?: string;
}

type Column = {
  label: string;
  value: string;
  keys: string[];
};

/**
 * Transform backend data into a standardized shape:
 * - Sets `label` using `columns.keys` or `columns.label`
 * - Ensures each item has a unique `value` (previously was `id`).
 */
type ProcessOptions = (columns: Column, data: Item[]) => Item[];

const processOptions: ProcessOptions = (columns, data = []) => {
  return (
    data?.map((item: any) => ({
      ...item,
      label: !columns?.keys?.length
        ? item?.title || item?.label || item?.[columns?.label]
        : columns?.keys.map((key: string) => item?.[key]).join(" "),

      // Overwrite or generate a `value` if missing
      value:
        item?.id ||
        item?.value ||
        Math.random().toString(36).substring(2), // random fallback if no value
      // Recursively process children
      ...((item?.rows || item?.children) && {rows: processOptions(columns, item?.rows || item?.children)})
    })) || []
  );
};

function hasNestedRows(items: Item[] = []): boolean {
  for (const item of items) {
    if (item.rows && item.rows.length > 0) {
      return true;
    }

    if (item.rows && hasNestedRows(item.rows)) {
      return true;
    }
  }

  return false;
}

export const optionInterceptor = async (columns: any[]) => {
  return await Promise.all(
    columns.map(async (column) => {

      // Only process if type is "select" or "dropdown"
      if (["select", "dropdown"].includes(column.type)) {
        const api = column?.api_path || column?.apiPath;
        let processedOptions: Item[] = [];

        // if we have an API and it's not parameterized (":")
        if (api && !api.includes("/:")) {
          try {
            const apiResponse = await axios.get(api, {
              baseURL: process.env.NEXT_PUBLIC_API_URL,
              withCredentials: true,
            });

            processedOptions = processOptions(column, apiResponse?.data?.data || []);
          } catch (error) {
            console.warn(`Error fetching data from ${api}:`, error);

            // Fall back to column.options if API fails
            processedOptions = processOptions(column, column.options || []);
          }
        } else if (column.options) {
          // If no valid API path but already have options
          processedOptions = processOptions(column, column.options);
        }

        console.log("Processed Options", processedOptions);

        // Check if the options are nested
        if (hasNestedRows(processedOptions)) {
          column.type = "tree";
        } else {
          column.type = "select"; // or "dropdown" if you prefer
        }

        // Return updated column with processed options
        return {
          ...column,
          options: processedOptions,
        };
      }

      // If column.type is not "select" or "dropdown", return as-is
      return column;
    })
  );
};


/**
 * Main response interceptor to handle columns (fetch their "options" if needed).
 */
tableAxios.interceptors.response.use(async (response) => {
  try {
    console.log("Response Interceptor", response);
    const columns: any[] = response.data?.meta?.schema?.table?.columns;

    if (!columns?.length) return response;

    const apiResponsesWithIndex = await optionInterceptor(columns);

    return {
      ...response,
      data: {
        ...response.data,
        meta: {
          ...response.data?.meta,
          schema: {
            ...response.data?.meta?.schema,
            table: {
              ...response.data?.meta?.schema?.table,
              columns: apiResponsesWithIndex,
            },
          },
        },
      },
    };
  } catch (e) {
    console.log(e);

    return response;
  }
});

export default tableAxios.get;
