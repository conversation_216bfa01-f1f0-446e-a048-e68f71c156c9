import React, { useEffect, useState } from 'react';

import { TextField, MenuItem, CircularProgress } from '@mui/material';
import axios from 'axios';

import type { MRT_EditCellProps } from 'material-react-table';

type Meta = {
    optionsEndpoint: string;
    dependents: string[];
    paramsMapping: Record<string, string>;
    labelField?: string;
    valueField?: string;
};

export const DependentSelectEdit = <
    TData extends Record<string, any>
>({
    cell,
    column,
    row,
    table,
}: MRT_EditCellProps<TData>) => {

    console.log('DependentSelectEdit', { cell, column, row, table });

    const meta = column.columnDef.meta as Meta;
    const { optionsEndpoint, dependents = [], paramsMapping, labelField = 'name', valueField = 'id' } = meta;

    console.log(meta)

    // Build an object of current parent values
    const parentParams = dependents?.reduce((p, key) => {
        p[paramsMapping[key] || key] = row.original[key];

        return p;
    }, {} as Record<string, string>);

    const [options, setOptions] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);

    // Whenever any parent value changes, re-fetch
    useEffect(() => {
        // All dependents must be truthy
        if (dependents.every(k => row.original[k])) {
            setLoading(true);
            axios
                .get(optionsEndpoint, { params: parentParams })
                .then(r => setOptions(r.data))
                .finally(() => setLoading(false));
        } else {
            setOptions([]);
        }
    }, dependents.map(k => row.original[k]).concat(optionsEndpoint));

    return (
        <TextField
            select
            fullWidth
            value={cell.getValue() ?? ''}
            onChange={(e) =>
                table.setEditingCell(row.id, column.id, e.target.value)
            }
            disabled={loading || options.length === 0}
            helperText={
                loading
                    ? <CircularProgress size={16} />
                    : options.length === 0 && !dependents.every(k => row.original[k])
                        ? `Select ${dependents.join(', ')} first`
                        : undefined
            }
        >
            {options.map(opt => (
                <MenuItem key={opt[valueField]} value={opt[valueField]}>
                    {opt[labelField]}
                </MenuItem>
            ))}
        </TextField>
    );
};
