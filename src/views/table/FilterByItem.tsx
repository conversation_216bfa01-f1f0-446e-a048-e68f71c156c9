// FilterByItem.tsx
import type {
  JSXElementConstructor,
  ReactElement,
  ReactPortal,
} from "react"

import {
  MenuItem,
  Typography,
  Checkbox,
  FormControlLabel,
  Radio,
  RadioGroup,
} from "@mui/material"

export function FilterByItem(props: {
  value: {
    key: string
    title:
      | string
      | number
      | boolean
      | ReactElement<any, string | JSXElementConstructor<any>>
      | ReactPortal
      | null
      | undefined
    options: { [s: string]: unknown } | ArrayLike<unknown>
    select_single?: boolean
  }
  handleChange: (option: string, key: string, single: boolean) => void
  filters: Record<string, string[]>
}) {
  const { value, handleChange, filters } = props

  return (
    <>
      <MenuItem>
        <Typography variant="body2">{value.title}</Typography>
      </MenuItem>

      {value.select_single ? (
        <RadioGroup
          value={filters[value.key]?.[0] ?? ""}
          onChange={(e) =>
            handleChange(e.target.value, value.key, true)
          }
        >
          {Object.entries(value.options || {}).map(
            ([optionKey, optionValue]) => (
              <MenuItem key={optionKey} disableRipple>
                <FormControlLabel
                  value={optionKey}
                  control={<Radio color="secondary" />}
                  label={optionValue as string}
                />
              </MenuItem>
            )
          )}
        </RadioGroup>
      ) : (
        Object.entries(value.options || {}).map(
          ([optionKey, optionValue]) => (
            <MenuItem
              key={optionKey}
              onClick={() =>
                handleChange(optionKey, value.key, false)
              }
              disableRipple
            >
              <FormControlLabel
                control={
                  <Checkbox
                    checked={
                      filters[value.key]?.includes(optionKey) ?? false
                    }
                    name={optionValue as string}
                    color="secondary"
                  />
                }
                label={optionValue as string}
              />
            </MenuItem>
          )
        )
      )}
    </>
  )
}
