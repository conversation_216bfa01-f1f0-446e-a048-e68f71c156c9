import { useState } from 'react'

import { Icon } from '@iconify/react/dist/iconify.js'
import { FormControl, Grid2, IconButton, InputAdornment, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import type { SelectChangeEvent } from '@mui/material/Select'

const SelectBy = ({ handleSelectByChange, keysMap }) => {
  const [filterValue, setFilterValue] = useState('')
  const [selectByKey, setSelectByKey] = useState('')

  const handleSelectChange = (event: SelectChangeEvent<any>) => {
    setSelectByKey(event.target.value)
    setFilterValue('') // Reset search when select changes
  }

  const handleInputClear = () => {
    setFilterValue('')
    handleSelectByChange(selectByKey, '')
  }

  return (
    <Grid2 container spacing={1}>
      <Grid2 size="auto">
        <FormControl fullWidth size="small">
          <InputLabel id="selectBy">Select By</InputLabel>
          <Select
            labelId="selectBy"
            value={selectByKey}
            label="Select By"
            onChange={handleSelectChange}
            sx={{
              width: 150
            }}
          >
            {Object.entries(keysMap || {}).map(([key, value]) => (
              <MenuItem key={key} value={key}>
                {String(value)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid2>
      <Grid2 size="auto">
        <TextField
          placeholder={selectByKey ? `Search by ${keysMap[selectByKey]}` : "Search..."}
          fullWidth
          size="small"
          variant="outlined"
          disabled={!selectByKey}
          value={filterValue}
          onChange={(e) => setFilterValue(e.target.value)}
          slotProps={{
            input: {
              endAdornment: (
                <>
                  <InputAdornment position="end">
                    <IconButton size='small' onClick={handleInputClear} disabled={!filterValue}>
                      <Icon icon="ri-close-line" />
                    </IconButton>
                  </InputAdornment>
                  <InputAdornment position="end">
                    <IconButton size="small" disabled={!filterValue} onClick={() => handleSelectByChange(selectByKey, filterValue)}>
                      <Icon icon="ri-search-line" />
                    </IconButton>
                  </InputAdornment>
                </>
              )
            }
          }}
        />
      </Grid2>
    </Grid2>
  )
}

export default SelectBy
