import React, { useState } from 'react';
import { Box, Typography, Paper, Divider } from '@mui/material';
import CustomFilters from './CustomFilters';
import TestSimpleApiSelect from './SimpleApiSelect';

const TestPage: React.FC = () => {
  const [filters, setFilters] = useState({});
  const [loading, setLoading] = useState(false);

  // Test 1: Simple working API Select
  const [simpleValue, setSimpleValue] = useState('');

  // Test 2: CustomFilters with API
  const extraActions = [
    {
      key: 'unit_selection',
      title: 'Select Unit',
      placeholder: 'Choose a unit',
      api: 'https://apigw.cubeone.in/api/admin/units/list'
    },
    {
      key: 'static_test',
      title: 'Static Options',
      type: 'select',
      options: {
        'option1': 'Option 1',
        'option2': 'Option 2',
        'option3': 'Option 3'
      }
    }
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        API Select Component Tests
      </Typography>

      {/* Test 1: Simple API Select */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Test 1: Simple API Select (Direct Implementation)
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          This should work immediately and show units from the API
        </Typography>
        
        <Box sx={{ mt: 2, mb: 2 }}>
          <TestSimpleApiSelect />
        </Box>
        
        <Typography variant="body2">
          Selected Value: <strong>{simpleValue || 'None'}</strong>
        </Typography>
      </Paper>

      <Divider sx={{ my: 3 }} />

      {/* Test 2: CustomFilters with API */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Test 2: CustomFilters with API
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          This uses the CustomFilters component with API configuration
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 2, mb: 2 }}>
          <CustomFilters
            extraFilters={filters}
            setExtraFilters={setFilters}
            extraActions={extraActions}
            loading={loading}
          />
        </Box>
        
        <Typography variant="body2">
          Current Filters:
        </Typography>
        <pre style={{ 
          backgroundColor: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto'
        }}>
          {JSON.stringify(filters, null, 2)}
        </pre>
      </Paper>

      {/* Debug Instructions */}
      <Paper sx={{ p: 3, bgcolor: '#f8f9fa' }}>
        <Typography variant="h6" gutterBottom>
          Debug Instructions
        </Typography>
        <Typography variant="body2" component="div">
          <ol>
            <li><strong>Open Browser Console:</strong> Press F12 → Go to Console tab</li>
            <li><strong>Look for these logs:</strong>
              <ul>
                <li>🚀 Starting API call to: [URL]</li>
                <li>✅ API Response: [Response Object]</li>
                <li>📊 Response Data: [Data from API]</li>
                <li>🔄 Extracted Data: [Array of items]</li>
                <li>🎯 Transformed Options: [Dropdown options]</li>
              </ul>
            </li>
            <li><strong>Check Network Tab:</strong> See if API requests are being made</li>
            <li><strong>Test Both Components:</strong> Compare which one works</li>
          </ol>
        </Typography>
        
        <Box sx={{ mt: 2, p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>Expected Behavior:</strong><br/>
            - Test 1 should work immediately<br/>
            - Test 2 should show the same data in CustomFilters<br/>
            - Console should show detailed API logs<br/>
            - Dropdowns should populate with unit data
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default TestPage;
