import React, { useState, useEffect } from 'react';
import { FormControl, InputLabel, MenuItem, Select, CircularProgress } from '@mui/material';
import axios from 'axios';

interface ApiSelectProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  apiUrl?: string;
}

const SimpleApiSelect: React.FC<ApiSelectProps> = ({
  label = 'Select Option',
  placeholder = 'Choose an option',
  value = '',
  onChange = () => {},
  apiUrl = 'https://apigw.cubeone.in/api/admin/units/list'
}) => {
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      console.log('🚀 Starting API call to:', apiUrl);
      setLoading(true);
      
      try {
        const response = await axios.get(apiUrl);
        console.log('✅ API Response:', response);
        console.log('📊 Response Data:', response.data);
        
        const data = response.data?.data || response.data || [];
        console.log('🔄 Extracted Data:', data);
        console.log('📏 Data Length:', data.length);
        
        // Transform data for dropdown
        const transformedOptions = data.map((item: any, index: number) => {
          let label = '';
          
          if (item.unitNo) {
            const occupantInfo = item.occupantName ? ` - ${item.occupantName}` : '';
            label = `${item.unitNo} (${item.buildingFloorNo})${occupantInfo}`;
          } else if (item.name) {
            label = item.name;
          } else {
            label = `Item ${index + 1}`;
          }
          
          return {
            label,
            value: item.id || index,
            originalData: item
          };
        });
        
        console.log('🎯 Transformed Options:', transformedOptions);
        setOptions(transformedOptions);
        
      } catch (error) {
        console.error('❌ API Error:', error);
        console.error('🔍 Error Details:', error.response?.data || error.message);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [apiUrl]);

  return (
    <FormControl variant="outlined" sx={{ minWidth: 250 }}>
      <InputLabel shrink>{label}</InputLabel>
      <Select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        label={label}
        disabled={loading}
        size="small"
        displayEmpty
        sx={{
          '& .MuiInputBase-root': {
            minHeight: '40px'
          }
        }}
        endAdornment={
          loading ? (
            <CircularProgress color="inherit" size={20} sx={{ mr: 2 }} />
          ) : null
        }
      >
        <MenuItem value="" disabled>
          <em>{placeholder}</em>
        </MenuItem>
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            <div style={{ width: '100%' }}>
              <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                {option.label}
              </div>
              {option.originalData && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                  {`${option.originalData.type || ''} | ${option.originalData.occupancy || ''} | Area: ${option.originalData.area || 'N/A'}`}
                </div>
              )}
            </div>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

// Test component to use the SimpleApiSelect
const TestSimpleApiSelect: React.FC = () => {
  const [selectedValue, setSelectedValue] = useState('');

  return (
    <div style={{ padding: '20px' }}>
      <h2>Simple API Select Test</h2>
      <div style={{ marginBottom: '20px' }}>
        <SimpleApiSelect
          label="Select Unit"
          placeholder="Choose a unit"
          value={selectedValue}
          onChange={setSelectedValue}
          apiUrl="https://apigw.cubeone.in/api/admin/units/list"
        />
      </div>
      
      <div style={{ marginTop: '20px', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <h3>Selected Value:</h3>
        <pre>{selectedValue || 'None selected'}</pre>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>Instructions:</h3>
        <ol>
          <li>Open Browser Console (F12 → Console tab)</li>
          <li>Look for logs starting with 🚀, ✅, 📊, 🔄, 🎯</li>
          <li>The dropdown should populate with units from the API</li>
          <li>Select an option to see the value change</li>
        </ol>
      </div>
    </div>
  );
};

export default TestSimpleApiSelect;
export { SimpleApiSelect };
