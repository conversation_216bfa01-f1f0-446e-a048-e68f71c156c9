import React, { useEffect } from 'react';

import { FormControl, InputLabel } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';

interface SingleDateFilterProps {
  filters: Record<string, any>;
  setFilters: (filters: Record<string, any> | ((prevFilters: Record<string, any>) => Record<string, any>)) => void;
  dateKey?: string;
  label?: string;
  placeholder?: string;
  minDate?: string | Date;
  maxDate?: string | Date;
  disabled?: boolean;
  defaultToToday?: boolean;
}

const SingleDateFilter: React.FC<SingleDateFilterProps> = ({
  filters,
  setFilters,
  dateKey = 'selected_date',
  label = 'Select Date',
  placeholder = 'Choose date',
  minDate,
  maxDate,
  disabled = false,
  defaultToToday = true
}) => {
  // Set today's date as default when component mounts
  useEffect(() => {
    if (defaultToToday && !filters[dateKey]) {
      
      const today = dayjs().format('YYYY-MM-DD');
      setFilters({
        ...filters,
        [dateKey]: today
      });
    }
  }, [defaultToToday, filters, dateKey, setFilters]);

  // Convert string date to Dayjs for MUI DatePicker
  const dayjsValue = filters[dateKey] ? dayjs(filters[dateKey]) : null;

  // Handle date change from MUI DatePicker
  const handleDateChange = (newValue: Dayjs | null) => {
    setFilters({
      ...filters,
      [dateKey]: newValue ? newValue.format('YYYY-MM-DD') : null
    });
  };

  // Convert min/max dates to Dayjs if provided
  const minDateDayjs = minDate ? dayjs(minDate) : undefined;
  const maxDateDayjs = maxDate ? dayjs(maxDate) : undefined;

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <FormControl variant="outlined" sx={{ minWidth: 200 }} disabled={disabled}>
        <InputLabel id={`${dateKey}-label`} shrink>
          {label}
        </InputLabel>
        <DatePicker
          label={label}
          value={dayjsValue}
          onChange={handleDateChange}
          minDate={minDateDayjs}
          maxDate={maxDateDayjs}
          disabled={disabled}
          slotProps={{
            textField: {
              size: 'small',
              fullWidth: true,
              placeholder: placeholder,
              InputLabelProps: {
                shrink: true,
              }
            }
          }}
          sx={{
            '& .MuiInputBase-root': {
              minHeight: '40px'
            }
          }}
        />
      </FormControl>
    </LocalizationProvider>
  );
};

export default SingleDateFilter;
