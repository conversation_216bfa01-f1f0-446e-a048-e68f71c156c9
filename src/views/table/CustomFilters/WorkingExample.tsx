import React, { useState } from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';
import CustomFilters from './CustomFiltersNew';

const WorkingExample: React.FC = () => {
  const [filters, setFilters] = useState({});
  const [loading, setLoading] = useState(false);

  // Test configuration
  const extraActions = [
    {
      key: 'unit_autocomplete',
      title: 'Select Unit (API)',
      api: 'https://apigw.cubeone.in/api/admin/units/list' // This triggers autocomplete
    },
    {
      key: 'status_select',
      title: 'Status',
      type: 'select',
      options: {
        'active': 'Active',
        'inactive': 'Inactive',
        'pending': 'Pending'
      }
    },
    {
      key: 'search_text',
      title: 'Search',
      placeholder: 'Enter search term'
    }
  ];

  const clearFilters = () => {
    setFilters({});
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200 }}>
      <Typography variant="h4" gutterBottom>
        ✅ Working Autocomplete Example
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          CustomFilters with API Autocomplete
        </Typography>
        
        <Typography variant="body2" color="text.secondary" gutterBottom>
          This example shows three types of filters:
        </Typography>
        <ul>
          <li><strong>Unit Autocomplete:</strong> Fetches data from API and shows searchable dropdown</li>
          <li><strong>Status Select:</strong> Static dropdown with predefined options</li>
          <li><strong>Search TextField:</strong> Regular text input</li>
        </ul>
        
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 3, mb: 2 }}>
          <CustomFilters
            extraFilters={filters}
            setExtraFilters={setFilters}
            extraActions={extraActions}
            loading={loading}
          />
          <Button 
            variant="outlined" 
            color="secondary" 
            onClick={clearFilters}
            size="small"
          >
            Clear
          </Button>
        </Box>
        
        <Typography variant="body2" sx={{ mt: 2 }}>
          <strong>Current Filter Values:</strong>
        </Typography>
        <pre style={{ 
          backgroundColor: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto',
          border: '1px solid #ddd'
        }}>
          {JSON.stringify(filters, null, 2)}
        </pre>
      </Paper>

      <Paper sx={{ p: 3, bgcolor: '#e8f5e8' }}>
        <Typography variant="h6" gutterBottom color="success.main">
          🔍 Debug Instructions
        </Typography>
        <Typography variant="body2" component="div">
          <ol>
            <li><strong>Open Browser Console:</strong> Press F12 → Go to Console tab</li>
            <li><strong>Look for these logs:</strong>
              <ul>
                <li>🔍 Checking actions for API calls: [array]</li>
                <li>✅ Found API action: unit_autocomplete</li>
                <li>🚀 Fetching data for unit_autocomplete</li>
                <li>📡 Making API call to: https://apigw.cubeone.in/api/admin/units/list</li>
                <li>✅ API Response: [response object]</li>
                <li>🏠 Extracted Unit Numbers: [array of unit numbers]</li>
                <li>🌐 Rendering Autocomplete for unit_autocomplete</li>
                <li>📋 Options for unit_autocomplete: [array]</li>
              </ul>
            </li>
            <li><strong>Test the Autocomplete:</strong>
              <ul>
                <li>Click on "Select Unit (API)" field</li>
                <li>You should see unit numbers like "Dup01", "GF001", etc.</li>
                <li>Type to search through options</li>
                <li>Select an option and see it appear in the filter values</li>
              </ul>
            </li>
          </ol>
        </Typography>
        
        <Box sx={{ mt: 2, p: 2, bgcolor: '#fff', borderRadius: 1, border: '1px solid #c8e6c9' }}>
          <Typography variant="body2">
            <strong>Expected Behavior:</strong><br/>
            ✅ API call should be made automatically<br/>
            ✅ Loading spinner should appear briefly<br/>
            ✅ Dropdown should populate with unit numbers<br/>
            ✅ Search should work by typing<br/>
            ✅ Selection should update the filter values<br/>
            ✅ Console should show detailed logs
          </Typography>
        </Box>
      </Paper>

      <Paper sx={{ p: 3, mt: 3, bgcolor: '#f8f9fa' }}>
        <Typography variant="h6" gutterBottom>
          📋 How to Use in Your Code
        </Typography>
        <Typography variant="body2" component="div">
          <strong>1. Import the new component:</strong>
          <pre style={{ 
            backgroundColor: '#fff', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            margin: '10px 0',
            border: '1px solid #ddd'
          }}>
{`import CustomFilters from './CustomFilters/CustomFiltersNew';`}
          </pre>
          
          <strong>2. Use with API autocomplete:</strong>
          <pre style={{ 
            backgroundColor: '#fff', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            margin: '10px 0',
            border: '1px solid #ddd'
          }}>
{`const extraActions = [
  {
    key: 'unit_selection',
    title: 'Select Unit',
    api: 'https://apigw.cubeone.in/api/admin/units/list'
  }
];

<CustomFilters
  extraFilters={filters}
  setExtraFilters={setFilters}
  extraActions={extraActions}
  loading={false}
/>`}
          </pre>
        </Typography>
      </Paper>
    </Box>
  );
};

export default WorkingExample;
