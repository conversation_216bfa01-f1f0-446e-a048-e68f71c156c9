import React, { useState } from 'react';
import { Box, Typography, Paper, Button, Divider } from '@mui/material';
import CustomFilters from './CustomFilters';
import AutocompleteFilter from './AutocompleteFilter';

const AutocompleteFilterExample: React.FC = () => {
  const [filters, setFilters] = useState({});
  const [standaloneFilters, setStandaloneFilters] = useState({});
  const [loading, setLoading] = useState(false);

  // Example configuration for CustomFilters with autocomplete
  const extraActions = [
    {
      key: 'unit_autocomplete',
      title: 'Select Unit',
      type: 'autocomplete', // Explicit type
      api: 'https://apigw.cubeone.in/api/admin/units/list',
      placeholder: 'Search for a unit...',
      labelField: 'unitNo',
      valueField: 'id'
    },
    {
      key: 'unit_api_only',
      title: 'Unit (API Only)',
      api: 'https://apigw.cubeone.in/api/admin/units/list', // Just API property
      placeholder: 'Search units...'
    },
    {
      key: 'status_select',
      title: 'Status',
      type: 'select',
      options: {
        'active': 'Active',
        'inactive': 'Inactive',
        'pending': 'Pending'
      }
    },
    {
      key: 'search_text',
      title: 'Search',
      placeholder: 'Enter search term'
    }
  ];

  const clearFilters = () => {
    setFilters({});
  };

  const clearStandaloneFilters = () => {
    setStandaloneFilters({});
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200 }}>
      <Typography variant="h4" gutterBottom>
        🎯 Autocomplete Filter Component
      </Typography>

      {/* Standalone AutocompleteFilter */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          1. Standalone AutocompleteFilter Component
        </Typography>
        
        <Typography variant="body2" color="text.secondary" gutterBottom>
          This is the standalone AutocompleteFilter component that can be used independently.
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 2, mb: 2 }}>
          <AutocompleteFilter
            filters={standaloneFilters}
            setFilters={setStandaloneFilters}
            filterKey="standalone_unit"
            label="Select Unit (Standalone)"
            placeholder="Search for a unit..."
            api="https://apigw.cubeone.in/api/admin/units/list"
            labelField="unitNo"
            valueField="id"
            disabled={loading}
            size="small"
          />
          <Button 
            variant="outlined" 
            color="secondary" 
            onClick={clearStandaloneFilters}
            size="small"
          >
            Clear
          </Button>
        </Box>
        
        <Typography variant="body2" sx={{ mt: 2 }}>
          <strong>Standalone Filter Values:</strong>
        </Typography>
        <pre style={{ 
          backgroundColor: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto',
          border: '1px solid #ddd'
        }}>
          {JSON.stringify(standaloneFilters, null, 2)}
        </pre>
      </Paper>

      <Divider sx={{ my: 3 }} />

      {/* CustomFilters with AutocompleteFilter */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          2. CustomFilters with Integrated AutocompleteFilter
        </Typography>
        
        <Typography variant="body2" color="text.secondary" gutterBottom>
          This shows how the AutocompleteFilter integrates with the existing CustomFilters component.
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 2, mb: 2 }}>
          <CustomFilters
            extraFilters={filters}
            setExtraFilters={setFilters}
            extraActions={extraActions}
            loading={loading}
          />
          <Button 
            variant="outlined" 
            color="secondary" 
            onClick={clearFilters}
            size="small"
          >
            Clear
          </Button>
        </Box>
        
        <Typography variant="body2" sx={{ mt: 2 }}>
          <strong>CustomFilters Values:</strong>
        </Typography>
        <pre style={{ 
          backgroundColor: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto',
          border: '1px solid #ddd'
        }}>
          {JSON.stringify(filters, null, 2)}
        </pre>
      </Paper>

      {/* Usage Instructions */}
      <Paper sx={{ p: 3, bgcolor: '#e8f5e8' }}>
        <Typography variant="h6" gutterBottom color="success.main">
          📋 How to Use AutocompleteFilter
        </Typography>
        
        <Typography variant="body2" component="div">
          <strong>Method 1: Standalone Component</strong>
          <pre style={{ 
            backgroundColor: '#fff', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            margin: '10px 0',
            border: '1px solid #c8e6c9'
          }}>
{`import AutocompleteFilter from './AutocompleteFilter';

<AutocompleteFilter
  filters={filters}
  setFilters={setFilters}
  filterKey="unit_selection"
  label="Select Unit"
  placeholder="Search for a unit..."
  api="https://apigw.cubeone.in/api/admin/units/list"
  labelField="unitNo"
  valueField="id"
/>`}
          </pre>
          
          <strong>Method 2: With CustomFilters (Type: autocomplete)</strong>
          <pre style={{ 
            backgroundColor: '#fff', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            margin: '10px 0',
            border: '1px solid #c8e6c9'
          }}>
{`const extraActions = [
  {
    key: 'unit_selection',
    title: 'Select Unit',
    type: 'autocomplete',
    api: 'https://apigw.cubeone.in/api/admin/units/list',
    labelField: 'unitNo',
    valueField: 'id'
  }
];`}
          </pre>
          
          <strong>Method 3: With CustomFilters (API property only)</strong>
          <pre style={{ 
            backgroundColor: '#fff', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            margin: '10px 0',
            border: '1px solid #c8e6c9'
          }}>
{`const extraActions = [
  {
    key: 'unit_selection',
    title: 'Select Unit',
    api: 'https://apigw.cubeone.in/api/admin/units/list'
  }
];`}
          </pre>
        </Typography>
        
        <Box sx={{ mt: 2, p: 2, bgcolor: '#fff', borderRadius: 1, border: '1px solid #c8e6c9' }}>
          <Typography variant="body2">
            <strong>✅ Features:</strong><br/>
            • Fetches data from any API endpoint<br/>
            • Configurable label and value fields<br/>
            • Loading states with spinner<br/>
            • Error handling<br/>
            • Search/filter functionality<br/>
            • Rich dropdown options with additional info<br/>
            • Consistent styling with other filters<br/>
            • TypeScript support
          </Typography>
        </Box>
      </Paper>

      {/* Debug Instructions */}
      <Paper sx={{ p: 3, mt: 3, bgcolor: '#f8f9fa' }}>
        <Typography variant="h6" gutterBottom>
          🔍 Console Debug Logs
        </Typography>
        <Typography variant="body2" component="div">
          Open browser console to see these logs:
          <ul>
            <li>🚀 AutocompleteFilter: Fetching data for [key] from: [API URL]</li>
            <li>✅ AutocompleteFilter: API Response for [key]: [Response]</li>
            <li>📊 AutocompleteFilter: Extracted data for [key]: [Data Array]</li>
            <li>🎯 AutocompleteFilter: Transformed options for [key]: [Options]</li>
            <li>🎯 AutocompleteFilter: Selected for [key]: [Selected Value]</li>
          </ul>
        </Typography>
      </Paper>
    </Box>
  );
};

export default AutocompleteFilterExample;
