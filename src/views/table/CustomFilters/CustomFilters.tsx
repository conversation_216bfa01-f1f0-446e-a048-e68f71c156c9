import React, { useEffect, useState } from 'react'

import LoadingButton from '@mui/lab/LoadingButton';
import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material';

import FinancialYearFilterMUI from './FinancialYearFilter';
import TailwindDateRangePicker from './DateRangePicker';
import TransactionPeriodFilter from './TransactionPeriodFilter';

const CustomFilters = ({ extraFilters, loading = false, setExtraFilters, extraActions = [] }) => {
  const [tempFilters, setTempFilters] = useState(extraFilters);

  useEffect(() => {
    setTempFilters(extraFilters);
  }, [extraFilters]);

  return (
    <>
      {extraActions.map((action, index) => {
        if (action.type === 'transaction_period') {
          return (
            <TransactionPeriodFilter
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              api={action.api}
            />
          );
        }

        if (action.type === 'financial_year') {
          return (
            <FinancialYearFilterMUI
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              api={action.api}
              hideMonthSelect={false}
            />
          );
        }

        if (action.type === 'select') {
          return (
            <FormControl sx={{ minWidth: 120 }} key={action.key}>
              <InputLabel id={action.key}>{action?.title}</InputLabel>
              <Select
                key={index}
                title={action.title || action.key}
                value={String(tempFilters[action.key] ?? action?.default)}
                onChange={(e) => {
                  setTempFilters({
                    ...tempFilters,
                    [action.key]: e.target.value
                  });
                }}
                labelId={action.key}
                label={action.title || action.key}
                autoWidth
              >
                {Object.entries(action.options).map(([key, value]) => (
                  <MenuItem key={key} value={String(key)} >{String(value)}</MenuItem>
                ))}
              </Select>
            </FormControl>
          );
        }

        if (action.type === 'daterange') {
          return (
            <div key={action.key} className="w-full sm:w-auto">
              <TailwindDateRangePicker
                label={action?.title || action.key || 'Date Range'}

                // pull the two values out of tempFilters (might be undefined)
                value={[
                  tempFilters[action?.startKey || "startDate"],
                  tempFilters[action?.endKey || "endDate"]
                ]}

                minDate={action?.minDate}
                maxDate={action?.maxDate}

                // when user picks [start, end], write back into tempFilters
                onChange={([newStart, newEnd]) => {
                  setTempFilters({
                    ...tempFilters,
                    [action?.startKey || "startDate"]: newStart,
                    [action?.endKey || "endDate"]: newEnd
                  });
                }}

                // optional: disable the picker if your parent says so
                disabled={loading}
              />
            </div>
          );
        }

        return <FormControl key={action.key} >
          <InputLabel id={action.key}>{action?.title}</InputLabel>
          <TextField
            key={index}
            title={action.title || action.key}
            value={tempFilters[action.key] || ''}
            onChange={(e) => {
              setTempFilters({
                ...tempFilters,
                [action.key]: e.target.value
              });
            }}
            label={action.title || action.key}
          />
        </FormControl>
      })}
      <LoadingButton
        variant='outlined'
        loading={loading}
        onClick={() => {
          setExtraFilters(tempFilters);
        }}
      >
        Apply
      </LoadingButton>
    </>
  )
}

export default CustomFilters