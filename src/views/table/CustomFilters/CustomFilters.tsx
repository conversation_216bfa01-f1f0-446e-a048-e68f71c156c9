import React, { useEffect, useState } from 'react'

import LoadingButton from '@mui/lab/LoadingButton';
import { FormControl, InputLabel, MenuItem, Select, CircularProgress } from '@mui/material';
import axios from 'axios';

import FinancialYearFilterMUI from './FinancialYearFilter';
import TailwindDateRangePicker from './DateRangePicker';
import TransactionPeriodFilter from './TransactionPeriodFilter';
import SingleDateFilter from './SingleDateFilter';

const CustomFilters = ({ extraFilters, loading = false, setExtraFilters, extraActions = [] }) => {
  console.log('🎯 CustomFilters rendered with:', { extraFilters, extraActions, loading });

  const [tempFilters, setTempFilters] = useState(extraFilters);
  const [apiOptions, setApiOptions] = useState({});
  const [apiLoading, setApiLoading] = useState({});

  useEffect(() => {
    setTempFilters(extraFilters);
  }, [extraFilters]);

  // Fetch API data for all actions with API on component mount
  useEffect(() => {
    console.log('🔍 Checking extraActions for API calls:', extraActions);
    extraActions.forEach(action => {
      console.log(`🔍 Checking action:`, action);
      if (action.api && !apiOptions[action.key]) {
        console.log(`✅ Will fetch API for ${action.key}:`, action.api);
        fetchApiData(action);
      } else if (action.api && apiOptions[action.key]) {
        console.log(`⏭️ Skipping ${action.key} - already has data`);
      } else {
        console.log(`⏭️ Skipping ${action.key} - no API property`);
      }
    });
  }, [extraActions]);

  // Function to fetch API data for typehead
  const fetchApiData = async (action) => {
    if (!action.api || apiOptions[action.key]) return;

    console.log(`🚀 Fetching data for ${action.key} from:`, action.api);
    setApiLoading(prev => ({ ...prev, [action.key]: true }));

    try {
      const response = await axios.get(action.api);
      console.log(`✅ API Response for ${action.key}:`, response);
      console.log(`📊 Response Data:`, response.data);

      const data = response.data?.data || response.data || [];
      console.log(`🔄 Extracted Data Array:`, data);
      console.log(`📏 Data Length:`, data.length);

      // Transform data based on action configuration
      const options = data.map((item, index) => {
        console.log(`🔍 Processing item:`, item);

        // Create a comprehensive label for units API response
        let label = '';

        // If specific labelField is provided, use it
        if (action.labelField && item[action.labelField]) {
          label = String(item[action.labelField]);
        }
        // For units API, create a meaningful label
        else if (item.unitNo) {
          const occupantInfo = item.occupantName ? ` - ${item.occupantName}` : '';
          label = `${item.unitNo} (${item.buildingFloorNo})${occupantInfo}`;
        }
        // Fallback to other common field names
        else if (item.name) {
          label = String(item.name);
        }
        else if (item.title) {
          label = String(item.title);
        }
        else if (item.label) {
          label = String(item.label);
        }
        // If no recognizable field, show the first few fields of the object
        else {
          const keys = Object.keys(item).slice(0, 3);
          label = keys.map(key => `${key}: ${item[key]}`).join(' | ');
        }

        // Get value field
        const value = item[action.valueField || 'id'] || item.value || item.id || index;

        const transformedItem = {
          label: label || `Item ${index + 1}`,
          value: value,
          originalData: item // Keep original data for reference
        };

        console.log(`✨ Transformed item:`, transformedItem);
        return transformedItem;
      });

      console.log(`🎯 Final Options for ${action.key}:`, options);
      setApiOptions(prev => ({ ...prev, [action.key]: options }));
    } catch (error) {
      console.error(`❌ Error fetching data for ${action.key}:`, error);
      console.error(`🔍 Error details:`, error.response?.data || error.message);
      setApiOptions(prev => ({ ...prev, [action.key]: [] }));
    } finally {
      setApiLoading(prev => ({ ...prev, [action.key]: false }));
    }
  };

  return (
    <>
      {extraActions.map((action, index) => {
        console.log(`🔄 Processing action ${index}:`, action);

        if (action.type === 'transaction_period') {
          console.log(`📋 Rendering transaction_period for ${action.key}`);
          return (
            <TransactionPeriodFilter
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              api={action.api}
            />
          );
        }

        if (action.type === 'financial_year') {
          return (
            <FinancialYearFilterMUI
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              api={action.api}
              hideMonthSelect={false}
            />
          );
        }

        if (action.type === 'select') {
          return (
            <FormControl sx={{ minWidth: 120 }} key={action.key}>
              <InputLabel id={action.key}>{action?.title}</InputLabel>
              <Select
                key={index}
                title={action.title || action.key}
                value={String(tempFilters[action.key] ?? action?.default)}
                onChange={(e) => {
                  setTempFilters({
                    ...tempFilters,
                    [action.key]: e.target.value
                  });
                }}
                labelId={action.key}
                label={action.title || action.key}
                autoWidth
              >
                {Object.entries(action.options).map(([key, value]) => (
                  <MenuItem key={key} value={String(key)} >{String(value)}</MenuItem>
                ))}
              </Select>
            </FormControl>
          );
        }

        if (action.type === 'daterange') {
          return (
            <div key={action.key} className="w-full sm:w-auto">
              <TailwindDateRangePicker
                label={action?.title || action.key || 'Date Range'}

                // pull the two values out of tempFilters (might be undefined)
                value={[
                  tempFilters[action?.startKey || "startDate"],
                  tempFilters[action?.endKey || "endDate"]
                ]}

                minDate={action?.minDate}
                maxDate={action?.maxDate}

                // when user picks [start, end], write back into tempFilters
                onChange={([newStart, newEnd]) => {
                  setTempFilters({
                    ...tempFilters,
                    [action?.startKey || "startDate"]: newStart,
                    [action?.endKey || "endDate"]: newEnd
                  });
                }}

                // optional: disable the picker if your parent says so
                disabled={loading}
              />
            </div>
          );
        }

        if (action.type === 'date') {
          return (
            <SingleDateFilter
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              dateKey={action.key || 'selected_date'}
              label={action?.title || action.key || 'Select Date'}
              placeholder={action?.placeholder || 'Choose date'}
              minDate={action?.minDate}
              maxDate={action?.maxDate}
              disabled={loading}
              defaultToToday={action?.defaultToToday !== false} // Default to true unless explicitly set to false
            />
          );
        }

        // Check if this action should use API select (has api property)
        if (action.api) {
          console.log(`🌐 Rendering API Select for ${action.key}:`, action.api);
          const options = apiOptions[action.key] || [];
          console.log(`📋 Options for ${action.key}:`, options);

          return (
            <FormControl variant="outlined" sx={{ minWidth: 200 }} key={action.key}>
              <InputLabel id={`${action.key}-label`} shrink>
                {action?.title || action.key}
              </InputLabel>
              <Select
                labelId={`${action.key}-label`}
                value={tempFilters[action.key] || ''}
                onChange={(e) => {
                  const selectedValue = e.target.value;
                  const selectedOption = options.find(option => option.value === selectedValue);
                  console.log(`🎯 Selected option:`, selectedOption);
                  setTempFilters({
                    ...tempFilters,
                    [action.key]: selectedValue
                  });
                }}
                label={action?.title || action.key}
                disabled={loading || apiLoading[action.key]}
                size="small"
                displayEmpty
                sx={{
                  '& .MuiInputBase-root': {
                    minHeight: '40px'
                  }
                }}
                endAdornment={
                  apiLoading[action.key] ? (
                    <CircularProgress color="inherit" size={20} sx={{ mr: 2 }} />
                  ) : null
                }
              >
                <MenuItem value="" disabled>
                  <em>{action?.placeholder || `Select ${action?.title || action.key}`}</em>
                </MenuItem>
                {options.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    <div style={{ width: '100%' }}>
                      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                        {option.label}
                      </div>
                      {option.originalData && (
                        <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                          {`${option.originalData.buildingFloorNo} | ${option.originalData.type} | ${option.originalData.occupancy}`}
                        </div>
                      )}
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          );
        }

        return (
          <FormControl variant="outlined" sx={{ minWidth: 200 }} key={action.key}>
            <InputLabel id={`${action.key}-default-label`} shrink>
              {action?.title || action.key}
            </InputLabel>
            <Select
              labelId={`${action.key}-default-label`}
              value={tempFilters[action.key] || ''}
              onChange={(e) => {
                setTempFilters({
                  ...tempFilters,
                  [action.key]: e.target.value
                });
              }}
              label={action?.title || action.key}
              disabled={loading}
              size="small"
              displayEmpty
              sx={{
                '& .MuiInputBase-root': {
                  minHeight: '40px'
                }
              }}
            >
              <MenuItem value="" disabled>
                <em>{action?.placeholder || `Select ${action?.title || action.key}`}</em>
              </MenuItem>
              {action.options && Object.entries(action.options).map(([key, value]) => (
                <MenuItem key={key} value={String(key)}>
                  {String(value)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )
      })}
      <LoadingButton
        variant='outlined'
        loading={loading}
        onClick={() => {
          setExtraFilters(tempFilters);
        }}
      >
        Apply
      </LoadingButton>
    </>
  )
}

export default CustomFilters