import React, { useEffect, useState } from 'react'

import LoadingButton from '@mui/lab/LoadingButton';
import { FormControl, InputLabel, MenuItem, Select, TextField, Autocomplete, CircularProgress } from '@mui/material';
import axios from 'axios';

import FinancialYearFilterMUI from './FinancialYearFilter';
import TailwindDateRangePicker from './DateRangePicker';
import TransactionPeriodFilter from './TransactionPeriodFilter';
import SingleDateFilter from './SingleDateFilter';

const CustomFilters = ({ extraFilters, loading = false, setExtraFilters, extraActions = [] }) => {
  const [tempFilters, setTempFilters] = useState(extraFilters);
  const [apiOptions, setApiOptions] = useState({});
  const [apiLoading, setApiLoading] = useState({});

  useEffect(() => {
    setTempFilters(extraFilters);
  }, [extraFilters]);

  // Function to fetch API data for typehead
  const fetchApiData = async (action) => {
    if (!action.api || apiOptions[action.key]) return;

    console.log(`🚀 Fetching data for ${action.key} from:`, action.api);
    setApiLoading(prev => ({ ...prev, [action.key]: true }));

    try {
      const response = await axios.get(action.api);
      console.log(`✅ API Response for ${action.key}:`, response);
      console.log(`📊 Response Data:`, response.data);

      const data = response.data?.data || response.data || [];
      console.log(`🔄 Extracted Data Array:`, data);
      console.log(`📏 Data Length:`, data.length);

      // Transform data based on action configuration
      const options = data.map((item, index) => {
        console.log(`🔍 Processing item:`, item);

        // Create a comprehensive label showing all available data
        let label = '';

        // If specific labelField is provided, use it
        if (action.labelField && item[action.labelField]) {
          label = String(item[action.labelField]);
        }
        // Otherwise, try common field names
        else if (item.name) {
          label = String(item.name);
        }
        else if (item.title) {
          label = String(item.title);
        }
        else if (item.label) {
          label = String(item.label);
        }
        else if (item.unit_flat_number) {
          label = `Unit ${item.unit_flat_number}`;
        }
        else if (item.flat_number) {
          label = `Flat ${item.flat_number}`;
        }
        else if (item.unit_number) {
          label = `Unit ${item.unit_number}`;
        }
        // If no recognizable field, show the first few fields of the object
        else {
          const keys = Object.keys(item).slice(0, 3);
          label = keys.map(key => `${key}: ${item[key]}`).join(' | ');
        }

        // Get value field
        const value = item[action.valueField || 'id'] || item.value || item.id || index;

        const transformedItem = {
          label: label || `Item ${index + 1}`,
          value: value,
          originalData: item // Keep original data for reference
        };

        console.log(`✨ Transformed item:`, transformedItem);
        return transformedItem;
      });

      console.log(`🎯 Final Options for ${action.key}:`, options);
      setApiOptions(prev => ({ ...prev, [action.key]: options }));
    } catch (error) {
      console.error(`❌ Error fetching data for ${action.key}:`, error);
      console.error(`🔍 Error details:`, error.response?.data || error.message);
      setApiOptions(prev => ({ ...prev, [action.key]: [] }));
    } finally {
      setApiLoading(prev => ({ ...prev, [action.key]: false }));
    }
  };

  return (
    <>
      {extraActions.map((action, index) => {
        if (action.type === 'transaction_period') {
          return (
            <TransactionPeriodFilter
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              api={action.api}
            />
          );
        }

        if (action.type === 'financial_year') {
          return (
            <FinancialYearFilterMUI
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              api={action.api}
              hideMonthSelect={false}
            />
          );
        }

        if (action.type === 'select') {
          return (
            <FormControl sx={{ minWidth: 120 }} key={action.key}>
              <InputLabel id={action.key}>{action?.title}</InputLabel>
              <Select
                key={index}
                title={action.title || action.key}
                value={String(tempFilters[action.key] ?? action?.default)}
                onChange={(e) => {
                  setTempFilters({
                    ...tempFilters,
                    [action.key]: e.target.value
                  });
                }}
                labelId={action.key}
                label={action.title || action.key}
                autoWidth
              >
                {Object.entries(action.options).map(([key, value]) => (
                  <MenuItem key={key} value={String(key)} >{String(value)}</MenuItem>
                ))}
              </Select>
            </FormControl>
          );
        }

        if (action.type === 'daterange') {
          return (
            <div key={action.key} className="w-full sm:w-auto">
              <TailwindDateRangePicker
                label={action?.title || action.key || 'Date Range'}

                // pull the two values out of tempFilters (might be undefined)
                value={[
                  tempFilters[action?.startKey || "startDate"],
                  tempFilters[action?.endKey || "endDate"]
                ]}

                minDate={action?.minDate}
                maxDate={action?.maxDate}

                // when user picks [start, end], write back into tempFilters
                onChange={([newStart, newEnd]) => {
                  setTempFilters({
                    ...tempFilters,
                    [action?.startKey || "startDate"]: newStart,
                    [action?.endKey || "endDate"]: newEnd
                  });
                }}

                // optional: disable the picker if your parent says so
                disabled={loading}
              />
            </div>
          );
        }

        if (action.type === 'date') {
          return (
            <SingleDateFilter
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              dateKey={action.key || 'selected_date'}
              label={action?.title || action.key || 'Select Date'}
              placeholder={action?.placeholder || 'Choose date'}
              minDate={action?.minDate}
              maxDate={action?.maxDate}
              disabled={loading}
              defaultToToday={action?.defaultToToday !== false} // Default to true unless explicitly set to false
            />
          );
        }

        // Check if this action should use typehead (has api property)
        if (action.api) {
          // Fetch data when component mounts or action changes
          useEffect(() => {
            fetchApiData(action);
          }, [action.api]);

          const options = apiOptions[action.key] || [];
          const currentValue = options.find(option => option.value === tempFilters[action.key]) || null;

          return (
            <FormControl variant="outlined" sx={{ minWidth: 200 }} key={action.key}>
              <Autocomplete
                options={options}
                value={currentValue}
                onChange={(event, newValue) => {
                  console.log(`🎯 Selected option:`, newValue);
                  setTempFilters({
                    ...tempFilters,
                    [action.key]: newValue?.value || ''
                  });
                }}
                getOptionLabel={(option) => option.label || ''}
                isOptionEqualToValue={(option, value) => option.value === value?.value}
                loading={apiLoading[action.key]}
                disabled={loading || apiLoading[action.key]}
                size="small"
                renderOption={(props, option) => (
                  <li {...props} key={option.value}>
                    <div style={{ width: '100%' }}>
                      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                        {option.label}
                      </div>
                      {option.originalData && (
                        <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                          {Object.entries(option.originalData)
                            .slice(0, 3)
                            .map(([key, value]) => `${key}: ${value}`)
                            .join(' | ')}
                        </div>
                      )}
                    </div>
                  </li>
                )}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={action?.title || action.key}
                    placeholder={action?.placeholder || `Search ${action?.title || action.key}`}
                    slotProps={{
                      inputLabel: {
                        shrink: true,
                      },
                      input: {
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {apiLoading[action.key] ? <CircularProgress color="inherit" size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      },
                    }}
                    sx={{
                      '& .MuiInputBase-root': {
                        minHeight: '40px'
                      }
                    }}
                  />
                )}
                sx={{
                  '& .MuiInputBase-root': {
                    minHeight: '40px'
                  }
                }}
              />
            </FormControl>
          );
        }

        return (
          <FormControl variant="outlined" sx={{ minWidth: 200 }} key={action.key}>
            <TextField
              key={index}
              label={action?.title || action.key}
              placeholder={action?.placeholder || `Enter ${action?.title || action.key}`}
              type={action.type === 'number' ? 'number' : 'text'}
              value={tempFilters[action.key] || ''}
              onChange={(e) => {
                let value = e.target.value;

                // Handle number type validation
                if (action.type === 'number') {
                  // Allow empty string, numbers, and decimal points
                  if (value !== '' && !/^\d*\.?\d*$/.test(value)) {
                    return; // Don't update if invalid number format
                  }
                }

                setTempFilters({
                  ...tempFilters,
                  [action.key]: value
                });
              }}
              size="small"
              fullWidth
              disabled={loading}
              slotProps={{
                inputLabel: {
                  shrink: true,
                },
                htmlInput: {
                  ...(action.type === 'number' && {
                    min: action?.min || 0,
                    max: action?.max,
                    step: action?.step || 'any'
                  }),
                  ...(action.type === 'text' && {
                    minLength: action?.minLength,
                    maxLength: action?.maxLength
                  })
                }
              }}
              sx={{
                '& .MuiInputBase-root': {
                  minHeight: '40px'
                }
              }}
            />
          </FormControl>
        )
      })}
      <LoadingButton
        variant='outlined'
        loading={loading}
        onClick={() => {
          setExtraFilters(tempFilters);
        }}
      >
        Apply
      </LoadingButton>
    </>
  )
}

export default CustomFilters