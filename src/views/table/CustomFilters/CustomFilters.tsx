import React, { useEffect, useState } from 'react';

import LoadingButton from '@mui/lab/LoadingButton';
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import axios from 'axios';

import FinancialYearFilterMUI from './FinancialYearFilter';
import TailwindDateRangePicker from './DateRangePicker';
import TransactionPeriodFilter from './TransactionPeriodFilter';
import SingleDateFilter from './SingleDateFilter';
import AutocompleteFilter from './AutocompleteFilter';

interface CustomFiltersProps {
  extraFilters: Record<string, any>;
  loading?: boolean;
  setExtraFilters: (filters: Record<string, any>) => void;
  extraActions?: any[];
}

const CustomFilters: React.FC<CustomFiltersProps> = ({
  extraFilters,
  loading = false,
  setExtraFilters,
  extraActions = []
}) => {
  const [tempFilters, setTempFilters] = useState(extraFilters);
  const [apiData, setApiData] = useState<Record<string, string[]>>({});
  const [apiLoading, setApiLoading] = useState<Record<string, boolean>>({});

  useEffect(() => {
    setTempFilters(extraFilters);
  }, [extraFilters]);

  // Function to fetch unit data from API
  const fetchUnitData = async (actionKey: string) => {
    console.log(`🚀 Fetching data for ${actionKey}`);

    if (apiData[actionKey] || apiLoading[actionKey]) {

      console.log(`⏭️ Skipping ${actionKey} - already loaded or loading`);

      return;
    }

    setApiLoading(prev => ({ ...prev, [actionKey]: true }));

    try {
      const response = await axios.get('https://apigw.cubeone.in/api/admin/units/list');

      console.log('✅ API Response:', response.data);

      const units = response.data?.data || [];

      console.log('📊 Units data:', units);

      const unitNumbers = units.map((unit: any) => unit.unitNo).filter(Boolean);

      console.log('🏠 Extracted Unit Numbers:', unitNumbers);

      setApiData(prev => ({ ...prev, [actionKey]: unitNumbers }));
    } catch (error) {
      console.error('❌ Error fetching units:', error);
      setApiData(prev => ({ ...prev, [actionKey]: [] }));
    } finally {
      setApiLoading(prev => ({ ...prev, [actionKey]: false }));
    }
  };

  // Fetch API data for actions with api property
  useEffect(() => {
    console.log('🔍 Checking actions for API calls:', extraActions);
    extraActions.forEach(action => {
      if (action.api) {
        console.log(`✅ Found API action: ${action.key}`);
        fetchUnitData(action.key);
      }
    });
  }, [extraActions]);

  return (
    <>
      {extraActions.map((action, index) => {
        if (action.type === 'transaction_period') {
          return (
            <TransactionPeriodFilter
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              api={action.api}
            />
          );
        }

        if (action.type === 'financial_year') {
          return (
            <FinancialYearFilterMUI
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              api={action.api}
              hideMonthSelect={false}
            />
          );
        }

        if (action.type === 'select') {
          return (
            <FormControl sx={{ minWidth: 120 }} key={action.key}>
              <InputLabel id={action.key}>{action?.title}</InputLabel>
              <Select
                key={index}
                title={action.title || action.key}
                value={String(tempFilters[action.key] ?? action?.default)}
                onChange={(e) => {
                  setTempFilters({
                    ...tempFilters,
                    [action.key]: e.target.value
                  });
                }}
                labelId={action.key}
                label={action.title || action.key}
                autoWidth
              >
                {Object.entries(action.options).map(([key, value]) => (
                  <MenuItem key={key} value={String(key)} >{String(value)}</MenuItem>
                ))}
              </Select>
            </FormControl>
          );
        }

        if (action.type === 'daterange') {
          return (
            <div key={action.key} className="w-full sm:w-auto">
              <TailwindDateRangePicker
                label={action?.title || action.key || 'Date Range'}

                // pull the two values out of tempFilters (might be undefined)
                value={[
                  tempFilters[action?.startKey || "startDate"],
                  tempFilters[action?.endKey || "endDate"]
                ]}

                minDate={action?.minDate}
                maxDate={action?.maxDate}

                // when user picks [start, end], write back into tempFilters
                onChange={([newStart, newEnd]) => {
                  setTempFilters({
                    ...tempFilters,
                    [action?.startKey || "startDate"]: newStart,
                    [action?.endKey || "endDate"]: newEnd
                  });
                }}

                // optional: disable the picker if your parent says so
                disabled={loading}
              />
            </div>
          );
        }

        if (action.type === 'date') {
          return (
            <SingleDateFilter
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              dateKey={action.key || 'selected_date'}
              label={action?.title || action.key || 'Select Date'}
              placeholder={action?.placeholder || 'Choose date'}
              minDate={action?.minDate}
              maxDate={action?.maxDate}
              disabled={loading}
            />
          );
        }

        if (action.type === 'autocomplete') {
          return (
            <AutocompleteFilter
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              filterKey={action.key}
              label={action?.title || action.key}
              placeholder={action?.placeholder || `Search ${action?.title || action.key}`}
              api={action.api}
              labelField={action?.labelField || 'unitNo'}
              valueField={action?.valueField || 'id'}
              disabled={loading}
              size="small"
            />
          );
        }

        return<FormControl variant="outlined" sx={{ minWidth: 200 }} key={action.key}>
        <TextField
          key={index}
          label={action?.title || action.key}
          placeholder={action?.placeholder || `Enter ${action?.title || action.key}`}
          type={action.type === 'number' ? 'number' : 'text'}

          value={tempFilters[action.key] || ''}

          onChange={(e) => {
            
            let value = e.target.value;

            // Handle number type validation
            if (action.type === 'number') {
              // Allow empty string, numbers, and decimal points
              if (value !== '' && !/^\d*\.?\d*$/.test(value)) {
                return; // Don't update if invalid number format
              }
            }

            setTempFilters({
              ...tempFilters,
              [action.key]: value
            });
          }}
          size="small"
          fullWidth
          disabled={loading}
          slotProps={{
            inputLabel: {
              shrink: true,
            },
            htmlInput: {
              ...(action.type === 'number' && {
                min: action?.min || 0,
                max: action?.max,
                step: action?.step || 'any'
              }),
              ...(action.type === 'text' && {
                minLength: action?.minLength,
                maxLength: action?.maxLength
              })
            }
          }}
          sx={{
            '& .MuiInputBase-root': {
              minHeight: '40px'
            }
          }}
        />
      </FormControl>


      })}
      <LoadingButton
        variant='outlined'
        loading={loading}
        onClick={() => {
          setExtraFilters(tempFilters);
        }}
      >
        Apply
      </LoadingButton>
    </>
  )
}

export default CustomFilters