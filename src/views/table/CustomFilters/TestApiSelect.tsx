import React, { useState } from 'react';
import CustomFilters from './CustomFilters';

const TestApiSelect = () => {
  const [filters, setFilters] = useState({});
  const [loading, setLoading] = useState(false);

  // Example configuration for API-based Select
  const extraActions = [
    {
      key: 'unit_selection',
      title: 'Select Unit',
      placeholder: 'Choose a unit',
      api: 'https://apigw.cubeone.in/api/admin/units/list',
      labelField: 'unitNo', // Optional: specify which field to use as label
      valueField: 'id' // Optional: specify which field to use as value
    },
    {
      key: 'building_selection', 
      title: 'Select Building',
      placeholder: 'Choose a building',
      api: 'https://apigw.cubeone.in/api/admin/buildings/list' // Another API example
    },
    {
      key: 'static_status',
      title: 'Status',
      type: 'select',
      options: {
        'active': 'Active',
        'inactive': 'Inactive',
        'pending': 'Pending'
      }
    }
  ];

  return (
    <div style={{ padding: '20px' }}>
      <h2>Test API Select Component</h2>
      <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap', marginBottom: '20px' }}>
        <CustomFilters
          extraFilters={filters}
          setExtraFilters={setFilters}
          extraActions={extraActions}
          loading={loading}
        />
      </div>
      
      <div style={{ marginTop: '20px', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <h3>Current Filter Values:</h3>
        <pre>{JSON.stringify(filters, null, 2)}</pre>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>Console Instructions:</h3>
        <ol>
          <li>Open Browser Console (F12 → Console tab)</li>
          <li>Look for logs starting with 🔍, 🚀, ✅, 📊</li>
          <li>Check if API calls are being made</li>
          <li>Verify the response structure</li>
        </ol>
      </div>
    </div>
  );
};

export default TestApiSelect;
