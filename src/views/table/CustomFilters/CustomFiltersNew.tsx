import React, { useEffect, useState } from 'react';
import LoadingButton from '@mui/lab/LoadingButton';
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Autocomplete,
  CircularProgress
} from '@mui/material';
import axios from 'axios';

import FinancialYearFilterMUI from './FinancialYearFilter';
import TailwindDateRangePicker from './DateRangePicker';
import TransactionPeriodFilter from './TransactionPeriodFilter';
import SingleDateFilter from './SingleDateFilter';

interface CustomFiltersProps {
  extraFilters: Record<string, any>;
  loading?: boolean;
  setExtraFilters: (filters: Record<string, any>) => void;
  extraActions?: any[];
}

const CustomFilters: React.FC<CustomFiltersProps> = ({
  extraFilters,
  loading = false,
  setExtraFilters,
  extraActions = []
}) => {
  const [tempFilters, setTempFilters] = useState(extraFilters);
  const [apiData, setApiData] = useState<Record<string, string[]>>({});
  const [apiLoading, setApiLoading] = useState<Record<string, boolean>>({});

  useEffect(() => {
    setTempFilters(extraFilters);
  }, [extraFilters]);

  // Function to fetch unit data from API
  const fetchUnitData = async (actionKey: string) => {
    console.log(`🚀 Fetching data for ${actionKey}`);
    
    if (apiData[actionKey] || apiLoading[actionKey]) {
      console.log(`⏭️ Skipping ${actionKey} - already loaded or loading`);
      return;
    }
    
    setApiLoading(prev => ({ ...prev, [actionKey]: true }));
    
    try {
      console.log('📡 Making API call to: https://apigw.cubeone.in/api/admin/units/list');
      const response = await axios.get('https://apigw.cubeone.in/api/admin/units/list');
      console.log('✅ API Response:', response.data);
      
      const units = response.data?.data || [];
      console.log('📊 Units data:', units);
      
      const unitNumbers = units.map((unit: any) => unit.unitNo).filter(Boolean);
      console.log('🏠 Extracted Unit Numbers:', unitNumbers);
      
      setApiData(prev => ({ ...prev, [actionKey]: unitNumbers }));
    } catch (error) {
      console.error('❌ Error fetching units:', error);
      setApiData(prev => ({ ...prev, [actionKey]: [] }));
    } finally {
      setApiLoading(prev => ({ ...prev, [actionKey]: false }));
    }
  };

  // Fetch API data for actions with api property
  useEffect(() => {
    console.log('🔍 Checking actions for API calls:', extraActions);
    extraActions.forEach(action => {
      if (action.api) {
        console.log(`✅ Found API action: ${action.key}`);
        fetchUnitData(action.key);
      }
    });
  }, [extraActions]);

  return (
    <>
      {extraActions.map((action, index) => {
        console.log(`🔄 Processing action ${index}:`, action);

        if (action.type === 'transaction_period') {
          return (
            <TransactionPeriodFilter
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              api={action.api}
            />
          );
        }

        if (action.type === 'financial_year') {
          return (
            <FinancialYearFilterMUI
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              api={action.api}
              hideMonthSelect={false}
            />
          );
        }

        if (action.type === 'select') {
          return (
            <FormControl sx={{ minWidth: 120 }} key={action.key}>
              <InputLabel id={action.key}>{action?.title}</InputLabel>
              <Select
                key={index}
                title={action.title || action.key}
                value={String(tempFilters[action.key] ?? action?.default)}
                onChange={(e) => {
                  setTempFilters({
                    ...tempFilters,
                    [action.key]: e.target.value
                  });
                }}
                labelId={action.key}
                label={action.title || action.key}
                autoWidth
              >
                {Object.entries(action.options).map(([key, value]) => (
                  <MenuItem key={key} value={String(key)}>{String(value)}</MenuItem>
                ))}
              </Select>
            </FormControl>
          );
        }

        if (action.type === 'daterange') {
          return (
            <div key={action.key} className="w-full sm:w-auto">
              <TailwindDateRangePicker
                label={action?.title || action.key || 'Date Range'}
                value={[
                  tempFilters[action?.startKey || "startDate"],
                  tempFilters[action?.endKey || "endDate"]
                ]}
                minDate={action?.minDate}
                maxDate={action?.maxDate}
                onChange={([newStart, newEnd]) => {
                  setTempFilters({
                    ...tempFilters,
                    [action?.startKey || "startDate"]: newStart,
                    [action?.endKey || "endDate"]: newEnd
                  });
                }}
                disabled={loading}
              />
            </div>
          );
        }

        if (action.type === 'date') {
          return (
            <SingleDateFilter
              key={index}
              filters={tempFilters}
              setFilters={setTempFilters}
              dateKey={action.key || 'selected_date'}
              label={action?.title || action.key || 'Select Date'}
              placeholder={action?.placeholder || 'Choose date'}
              minDate={action?.minDate}
              maxDate={action?.maxDate}
              disabled={loading}
            />
          );
        }

        // Check if this action should use autocomplete (has api property)
        if (action.api) {
          console.log(`🌐 Rendering Autocomplete for ${action.key}`);
          const options = apiData[action.key] || [];
          console.log(`📋 Options for ${action.key}:`, options);

          return (
            <FormControl key={action.key} sx={{ minWidth: 200 }}>
              <Autocomplete
                options={options}
                value={tempFilters[action.key] || null}
                onChange={(event, newValue) => {
                  console.log('🎯 Autocomplete selected:', newValue);
                  setTempFilters({
                    ...tempFilters,
                    [action.key]: newValue || ''
                  });
                }}
                loading={apiLoading[action.key]}
                size="small"
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={action?.title || action.key}
                    slotProps={{
                      input: {
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {apiLoading[action.key] ? (
                              <CircularProgress color="inherit" size={20} />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      },
                    }}
                  />
                )}
                renderOption={(props, option) => (
                  <li {...props} key={option}>
                    {option}
                  </li>
                )}
                isOptionEqualToValue={(option, value) => option === value}
                noOptionsText={
                  apiLoading[action.key]
                    ? "Loading..."
                    : "No units found"
                }
              />
            </FormControl>
          );
        }

        // Default: Regular TextField
        console.log(`📝 Rendering TextField for ${action.key}`);
        return (
          <FormControl key={action.key} sx={{ minWidth: 200 }}>
            <TextField
              label={action?.title || action.key}
              value={tempFilters[action.key] || ''}
              onChange={(e) => {
                setTempFilters({
                  ...tempFilters,
                  [action.key]: e.target.value
                });
              }}
              size="small"
              variant="outlined"
              placeholder={action?.placeholder || `Enter ${action?.title || action.key}`}
            />
          </FormControl>
        );
      })}
      <LoadingButton
        variant="outlined"
        loading={loading}
        onClick={() => {
          setExtraFilters(tempFilters);
        }}
      >
        Apply
      </LoadingButton>
    </>
  );
};

export default CustomFilters;
