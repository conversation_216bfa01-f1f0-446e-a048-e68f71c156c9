import React, { useEffect } from 'react';

import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Typography,
  IconButton,
  Stack
} from '@mui/material';
import type { SelectChangeEvent } from '@mui/material';

import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

import FinancialYearFilterMUI from './FinancialYearFilter';

interface TransactionPeriodFilterProps {
  filters: Record<string, any>;
  setFilters: (filters: Record<string, any>) => void;
  api?: string;
}

const months = [
  { value: '1', label: 'January' },
  { value: '2', label: 'February' },
  { value: '3', label: 'March' },
  { value: '4', label: 'April' },
  { value: '5', label: 'May' },
  { value: '6', label: 'June' },
  { value: '7', label: 'July' },
  { value: '8', label: 'August' },
  { value: '9', label: 'September' },
  { value: '10', label: 'October' },
  { value: '11', label: 'November' },
  { value: '12', label: 'December' }
];

// Generate years from 2017 to 2026
const years = Array.from({ length: 10 }, (_, i) => ({
  value: String(2017 + i),
  label: String(2017 + i)
}));

const TransactionPeriodFilter: React.FC<TransactionPeriodFilterProps> = ({
  filters,
  setFilters,
  api = '/admin/accountsetting/accountset'
}) => {
  // Set default values if not already in filters
  useEffect(() => {
    if (!filters.month || !filters.year) {
      setFilters({
        ...filters,
        month: filters.month || '5', // Default to May
        year: filters.year || new Date().getFullYear().toString()
      });
    }
  }, [filters, setFilters]);

  const handleMonthChange = (event: SelectChangeEvent) => {
    setFilters({
      ...filters,
      month: event.target.value as string
    });
  };

  const handleYearChange = (event: SelectChangeEvent) => {
    setFilters({
      ...filters,
      year: event.target.value as string
    });
  };

  const handlePrevMonth = () => {
    const currentMonth = parseInt(filters.month || '1');
    const currentYear = parseInt(filters.year || new Date().getFullYear().toString());

    if (currentMonth === 1) {
      // If January, go to December of previous year
      setFilters({
        ...filters,
        month: '12',
        year: (currentYear - 1).toString()
      });
    } else {
      // Otherwise just decrease the month
      setFilters({
        ...filters,
        month: (currentMonth - 1).toString()
      });
    }
  };

  const handleNextMonth = () => {
    const currentMonth = parseInt(filters.month || '1');
    const currentYear = parseInt(filters.year || new Date().getFullYear().toString());

    if (currentMonth === 12) {
      // If December, go to January of next year
      setFilters({
        ...filters,
        month: '1',
        year: (currentYear + 1).toString()
      });
    } else {
      // Otherwise just increase the month
      setFilters({
        ...filters,
        month: (currentMonth + 1).toString()
      });
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* Section Headers - on the same line */}
      <Box sx={{ display: 'flex', gap: 4, mb: 1 }}>
        <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'text.secondary' }}>
          View as monthly
        </Typography>
        <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'text.secondary' }}>
          View as yearly
        </Typography>
      </Box>

      <Box sx={{ display: 'flex', flexDirection: 'row', gap: 4 }}>
        {/* Monthly View Section */}
        <Box sx={{ mr: 4 }}>
          <Stack direction="row" spacing={1} alignItems="center">
            <IconButton size="small" onClick={handlePrevMonth}>
              <ChevronLeftIcon />
            </IconButton>

            <FormControl variant="outlined" size="small" sx={{ minWidth: 120 }}>
              <InputLabel id="month-select-label">Month</InputLabel>
              <Select
                labelId="month-select-label"
                id="month-select"
                value={filters.month || '5'} // Default to May
                onChange={handleMonthChange}
                label="Month"
              >
                {months.map((month) => (
                  <MenuItem key={month.value} value={month.value}>
                    {month.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl variant="outlined" size="small" sx={{ minWidth: 100 }}>
              <InputLabel id="year-select-label">Year</InputLabel>
              <Select
                labelId="year-select-label"
                id="year-select"
                value={filters.year || new Date().getFullYear().toString()}
                onChange={handleYearChange}
                label="Year"
              >
                {years.map((year) => (
                  <MenuItem key={year.value} value={year.value}>
                    {year.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <IconButton size="small" onClick={handleNextMonth}>
              <ChevronRightIcon />
            </IconButton>
          </Stack>
        </Box>

        {/* Yearly View Section */}
        <Box>
          <FinancialYearFilterMUI
            filters={filters}
            setFilters={setFilters}
            api={api}
            hideMonthSelect={true}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default TransactionPeriodFilter;
