import React from 'react';
import EnhancedRangePicker from './DateRangePicker';

interface AnyDateRangePickerProps {
  value: [string | null, string | null];
  onChange: (val: [string | null, string | null]) => void;
  label?: string;
  disabled?: boolean;
  size?: 'small' | 'medium';
  error?: boolean;
}

/**
 * A wrapper component for DateRangePicker that allows selecting any date
 * without financial year restrictions
 */
const AnyDateRangePicker: React.FC<AnyDateRangePickerProps> = ({
  value,
  onChange,
  label = 'Select any date range',
  disabled = false,
  size = 'small',
  error = false,
}) => {
  return (
    <EnhancedRangePicker
      value={value}
      onChange={onChange}
      label={label}
      disabled={disabled}
      size={size}
      error={error}
      allowAnyDate={true} // This enables selecting any date
    />
  );
};

export default AnyDateRangePicker;
