import React from 'react';
import { FormControl, InputLabel, Select, MenuItem } from '@mui/material';

interface BalanceTypeFilterProps {
  filters: Record<string, any>;
  setFilters: (filters: Record<string, any>) => void;
  disabled?: boolean;
}

const BalanceTypeFilter: React.FC<BalanceTypeFilterProps> = ({
  filters,
  setFilters,
  disabled = false
}) => {
  const balanceTypes = [
    { value: 'all', label: 'All Balances' },
    { value: 'debit_only', label: 'Debit Balances Only' },
    { value: 'credit_only', label: 'Credit Balances Only' },
    { value: 'non_zero', label: 'Non-Zero Balances' },
    { value: 'zero_balance', label: 'Zero Balances' }
  ];

  const handleChange = (value: string) => {
    setFilters({
      ...filters,
      balance_type: value
    });
  };

  return (
    <FormControl variant="outlined" sx={{ minWidth: 160 }} disabled={disabled}>
      <InputLabel id="balance-type-label" shrink>
        Balance Type
      </InputLabel>
      <Select
        labelId="balance-type-label"
        value={filters.balance_type || 'all'}
        onChange={(e) => handleChange(e.target.value)}
        label="Balance Type"
        size="small"
        sx={{
          '& .MuiInputBase-root': {
            minHeight: '40px'
          }
        }}
      >
        {balanceTypes.map((type) => (
          <MenuItem key={type.value} value={type.value}>
            {type.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default BalanceTypeFilter;
