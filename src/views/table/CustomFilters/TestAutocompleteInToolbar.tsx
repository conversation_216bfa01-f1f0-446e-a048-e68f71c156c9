import React, { useState } from 'react';
import { Box, Typography, Paper, Button, Alert } from '@mui/material';
import TopToolbarCustomActions from '../TopToolbarCustomActions';

// Mock table object for testing
const createMockTable = () => ({
  getAllColumns: () => [
    {
      id: 'id',
      columnDef: { accessorKey: 'id', meta: { default: null } }
    },
    {
      id: 'name',
      columnDef: { accessorKey: 'name', meta: { default: '' } }
    }
  ],
  setRowSelection: () => {},
  setColumnFilters: () => {},
  setShowColumnFilters: () => {},
  getRow: () => ({})
});

// Mock schema with autocomplete in extraFilters
const createMockSchema = () => ({
  tableTitle: 'Test Table with Autocomplete',
  isAddable: true,
  isEditable: true,
  editMode: 'table',
  isSearchable: true,
  enableSelectBy: false,
  enableFilterBy: false,
  options: [],
  actions: [
    {
      title: 'Add Item',
      icon: 'ri-add-line',
      color: 'primary',
      variant: 'contained'
    }
  ],
  tabs: [],
  selectBy: {},
  extraFilters: [
    // Test 1: Autocomplete with explicit type
    {
      key: 'unit_autocomplete_explicit',
      title: 'Unit (Explicit Type)',
      type: 'autocomplete',
      api: 'https://apigw.cubeone.in/api/admin/units/list',
      placeholder: 'Search units...',
      labelField: 'unitNo',
      valueField: 'id'
    },
    // Test 2: Autocomplete with just API property
    {
      key: 'unit_autocomplete_api',
      title: 'Unit (API Only)',
      api: 'https://apigw.cubeone.in/api/admin/units/list',
      placeholder: 'Search units...'
    },
    // Test 3: Regular select for comparison
    {
      key: 'status_select',
      title: 'Status',
      type: 'select',
      options: {
        'active': 'Active',
        'inactive': 'Inactive',
        'pending': 'Pending'
      }
    },
    // Test 4: Regular text field for comparison
    {
      key: 'search_text',
      title: 'Search',
      placeholder: 'Enter search term'
    }
  ]
});

const TestAutocompleteInToolbar: React.FC = () => {
  const [extraFilters, setExtraFilters] = useState({});
  const [rowSelection, setRowSelection] = useState({});
  const [currentTab, setCurrentTab] = useState(null);
  const [columnFilters, setColumnFilters] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editedRows, setEditedRows] = useState([]);
  const [rows, setRows] = useState([]);

  const mockTable = createMockTable();
  const mockSchema = createMockSchema();

  const handleTabChange = (e: any, value: string) => {
    setCurrentTab(value);
  };

  const handleResetFilters = () => {
    setExtraFilters({});
    setColumnFilters([]);
  };

  const handleSaveChanges = () => {
    console.log('Save changes called');
    setIsEditing(false);
  };

  const handleResetChanges = () => {
    console.log('Reset changes called');
    setEditedRows([]);
    setIsEditing(false);
  };

  const refetch = () => {
    console.log('Refetch called');
  };

  const clearFilters = () => {
    setExtraFilters({});
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1400 }}>
      <Typography variant="h4" gutterBottom>
        🧪 Autocomplete in TopToolbarCustomActions
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        This demonstrates the AutocompleteFilter component integrated into the TopToolbarCustomActions.
        The toolbar now includes both the standalone autocomplete and the ones from extraFilters.
      </Alert>

      {/* Mock Toolbar */}
      <Paper sx={{ mb: 3, border: '2px solid #e0e0e0' }}>
        <Typography variant="h6" sx={{ p: 2, bgcolor: '#f5f5f5', borderBottom: '1px solid #e0e0e0' }}>
          TopToolbarCustomActions with Autocomplete
        </Typography>
        
        <TopToolbarCustomActions
          table={mockTable}
          schema={mockSchema}
          setEditedRows={setEditedRows}
          setIsEditing={setIsEditing}
          setRows={setRows}
          handleSaveChanges={handleSaveChanges}
          handleResetChanges={handleResetChanges}
          rowSelection={rowSelection}
          isEditing={isEditing}
          refetch={refetch}
          isGate={false}
          loading={false}
          extraFilters={extraFilters}
          setExtraFilters={setExtraFilters}
          handleTabChange={handleTabChange}
          currentTab={currentTab}
          handleResetFilters={handleResetFilters}
          columnFilters={columnFilters}
          tableIndex={0}
        />
      </Paper>

      {/* Filter Values Display */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            Current Filter Values
          </Typography>
          <Button 
            variant="outlined" 
            color="secondary" 
            onClick={clearFilters}
            size="small"
          >
            Clear All Filters
          </Button>
        </Box>
        
        <pre style={{ 
          backgroundColor: '#f5f5f5', 
          padding: '15px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto',
          border: '1px solid #ddd',
          minHeight: '100px'
        }}>
          {JSON.stringify(extraFilters, null, 2)}
        </pre>
      </Paper>

      {/* Test Instructions */}
      <Paper sx={{ p: 3, bgcolor: '#e8f5e8' }}>
        <Typography variant="h6" gutterBottom color="success.main">
          🔍 Testing Instructions
        </Typography>
        
        <Typography variant="body2" component="div">
          <strong>What to Test:</strong>
          <ol>
            <li><strong>Standalone Autocomplete:</strong> "Test Unit Selection" - Added directly to toolbar</li>
            <li><strong>Schema Autocomplete (Explicit):</strong> "Unit (Explicit Type)" - From extraFilters with type: 'autocomplete'</li>
            <li><strong>Schema Autocomplete (API):</strong> "Unit (API Only)" - From extraFilters with just api property</li>
            <li><strong>Regular Select:</strong> "Status" - For comparison</li>
            <li><strong>Regular TextField:</strong> "Search" - For comparison</li>
          </ol>
          
          <strong>Expected Behavior:</strong>
          <ul>
            <li>✅ All autocomplete fields should fetch unit data from API</li>
            <li>✅ Loading spinners should appear briefly</li>
            <li>✅ Dropdowns should populate with unit numbers (Dup01, GF001, etc.)</li>
            <li>✅ Search should work by typing</li>
            <li>✅ Selections should update the filter values above</li>
            <li>✅ Console should show detailed API logs</li>
          </ul>
          
          <strong>Console Logs to Look For:</strong>
          <ul>
            <li>🚀 AutocompleteFilter: Fetching data for [key] from: [API URL]</li>
            <li>✅ AutocompleteFilter: API Response for [key]: [Response]</li>
            <li>📊 AutocompleteFilter: Extracted data for [key]: [Data]</li>
            <li>🎯 AutocompleteFilter: Transformed options for [key]: [Options]</li>
            <li>🎯 AutocompleteFilter: Selected for [key]: [Selected Value]</li>
          </ul>
        </Typography>
      </Paper>

      {/* Integration Guide */}
      <Paper sx={{ p: 3, mt: 3, bgcolor: '#f8f9fa' }}>
        <Typography variant="h6" gutterBottom>
          📋 How to Add Autocomplete to Your Schema
        </Typography>
        
        <Typography variant="body2" component="div">
          <strong>Method 1: Add to extraFilters in your schema</strong>
          <pre style={{ 
            backgroundColor: '#fff', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            margin: '10px 0',
            border: '1px solid #ddd'
          }}>
{`extraFilters: [
  {
    key: 'unit_selection',
    title: 'Select Unit',
    type: 'autocomplete',
    api: 'https://apigw.cubeone.in/api/admin/units/list',
    labelField: 'unitNo',
    valueField: 'id',
    placeholder: 'Search for a unit...'
  }
]`}
          </pre>
          
          <strong>Method 2: Add directly to TopToolbarCustomActions (as shown above)</strong>
          <pre style={{ 
            backgroundColor: '#fff', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            margin: '10px 0',
            border: '1px solid #ddd'
          }}>
{`<AutocompleteFilter
  filters={extraFilters}
  setFilters={setExtraFilters}
  filterKey="unit_selection"
  label="Select Unit"
  api="https://apigw.cubeone.in/api/admin/units/list"
  labelField="unitNo"
  valueField="id"
/>`}
          </pre>
        </Typography>
      </Paper>
    </Box>
  );
};

export default TestAutocompleteInToolbar;
