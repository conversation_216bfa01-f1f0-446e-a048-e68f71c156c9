import React, { useState, useEffect } from 'react';
import { FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import axios from 'axios';

interface AccountType {
  value: string;
  label: string;
}

interface AccountTypeFilterProps {
  filters: Record<string, any>;
  setFilters: (filters: Record<string, any>) => void;
  api?: string;
  disabled?: boolean;
}

const AccountTypeFilter: React.FC<AccountTypeFilterProps> = ({
  filters,
  setFilters,
  api = '/admin/accounts/account-types',
  disabled = false
}) => {
  const [accountTypes, setAccountTypes] = useState<AccountType[]>([]);
  const [loading, setLoading] = useState(false);

  // Default account types for Trial Balance
  const defaultAccountTypes: AccountType[] = [
    { value: 'all', label: 'All Accounts' },
    { value: 'asset', label: 'Assets' },
    { value: 'liability', label: 'Liabilities' },
    { value: 'income', label: 'Income' },
    { value: 'expense', label: 'Expenses' },
    { value: 'equity', label: 'Equity' }
  ];

  useEffect(() => {
    const fetchAccountTypes = async () => {
      try {
        setLoading(true);
        const response = await axios.get(api);
        if (response.data && Array.isArray(response.data)) {
          setAccountTypes([
            { value: 'all', label: 'All Accounts' },
            ...response.data.map((type: any) => ({
              value: type.id || type.value || type.code,
              label: type.name || type.label || type.title
            }))
          ]);
        } else {
          setAccountTypes(defaultAccountTypes);
        }
      } catch (error) {
        console.warn('Failed to fetch account types, using defaults:', error);
        setAccountTypes(defaultAccountTypes);
      } finally {
        setLoading(false);
      }
    };

    fetchAccountTypes();
  }, [api]);

  const handleChange = (value: string) => {
    setFilters({
      ...filters,
      account_type: value
    });
  };

  return (
    <FormControl variant="outlined" sx={{ minWidth: 150 }} disabled={disabled || loading}>
      <InputLabel id="account-type-label" shrink>
        Account Type
      </InputLabel>
      <Select
        labelId="account-type-label"
        value={filters.account_type || 'all'}
        onChange={(e) => handleChange(e.target.value)}
        label="Account Type"
        size="small"
        sx={{
          '& .MuiInputBase-root': {
            minHeight: '40px'
          }
        }}
      >
        {accountTypes.map((type) => (
          <MenuItem key={type.value} value={type.value}>
            {type.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default AccountTypeFilter;
