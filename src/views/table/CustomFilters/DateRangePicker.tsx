import React, { useState, useEffect, useMemo } from 'react';

import {
  TextField,
  Popover,
  Box,
  Button,
  IconButton,
  CircularProgress,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined';
import ShortcutOutlinedIcon from '@mui/icons-material/ShortcutOutlined';
import { DateCalendar, PickersDay } from '@mui/x-date-pickers';
import type { PickersDayProps } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';

import { parseDateString } from '@/utils/date';
import { useIndexedDB } from '@/hooks/useIndexedDB';
import { tableNames } from '@/data/db';

// — helpers unchanged —
function escapeRegex(str: string) { return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); }

function replaceFinancialPlaceholders(input: string, data: Record<string, string>) {
  const keys = Object.keys(data).filter(k => data[k]);

  if (!keys.length || !input) return input;
  const pattern = new RegExp(keys.map(escapeRegex).join('|'), 'g');

  return input.replace(pattern, m => data[m] || m);
}

function clampDate(d: Dayjs, min: Dayjs, max: Dayjs): Dayjs {
  if (d.isBefore(min, 'month')) return min;
  if (d.isAfter(max, 'month')) return max;

  return d;
}

// Define a custom type for the day component props
interface CustomDayProps extends PickersDayProps<Dayjs> {
  startDate?: Dayjs | null;
  endDate?: Dayjs | null;
}

// — picks days with range highlights —
const CustomDay = (props: CustomDayProps) => {
  const { day, outsideCurrentMonth, startDate, endDate, ...rest } = props;
  const isBetween = startDate && endDate && day.isAfter(startDate, 'day') && day.isBefore(endDate, 'day');
  const isStart = startDate && day.isSame(startDate, 'day');
  const isEnd = endDate && day.isSame(endDate, 'day');

  return (
    <PickersDay
      {...rest}
      day={day}
      outsideCurrentMonth={outsideCurrentMonth}
      selected={isStart || isEnd}
      sx={{
        // Improved styling for days between start and end
        ...(isBetween && {
          bgcolor: 'primary.light',
          color: 'primary.contrastText',
          '&:hover': {
            bgcolor: 'primary.main',
            color: 'primary.contrastText',
          },

          // Add a subtle transition effect
          transition: 'background-color 0.2s ease, color 0.2s ease',
        }),

        // Enhanced styling for start date
        ...(isStart && {
          borderRadius: '50% 0 0 50%',
          bgcolor: 'primary.dark',
          color: 'primary.contrastText',
          fontWeight: 'bold',

          // Add box shadow for better visibility
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          '&:hover': {
            bgcolor: 'primary.dark',
            opacity: 0.9,
          },
        }),

        // Enhanced styling for end date
        ...(isEnd && {
          borderRadius: '0 50% 50% 0',
          bgcolor: 'primary.dark',
          color: 'primary.contrastText',
          fontWeight: 'bold',

          // Add box shadow for better visibility
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          '&:hover': {
            bgcolor: 'primary.dark',
            opacity: 0.9,
          },
        }),

        // Special case for when start and end are the same day
        ...((isStart && isEnd) && {
          borderRadius: '50%',
        }),
      }}
    />
  );
};

interface EnhancedRangePickerProps {
  value: [string | null, string | null];
  onChange: (val: [string | null, string | null]) => void;
  label?: string;
  inputFormat?: string;
  disabled?: boolean;
  minDate?: string;
  maxDate?: string;
  size?: 'small' | 'medium';
  error?: boolean;
  allowAnyDate?: boolean; // New prop to allow selecting any date
}

export default function EnhancedRangePicker({
  value,
  onChange,
  label = 'Select date range',
  inputFormat = 'YYYY-MM-DD',
  disabled = false,
  size = 'small',
  minDate: uiMin,
  maxDate: uiMax,
  error = false,
  allowAnyDate = true,
}: EnhancedRangePickerProps) {
  const theme = useTheme();

  // — fetch financial years —
  const tables = useMemo(() => [tableNames.financialYears], []);
  const { getAllValue, isDBConnecting } = useIndexedDB('soc-db', tables);

  const [financialData, setFinancialData] = useState({
    financialYearStart: '',
    financialYearEnd: '',
    currentFinancialStart: '',
    currentFinancialEnd: '',
    lastFinancialStart: '',
    lastFinancialEnd: '',
  });

  useEffect(() => {

    if (isDBConnecting) return;

    (async () => {
      const fy = await getAllValue(tableNames.financialYears);

      const sorted = [...fy].sort(
        (a, b) => new Date(a.fy_start_date).valueOf() - new Date(b.fy_start_date).valueOf()
      );

      const idx = sorted.findIndex(y => !y.closed);
      const curr = sorted[idx] || {};
      const last = idx > 0 ? sorted[idx - 1] : {};

      setFinancialData({
        financialYearStart: sorted[0]?.fy_start_date || '',
        financialYearEnd: sorted[sorted.length - 1]?.fy_end_date || '',
        currentFinancialStart: curr.fy_start_date || '',
        currentFinancialEnd: curr.fy_end_date || '',
        lastFinancialStart: last.fy_start_date || '',
        lastFinancialEnd: last.fy_end_date || '',
      });
    })();
  }, [getAllValue, isDBConnecting]);

  // — compute bounds —
  const minDate = allowAnyDate
    ? dayjs('1900-01-01') // Allow very old dates when allowAnyDate is true
    : parseDateString(
        replaceFinancialPlaceholders(uiMin || financialData.financialYearStart, financialData),
        false
      );

  const maxDate = allowAnyDate
    ? dayjs('2100-12-31') // Allow far future dates when allowAnyDate is true
    : parseDateString(
        replaceFinancialPlaceholders(uiMax || financialData.financialYearEnd, financialData),
        true
      );

  // — popover state —
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const open = Boolean(anchorEl);
  const [showShortcuts, setShowShortcuts] = useState(false);

  // — local range state —
  const [start, setStart] = useState<Dayjs | null>(value[0] ? dayjs(value[0]) : null);
  const [end, setEnd] = useState<Dayjs | null>(value[1] ? dayjs(value[1]) : null);

  useEffect(() => {
    setStart(value[0] ? dayjs(value[0]) : null);
    setEnd(value[1] ? dayjs(value[1]) : null);
  }, [value]);

  const display = start && end
    ? `${start.format(inputFormat)} → ${end.format(inputFormat)}`
    : '';

  // — figure out which month to show —
  // Set initial months for the two calendars - always show different months
  // Left calendar shows current month, right calendar shows next month
  const currentMonth = dayjs().startOf('month');
  const nextMonth = currentMonth.add(1, 'month');

  // Initialize reference dates for the two calendars
  const initialRefLeft = clampDate(currentMonth, minDate, maxDate);
  const initialRefRight = clampDate(nextMonth, minDate, maxDate);

  // Store the reference dates in state to maintain independence
  const [refLeft, setRefLeft] = useState(initialRefLeft);
  const [refRight, setRefRight] = useState(initialRefRight);

  // Update reference dates when min/max dates change
  useEffect(() => {
    setRefLeft(prev => clampDate(prev || initialRefLeft, minDate, maxDate));
    setRefRight(prev => clampDate(prev || initialRefRight, minDate, maxDate));
  }, [minDate, maxDate, initialRefLeft, initialRefRight]);

  // — build & filter shortcuts out of bounds —
  const rawShortcuts = [
    {
      label: 'Current Financial Year',
      description: 'Select the entire current financial year',
      range: [dayjs(financialData.currentFinancialStart), dayjs(financialData.currentFinancialEnd)] as const,
      icon: 'ri-calendar-check-line'
    },
    {
      label: 'Last Financial Year',
      description: 'Select the entire previous financial year',
      range: [dayjs(financialData.lastFinancialStart), dayjs(financialData.lastFinancialEnd)] as const,
      icon: 'ri-calendar-2-line'
    },
    {
      label: 'Last 7 Days',
      description: 'Select the past week',
      range: [dayjs().subtract(7, 'day'), dayjs()] as const,
      icon: 'ri-calendar-event-line'
    },
    {
      label: 'Last Month',
      description: 'Select the past 30 days',
      range: [dayjs().subtract(1, 'month'), dayjs()] as const,
      icon: 'ri-calendar-todo-line'
    },
    {
      label: 'Last Year',
      description: 'Select the past 365 days',
      range: [dayjs().subtract(1, 'year'), dayjs()] as const,
      icon: 'ri-calendar-line'
    },
  ];

  // Filter all shortcuts by min/max date bounds (only when not allowing any date)
  const shortcuts = allowAnyDate
    ? rawShortcuts // Show all shortcuts when any date is allowed
    : rawShortcuts.filter(({ range: [s, e] }) =>
        !s.isBefore(minDate, 'day') && !e.isAfter(maxDate, 'day')
      );

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      {/* trigger field */}
      <TextField
        label={label}
        value={display}
        size={size}
        onClick={e => !disabled && setAnchorEl(e.currentTarget)}
        disabled={disabled || isDBConnecting}
        slotProps={{
          input: {
            endAdornment: (
              <>
                {isDBConnecting && <CircularProgress size={20} />}
                <Tooltip title="Open Calendar">
                  <IconButton size="small" onClick={() => setAnchorEl(null)}>
                    <CalendarTodayOutlinedIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title={showShortcuts ? 'Hide Shortcuts' : 'Show Shortcuts'}>
                  <IconButton size="small" onClick={() => setShowShortcuts(v => !v)}>
                    <ShortcutOutlinedIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </>
            )
          }
        }}
        fullWidth={true}
        error={error || Boolean(start && end && start.isAfter(end, 'day'))}
        helperText={start && end && start.isAfter(end, 'day') ? 'Start date must be before end date' : ''}
      />

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={() => { setAnchorEl(null); setShowShortcuts(false); }}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
        slotProps={{
          paper: {
            sx: {
              width: { xs: '95vw', sm: 620 },
              maxWidth: '95vw',
              bgcolor: theme.palette.background.paper,
              color: theme.palette.text.primary,
            }
          }
        }}
      >
        {/* header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            px: 2,
            py: 1,
            bgcolor: theme.palette.background.default,
          }}
        >
          <Typography variant="body2" fontWeight="medium">{label}</Typography>
          <IconButton size="small" onClick={() => setShowShortcuts(v => !v)}>
            {showShortcuts
              ? <CalendarTodayOutlinedIcon fontSize="small" />
              : <ShortcutOutlinedIcon fontSize="small" />}
          </IconButton>
        </Box>

        {showShortcuts ? (
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
              gap: 1,
              p: 2,
              bgcolor: theme.palette.background.paper,
            }}
          >
            {shortcuts.map(({ label, description, range, icon }) => {
              const [s, e] = range;

              // Check if this shortcut is currently selected
              const isSelected = start && end &&
                s && e &&
                start.format(inputFormat) === s.format(inputFormat) &&
                end.format(inputFormat) === e.format(inputFormat);

              return (
                <Button
                  key={label}
                  fullWidth
                  variant={isSelected ? "contained" : "outlined"}
                  color={isSelected ? "primary" : "inherit"}
                  onClick={() => {
                    // Apply the range without switching views
                    setAnchorEl(null);
                    onChange([s ? s.format(inputFormat) : null, e ? e.format(inputFormat) : null]);
                  }}
                  sx={{
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    padding: '12px',
                    transition: 'all 0.2s ease',
                    backgroundColor: isSelected ? 'primary.main' : 'background.paper',
                    borderColor: isSelected ? 'primary.dark' : 'divider',
                    color: isSelected ? 'primary.contrastText' : 'text.primary',
                    position: 'relative',
                    overflow: 'hidden',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                      backgroundColor: isSelected ? 'primary.dark' : 'action.hover',
                      borderColor: isSelected ? 'primary.dark' : 'primary.main'
                    },
                    ...(isSelected && {
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        width: '0',
                        height: '0',
                        borderStyle: 'solid',
                        borderWidth: '0 24px 24px 0',
                        borderColor: 'transparent primary.dark transparent transparent',
                        transform: 'rotate(0deg)',
                      }
                    })
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', mb: 0.5 }}>
                    {icon && <i className={icon} style={{
                      marginRight: '8px',
                      fontSize: '1.2rem',
                      color: isSelected ? 'inherit' : theme.palette.primary.main
                    }} />}
                    <Typography variant="subtitle2" fontWeight="bold" color={isSelected ? 'inherit' : 'text.primary'}>
                      {label}
                    </Typography>
                  </Box>
                  {description && (
                    <Typography variant="caption" color={isSelected ? 'inherit' : 'text.secondary'} sx={{ opacity: isSelected ? 1 : 0.9 }}>
                      {description}
                    </Typography>
                  )}
                  <Typography
                    variant="caption"
                    color={isSelected ? 'inherit' : 'primary'}
                    sx={{ mt: 0.5, fontWeight: 'medium' }}
                  >
                    {s?.format(inputFormat)} → {e?.format(inputFormat)}
                  </Typography>
                </Button>
              );
            })}
            <Button
              fullWidth
              variant="outlined"
              onClick={() => setShowShortcuts(false)}
              startIcon={<i className="ri-arrow-left-line" />}
              sx={{
                mt: 1,
                padding: '10px',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                }
              }}
            >
              Back to Calendar
            </Button>
          </Box>
        ) : (
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 0, sm: 2 },
              p: 2,
              bgcolor: theme.palette.background.paper,
            }}
          >
            <DateCalendar
              value={start}
              onChange={d => {
                // Simplified date selection logic for first calendar
                if (!start && !end) {
                  // If no dates are selected, set the first date
                  setStart(d);
                } else if (start && !end) {
                  // If only start date is selected
                  // Always create a range with the earlier date as start and later date as end
                  if (d && d.isBefore(start, 'day')) {
                    setEnd(start);
                    setStart(d);
                  } else {
                    setEnd(d);
                  }
                } else if (!start && end) {
                  // If only end date is selected (rare case)
                  // Always create a range with the earlier date as start and later date as end
                  if (d && d.isBefore(end, 'day')) {
                    setStart(d);
                  } else {
                    setStart(end);
                    setEnd(d);
                  }
                } else {
                  // If both dates are set, start a new selection
                  setStart(d);
                  setEnd(null);
                }
              }}
              referenceDate={refLeft}
              onMonthChange={(newMonth) => {
                // Allow independent month navigation
                setRefLeft(newMonth);
              }}
              minDate={minDate}
              maxDate={maxDate}
              slots={{ day: CustomDay }}
              slotProps={{ day: { startDate: start, endDate: end } } as any}
            />
            <DateCalendar
              value={end}
              onChange={d => {
                // Simplified date selection logic for second calendar
                if (!start && !end) {
                  // If no dates are selected, set the first date
                  setStart(d);
                } else if (start && !end) {
                  // If only start date is selected
                  // Always create a range with the earlier date as start and later date as end
                  if (d && d.isBefore(start, 'day')) {
                    setEnd(start);
                    setStart(d);
                  } else {
                    setEnd(d);
                  }
                } else if (!start && end) {
                  // If only end date is selected (rare case)
                  // Always create a range with the earlier date as start and later date as end
                  if (d && d.isBefore(end, 'day')) {
                    setStart(d);
                  } else {
                    setStart(end);
                    setEnd(d);
                  }
                } else {
                  // If both dates are set, start a new selection
                  setStart(d);
                  setEnd(null);
                }
              }}
              referenceDate={refRight}
              onMonthChange={(newMonth) => {
                // Allow independent month navigation
                setRefRight(newMonth);
              }}
              minDate={minDate}
              maxDate={maxDate}
              slots={{ day: CustomDay }}
              slotProps={{ day: { startDate: start, endDate: end } } as any}
            />
          </Box>
        )}

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            p: 1,
            bgcolor: theme.palette.background.default,
          }}
        >
          {!showShortcuts && (
            <>
              <Button
                size="small"
                onClick={() => setAnchorEl(null)}
                startIcon={<i className="ri-close-line" />}
                sx={{
                  mr: 1,
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    backgroundColor: 'error.light',
                    color: 'error.dark'
                  }
                }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={() => {
                  // Apply the range without switching views
                  setAnchorEl(null);
                  onChange([start ? start.format(inputFormat) : null, end ? end.format(inputFormat) : null]);
                }}
                disabled={!start || !end}
                startIcon={<i className="ri-check-line" />}
                sx={{
                  transition: 'all 0.2s ease',
                  '&:not(:disabled):hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                  }
                }}
              >
                Apply Selection
              </Button>
            </>
          )}
        </Box>
      </Popover>
    </LocalizationProvider>
  );
}
