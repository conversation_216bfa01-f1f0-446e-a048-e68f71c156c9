import React, { useMemo } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  Paper,
  Chip,
  IconButton,
  Divider
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import ClearIcon from '@mui/icons-material/Clear';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import dayjs, { Dayjs } from 'dayjs';

interface DateFilterItem {
  id: string | number;
  date: string | Date;
  title: string;
  description?: string;
  [key: string]: any;
}

interface DateFilterMUIProps {
  selectedDate: Date | null;
  onDateChange: (date: Date | null) => void;
  items: DateFilterItem[];
  label?: string;
  placeholder?: string;
}

const DateFilterMUI: React.FC<DateFilterMUIProps> = ({
  selectedDate,
  onDateChange,
  items,
  label = "Filter by Date",
  placeholder = "Select a date"
}) => {
  // Convert Date to Dayjs for MUI DatePicker
  const dayjsValue = selectedDate ? dayjs(selectedDate) : null;

  // Handle date change from MUI DatePicker
  const handleDateChange = (newValue: Dayjs | null) => {
    onDateChange(newValue ? newValue.toDate() : null);
  };

  // Filter items based on selected date
  const filteredItems = useMemo(() => {
    if (!selectedDate) return items;

    const selectedDateString = dayjs(selectedDate).format('YYYY-MM-DD');
    
    return items.filter(item => {
      const itemDateString = dayjs(item.date).format('YYYY-MM-DD');
      return itemDateString === selectedDateString;
    });
  }, [items, selectedDate]);

  // Clear the selected date
  const handleClear = () => {
    onDateChange(null);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ width: '100%', maxWidth: 600, mx: 'auto', p: 2 }}>
        {/* Header */}
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CalendarTodayIcon color="primary" />
          {label}
        </Typography>

        {/* Date Picker Section */}
        <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <DatePicker
              label={placeholder}
              value={dayjsValue}
              onChange={handleDateChange}
              sx={{ flex: 1 }}
              slotProps={{
                textField: {
                  size: 'small',
                  fullWidth: true
                }
              }}
            />
            
            {selectedDate && (
              <IconButton 
                onClick={handleClear}
                color="secondary"
                size="small"
                title="Clear date filter"
              >
                <ClearIcon />
              </IconButton>
            )}
          </Box>

          {/* Selected Date Display */}
          {selectedDate && (
            <Box sx={{ mt: 2 }}>
              <Chip
                label={`Filtered by: ${dayjs(selectedDate).format('MMM DD, YYYY')}`}
                color="primary"
                variant="outlined"
                onDelete={handleClear}
                deleteIcon={<ClearIcon />}
              />
            </Box>
          )}
        </Paper>

        {/* Results Section */}
        <Paper elevation={1} sx={{ overflow: 'hidden' }}>
          <Box sx={{ p: 2, bgcolor: 'grey.50' }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
              Results ({filteredItems.length} {filteredItems.length === 1 ? 'item' : 'items'})
            </Typography>
          </Box>
          
          <Divider />

          {filteredItems.length === 0 ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                {selectedDate 
                  ? `No items found for ${dayjs(selectedDate).format('MMM DD, YYYY')}`
                  : 'Select a date to filter items'
                }
              </Typography>
            </Box>
          ) : (
            <List sx={{ py: 0 }}>
              {filteredItems.map((item, index) => (
                <React.Fragment key={item.id}>
                  <ListItem sx={{ py: 2 }}>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                          {item.title}
                        </Typography>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                            {dayjs(item.date).format('MMM DD, YYYY [at] h:mm A')}
                          </Typography>
                          {item.description && (
                            <Typography variant="body2" color="text.secondary">
                              {item.description}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < filteredItems.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </Paper>

        {/* Summary Footer */}
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Showing {filteredItems.length} of {items.length} total items
          </Typography>
        </Box>
      </Box>
    </LocalizationProvider>
  );
};

export default DateFilterMUI;
