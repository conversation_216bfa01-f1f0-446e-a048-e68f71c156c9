import React, { useState, useEffect } from 'react';

import { 
  FormControl, 
  Autocomplete, 
  TextField, 
  CircularProgress 
} from '@mui/material';
import axios from 'axios';

interface AutocompleteFilterProps {
  filters: Record<string, any>;
  setFilters: (filters: Record<string, any>) => void;
  filterKey: string;
  label?: string;
  placeholder?: string;
  api: string;
  labelField?: string;
  valueField?: string;
  disabled?: boolean;
  size?: 'small' | 'medium';
}

const AutocompleteFilter: React.FC<AutocompleteFilterProps> = ({
  filters,
  setFilters,
  filterKey,
  label = 'Select Option',
  placeholder = 'Choose an option',
  api,
  labelField = 'unitNo',
  valueField = 'id',
  disabled = false,
  size = 'small'
}) => {
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch data from API
  useEffect(() => {
    const fetchData = async () => {
      if (!api) return;

      console.log(`🚀 AutocompleteFilter: Fetching data for ${filterKey} from:`, api);
      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(api);

        console.log(`✅ AutocompleteFilter: API Response for ${filterKey}:`, response.data);

        const data = response.data?.data || response.data || [];
        
        console.log(`📊 AutocompleteFilter: Extracted data for ${filterKey}:`, data);

        // Transform data based on labelField and valueField
        const transformedOptions = data.map((item: any, index: number) => {
          let label = '';
          let value = '';

          // Get label
          if (item[labelField]) {
            label = String(item[labelField]);
          } else if (item.name) {
            label = String(item.name);
          } else if (item.title) {
            label = String(item.title);
          } else if (item.unitNo) {
            label = String(item.unitNo);
          } else {
            label = `Item ${index + 1}`;
          }

          // Get value
          if (item[valueField]) {
            value = item[valueField];
          } else if (item.id) {
            value = item.id;
          } else if (item.value) {
            value = item.value;
          } else {
            value = label;
          }

          return {
            label,
            value,
            originalData: item
          };
        });

        console.log(`🎯 AutocompleteFilter: Transformed options for ${filterKey}:`, transformedOptions);
        setOptions(transformedOptions);

      } catch (error) {
        console.error(`❌ AutocompleteFilter: Error fetching data for ${filterKey}:`, error);
        setError('Failed to load options');
        setOptions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [api, filterKey, labelField, valueField]);

  // Get current selected value
  const currentValue = options.find(option => option.value === filters[filterKey]) || null;

  const handleChange = (event: any, newValue: any) => {
    console.log(`🎯 AutocompleteFilter: Selected for ${filterKey}:`, newValue);
    setFilters({
      ...filters,
      [filterKey]: newValue?.value || ''
    });
  };

  return (
    <FormControl variant="outlined" sx={{ minWidth: 200 }}>
      <Autocomplete
        options={options}
        value={currentValue}
        onChange={handleChange}
        loading={loading}
        disabled={disabled || loading}
        size={size}
        getOptionLabel={(option) => option?.label || ''}
        isOptionEqualToValue={(option, value) => option?.value === value?.value}
        renderInput={(params) => (
          <TextField
            {...params}
            label={label}
            placeholder={placeholder}
            error={!!error}
            helperText={error}
            slotProps={{
              inputLabel: {
                shrink: true,
              },
              input: {
                ...params.InputProps,
                endAdornment: (
                  <>
                    {loading ? (
                      <CircularProgress color="inherit" size={20} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </>
                ),
              },
            }}
            sx={{
              '& .MuiInputBase-root': {
                minHeight: '40px'
              }
            }}
          />
        )}
        renderOption={(props, option) => (
          <li {...props} key={option.value}>
            <div style={{ width: '100%' }}>
              <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                {option.label}
              </div>
              {option.originalData && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                  {option.originalData.buildingFloorNo && `${option.originalData.buildingFloorNo} | `}
                  {option.originalData.type && `${option.originalData.type} | `}
                  {option.originalData.occupancy && option.originalData.occupancy}
                </div>
              )}
            </div>
          </li>
        )}
        noOptionsText={
          loading 
            ? "Loading..." 
            : error 
            ? "Error loading options"
            : "No options found"
        }
        sx={{
          '& .MuiInputBase-root': {
            minHeight: '40px'
          }
        }}
      />
    </FormControl>
  );
};

export default AutocompleteFilter;
