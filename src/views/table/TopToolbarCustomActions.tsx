import React, { use<PERSON><PERSON>back, useState } from "react";

import { useRouter } from "next/navigation";

import Grid2 from "@mui/material/Grid2";
import { Box, Button, ButtonGroup, Tab, Tabs, Typography } from "@mui/material";
import LoadingButton from "@mui/lab/LoadingButton";
import { toast } from "react-toastify";
import axios from "axios";
import {
  MRT_GlobalFilterTextField,
  MRT_ShowHideColumnsButton,
  MRT_ToggleFiltersButton,
  MRT_ToggleFullScreenButton,
} from "material-react-table";

import { Actions } from "./Actions";
import CustomFilters from "./CustomFilters/CustomFilters";
import RowSelectionActions from "./RowSelectionActions";
import OptionsMenu from "./OptionsMenu";
import { FilterBy } from "./FilterBy";
import SelectBy from "./SelectBy";
import { replacePlaceholders } from "@/utils/replacePlaceholders";
import { ensureAdminModeInPath } from "@/utils/string";

/**
 * Custom toolbar for multi-table layouts.
 */
export interface TopToolbarCustomActionsProps {
  table: any;
  schema: any;
  setEditedRows: (updater: any) => void;
  setIsEditing: (v: boolean) => void;
  setRows: (updater: any) => void;
  handleSaveChanges: () => void;
  handleResetChanges: () => void;
  rowSelection: Record<string, boolean>;
  isEditing: boolean;
  refetch: () => void;
  isGate: boolean;
  loading: boolean;
  extraFilters: Record<string, any>;
  setExtraFilters: (v: Record<string, any>) => void;
  handleTabChange: (e: any, value: string) => void;
  currentTab: string | null;
  handleResetFilters: () => void;
  columnFilters: any[];
  tableIndex: number;
}

const TopToolbarCustomActions: React.FC<TopToolbarCustomActionsProps> = ({
  table,
  schema,
  setEditedRows,
  setIsEditing,
  setRows,
  handleSaveChanges,
  handleResetChanges,
  rowSelection,
  isEditing,
  refetch,
  isGate,
  loading: tableLoading,
  extraFilters,
  setExtraFilters,
  handleTabChange,
  currentTab,
  handleResetFilters,
  columnFilters,
  tableIndex,
}) => {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // Toolbar setup

  // Handler to add a new row
  const handleAddRow = useCallback(() => {
    try {
      const cols = table.getAllColumns();

      if (!cols.length) throw new Error("No columns defined.");

      const newRow = cols.reduce((acc: any, col: any) => {
        const key = col.columnDef?.accessorKey || col.id;

        acc[key] = col.columnDef?.meta?.default ?? null;

        return acc;
      }, {});

      if (schema.editMode === "table") {

        setEditedRows((prev: any[]) => [
          { ...newRow, id: `row_${Date.now()}`, _action: "add" },
          ...prev,
        ]);
        setIsEditing(true);
      } else {
        setRows((prev: any[]) => [
          { ...newRow, id: `row_${Date.now()}`, _action: "add" },
          ...prev,
        ]);
      }
    } catch (err: any) {
      console.warn(err);
      toast.error(err.message || "Failed to add row.");
    }
  }, [table, schema, setEditedRows, setIsEditing, setRows]);

  const handleActionClick = useCallback(async (action) => {
    if (!action) return;

    const selected = Object.keys(rowSelection);

    if (action?.form) {
      router.push(`/admin/${action?.mode}/form/${action.form}?id=${selected}`);
    } else if (action?.redirect && typeof action.redirect === "string") {
      if (/(:id|\[id\])/.test(action?.redirect)) {
        const url = replacePlaceholders(action.redirect, {
          id: selected
        });

        router.push(ensureAdminModeInPath(url, action?.mode || "society"));
      } else router.push(ensureAdminModeInPath(action.redirect + selected.join(","), action?.mode || "society"));
    } else if (action.api) {
      setLoading(true);
      const method = action.api.method || "post";
      const useQueryIn = ["get", "delete"];

      const url = replacePlaceholders(action.api.url, {
        id: selected,
      });

      const formatData = (method) => {
        if (useQueryIn.includes(method)) {
          return { params: selected };
        } else {
          return { data: selected };
        }
      };

      try {
        const { data, status } = await axios({
          method,
          url,
          ...formatData(method),
          baseURL: isGate ? "/api" : process.env.NEXT_PUBLIC_API_URL,
          validateStatus: () => true,
          withCredentials: true,
        });

        toast(data?.message || "Please try again!", {
          type: status < 300 ? "success" : "error",
        });

        if (status < 300) {
          table.setRowSelection({});
        }
      } catch (err) {
        toast?.error(err?.message || "Something Went Wrong!");
      } finally {
        setLoading(false);
      }
    }

    if (action?.rerender) {
      refetch();
    }
  },
    [rowSelection, router, table, isGate, refetch]
  );

  const handleFilterByChange = useCallback((updated = {}) => {
    table?.setColumnFilters((p) => {
      Object.keys(updated).forEach((key) => {
        const index = p.findIndex((f) => f.id === key);

        if (index > -1) {
          p[index].value = updated[key];
        } else {
          p.push({ id: key, value: updated[key] });
        }
      });

      return p;
    });

    const showFilters = columnFilters?.some((item) => {
      const value = updated[item.id] || item.value;

      return Array.isArray(value) ? value.length > 0 : !!value;
    })

    table?.setShowColumnFilters(showFilters);
  },
    [columnFilters, table]
  );

  const handleSelectByChange = useCallback((id, value) => {
    table?.setColumnFilters((p) => {
      const index = p.findIndex((f) => f.id === id);

      if (index > -1) {
        p[index].value = value;
      } else {
        p.push({ id, value });
      }

      return p;
    });

    const showFilters = columnFilters?.some((item) => {
      const itemValue = item?.id === id ? value : item?.value;

      return Array.isArray(itemValue) ? itemValue.length > 0 : !!itemValue;
    })

    table?.setShowColumnFilters(showFilters);
  },
    [columnFilters, table]
  );

  return (
    <Box sx={{ position: "relative" }}>
      {(loading || tableLoading) && <Box sx={{ /* overlay styles */ }} />}
      <Grid2 container direction="column" px={2} pt={2} spacing={1}>
        {/* Top Row */}
        <Grid2 container alignItems="center" justifyContent="space-between">
          {/* Left side */}
          <Grid2 container alignItems="center" spacing={1}>
            {schema.tableTitle && (
              <Typography variant="h5">
                {Array.isArray(schema.tableTitle)
                  ? schema.tableTitle[tableIndex]
                  : schema.tableTitle}
              </Typography>
            )}
            {schema.options.length > 0 && <OptionsMenu options={schema.options} />}
            {schema.isAddable && tableIndex === 0 && (
              <LoadingButton
                size="small"
                onClick={handleAddRow}
                startIcon={<i className="ri-add-line" />}
                variant="outlined"
                loading={loading || tableLoading}
              >
                Add Row
              </LoadingButton>
            )}
            {schema.isEditable && schema.editMode === "table" && tableIndex === 0 && (
              <ButtonGroup variant="outlined">
                <LoadingButton
                  onClick={handleSaveChanges}
                  size="small"
                  disabled={!isEditing}
                >
                  Save Changes
                </LoadingButton>
                <LoadingButton
                  onClick={handleResetChanges}
                  size="small"
                  disabled={!isEditing}
                >
                  Reset Changes
                </LoadingButton>
              </ButtonGroup>
            )}
            <RowSelectionActions
              rowSelection={rowSelection}
              schema={schema}
              handleActionClick={handleActionClick}
              loading={loading}
              tableLoading={tableLoading}
              getRow={table.getRow}
            />
          </Grid2>
          {/* Right side */}
          <Actions actions={schema.actions} />
        </Grid2>
        {/* Bottom Row */}
        {schema.enableSelectBy || schema.enableFilterBy || schema.extraFilters.length ? (
          <Grid2 container alignItems="center" justifyContent="space-between">
            {/* Left filters */}
            <Grid2 container spacing={1} alignItems="center">
              {schema.enableSelectBy && <SelectBy handleSelectByChange={handleSelectByChange} keysMap={schema.selectBy} />}
              {schema.enableFilterBy && <FilterBy schema={schema} handleClick={handleFilterByChange} filters={columnFilters} />}
              {schema.extraFilters.length > 0 && <CustomFilters extraFilters={extraFilters} setExtraFilters={setExtraFilters} extraActions={schema?.extraFilters} />}

              {/* <DateFilterMUI
                selectedDate={extraFilters?.date}
                onDateChange={(date) => setExtraFilters({ ...extraFilters, date })}
                items={[]}
              /> */}

            </Grid2>
            {/* Right toggles */}
            <Grid2>
              {schema.isSearchable && <MRT_GlobalFilterTextField table={table} />}
              {(schema.enableSelectBy || schema.enableFilterBy || schema.extraFilters.length > 0) && <MRT_ToggleFiltersButton table={table} />}
              <MRT_ShowHideColumnsButton table={table} />
              <MRT_ToggleFullScreenButton table={table} />
              <Button size="small" onClick={handleResetFilters} disabled={loading || tableLoading}>Reset Filters</Button>
              <Button size="small" onClick={refetch} disabled={loading || tableLoading}>Refresh</Button>
            </Grid2>
          </Grid2>
        ) : null}
        {/* Tabs Row */}
        {schema.tabs.length > 0 && tableIndex === 0 && (
          <Grid2>
            <Tabs value={currentTab || String(schema.tabs[0]).toLowerCase()} onChange={handleTabChange}>
              {schema.tabs.map((tab) => <Tab key={tab} label={tab} value={String(tab).toLowerCase()} />)}
            </Tabs>
          </Grid2>
        )}
      </Grid2>
    </Box>
  );
};

export default TopToolbarCustomActions;