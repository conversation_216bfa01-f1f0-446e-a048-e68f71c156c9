import { useRouter } from 'next/navigation'

import { <PERSON><PERSON>, Grid2 } from '@mui/material'
import axios from 'axios'
import { toast } from 'react-toastify'

import OptionsMenu from './OptionsMenu'
import { ensureAdminModeInPath } from '@/utils/string'

type color = 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info'
type variant = 'text' | 'outlined' | 'contained'

export type actionType = {
  id(arg0: string, id: any): unknown
  selected: boolean
  icon: string
  title: string
  add_row?: boolean
  color?: color
  mode?: 'society' | 'gate'
  api?: {
    url: string
    method: string
    type?: 'download'
  }
  form?: string
  redirect?: string
  variant?: variant
  disabled?: boolean
  options?: Array<actionType>
  go_back?: boolean
}

type AllowedColor =
  | "primary"
  | "secondary"
  | "success"
  | "error"
  | "warning"
  | "info";

type AllowedVariant = 'text' | 'outlined' | 'contained';

export const validateColor = (color: string = 'primary'): AllowedColor => {
  const colors = ['primary', 'secondary', 'success', 'error', 'warning', 'info'] as const;

  return (colors.includes(color as AllowedColor) ? (color as AllowedColor) : 'primary');
}

export const validateVariant = (variant: string = 'outlined'): AllowedVariant => {
  const variants = ['text', 'outlined', 'contained'] as const;

  return variants.includes(variant as AllowedVariant) ? (variant as AllowedVariant) : 'outlined';
};

export const Actions = ({ actions = [] }: { actions: actionType[] }) => {
  const { push, back } = useRouter()

  /**
   * The onClick function handles different actions such as redirecting, making API calls, displaying
   * toast messages, and setting a form key.
   * @param {actionType} action - The `action` parameter is an object that contains three properties:
   * `api`, `form`, and `redirect`.
   */
  const onClick = async (action: actionType, fallback = () => { }) => {
    try {
      const { api, form, redirect, mode = "society" } = action || {}

      if (action?.go_back) {
        back();
      } else if (redirect) {
        push(ensureAdminModeInPath(redirect, mode))
      } else if (api?.url) {

        let id = null;

        if (api?.type === 'download') {
          id = toast.loading('Downloading...', {
            icon: <i className='line-md-downloading-loop' style={{ fontSize: "32px" }} />,
          })
        }

        const result = await axios(api?.url, {
          method: api?.method || 'GET',
          validateStatus: () => true,
          baseURL: process.env.NEXT_PUBLIC_API_URL,
          withCredentials: true,
        })

        if (api?.type === 'download') {
          if (result?.data?.status === 'success') {
            const link = document.createElement('a')

            link.href = result?.data?.data?.url
            link.setAttribute('download', 'file')

            link.click()
            link.remove()

            toast.update(id, {
              render: result?.data?.message || 'Downloaded Successfully', type: "success", isLoading: false,
              autoClose: (process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000) as number,
            });
          } else {
            toast.update(id, {
              render: result?.data?.message || 'Download Failed', type: "error", isLoading: false,
              autoClose: (process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000) as number
            });
          }
        } else {
          if (result?.data?.status === 'success') {
            if (api?.type === "pdf") {
              window.open(result?.data?.data?.url, "_blank");
            }

            toast.success(result?.data?.message || 'Success')
          } else {
            toast.error(result?.data?.message || 'Something went wrong!')
          }
        }

        if (result?.data?.action) {
          onClick(result?.data?.action)
        }
      } else if (form) {        
        push(`/admin/${mode}/form/${form}`)
      } else {
        fallback?.()
      }
    } catch (error) {
      console.log(error)
    }
  }

  /* The `return` statement is rendering a list of buttons or an options menu based on the `actions`
 array. */
  return (
    <Grid2>
      {actions.map((item: actionType, id: number) => {
        const { icon, title, color = 'primary', variant = 'contained', disabled = false, options = [] } = item || {}

        // const handleOptionChange = (newValue: string) => {
        //   const option = options.find(p => p.title === newValue)
        //   if (option) onClick(option)
        // }

        if(options?.length) return (
          <OptionsMenu
            key={id}
            title={title}
            options={options}
            variant={validateVariant(variant)}
            color={validateColor(color)}
            icon={icon}
            onClick={onClick}
          />
        );
        
        return (
          <Button
            key={id}
            color={validateColor(color)}
            variant={validateVariant(variant)}
            startIcon={<i className={icon} />}
            onClick={() => onClick(item)}
            disabled={disabled}
            size='small'
            sx={{ m: 1 }}
          >
            {title}
          </Button>
        ) })}
    </Grid2>
  )
}
