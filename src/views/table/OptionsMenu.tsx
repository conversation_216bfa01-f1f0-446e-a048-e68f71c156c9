import { useId, useState } from 'react';
import type { MouseEvent } from 'react';

import { useRouter, useSearchParams, usePathname } from 'next/navigation';

// ** MUI Imports
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';

import LoadingButton from '@mui/lab/LoadingButton';
import { Icon } from '@iconify/react/dist/iconify.js';

import KeyboardArrowDownRoundedIcon from '@mui/icons-material/KeyboardArrowDownRounded';
import type { ButtonOwnProps } from '@mui/material';

import { replacePlaceholders } from '@/utils/replacePlaceholders';
import type { actionType } from './Actions';
import { ensureAdminModeInPath } from '@/utils/string';

type buttonVariant = 'text' | 'outlined' | 'contained';
type OptionsMenuProps = {
  options: Array<actionType>;
  variant?: buttonVariant;
  icon?: string;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  disabled?: boolean;
  title?: string;
  onClick?: (action: actionType) => void;
  loading?: boolean,
  buttonSize?: ButtonOwnProps["size"]
};

const OptionsMenu = ({
  options = [],
  variant = 'text',
  color = 'primary',
  disabled = false,
  loading = false,
  onClick,
  buttonSize = "small",
  title = "Select Option",
  icon = ""
}: OptionsMenuProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const id = useId();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Extract `id` from the URL path or query parameters.
  const urlId = searchParams.get('id') || pathname.split('/').pop();

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (index: number) => {
    // Execute any provided callback.
    if (onClick) {
      console.log(options?.[index])
      onClick(options[index]);
    }

    handleClose();

    // Redirect if the option has a redirect property.
    if (options[index]?.redirect) {
      const redirectUrl = replacePlaceholders(options?.[index].redirect, { id: urlId || '' });

      console.log('Redirect URL after replacement with urlId:', redirectUrl);
      router.push(ensureAdminModeInPath(redirectUrl, options[index]?.mode || 'society'));
    }
  };

  return (
    <>
      <LoadingButton

        // style={{ margin: '10px' }}
        color={color}
        size={buttonSize}
        variant={variant}
        onClick={handleClick}
        disabled={disabled}
        aria-controls={id}
        loading={loading}
        aria-haspopup='true'
        endIcon={
          <KeyboardArrowDownRoundedIcon
          sx={{
            transition: 'transform 0.3s ease',
            transform: Boolean(anchorEl) ? 'rotate(180deg)' : 'rotate(0deg)',
          }}
        />
        }
        {...(icon && {
          startIcon: <Icon icon={icon} />
        })}
      >
        {(options?.find((op)=>!!op?.selected))?.title || title}
      </LoadingButton>
      <Menu
        keepMounted
        id={id}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={Boolean(anchorEl)}
      >
        {options.map((option, index) => (
          <MenuItem
            key={index}
            onClick={() => handleMenuItemClick(index)}

            // Optionally, add visual feedback for the initially selected option.
            selected={!!option?.selected}
            color={color}
          >
            {option?.icon ? <i className={option.icon} /> : null}
            {option.title}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default OptionsMenu;
