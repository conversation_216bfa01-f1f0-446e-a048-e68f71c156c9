import React from "react";

import { Select, MenuItem, ListSubheader } from "@mui/material";

const CustomEditDropdown = (props) => {
  const { id, value, field, api, colDef } = props;
  const options = colDef.options || []; // Pass options as part of colDef

  const handleChange = (event) => {
    const newValue = event.target.value;

    api.setEditCellValue({ id, field, value: newValue });

    // api?.commitCellChange({ id, field });
    // api?.setCellMode(id, field, "view");
  };

  // Render nested or flat dropdown options
  const renderOptions = (options) => {
    return options.map((option) => {
      if (option?.children || option?.rows) {
        // Handle nested dropdowns
        return [
          <ListSubheader key={`header-${option?.id || option?.value}`}>
            {option.label || option?.title}
          </ListSubheader>,
          ...renderOptions(option?.children || option?.rows),
        ];
      }

      return (
        <MenuItem key={option?.id || option?.value} value={option?.id || option?.value}>
          {option.label || option?.title}
        </MenuItem>
      );
    });
  };

  return (
    <Select
      value={value || ""}
      onChange={handleChange}
      autoWidth
      sx={{ width: "100%" }}
    >
      {renderOptions(options)}
    </Select>
  );
};

export default CustomEditDropdown;
