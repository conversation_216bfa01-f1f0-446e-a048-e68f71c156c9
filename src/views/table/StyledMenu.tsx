import { styled, alpha } from "@mui/material";
import Menu from "@mui/material/Menu";
import type { MenuProps } from "@mui/material/Menu";
import { DataGrid, gridClasses } from "@mui/x-data-grid";

export const StyledMenu = styled((props: MenuProps) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: "bottom",
      horizontal: "right",
    }}
    transformOrigin={{
      vertical: "top",
      horizontal: "right",
    }}
    {...props}
  />
))(({ theme }) => ({
  "& .MuiPaper-root": {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 180,
    color: theme.palette.mode === "light" ? "rgb(55, 65, 81)" : theme.palette.grey[300],
    boxShadow:
      "rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px",
    "& .MuiMenu-list": {
      padding: "4px 0",
    },
    "& .MuiMenuItem-root": {
      "& .MuiSvgIcon-root": {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5),
      },
      "&:active": {
        backgroundColor: alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),
      },
    },
  },
}));

export const StyledDataGrid = styled(DataGrid)(({ theme }) => ({
  [`& .${gridClasses.row}:nth-of-type(even)`]: {
    backgroundColor: theme.palette.background.default,
  },

  // [`& .${gridClasses.columnHeaderTitle}, & .columnHeaderTitle`]: {
  //   fontWeight: "bold",
  //   wordBreak: "break-all",
  //   fontSize: "0.75rem",
  //   textTransform: "uppercase",
  //   fontFamily: "inherit",
  // },

  // [`& .${gridClasses.cell}`]: {
  //   display: "flex",
  //   alignItems: "center",
  //   justifyContent: "flex-start",
  //   padding: `${theme.spacing(2)} 0`
  // },

  [`& .${gridClasses.scrollArea}`]: {
    margin: theme.spacing(0, 1),
  },

  [`& .${gridClasses.row}`]: {
    "&:hover": {
      backgroundColor: alpha(theme.palette.info.main, theme.palette.action.hoverOpacity),
    },
  },

  "& .depth-1": {
    paddingLeft: theme.spacing(2),
  },
  "& .depth-2": {
    paddingLeft: theme.spacing(4),
  },
  "& .depth-3": {
    paddingLeft: theme.spacing(6),
  },
  "& .depth-4": {
    paddingLeft: theme.spacing(8),
  },
  "& .depth-5": {
    paddingLeft: theme.spacing(10),
  },
  "& .depth-6": {
    paddingLeft: theme.spacing(12),
  },
}));

