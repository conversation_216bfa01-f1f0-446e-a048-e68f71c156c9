import React from "react"

import { FormControl, <PERSON>Subheader, MenuItem, Select } from "@mui/material"

type Options = {
  title: string
  value: string
  rows?: any[]
}

type EditableSelectProps = {
  value?: string
  options?: Options[]
}

const EditableSelect = ({ value = "", options = [] }: EditableSelectProps) => {
  return (
    <FormControl fullWidth>
      <Select defaultValue={value}>
        {options?.map(option => (
          <MenuItems key={option?.value} item={option} />
        ))}
      </Select>
    </FormControl>
  )
}

type MenuItemsProps = {
  item: Options
}

const MenuItems = ({ item }: MenuItemsProps) => {
  if (item?.rows?.length) {
    return (
      <>
        <ListSubheader>{item?.title}</ListSubheader>
        {item?.rows?.map(row => (
          <MenuItems key={row?.title} item={row} />
        ))}
      </>
    )
  }

  return (
    <MenuItem key={item?.value} value={item?.value}>
      {item?.title}
    </MenuItem>
  )
}

export default EditableSelect
