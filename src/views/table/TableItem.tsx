import { Grid } from '@mui/material';

import { StyledDataGrid } from './StyledMenu';

const TableItem = ({
  index,
  schema,
  rows,
  row,
  filters,
  selected,
  setSelected,
  loading,
  handleRowClick,
  handlePageChange,
  handleSortChange,
}) => {
  return (
    <Grid item xs={12} md={12 / rows.length}>
      <StyledDataGrid
        autoHeight
        key={index}
        columns={schema.columns[index] || schema.columns[0]}
        rows={row}
        paginationModel={{ page: filters.page, pageSize: filters.per_page }}
        checkboxSelection={!!schema.multiSelect}
        rowSelectionModel={selected}
        onRowSelectionModelChange={setSelected}
        disableRowSelectionOnClick
        rowCount={schema.total ?? rows.length}
        sortingMode={schema.mode}
        filterMode={schema.mode}
        onFilterModelChange={handleSortChange}
        onPaginationModelChange={handlePageChange}
        loading={loading}
        onRowClick={({ id }) => handleRowClick(id, index)}
      />
    </Grid>
  );
};

export default TableItem;
