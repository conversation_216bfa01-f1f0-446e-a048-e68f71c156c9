import React from 'react'

import LoadingButton from '@mui/lab/LoadingButton'

import { injectFieldsInTitle } from '@/utils/injectFieldsInTitle'

import OptionsMenu from './OptionsMenu'
import { validateColor, validateVariant } from './Actions'

const RowSelectionActions = (
  {
    rowSelection,
    schema,
    handleActionClick,
    loading,
    tableLoading,
    getRow
  }
) => {
  if (!Object.keys(rowSelection).length || !schema?.selectActions?.length) return <></>

  return (
    <>
      {schema?.selectActions?.map((action: any) => (
        !action?.options?.length ?
          <LoadingButton
            key={action.title}
            size="small"
            variant='outlined'
            color={action.color || "primary"}
            startIcon={<i className={action.icon} />}
            onClick={() => handleActionClick(action)}
            loading={loading || tableLoading}

          // sx={{ mx: 1 }}
          >
            {injectFieldsInTitle(action.title, Object.keys(rowSelection).map(id => getRow(id)?.original))}
          </LoadingButton>
          : <OptionsMenu
            key={action?.title}
            title={action?.title}
            options={action?.options}
            variant={validateVariant(action?.variant)}
            color={validateColor(action?.color)}
            onClick={(action) => handleActionClick(action)}
            loading={loading || tableLoading}
            icon={action?.icon}
          />
      ))}
    </>
  )
}

export default RowSelectionActions