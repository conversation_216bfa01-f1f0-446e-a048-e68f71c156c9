import { type Mouse<PERSON><PERSON>, useEffect, useMemo, useState } from 'react';

import {
  FormControl,
  InputLabel,
  OutlinedInput,
  IconButton,
  Popover,
  Box,
} from '@mui/material';
import { ArrowDropDown } from '@mui/icons-material';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';

/** 
 * Updated: Each item must have a unique `value` 
 * instead of `id`.
 */
export interface NestedItem {

  /** Unique item value (previously `id`) */
  value: string;

  /** Display text for this item */
  title: string;

  /** Children (nested items) */
  rows?: NestedItem[];

  [key: string]: any;
}

/** The tree item shape that RichTreeView expects. */
interface MyRichTreeItem {

  /** This `id` is mapped from NestedItem.value */
  id: string;
  label: string;
  children?: MyRichTreeItem[];
  rows?: MyRichTreeItem[];
}

interface TreeSelectProps {

  /** The nested data array, now using `value` instead of `id` */
  data: NestedItem[];

  /** The currently selected item (the "value") */
  value: string | null;

  /** Label to display above the input */
  label?: string;

  /** Reference to the table */
  table: any;

  /** The cell info from Material React Table */
  cell: any;

  /** If the field is required */
  isRequired?: boolean;
}

const TreeSelect: React.FC<TreeSelectProps> = ({
  data,
  value,
  label = 'Select an option',
  table,
  cell,
  isRequired = false,
}) => {
  // console.log('TreeSelect', { data, value, label, table, cell, isRequired });

  // 1) Transform your nested data into the shape RichTreeView needs
  const items = useMemo(() => transformNestedData(data), [data]);

  console.log('items', items, data);

  // 2) Gather all node IDs so they're fully expanded
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  useEffect(() => {
    const allIds = gatherAllIds(items);

    setExpandedItems(allIds);
  }, [items]);

  // 3) Popover anchor for the "select" dropdown
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const open = Boolean(anchorEl);

  const handleOpen = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  // 4) Helper to check if an item has children => means it's a parent => not selectable
  const hasChildren = (item: MyRichTreeItem) =>
    Array.isArray(item.children) && item.children.length > 0;

  // 5) Utility: find an item by RichTreeView's `id`
  const findItemById = (nodes: MyRichTreeItem[], id: string): MyRichTreeItem | undefined => {
    for (const node of nodes) {
      if (node.id === id) return node;

      if (node.children) {
        const found = findItemById(node.children, id);

        if (found) return found;
      }
    }


    return undefined;
  };

  // We want to show the label (node.label) of the currently selected `value`.
  const displayLabel = useMemo(() => {
    if (!value) return '';
    const found = findItemById(items, value);


    return found ? found.label : '';
  }, [value, items]);

  // 6) Handle item selection toggles
  //    If user selects (isSelected === true) a LEAF => close popover & onChange
  const handleItemSelectionToggle = (
    event: React.SyntheticEvent,
    itemId: string,
    isSelected: boolean,
  ) => {
    if (isSelected) {
      const selectedItem = findItemById(items, itemId);

      console.log('selectedItem', selectedItem);

      if (!selectedItem) return;

      // If it's a parent => do nothing
      if (hasChildren(selectedItem)) return;

      // Else it's a leaf => pass the `itemId` as the updated value
      table.setEditingCell(cell.row.id, cell.column.id, selectedItem.id);

      if (isRequired && !selectedItem?.id) {
        table.setEditingCell(cell.row.id, '_error', { ...cell.row.original?._error, [cell.column.id]: 'This field is required' });
      } else {
        table.setEditingCell(cell.row.id, '_error', { ...cell.row.original?._error, [cell.column.id]: null });
      }

      handleClose();
    }
  };

  return (
    <FormControl variant="outlined" fullWidth>
      <InputLabel id="tree-select-label">{label}</InputLabel>
      <OutlinedInput
        label={label}
        value={displayLabel}
        onClick={handleOpen}
        readOnly
        endAdornment={
          <IconButton size="small" onClick={handleOpen} edge="end">
            <ArrowDropDown />
          </IconButton>
        }
      />

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
      >
        <Box sx={{ minWidth: 250, maxHeight: 300, overflowY: 'auto', p: 1 }}>
          <RichTreeView
            items={items}
            getItemId={(item) => item.id}
            getItemLabel={(item) => item.label}
            expandedItems={expandedItems}
            onItemSelectionToggle={handleItemSelectionToggle}
          />
        </Box>
      </Popover>
    </FormControl>
  );
};

// ---------- Helpers ------------

/**
 * Recursively transforms your `NestedItem[]` into
 * the `MyRichTreeItem[]` shape that RichTreeView expects.
 * We map `item.value` => `id`, and `item.title` => `label`.
 */
function transformNestedData(items: NestedItem[]): MyRichTreeItem[] {
console.log('transformNestedData', items);

  return items.map((item) => {
    const hasChildren = !!item.rows?.length;

    return {
      // RichTreeView expects "id", we supply from item.value
      id: item?.value,
      label: item?.title || item?.label,
      children: hasChildren ? transformNestedData(item.rows!) : undefined,
    };
  });
}

/**
 * Collect all item IDs so that we can expand them all.
 */
function gatherAllIds(items: MyRichTreeItem[]): string[] {
  return items.flatMap((item) => {
    const childIds = item.children ? gatherAllIds(item.children) : [];

    return [item.id, ...childIds];
  });
}

/**
 * Find an item by its ID in the tree structure.
 * This is used to find the label of the currently selected value.
 */
export const getItemById = (
  nodes: MyRichTreeItem[],
  id: string,
): MyRichTreeItem | undefined => {
  for (const node of nodes) {
    if (node.id === id) return node;

    if (node?.children || node?.rows) {
      const found = getItemById(node?.children || node?.rows, id);

      if (found) return found;
    }
  }

  return undefined;
}


export default TreeSelect;
