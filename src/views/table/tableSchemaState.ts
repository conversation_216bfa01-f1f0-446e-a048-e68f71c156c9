export const validateArray = (arr: any[]) => {
  return Array.isArray(arr) ? arr : []
}

type filterType = null | {
  [key: string]: {
    title: string
    options: {
      [key: string]: string
    }
  }
}

export type State = {
  columns: Array<any>
  actions: Array<any>
  fields: Array<any>
  tableTitle: string
  formKey: string
  total: number
  tabs: Array<string>
  options: { title: string; redirect: string, icon: string }[]
  mode: "server" | "client"
  selectActions: Array<any>
  multiSelect: boolean
  selectBy: null | {
    [key: string]: string
  }
  filterBy: filterType
  filters: filterType
  selectedItem: null | {
    [key: string]: string
  }
}

export type Action =
  | { type: "setColumns"; payload: Array<any> }
  | { type: "setActions"; payload: Array<any> }
  | { type: "setFields"; payload: Array<any> }
  | { type: "setTableTitle"; payload: string }
  | { type: "setFormKey"; payload: string; selectedItem: null | { [key: string]: string } }
  | { type: "setTotal"; payload: number }
  | { type: "setAll"; payload: Partial<State> }

/**
 * This is a TypeScript reducer function that handles various actions to update the state of a table or
 * form.
 * @param {State} state - The current state of the application, which is an object containing various
 * properties.
 * @param {Action} action - The `action` parameter is an object that describes the action being
 * performed. It has a `type` property that indicates the type of action being performed, and a
 * `payload` property that contains any data associated with the action. The `switch` statement in the
 * `reducer` function uses the
 * @returns The reducer function is returning a new state object based on the action type and payload.
 * The returned state object includes the previous state spread using the spread operator, and the
 * updated values based on the action type and payload. If the action type is not recognized, the
 * function returns the previous state.
 */
export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "setColumns":
      return { ...state, columns: validateArray(action.payload) }
    case "setActions":
      return { ...state, actions: validateArray(action.payload) }
    case "setFields":
      return { ...state, fields: validateArray(action.payload) }
    case "setTableTitle":
      return { ...state, tableTitle: action.payload }
    case "setFormKey":
      return { ...state, formKey: action.payload, selectedItem: action.selectedItem }
    case "setTotal":
      return { ...state, total: Math.abs(action.payload) || 0 }
    case "setAll":
      return { ...state, ...action.payload }
    default:
      return state
  }
}

export const initialState = {
  columns: [],
  actions: [],
  fields: [],
  tableTitle: "",
  formKey: "",
  total: 0,
  tabs: [],
  options: [],
  mode: "server" as const,
  filterBy: null,
  filters: null,
  multiSelect: false,
  selectBy: null,
  selectActions: [],
  selectedItem: null
}
