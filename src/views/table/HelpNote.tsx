// @ts-nocheck

import Card from "@mui/material/Card"
import Typography from "@mui/material/Typography"
import CardContent from "@mui/material/CardContent"
import Grid from "@mui/material/Grid"

import { makeStyles } from "@mui/material"

const useStyles = makeStyles(() => ({
  Main_Heading: {
    marginBottom: "5px",
    fontWeight: "bold",
    color: "#0a4f92 "
  },
  list_styling: {
    marginBottom: "10px",
    fontSize: "14px"
  }
}))

const HelpNote = () => {
  const classes = useStyles()

  return (
    <Grid item xs={12}>
      <Card>
        <CardContent>
          <Typography
            variant='body1'
            className={classes.Main_Heading}

          // sx={{ mb: 2, fontWeight: 'bold', color: '#0a4f92' }}
          >
            Help Note
          </Typography>
          <ol>
            <li className={classes.list_styling}>
              To change the sequence of particulars, select particular row. Drag the selected particular row and place on other row to change.
            </li>
            <li className={classes.list_styling}>
              Edit display name by changing display name text.
            </li>
            <Grid>
              <li className={classes.list_styling}>
                To change order of column, Click on arrow beside to column header.
                <i
                  className='ic:outline-remove-red-eye'
                  style={{ margin: "-10px 10px 0px 10px", fontSize: "20px", color: "#0a4f92" }}
                />
                icon.
              </li>
            </Grid>
            <li className={classes.list_styling}>
              To Edit Issue details, click on{" "}
              <i
                className='material-symbols:edit-square-outline-rounded'
                style={{ margin: "-10px 10px 0px 10px", fontSize: "20px", color: "#0a4f92" }}
              />{" "}
              icon.
            </li>
            <li>
              To Delete Issue, click on{" "}
              <i
                className='mingcute:delete-2-fill'
                style={{ margin: "-10px 10px 0px 10px", fontSize: "20px", color: "#0a4f92" }}
              />{" "}
              icon.
            </li>
          </ol>
        </CardContent>
      </Card>
    </Grid>
  )
}

export default HelpNote
