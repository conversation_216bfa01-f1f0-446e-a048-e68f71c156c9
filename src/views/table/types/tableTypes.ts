// types/tableTypes.ts

import type { MRT_ColumnDef } from "material-react-table";

export interface TableSchema {
  rows: any[][];
  editedRows: any[][];
  columns: MRT_ColumnDef<any>[][];
  actions: any[];
  totalTables: number;
  fields: any[];
  tableTitle: string;
  note: string;
  total: number;
  tabs: any[];
  options: any[];
  filters: Record<string, any>;
  extraFilters: ExtraFilter[];
  selectBy: any;
  multiSelect: boolean;
  filterBy: Record<string, any>;
  selectActions: any[];
  isSearchable: boolean;
  editMode: "table" | "row" | "cell";
  isEditable: boolean;
  bulkUpdatePath: string | null;
  updatePath: string | null;
  isAddable: boolean;
  rowActions: any[];
  orderable: boolean;
  sendFullParams: boolean;
  enableSelectOn: any;
  enableSelectBy: boolean;
  enableFilterBy: boolean;
  tableDirection: "column" | "row";
}

export interface ExtraFilter {
  key: string;
  title?: string;
  [prop: string]: any;
}

export interface ApiResponse {
  data: {
    data: any[];
    meta: {
      schema: {
        table: Partial<TableSchema>
      };
      pagination: {
        total: number;
      };
      errors?: Record<string, any>;
    };
    message?: string | string[];
    status?: string;
  }
  status: number;
  message: string | string[];
  errors?: Record<string, any>;
}
