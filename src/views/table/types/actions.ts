export interface Action {
    icon?: string;
    title?: string;
    color?: string;
    hide_on?: Record<string, string[]>;
    disable_on?: Record<string, string[]>;
    variant?: string;
    isadd?: boolean;
    isdelete?: boolean;
    isedit?: boolean;
    api?: {
        url: string;
        method: string;
        type?: 'download' | 'view';
        rerender?: boolean;
    };
    shimmer?: string;
    href?: string;
    form?: string;
    target?: string;
    show_on?: Record<string, string[]>;
    options?: Action[]
}

export interface RowActionsProps {
    row: Record<string, any>;
    actions: Action[];
    setEditedRows?: (editedRows: any) => void;
    setIsEditing?: (isEditing: boolean) => void;
    table?: any;
    refetch?: () => void;
}