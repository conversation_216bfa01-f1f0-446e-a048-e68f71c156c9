import type { CSSProperties } from 'react'

import Link from 'next/link'

import { Tooltip, Typography } from '@mui/material'

import { replacePlaceholders } from '@/utils/replacePlaceholders'
import StyledIconButton from './StyledIconButton'
import { ensureAdminModeInPath } from '@/utils/string'

const LinkAction = ({ action, row, index, loadingActionIndex, disabled }) => {
    const {
        icon,
        title = '',
        color = 'primary',
        shimmer = 'line-md-loading-loop',
        href = '',
        form = '',
        mode = 'society',
    } = action;

    const adminModePath = ensureAdminModeInPath(href, mode)

    const idPlaceholderRegex = /(?:\/:id|\/\[id\])/;
    const hasIdPlaceholder = idPlaceholderRegex.test(form);
    const url = `/admin/${mode}/form/${form}${hasIdPlaceholder ? '' : `/${row?.id}`}`;
    const localizedHref = replacePlaceholders(adminModePath || url, row);

    const iconStyle: CSSProperties = {
        pointerEvents: disabled ? 'none' : 'auto',
        opacity: disabled ? 0.5 : 1,
        cursor: disabled ? 'pointer' : 'pointer',
        color,
    };

    return (
        <Link
            href={localizedHref}
            passHref={true}
            prefetch={true}
            {...(action.target && { target: action.target })}
        >
            <Tooltip title={title} disableInteractive>
                <span>
                    {icon ? (
                        <StyledIconButton
                            disabled={disabled}
                            customColor={color}
                        >
                            <i className={loadingActionIndex === index ? shimmer : icon} />
                        </StyledIconButton>
                    ) : (
                        <Typography variant="body2" sx={iconStyle}>
                            {title}
                        </Typography>
                    )}
                </span>
            </Tooltip>
        </Link>
    )
}

export default LinkAction