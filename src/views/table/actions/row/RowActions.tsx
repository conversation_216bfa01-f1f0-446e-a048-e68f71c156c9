import { useState } from 'react';
import type { MouseEvent, ReactNode } from 'react';

import axios from 'axios';
import { Grid2, Tooltip } from '@mui/material';
import { toast } from 'react-toastify';

import { replacePlaceholders } from '@/utils/replacePlaceholders';
import MenuIcon from './MenuAction';
import LinkAction from './LinkAction';
import type { Action, RowActionsProps } from '../../types/actions';
import StyledIconButton from './StyledIconButton';
import BulkEditAction from './BulkEditAction';
import { ellipsis } from '@/utils/string';

const RenderRowActions: React.FC<RowActionsProps> = ({
  row,
  actions,
  setEditedRows,
  setIsEditing,
  table,
  refetch,
}) => {
  const [loadingActionIndex, setLoadingActionIndex] = useState<number | null>(null);
  let direction: "row" | "column" = "row";

  /**
   * Handles a click event and performs an API action (download, view, or default).
   * All original functionality is preserved.
   */
  const onClick = async (
    event: MouseEvent,
    action?: Action,
    index?: number
  ): Promise<void> => {
    'use strict';

    // Prevent event bubbling and default behavior
    event.stopPropagation();
    event.preventDefault();

    // Validate the action configuration
    if (!action?.api || typeof action.api.url !== 'string') {
      console.warn('Invalid action configuration:', action);
      toast.error('Invalid action configuration');

      return;
    }

    // Initialize a toast notification based on the API type
    let toastId: string | number | null = null;

    switch (action.api.type) {
      case 'download':
        toastId = toast.loading('Downloading...', {
          icon: <i className="line-md-downloading-loop" style={{ fontSize: '32px' }} />,
        });
        break;
      case 'view':
        toastId = toast.loading('Opening...', {
          icon: <i className="line-md-loading-loop" style={{ fontSize: '32px' }} />,
        });
        break;
      default:
        toastId = toast.loading('Loading...', {
          icon: <i className="line-md-loading-loop" style={{ fontSize: '32px' }} />,
        });
    }

    // Open a new tab if the action is for viewing a PDF
    let newTab: Window | null = null;

    if (action.api.type === 'view') {
      newTab = window.open(`${process.env.NEXT_PUBLIC_BASE_URL}/pdf.html`, '_blank');

      if (!newTab) {
        toast.update(toastId, {
          render: 'Please allow pop-ups for this website',
          type: 'error',
          isLoading: false,
          autoClose: Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000),
        });

        return;
      }
    }

    // Set a loading indicator
    setLoadingActionIndex(index ?? null);

    try {
      // Replace placeholders in the API URL with dynamic values
      const url = replacePlaceholders(action.api.url, row);

      const response = await axios(encodeURI(url), {
        method: action.api.method,
        baseURL: process.env.NEXT_PUBLIC_API_URL,
        validateStatus: () => true,
        withCredentials: true,
      });

      // Validate the API response
      if (!response?.data || (!response.data.isSuccess && response.data.status !== 'success')) {
        throw new Error(response?.data?.message || 'API request failed');
      }

      // For view or download actions, retrieve and process the PDF file.
      if (action?.api?.type === 'view' || action?.api?.type === 'download') {
        const pdfUrl = response.data.data?.url;

        if (!pdfUrl) {
          throw new Error('PDF URL is missing in the response');
        }

        // Fetch the PDF file as a blob.
        const fileResponse = await axios(encodeURI(pdfUrl), {
          method: action.api.method,
          baseURL: process.env.NEXT_PUBLIC_API_URL,
          responseType: 'blob',
          validateStatus: () => true,
        });

        if (fileResponse.status !== 200) {
          throw new Error(fileResponse?.data?.message || 'Failed to fetch PDF file');
        }

        // Create a Blob from the fetched file
        const fileBlob = new Blob([fileResponse.data], {
          type:
            action.api.type === 'download'
              ? 'application/octet-stream'
              : 'application/pdf',
        });

        const fileURL = URL.createObjectURL(fileBlob);

        if (action.api.type === 'download') {
          // Create an anchor element to trigger the file download.
          const a = document.createElement('a');

          a.href = fileURL;
          a.download = 'download.pdf';
          a.click();
          URL.revokeObjectURL(fileURL);
          toast.update(toastId, {
            render: 'Downloaded successfully',
            type: 'success',
            isLoading: false,
            autoClose: Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000),
          });

          return;
        }

        // For viewing, embed the PDF in the new tab.
        if (newTab) {
          const pdfContainer = newTab.document.getElementById('pdfContainer');

          if (pdfContainer) {
            toast.update(toastId, {
              render: 'Loaded successfully',
              type: 'info',
              isLoading: false,
              autoClose: Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000),
            });
            pdfContainer.innerHTML = `<object data="${fileURL}" type="application/pdf" style="width:100%; height:100%;"></object>`;
            const pdfObject = newTab.document.querySelector('object');

            if (pdfObject) {
              pdfObject.addEventListener('load', () => {
                const overlay = newTab!.document.getElementById('spinnerOverlay');

                if (overlay) {
                  overlay.style.display = 'none';
                }

                newTab!.document.title = 'Your PDF is ready';
              });
            }
          }
        }
      } else {
        // For other API types, update the toast with the response message.
        toast.update(toastId, {
          render: response.data.message || 'Action completed successfully!',
          type: 'success',
          isLoading: false,
          autoClose: Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000),
        });
      }

      // Optionally re-fetch data if the action configuration requires it.
      if (action.api.rerender || action.api.method) {
        refetch?.();
      }
    } catch (error: unknown) {
      let errorMessage = 'Something went wrong!';

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      // Update the toast with the error message.
      toast.update(toastId, {
        render: errorMessage,
        type: 'error',
        isLoading: false,
        autoClose: Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000),
      });

      // If a new tab was opened, display an error page there.
      if (newTab) {
        newTab.document.open();
        newTab.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <title>Error! Your PDF is not ready</title>
              <style>
                html, body { margin:0; padding:0; overflow:hidden; height:100%; width:100%; }
                #errorContainer {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  height: 100vh;
                  width: 100vw;
                  font-family: Arial, sans-serif;
                  background-color: #f8d7da;
                  text-align: center;
                }
              </style>
            </head>
            <body>
              <div id="errorContainer">
                <div style="font-size:20px; margin-bottom:20px; color:#721c24;">Error: ${errorMessage}</div>
                <div style="font-size:16px; color:#721c24;">This window will close in <span id="countdown">3</span> seconds.</div>
              </div>
              <script>
                'use strict';
                var seconds = 3;
                var countdownElement = document.getElementById('countdown');
                var interval = setInterval(function() {
                  seconds--;
                  countdownElement.textContent = seconds;
                  if (seconds <= 0) {
                    clearInterval(interval);
                    window.close();
                  }
                }, 1000);
              </script>
            </body>
          </html>
        `);
        newTab.document.close();
      }
    } finally {
      // Clear the loading indicator
      setLoadingActionIndex(null);
    }
  };

  // Build an array of visible button components (filtering out hidden ones)
  const visibleButtons: ReactNode[] = actions
    .map((action, index) => {
      const {
        icon,
        title = '',
        color = 'primary',
        hide_on = {},
        disable_on = {},
        show_on = {},
        isadd,
        isdelete,
        isedit,
        shimmer = 'line-md-loading-loop',
        options = []
      } = action;

      // Check for disabled state
      const disabled = Object.keys(disable_on).some(key =>
        Array.isArray(disable_on[key])
          ? disable_on[key].includes(row[key])
          : row[key] === disable_on[key]
      );

      // Check for hidden state
      let hidden = Object.keys(hide_on).some(key =>
        Array.isArray(hide_on[key])
          ? hide_on[key].includes(row[key])
          : row[key] === hide_on[key]
      );

      if (show_on && Object.keys(show_on).length > 0) {
        const shouldShow = Object.keys(show_on).some(key =>
          Array.isArray(show_on[key])
            ? show_on[key].includes(row[key])
            : row[key] === show_on[key]
        );

        hidden = !shouldShow;
      }

      if (hidden) return null;

      // Render based on the action type.
      if (isadd || isdelete || isedit) {
        return (
          <BulkEditAction
            key={index}
            action={action}
            table={table}
            index={index}
            row={row}
            setEditedRows={setEditedRows}
            setIsEditing={setIsEditing}
          />
        );
      } else if (options && Array.isArray(options) && options.length && icon) {
        return (
          <MenuIcon
            key={index}
            icon={icon}
            options={options}
            disabled={disabled}
            color={color}
            onClick={onClick}
            index={index}
          />
        );
      } else if ((action?.href || action?.form) && !disabled) {
        return (
          <LinkAction
            key={index}
            action={action}
            row={row}
            index={index}
            loadingActionIndex={loadingActionIndex}
            disabled={disabled}
          />
        );
      } else if (action?.type === 'text') {
        direction = 'column';

        return (
          <Tooltip title={`${action?.title} ${row?.[action?.key]}`} disableInteractive key={index} >
            <span style={{ color: color }}>
              {ellipsis(`${action?.title} ${row?.[action?.key]}`, { maxLength: 15 })}
            </span>
          </Tooltip>
        );
      }

      return (
        <Tooltip title={title} disableInteractive key={index}>
          <span>
            <StyledIconButton
              size="small"
              disabled={disabled || loadingActionIndex === index}
              onClick={e => onClick(e, action, index)}
              customColor={color}
            >
              <i className={loadingActionIndex === index ? shimmer : icon} />
            </StyledIconButton>
          </span>
        </Tooltip>
      );
    })
    .filter(Boolean);

  // Determine how to split the visible buttons into rows:
  // - 3 or fewer buttons: a single row.
  // - More than 3 buttons: split into two rows.
  const totalVisible = actions?.length;
  let rowsToRender: ReactNode[][] = [];

  if (totalVisible <= 3) {
    rowsToRender = [visibleButtons];
  } else {
    const topRowCount = Math.ceil(totalVisible / 2);

    rowsToRender = [
      visibleButtons.slice(0, topRowCount),
      visibleButtons.slice(topRowCount)
    ];
  }

  // Render the rows using Grid2 for the layout
  return (
    <div>
      {rowsToRender.map((rowButtons, rowIndex) => (
        <Grid2 container spacing={1} key={`row-${rowIndex}`} direction={direction}>
          {rowButtons.map((button, btnIndex) => (
            <Grid2 size={12 / rowButtons.length} key={`btn-${rowIndex}-${btnIndex}`}>
              {button}
            </Grid2>
          ))}
        </Grid2>
      ))}
    </div>
  );
};

export default RenderRowActions;
