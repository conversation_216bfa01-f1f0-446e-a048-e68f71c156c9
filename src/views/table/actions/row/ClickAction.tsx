import type{ FC } from 'react';

import Tooltip from '@mui/material/Tooltip';

import StyledIconButton from './StyledIconButton';

interface ClickActionProps {
  title: string;
  index: number;
  disabled: boolean;
  loadingActionIndex: number;
  onClick: (event: React.MouseEvent<HTMLButtonElement>, action: any, index: number) => void;
  action: any;
  color: string;
  icon: string;
  shimmer: string;
}

const ClickAction: FC<ClickActionProps> = ({
  title,
  index,
  disabled,
  loadingActionIndex,
  onClick,
  action,
  color,
  icon,
  shimmer,
}) => {
  return (
    <Tooltip title={title} disableInteractive key={index}>
      <span>
        <StyledIconButton
          key={index}
          size="small"
          disabled={disabled || loadingActionIndex === index}
          onClick={(e) => onClick(e, action, index)}
          customColor={color}
        >
          <i className={loadingActionIndex === index ? shimmer : icon} />
        </StyledIconButton>
      </span>
    </Tooltip>
  );
};

export default ClickAction;
