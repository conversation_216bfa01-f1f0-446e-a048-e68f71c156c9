import type { FC } from 'react';

import Tooltip from '@mui/material/Tooltip';

import StyledIconButton from '../../actions/row/StyledIconButton'; // Adjust the import path as needed
import type { Action } from '../../types/actions';

interface TableActionButtonProps {
  index: number;
  disabled?: boolean;
  row: { [key: string]: any };
  table: {
    setEditingRow: (row: TableActionButtonProps['row']) => void;
  };
  setIsEditing?: (value: boolean) => void;
  setEditedRows: React.Dispatch<React.SetStateAction<TableActionButtonProps['row'][]>>;
  action: Action
}

const BulkEditAction: FC<TableActionButtonProps> = ({
  index,
  disabled,
  row,
  table,
  setIsEditing,
  setEditedRows,
  action
}) => {
  const {
    icon,
    title = '',
    isadd: isAdd = false,
    isdelete: isDelete = false,
    isedit: isEdit = false,
  } = action;

  // Only render the button if one of the actions is active.
  if (!(isAdd || isDelete || isEdit)) return null;

  const method = isAdd ? 'add' : isDelete ? 'delete' : 'edit';

  const colors: Record<string, 'success' | 'error' | 'primary'> = {
    add: 'success',
    delete: 'error',
    edit: 'primary',
  };

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsEditing?.(true);

    if (isEdit) {
      table.setEditingRow(row);

      return;
    }

    setEditedRows(prevRows => {
      const newRows = [...prevRows];
      const rowIndex = newRows.findIndex(r => r.id === row.id);

      if (rowIndex === -1) return newRows;

      const _action = newRows[rowIndex]._action;

      if (method === 'delete' && _action === 'add') {
        newRows.splice(rowIndex, 1);
      } else if (method === _action) {
        newRows[rowIndex] = { ...row, _action: null };
      } else {
        newRows[rowIndex] = { ...row, _action: method };
      }


      return newRows;
    });
  };

  return (
    <Tooltip title={title} disableInteractive key={index}>
      <span>
        <StyledIconButton
          key={index}
          size="small"
          disabled={disabled}
          customColor={colors[method]}
          onClick={handleClick}
        >
          <i className={icon} />
        </StyledIconButton>
      </span>
    </Tooltip>
  );
};

export default BulkEditAction;
