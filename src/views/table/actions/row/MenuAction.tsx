import React, { useId, useState } from 'react'

import { Menu, MenuItem } from '@mui/material'

import type { Action } from '../../types/actions';
import StyledIconButton from './StyledIconButton';

const MenuIcon = ({ icon = "", options = [], disabled = true, color="", onClick = () => {}, index = 0 }: { icon: string, options: Action[], disabled :boolean, color: string, onClick: any, index: number }) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const id = useId();
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <>
            <StyledIconButton
                disabled={disabled}
                customColor={color}
                aria-label="more"
                id={`${id}button`}
                aria-controls={open ? `${id}menu` : undefined}
                aria-expanded={open ? 'true' : undefined}
                aria-haspopup="true"
                onClick={handleClick}
            >
                <i className={icon} />
            </StyledIconButton>
            <Menu
                id={`${id}menu`}
                MenuListProps={{
                    'aria-labelledby': `${id}button`,
                }}
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
            >
                {options.map((option) => (
                    <MenuItem key={option?.title} onClick={(e) => onClick(e, option, index)}>
                        {option?.title}
                    </MenuItem>
                ))}
            </Menu>
        </>
    )
}

export default MenuIcon