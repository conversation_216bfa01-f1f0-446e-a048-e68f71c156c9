import { styled } from '@mui/material/styles';
import IconButton from '@mui/material/IconButton';
import type { IconButtonProps } from '@mui/material/IconButton';

import { validateColor } from '../../Actions';

interface StyledIconButtonProps extends IconButtonProps {
    customColor: string;
}

const StyledIconButton = styled(IconButton, {
    shouldForwardProp: (prop) => prop !== 'customColor',
  })<StyledIconButtonProps>(({ disabled, customColor, theme }) => ({
    pointerEvents: disabled ? 'none' : 'auto',
    opacity: disabled ? 0.5 : 1,
    cursor: 'pointer',
    color: theme.palette[validateColor(customColor)].main,
    size: 'small'
  }));

export default StyledIconButton;
