// types.ts
import type { ReactElement, JSXElementConstructor, ReactPortal } from "react"

export interface FilterDef {
  title:
    | string
    | number
    | boolean
    | ReactElement<any, string | JSXElementConstructor<any>>
    | ReactPortal
    | null
    | undefined
  options: { [s: string]: unknown } | ArrayLike<unknown>
  select_single?: boolean
}

// FilterBy.tsx
import { useEffect, useState } from "react"

import { Button, Popover, Grid2, MenuList, Divider } from "@mui/material"

import { FilterByItem } from "./FilterByItem"


type FilterByProps = {
  schema: {
    filterBy?: Record<string, FilterDef>
  }
  handleClick: (updated: any) => void
  filters: { id: string; value: string[] }[]
}

export function FilterBy({
  schema,
  handleClick,
  filters = [],
}: FilterByProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const makeInit = () =>
    filters.reduce(
      (acc, curr) => ({ ...acc, [curr.id]: curr.value }),
      {} as Record<string, string[]>
    )

  const [tempFilters, setTempFilters] = useState(makeInit)

  useEffect(() => {
    setTempFilters(makeInit())
  }, [filters])

  const handleChange = (
    value: string,
    field: string,
    single = false
  ) => {
    if (single) {
      const current = tempFilters[field]?.[0]

      setTempFilters((prev) => ({
        ...prev,
        [field]: current === value ? [] : [value],
      }))
    } else {
      const prev = tempFilters[field] ?? []

      const next = prev.includes(value)
        ? prev.filter((v) => v !== value)
        : [...prev, value]

      setTempFilters((prevState) => ({
        ...prevState,
        [field]: next,
      }))
    }
  }

  const handleApply = () => {
    handleClick(tempFilters)
    setAnchorEl(null)
  }

  return (
    <Grid2>
      <Button
        variant="outlined"
        color="secondary"
        onClick={(e) => setAnchorEl(e.currentTarget)}
        endIcon={<i className="ri-filter-3-line" />}
      >
        Filter By
      </Button>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={() => setAnchorEl(null)}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
        transformOrigin={{ vertical: "top", horizontal: "left" }}
        slotProps={{
          paper: {
            sx: {
              mt: 1,
              maxHeight: "50vh",
              display: "flex",
              flexDirection: "column",
            },
          },
        }}
      >
        <MenuList dense sx={{ maxHeight: "40vh", overflowY: "auto", p: 1 }}>
          {(
            Object.entries(schema.filterBy ?? {}) as [
              string,
              FilterDef
            ][]
          ).map(([key, def]) => (
            <FilterByItem
              key={key}
              value={{ ...def, key }}
              filters={tempFilters}
              handleChange={handleChange}
            />
          ))}
        </MenuList>

        <Divider />
        <Grid2 sx={{ p: 1 }}>
          <Button
            variant="contained"
            fullWidth
            color="primary"
            onClick={handleApply}
          >
            Apply Filter
          </Button>
        </Grid2>
      </Popover>
    </Grid2>
  )
}
