'use client';

import { useEffect, useState } from 'react';

import {
  I<PERSON>Button,
  Box,
  TextField,
  Stack,
  Typography,
} from '@mui/material';
import ShareOutlinedIcon from '@mui/icons-material/ShareOutlined';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CloseIcon from '@mui/icons-material/Close';
import MailOutlineIcon from '@mui/icons-material/MailOutline';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import { toast } from 'react-toastify';

const ShareIcon = () => {
  const [shareUrl, setShareUrl] = useState('');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setShareUrl(window.location.href);
    }
  }, []);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      toast.dismiss();
      toast.info('Link copied. Share it with other committee members.');
    } catch {
      toast.error('Failed to copy link.');
    }
  };

  const handleEmail = () => {
    const subject = encodeURIComponent(`Society Update: ${document.title}`);

    const body = encodeURIComponent(
      `Dear Committee Member,\n\nPlease review the following update from the society portal:\n\n${shareUrl}\n\nBest regards,\nSociety Management System`
    );
    
    window.open(`mailto:?subject=${subject}&body=${body}`, '_blank');
  };

  const getWhatsAppLink = () => {
    const message = `Dear Committee Member,\n\nPlease review this society update:\n${shareUrl}`;
    
    return `https://wa.me/?text=${encodeURIComponent(message)}`;
  };

  const handleShare = async () => {
    const shareData = {
      title: document.title,
      text: `Committee Update: ${document.title}`,
      url: shareUrl,
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
      } catch (err) {
        console.error('Share cancelled or failed:', err);
      }
    } else {
      toast(
        ({ closeToast }) => (
          <Box className="relative z-50">
            <IconButton
              size="small"
              onClick={closeToast}
              className="absolute top-0 right-0"
              aria-label="Close"
            >
              <CloseIcon fontSize="small" />
            </IconButton>

            <Typography variant="subtitle2" className="mb-2 pr-6">
              Your browser does not support native sharing. Use one of the options below:
            </Typography>

            <Stack direction="row" spacing={1} alignItems="center">
              <TextField
                size="small"
                fullWidth
                value={shareUrl}
                slotProps={{
                  input: {
                    className: 'bg-gray-100 text-gray-800',
                    readOnly: true,
                  },
                }}
              />

              <IconButton
                onClick={handleCopy}
                color="primary"
                aria-label="Copy Link"
                title="Copy Link"
              >
                <ContentCopyIcon />
              </IconButton>

              <IconButton
                onClick={handleEmail}
                color="primary"
                aria-label="Share via Email"
                title="Email"
              >
                <MailOutlineIcon />
              </IconButton>

              <a
                href={getWhatsAppLink()}
                target="_blank"
                rel="noopener noreferrer"
              >
                <IconButton
                  color="success"
                  aria-label="Share via WhatsApp"
                  title="WhatsApp"
                >
                  <WhatsAppIcon />
                </IconButton>
              </a>
            </Stack>
          </Box>
        ),
        {
          autoClose: 5000,
          closeOnClick: false,
          closeButton: false,
          draggable: false,
        }
      );
    }
  };

  return (
    <IconButton
      onClick={handleShare}
      color="primary"
      aria-label="Share"
      title="Share"
    >
      <ShareOutlinedIcon />
    </IconButton>
  );
};

export default ShareIcon;
