'use client';

import { useEffect, useState } from 'react';

import { IconButton, Tooltip } from '@mui/material';
import InstallMobileOutlinedIcon from '@mui/icons-material/InstallMobileOutlined';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
}

export default function InstallIcon() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);

  useEffect(() => {
    const handler = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setIsInstallable(true);
    };

    window.addEventListener('beforeinstallprompt', handler);
    
    return () => window.removeEventListener('beforeinstallprompt', handler);
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;
    
    console.log(`User response to the install prompt: ${outcome}`);

    setDeferredPrompt(null);
    setIsInstallable(false);

    // Optional: Track outcome with analytics
    // analytics.track('pwa_install_prompt_outcome', { outcome });
  };

  if (!isInstallable) return null;

  return (
    <Tooltip title="Install App">
      <IconButton
        color="primary"
        aria-label="Install App"
        onClick={handleInstallClick}
      >
        <InstallMobileOutlinedIcon />
      </IconButton>
    </Tooltip>
  );
}
