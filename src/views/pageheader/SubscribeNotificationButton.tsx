'use client';

import { useEffect, useState } from 'react';

import { IconButton } from '@mui/material';
import NotificationsActiveOutlinedIcon from '@mui/icons-material/NotificationsActiveOutlined';

// Utility to convert base64 VAPID public key to Uint8Array
function urlBase64ToUint8Array(base64String: string) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');
    const rawData = atob(base64);

    return new Uint8Array([...rawData].map(char => char.charCodeAt(0)));
}

// Dummy: Replace with your backend call
const subscribeUser = async (sub: any) => {
    console.log("User subscribed:", sub);
};

export default function SubscribeNotificationButton() {
    const [isSupported, setIsSupported] = useState(false);
    const [shouldShow, setShouldShow] = useState(false);

    useEffect(() => {
        if ('serviceWorker' in navigator && 'PushManager' in window && 'Notification' in window) {
            setIsSupported(true);
            checkIfSubscribed();
        }
    }, []);

    async function checkIfSubscribed() {
        const registration = await navigator.serviceWorker.register('/sw.js', {
            scope: '/',
            updateViaCache: 'none',
        });

        const existingSub = await registration.pushManager.getSubscription();
        const permission = Notification.permission;

        // Hide icon if already subscribed and granted
        if (existingSub && permission === 'granted') {
            setShouldShow(false);
        } else {
            setShouldShow(true);
        }
    }

    async function subscribeToPush() {
        const registration = await navigator.serviceWorker.ready;

        const sub = await registration.pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey: urlBase64ToUint8Array(
                process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!
            ),
        });

        const serializedSub = JSON.parse(JSON.stringify(sub));

        await subscribeUser(serializedSub);

        // Hide icon after successful subscription
        setShouldShow(false);
    }

    if (!isSupported || !shouldShow) return null;

    return (
        <IconButton
            color="primary"
            aria-label="Enable Notifications"
            onClick={subscribeToPush}
            title="Enable Notifications"
        >
            <NotificationsActiveOutlinedIcon />
        </IconButton>
    );
}
