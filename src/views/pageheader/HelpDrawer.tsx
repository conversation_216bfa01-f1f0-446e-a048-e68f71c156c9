'use client';

import { Drawer, Box, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

type HelpDrawerProps = {
  open: boolean;
  onClose: () => void;
  helpContent: string;
};

const HelpDrawer = ({ open, onClose, helpContent }: HelpDrawerProps) => {
  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: 600,
          maxWidth: '98%',
          p: 3,
          zIndex: 1300,
        },
      }}
    >
      {/* Close Icon Only */}
      <IconButton
        onClick={onClose}
        sx={{ position: 'absolute', top: 16, right: 16 }}
        aria-label="Close"
      >
        <CloseIcon color="error" />
      </IconButton>

      {/* Drawer Content */}
      <Box sx={{ mt: 5, overflowY: 'auto', flex: 1, pr: 1 }}>
        <div dangerouslySetInnerHTML={{ __html: helpContent }} />
      </Box>
    </Drawer>
  );
};

export default HelpDrawer;
