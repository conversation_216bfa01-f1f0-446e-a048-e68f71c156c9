import { Breadcrumbs, <PERSON>, Typography, Box, Grid2 } from '@mui/material';
import { Icon } from '@iconify/react/dist/iconify.js';

import ShareIcon from './ShareIconButton';
import HelpIcon from './HelpIconButton';
import InstallIcon from './InstallIcon';

type BreadcrumbItem = {
  href?: string;
  label: string;
  icon?: string;
};

type PageHeaderWithHelpProps = {
  items: BreadcrumbItem[];
};

const PageHeaderWithHelp = async ({ items }: PageHeaderWithHelpProps) => {
  const lastItem = items?.at(-1)

  return (
    <Box >
      {/* Top Section: Breadcrumbs + Help Icon */}
      <Grid2 container justifyContent="space-between" alignItems="center">

        <Breadcrumbs aria-label="breadcrumb">
          <Link href="/admin/dashboard">Home</Link>
          {items.map((item, index) => {
            const { href, label, icon } = item;
            const isInteractiveLink = href && !href.includes('[');

            return isInteractiveLink ? (
              <Link key={href} href={href}>
                {icon && <Icon icon={icon} />}
                {label}
              </Link>
            ) : (
              <Typography key={index} display="flex" alignItems="center">
                {icon && <Icon icon={icon} />}
                {label}
              </Typography>
            );
          })}
        </Breadcrumbs>

        <Grid2>
          <InstallIcon />
          <ShareIcon />
          <HelpIcon href={lastItem?.href}/>
        </Grid2>
      </Grid2>
    </Box>
  );
};

export default PageHeaderWithHelp;
