'use client';

import React, { useState, useRef } from 'react';

import {
  Box,
  Typography,
  TextField,
  Button,
  useTheme
} from '@mui/material';

import SaveIcon from '@mui/icons-material/Save';

import RichTextEditor from '../../components/richtext/RichTextEditor';

const ContributorsScreen: React.FC = () => {
  const theme = useTheme();

  // State for architect logo
  const [architectLogo, setArchitectLogo] = useState<File | null>(null);
  const [architectLogoUrl, setArchitectLogoUrl] = useState<string>('');
  const [architectDescription, setArchitectDescription] = useState<string>('Soyuz Talib Architects');
  const [architectWebsite, setArchitectWebsite] = useState<string>('https://www.stapi.co.in/');

  // State for facility management logo
  const [facilityLogo, setFacilityLogo] = useState<File | null>(null);
  const [facilityLogoUrl, setFacilityLogoUrl] = useState<string>('');
  const [facilityDescription, setFacilityDescription] = useState<string>('Cushman & Wakefield');
  const [facilityWebsite, setFacilityWebsite] = useState<string>('https://www.cushmanwakefield.com');

  // File input refs
  const architectFileInputRef = useRef<HTMLInputElement>(null);
  const facilityFileInputRef = useRef<HTMLInputElement>(null);

  console.log(architectLogo,facilityLogo);

  // Handle file upload for architect logo
  const handleArchitectLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {

    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];

      setArchitectLogo(file);

      // Create a preview URL

      const previewUrl = URL.createObjectURL(file);

      setArchitectLogoUrl(previewUrl);
    }
  };

  // Handle file upload for facility management logo
  const handleFacilityLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    
    if (event.target.files && event.target.files[0]) {

      const file = event.target.files[0];

      setFacilityLogo(file);

      // Create a preview URL
      
      const previewUrl = URL.createObjectURL(file);

      setFacilityLogoUrl(previewUrl);
    }
  };

  // Handle save

  const handleSave = () => {

    console.log('Saving contributors data');
    
    // Here you would typically make an API call to save the data
  };

  return (
    <Box sx={{
      p: 4,
      borderRadius: '5px',
      backgroundColor: 'white',
      border: '1px solid #e0e0e0',
      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
    }}>
      <Typography
        variant="h6"
        sx={{
          color: theme.palette.primary.main,
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          mb: 3
        }}
      >
        Contributors
      </Typography>

      {/* Architect Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2 }}>Architect Logo</Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
          <Box
            component="label"
            htmlFor="architect-logo-upload"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
              height: 120,
              border: '1px dashed',
              borderColor: 'divider',
              borderRadius: '5px',
              cursor: 'pointer',
              p: 2,
              position: 'relative',
              overflow: 'hidden',
              '&:hover': {
                borderColor: 'primary.main',
                bgcolor: 'action.hover',
              },
            }}
          >
            {architectLogoUrl ? (
              <Box
                component="img"
                src={architectLogoUrl}
                alt="Architect Logo"
                sx={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain',
                }}
              />
            ) : (
              <>
                <Typography variant="body2" color="textSecondary" align="center">
                  Choose file
                </Typography>
                <Typography variant="caption" color="textSecondary" align="center" sx={{ mt: 1 }}>
                  Recommendations: format: JPEG/PNG/GIF, width: 170px, height: as per text content length
                </Typography>
              </>
            )}
            <input
              id="architect-logo-upload"
              type="file"
              accept="image/*"
              onChange={handleArchitectLogoUpload}
              style={{ display: 'none' }}
              ref={architectFileInputRef}
            />
          </Box>

          <Button
            variant="contained"
            color="primary"
            onClick={() => architectFileInputRef.current?.click()}
            sx={{ minWidth: 100 }}
          >
            Choose file
          </Button>
        </Box>

        <Typography variant="body2" sx={{ mb: 2 }}>
          Here you can write about the architect of your society.
        </Typography>

        {/* Rich Text Editor */}
        <Box sx={{ mb: 2 }}>
          <RichTextEditor
            value={architectDescription}
            onChange={setArchitectDescription}
            rows={4}
            placeholder="Write about the architect..."
          />

          <Typography variant="subtitle2" sx={{ mb: 1 }}>Website</Typography>
          <TextField
            fullWidth
            value={architectWebsite}
            onChange={(e) => setArchitectWebsite(e.target.value)}
            variant="outlined"
            placeholder="https://www.example.com"
            size="small"
            sx={{ mb: 3 }}
          />
        </Box>
      </Box>

      {/* Facility Management Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2 }}>Facility Management Logo</Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
          <Box
            component="label"
            htmlFor="facility-logo-upload"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
              height: 120,
              border: '1px dashed',
              borderColor: 'divider',
              borderRadius: '5px',
              cursor: 'pointer',
              p: 2,
              position: 'relative',
              overflow: 'hidden',
              '&:hover': {
                borderColor: 'primary.main',
                bgcolor: 'action.hover',
              },
            }}
          >
            {facilityLogoUrl ? (
              <Box
                component="img"
                src={facilityLogoUrl}
                alt="Facility Management Logo"
                sx={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain',
                }}
              />
            ) : (
              <>
                <Typography variant="body2" color="textSecondary" align="center">
                  Choose file
                </Typography>
                <Typography variant="caption" color="textSecondary" align="center" sx={{ mt: 1 }}>
                  Recommendations: format: JPEG/PNG/GIF, width: 170px, height: as per text content length
                </Typography>
              </>
            )}
            <input
              id="facility-logo-upload"
              type="file"
              accept="image/*"
              onChange={handleFacilityLogoUpload}
              style={{ display: 'none' }}
              ref={facilityFileInputRef}
            />
          </Box>

          <Button
            variant="contained"
            color="primary"
            onClick={() => facilityFileInputRef.current?.click()}
            sx={{ minWidth: 100 }}
          >
            Choose file
          </Button>
        </Box>

        <Typography variant="body2" sx={{ mb: 2 }}>
          Here you can write about the facility management of your society.
        </Typography>

        {/* Rich Text Editor */}
        <Box sx={{ mb: 2 }}>
          <RichTextEditor
            value={facilityDescription}
            onChange={setFacilityDescription}
            rows={4}
            placeholder="Write about the facility management..."
          />

          <Typography variant="subtitle2" sx={{ mb: 1 }}>Website</Typography>
          <TextField
            fullWidth
            value={facilityWebsite}
            onChange={(e) => setFacilityWebsite(e.target.value)}
            variant="outlined"
            placeholder="https://www.example.com"
            size="small"
            sx={{ mb: 3 }}
          />
        </Box>
      </Box>

      {/* Save Button */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="success"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          sx={{
            borderRadius: '5px',
            px: 3,
            py: 1,
            bgcolor: '#8bc34a',
            '&:hover': {
              bgcolor: '#7cb342',
              boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
            }
          }}
        >
          Save
        </Button>
      </Box>
    </Box>
  );
};

export default ContributorsScreen;
