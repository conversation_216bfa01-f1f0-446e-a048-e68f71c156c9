'use client';

import { useState, useEffect, type FC } from 'react';

import dynamic from 'next/dynamic';

import {
  Box,
  Typography,
  Button,
  TextField,
  Grid,
} from '@mui/material';

import MyLocationIcon from '@mui/icons-material/MyLocation';

import LocationOnIcon from '@mui/icons-material/LocationOn';


// Import leaflet for icon fix
import L from 'leaflet';

// Fix for default marker icon issue in react-leaflet
// This needs to be outside the component to avoid re-initialization
if (typeof window !== 'undefined') {
  delete L.Icon.Default.prototype._getIconUrl;
  L.Icon.Default.mergeOptions({
    iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
    iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
    shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
  });
}

// Dynamically import the Map component to avoid SSR issues
const Map = dynamic(
  () => import('./LeafletMap'),
  { ssr: false }
);

// Define the interface for the component props
// interface LocationMapScreenProps {}

const LocationMapScreen: FC = () => {
  const [mapReady, setMapReady] = useState(false);

  // State for latitude and longitude
  const [latitude, setLatitude] = useState<string>('19.0670782669271');
  const [longitude, setLongitude] = useState<string>('72.9937112331904');
  const [position, setPosition] = useState<[number, number]>([19.0670782669271, 72.9937112331904]);



  // Update position when latitude or longitude changes
  useEffect(() => {
    const lat = parseFloat(latitude);

    const lng = parseFloat(longitude);

    if (!isNaN(lat) && !isNaN(lng)) {
      setPosition([lat, lng]);
    }
  }, [latitude, longitude]);

  // Set map as ready after component mounts with a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setMapReady(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);



  // Handle latitude change
  const handleLatitudeChange = (e: React.ChangeEvent<HTMLInputElement>) => {

    setLatitude(e.target.value);
    const lat = parseFloat(e.target.value);

    if (!isNaN(lat)) {
      setPosition([lat, position[1]]);
    }
  };

  // Handle longitude change

  const handleLongitudeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLongitude(e.target.value);

    const lng = parseFloat(e.target.value);

    if (!isNaN(lng)) {
      setPosition([position[0], lng]);
    }
  };

  // Handle marker drag from the Map component
  const handleMarkerDrag = (position: { lat: number; lng: number }) => {
    setPosition([position.lat, position.lng]);
    setLatitude(position.lat.toString());
    setLongitude(position.lng.toString());
  };

  // Handle save

  const handleSave = () => {

    console.log('Saving location:', { latitude, longitude });
    alert('Location saved successfully!');
  };

  // Handle current location
  const handleCurrentLocation = () => {

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          
          const { latitude, longitude } = position.coords;

          setLatitude(latitude.toString());
          setLongitude(longitude.toString());
          setPosition([latitude, longitude]);
        },
        (error) => {
          console.error('Error getting current location:', error);
          alert('Unable to retrieve your location. Please check your browser settings.');
        }
      );
    } else {

      alert('Geolocation is not supported by this browser.');
    }
  };

  // Open in Google Maps
  
  const openInGoogleMaps = () => {
    const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;

    window.open(url, '_blank');
  };

  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column', p: 0 }}>
      {/* Map Container - Takes up most of the space */}
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          height: '400px', // Fixed height to match the screenshot
          overflow: 'hidden',
          border: '1px solid #e0e0e0',
          borderRadius: '4px',
          mb: 3
        }}
      >
        {mapReady ? (
          <>
            <Map
              position={position}
              onMarkerDrag={handleMarkerDrag}
              latitude={latitude}
              longitude={longitude}
            />

            {/* Current Location Button */}
            <Button
              variant="contained"
              size="small"
              onClick={handleCurrentLocation}
              aria-label="Get Current Location"
              sx={{
                position: 'absolute',
                bottom: '20px',
                right: '20px',
                zIndex: 1000,
                minWidth: 'unset',
                width: '40px',
                height: '40px',
                p: 0,
                borderRadius: '4px',
                bgcolor: 'white',
                color: '#333',
                boxShadow: '0 2px 6px rgba(0,0,0,0.3)',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                }
              }}
            >
              <MyLocationIcon />
            </Button>

            {/* Open in Google Maps Button */}
            <Button
              variant="contained"
              size="small"
              onClick={openInGoogleMaps}
              aria-label="Open in Google Maps"
              sx={{
                position: 'absolute',
                bottom: '20px',
                right: '70px',
                zIndex: 1000,
                minWidth: 'unset',
                width: '40px',
                height: '40px',
                p: 0,
                borderRadius: '4px',
                bgcolor: 'white',
                color: '#333',
                boxShadow: '0 2px 6px rgba(0,0,0,0.3)',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                }
              }}
            >
              <LocationOnIcon />
            </Button>
          </>
        ) : (
          <Box
            sx={{
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              bgcolor: '#f5f5f5'
            }}
          >
            <Typography color="textSecondary">Map loading...</Typography>
          </Box>
        )}
      </Box>

      {/* Coordinates Inputs */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography sx={{ mr: 1, minWidth: '80px' }}>
              Latitude:*
            </Typography>
            <TextField
              fullWidth
              value={latitude}
              onChange={handleLatitudeChange}
              variant="outlined"
              size="small"
              placeholder="Enter latitude"
            />
          </Box>
        </Grid>
        <Grid item xs={12} md={6}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography sx={{ mr: 1, minWidth: '80px' }}>
              Longitude:*
            </Typography>
            <TextField
              fullWidth
              value={longitude}
              onChange={handleLongitudeChange}
              variant="outlined"
              size="small"
              placeholder="Enter longitude"
            />
          </Box>
        </Grid>
      </Grid>

      {/* Save Button */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="success"
          onClick={handleSave}
          sx={{
            borderRadius: '4px',
            bgcolor: '#8bc34a',
            '&:hover': {
              bgcolor: '#7cb342',
            }
          }}
        >
          Save
        </Button>
      </Box>
    </Box>
  );
};

export default LocationMapScreen;
