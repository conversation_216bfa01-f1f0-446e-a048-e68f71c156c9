"use client";

import React, { useEffect, useState } from "react";

import { useSearchParams } from "next/navigation";

import Grid2 from "@mui/material/Grid2";
import { Button, CircularProgress, Typography } from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import axios from "axios";

const PDFViewer = ({ customUrl = "" }) => {
  const [pdfUrl, setPdfUrl] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // get search params from url
  const queryParams = useSearchParams();
  
  console.log({ pdfUrl, customUrl, queryParams })

  useEffect(() => {
    if (!customUrl) {
      setError("No PDF URL provided.");
      setLoading(false);
      
return;
    }

    const fetchPdfUrl = async () => {
      try {
        const response = await axios.get(`${customUrl}/download/pdf`, {
          baseURL: process.env.NEXT_PUBLIC_API_URL,
          withCredentials: true,
          params: Object.fromEntries(queryParams.entries()),
        });

        console.log("PDF URL response:", response.data);

        // open in new tab
        window.open(response.data.data.url, "_blank");
        setPdfUrl(response?.data?.data?.url);
      } catch (err) {
        console.error("Error fetching PDF URL:", err);
        setError("Failed to load PDF.");
      } finally {
        setLoading(false);
      }
    };

    fetchPdfUrl();
  }, [customUrl, queryParams]);

  return (
    <div style={{  height: "100vh", padding: "20px" }}>
      {/* Header Section */}
      <Grid2 container alignItems="center" justifyContent="space-between">
        <Typography variant="h6">PDF Viewer</Typography>
        {customUrl && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<DownloadIcon />}
            onClick={() => window.open(pdfUrl, "_blank")}
          >
            Download PDF
          </Button>
        )}
      </Grid2>

      {/* PDF Display Section */}
      <div
        style={{
          marginTop: "10px",
          width: "90%",
          height: "90vh",
          border: "1px solid #ddd",
        }}
      >
        {loading ? (
          <CircularProgress />
        ) : error ? (
          <Typography color="error">{error}</Typography>
        ) : (
          <embed src={pdfUrl} type="application/pdf" width="100%" height="100%" />
        )}
      </div>
    </div>
  );
};

export default PDFViewer;
