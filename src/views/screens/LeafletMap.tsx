'use client';

import React, { useEffect } from 'react';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';

// import 'leaflet/dist/leaflet.css';

// Component to update map center when coordinates change
const ChangeMapView = ({ center }: { center: [number, number] }) => {

  const map = useMap();

  useEffect(() => {
    if (map) {

      // Use a fixed zoom level to avoid the error

      map.setView(center, 13);
    }
  }, [center, map]);

  return null;
};

interface LeafletMapProps {
  position: [number, number];
  onMarkerDrag: (position: { lat: number; lng: number }) => void;
  latitude: string;
  longitude: string;
}

const LeafletMap: React.FC<LeafletMapProps> = ({
  position,
  onMarkerDrag,
  latitude,
  longitude
}) => {

  // Handle marker drag

  const handleMarkerDrag = (e: any) => {

    const marker = e.target;

    const position = marker.getLatLng();

    onMarkerDrag(position);
  };

  return (
    <>
      <style jsx global>{`
        .leaflet-container {
          height: 100%;
          width: 100%;
        }
      `}</style>
      <MapContainer

        // @ts-ignore - center prop is valid but TypeScript doesn't recognize it

        center={position}
        zoom={15} // Higher zoom level for better detail
        style={{ height: '100%', width: '100%' }}

        // @ts-ignore - attributionControl prop is valid but TypeScript doesn't recognize it

        attributionControl={false} // Hide attribution for cleaner look
      >
        <TileLayer

          // @ts-ignore - attribution prop is valid but TypeScript doesn't recognize it

          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        {position && (
          <Marker
            position={position}

            // @ts-ignore - draggable prop is valid but TypeScript doesn't recognize it
            
            draggable={true}

            // @ts-ignore - eventHandlers prop is valid but TypeScript doesn't recognize it

            eventHandlers={{
              dragend: handleMarkerDrag
            }}
          >
            <Popup>
              {latitude}, {longitude}
            </Popup>
          </Marker>
        )}
        <ChangeMapView center={position} />

        {/* Add a small attribution in the corner */}
        
        <div style={{
          position: 'absolute',
          bottom: '2px',
          right: '2px',
          fontSize: '8px',
          backgroundColor: 'rgba(255,255,255,0.7)',
          padding: '1px 3px',
          borderRadius: '2px',
          zIndex: 1000
        }}>
          © OpenStreetMap
        </div>
      </MapContainer>
    </>
  );
};

export default LeafletMap;
