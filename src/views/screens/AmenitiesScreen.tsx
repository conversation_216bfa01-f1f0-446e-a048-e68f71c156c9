'use client';

import React, { useState } from 'react';

import {
  Box,
  Typography,
  Button,
  Grid,
  Checkbox,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SaveIcon from '@mui/icons-material/Save';

interface Amenity {
  id: string;
  name: string;
  checked: boolean;
}

const AmenitiesScreen: React.FC = () => {
  
  // Initial amenities data
  const [amenities, setAmenities] = useState<Amenity[]>([
    { id: '1', name: 'Swimming Pool', checked: true },
    { id: '2', name: 'Garden', checked: true },
    { id: '3', name: 'Golf Course', checked: true },
    { id: '4', name: 'Lift', checked: true },
    { id: '5', name: 'Gas Pipeline', checked: true },
    { id: '6', name: '24 Floor Titan Infrastructure', checked: true },
    { id: '7', name: 'High-Speed Elevators', checked: true },
    { id: '8', name: 'Waste Disposal Management', checked: true },
    { id: '9', name: 'High End security and emergency Management', checked: true },
    { id: '10', name: 'Over 200+ Parking Space', checked: true },
    { id: '11', name: 'Club House', checked: false },
    { id: '12', name: 'Gymnasium', checked: false },
    { id: '13', name: 'Library', checked: false },
    { id: '14', name: 'Terrace', checked: false },
    { id: '15', name: 'Children Play Ground', checked: false },
    { id: '16', name: 'Vaasttu Compliant', checked: true },
    { id: '17', name: 'Equipped with Facility Management Services', checked: true },
    { id: '18', name: 'Column less open floor plan', checked: false },
    { id: '19', name: 'Transportation powered by Schindlers\' port technology', checked: false },
    { id: '20', name: 'Water Storage', checked: false },
    { id: '21', name: 'Temple', checked: false },
    { id: '22', name: 'Parking Area', checked: false },
    { id: '23', name: 'Playhouse', checked: false },
    { id: '24', name: 'Yoga', checked: false },
    { id: '25', name: 'Generator Backup', checked: true },
    { id: '26', name: '3 Tier Security System', checked: true },
    { id: '27', name: 'Occupants privacy secured', checked: true },
    { id: '28', name: 'Grand Entrance Lobby', checked: true },
    { id: '29', name: '24*7 Power Back-up', checked: true }
  ]);
  
  // Dialog state
  const [openDialog, setOpenDialog] = useState(false);
  const [newAmenityName, setNewAmenityName] = useState('');
  
  // Handle checkbox change
  const handleCheckboxChange = (id: string) => {
    setAmenities(prev => 
      prev.map(amenity => 
        amenity.id === id ? { ...amenity, checked: !amenity.checked } : amenity
      )
    );
  };
  
  // Handle dialog open
  const handleOpenDialog = () => {
    setOpenDialog(true);
    setNewAmenityName('');
  };
  
  // Handle dialog close
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  
  // Handle adding new amenity
  const handleAddAmenity = () => {

    if (newAmenityName.trim() === '') {
      alert('Amenity name cannot be empty');
      
      return;
    }
    
    const newAmenity: Amenity = {
      id: Date.now().toString(),
      name: newAmenityName.trim(),
      checked: false
    };
    
    setAmenities(prev => [...prev, newAmenity]);
    handleCloseDialog();
  };
  
  // Handle save
  const handleSave = () => {
    console.log('Saving amenities:', amenities);

    // Here you would typically make an API call to save the data

    alert('Amenities saved successfully!');

  };
  
  // Group amenities into columns
  const leftColumn = amenities.slice(0, 10);
  const middleColumn = amenities.slice(10, 20);
  const rightColumn = amenities.slice(20);
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3 }}>
        Amenities Details
      </Typography>
      
      <Grid container spacing={2}>
        {/* Left Column */}
        <Grid item xs={12} md={4}>
          {leftColumn.map(amenity => (
            <FormControlLabel
              key={amenity.id}
              control={
                <Checkbox
                  checked={amenity.checked}
                  onChange={() => handleCheckboxChange(amenity.id)}
                  color="primary"
                />
              }
              label={amenity.name}
              sx={{ display: 'block', mb: 1 }}
            />
          ))}
        </Grid>
        
        {/* Middle Column */}
        <Grid item xs={12} md={4}>
          {middleColumn.map(amenity => (
            <FormControlLabel
              key={amenity.id}
              control={
                <Checkbox
                  checked={amenity.checked}
                  onChange={() => handleCheckboxChange(amenity.id)}
                  color="primary"
                />
              }
              label={amenity.name}
              sx={{ display: 'block', mb: 1 }}
            />
          ))}
        </Grid>
        
        {/* Right Column */}
        <Grid item xs={12} md={4}>
          {rightColumn.map(amenity => (
            <FormControlLabel
              key={amenity.id}
              control={
                <Checkbox
                  checked={amenity.checked}
                  onChange={() => handleCheckboxChange(amenity.id)}
                  color="primary"
                />
              }
              label={amenity.name}
              sx={{ display: 'block', mb: 1 }}
            />
          ))}
        </Grid>
      </Grid>
      
      {/* New Amenities Section */}
      <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="subtitle1" color="text.secondary">
          New More Amenities
        </Typography>
        
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleOpenDialog}
          sx={{
            borderRadius: '4px',
            textTransform: 'none'
          }}
        >
          New
        </Button>
      </Box>
      
      {/* Save Button */}
      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="success"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          sx={{
            borderRadius: '4px',
            bgcolor: '#8bc34a',
            '&:hover': {
              bgcolor: '#7cb342',
            }
          }}
        >
          Save
        </Button>
      </Box>
      
      {/* Dialog for adding new amenity */}
      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>Add New Amenity</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Amenity Name"
            type="text"
            fullWidth
            variant="outlined"
            value={newAmenityName}
            onChange={(e) => setNewAmenityName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleAddAmenity} variant="contained" color="primary">
            Add
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AmenitiesScreen;
