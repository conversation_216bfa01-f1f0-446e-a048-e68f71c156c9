import React, { useState, useEffect } from 'react';

import {
  Box,
  Typography,
  Button,
  useTheme
} from '@mui/material';

import axios from 'axios';

import RichTextEditor from '../../components/richtext/RichTextEditor';

const DeveloperScreen: React.FC = () => {

  const theme = useTheme();

  // We're using the URL directly, but keeping the setter for the upload function

  const [, setDeveloperLogo] = useState<File | null>(null);

  const [developerLogoUrl, setDeveloperLogoUrl] = useState<string>('');

  const [developerDescription, setDeveloperDescription] = useState<string>(
    'Greenspace Group is a premium real estate development company with its headquarter in Vashi, Navi Mumbai. The group is well-known across the fraternities for its artistic approach towards construction.\n\nThe real-estate giant has always been focused in integrating sustainability, opulence, and ingenuity in each of its projects. Every project developed by the Greenspace Group creates a legacy of trust, excellence, and dedication in their own unique ways. As the urban landscape of Navi Mumbai is expanding, Greenspace Group has been setting new milestones in the real estate construction industry by consistently developing exceptional projects in both residential & commercial sectors. The group is renowned for its sheer dedication to serve the best to its investors and buyers.\n\nGreenspace Group\'s portfolio consists of luxury villas, penthouses, premium residences, luxe apartments, urban studio apartments, premium commercial IT/ITES parks. The group is well-known for their modern designs and world-class amenities in each of its projects.'
  );

  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    // Fetch existing developer data
    const fetchDeveloperData = async () => {
      try {
        setIsLoading(true);

        const response = await axios.get('/admin/societies/developer', {
          withCredentials: true,
          baseURL: process.env.NEXT_PUBLIC_API_URL,
        });

        if (response.data && response.data.data) {
          const { logo, description } = response.data.data;

          if (logo) setDeveloperLogoUrl(logo);
          if (description) setDeveloperDescription(description);
        }
      } catch (error) {
        console.error('Error fetching developer data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDeveloperData();
  }, []);

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {

    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      
      setDeveloperLogo(file);

      // Create a preview URL

      const previewUrl = URL.createObjectURL(file);

      setDeveloperLogoUrl(previewUrl);

      // Upload the file

      const formData = new FormData();

      formData.append('files[]', file);

      try {

        setIsLoading(true);
        
        const response = await axios.post('/admin/file-upload', formData, {

          headers: {
            'Content-Type': 'multipart/form-data',
          },
          withCredentials: true,
          baseURL: process.env.NEXT_PUBLIC_API_URL,
        });

        if (response.data && response.data.data && response.data.data[0]) {
          setDeveloperLogoUrl(response.data.data[0]);
        }
      } catch (error) {
        console.error('Error uploading logo:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      await axios.post('/admin/societies/developer', {
        logo: developerLogoUrl,
        description: developerDescription
      }, {
        withCredentials: true,
        baseURL: process.env.NEXT_PUBLIC_API_URL,
      });

      // Show success message or notification
      alert('Developer information saved successfully!');
    } catch (error) {
      console.error('Error saving developer data:', error);
      alert('Failed to save developer information. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box sx={{
      p: 4,
      borderRadius: '5px',
      backgroundColor: 'white',
      border: '1px solid #e0e0e0',
      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
    }}>
      <Typography
        variant="h6"
        sx={{
          color: theme.palette.primary.main,
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          mb: 3
        }}
      >
        Developer
      </Typography>

      {/* Developer Logo */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2 }}>Developer Logo</Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box
            component="label"
            htmlFor="developer-logo-upload"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
              height: 120,
              border: '1px dashed',
              borderColor: 'divider',
              borderRadius: '5px',
              cursor: 'pointer',
              p: 2,
              position: 'relative',
              overflow: 'hidden',
              '&:hover': {
                borderColor: 'primary.main',
                bgcolor: 'action.hover',
              },
            }}
          >
            {developerLogoUrl ? (
              <Box
                component="img"
                src={developerLogoUrl}
                alt="Developer Logo"
                sx={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain',
                }}
              />
            ) : (
              <>
                <Typography variant="body2" color="textSecondary" align="center">
                  Choose file
                </Typography>
                <Typography variant="caption" color="textSecondary" align="center" sx={{ mt: 1 }}>
                  Recommendations: format: JPEG/PNG/GIF, width: greater than 150px, height: as per text content length
                </Typography>
              </>
            )}
            <input
              id="developer-logo-upload"
              type="file"
              accept="image/*"
              onChange={handleLogoUpload}
              style={{ display: 'none' }}
            />
          </Box>

          <Button
            variant="contained"
            color="primary"
            onClick={() => document.getElementById('developer-logo-upload')?.click()}
            sx={{ minWidth: 100 }}
          >
            Upload
          </Button>
        </Box>
      </Box>

      {/* Developer Description */}
      <Box>
        <Typography variant="body2" sx={{ mb: 2 }}>
          Here you can write about the developers of your society.
        </Typography>

        <RichTextEditor
          value={developerDescription}
          onChange={setDeveloperDescription}
          rows={12}
          placeholder="Write about the developer..."
        />
      </Box>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSave}
          disabled={isLoading}
          size="small"
          sx={{
            px: 3,
            py: 1,
            borderRadius: '5px',
            '&:hover': {
              boxShadow: 2,
            },
          }}
        >
          Save
        </Button>
      </Box>
    </Box>
  );
};

export default DeveloperScreen;
