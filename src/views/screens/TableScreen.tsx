import Grid from '@mui/material/Grid2'

// import { Children } from "react"

import ErrorBoundary from '@components/ErrorBoundary'
import RenderTable from '../table/components/RenderTable'

type TableScreenProps = {
  isGate?: boolean
  apiURL?: string | null
  hideActions?: boolean
}

const TableScreen: React.FC<TableScreenProps> = ({ isGate = false, apiURL = null }) => {
  return (
    <Grid container spacing={2}>
      <Grid size={12}>
        <ErrorBoundary>
            <RenderTable isGate={isGate} customURL={apiURL}/>
        </ErrorBoundary>
      </Grid>
    </Grid>
  )
}

export default TableScreen
