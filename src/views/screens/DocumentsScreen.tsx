'use client';

import React, { useState, useRef } from 'react';

import {
  Box,
  Typography,
  Button,
  Grid,
  Paper,
  IconButton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Breadcrumbs,
  Link as MuiLink
} from '@mui/material';

// Icons
import FolderIcon from '@mui/icons-material/Folder';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import CreateNewFolderIcon from '@mui/icons-material/CreateNewFolder';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';

interface DocumentItem {
  id: string;
  name: string;
  type: 'folder' | 'document';
  path?: string;
}

const DocumentsScreen: React.FC = () => {

  const fileInputRef = useRef<HTMLInputElement>(null);

  const [documents, setDocuments] = useState<DocumentItem[]>([

    { id: '1', name: '100', type: 'folder' },
    { id: '2', name: 'neeraj', type: 'folder' },
    { id: '3', name: 'kk', type: 'folder' },
    { id: '4', name: 'A102', type: 'folder' }
  ]);
  
  const [currentPath, setCurrentPath] = useState<string[]>(['Home']);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [dialogType, setDialogType] = useState<'new-folder' | 'new-document' | 'edit'>('new-folder');
  const [itemName, setItemName] = useState<string>('');
  const [editItemId, setEditItemId] = useState<string | null>(null);
  
  // Handle file selection

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {

    const files = event.target.files;

    if (files) {
      const validExtensions = ['.jpeg', '.jpg', '.png', '.pdf', '.xlsx', '.txt', '.word', '.xls'];
      const newFiles: File[] = [];
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
        
        if (validExtensions.includes(fileExtension)) {
          newFiles.push(file);
        } else {
          alert(`File ${file.name} has an invalid extension. Only jpeg, jpg, png, pdf, xlsx, txt, word, xls are allowed.`);
        }
      }
      
      setSelectedFiles(prev => [...prev, ...newFiles]);
    }
  };
  
  // Handle submit

  const handleSubmit = () => {

    console.log('Uploading files:', selectedFiles);

    // Here you would typically upload the files to your backend

    alert(`${selectedFiles.length} files uploaded successfully!`);
    setSelectedFiles([]);
  };
  
  // Handle new folder/document dialog
  const handleOpenDialog = (type: 'new-folder' | 'new-document') => {
    setDialogType(type);
    setItemName('');
    setOpenDialog(true);
  };
  
  // Handle edit dialog
  const handleEdit = (item: DocumentItem) => {
    setDialogType('edit');
    setItemName(item.name);
    setEditItemId(item.id);
    setOpenDialog(true);
  };
  
  // Handle dialog close

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setItemName('');
    setEditItemId(null);
  };
  
  // Handle dialog submit

  const handleDialogSubmit = () => {
    
    if (itemName.trim() === '') {
      alert('Name cannot be empty');
      
      return;
    }
    
    if (dialogType === 'edit' && editItemId) {
      // Edit existing item
      setDocuments(prev => 
        prev.map(item => 
          item.id === editItemId ? { ...item, name: itemName } : item
        )
      );
    } else {
      // Add new item
      const newItem: DocumentItem = {
        id: Date.now().toString(),
        name: itemName,
        type: dialogType === 'new-folder' ? 'folder' : 'document'
      };
      
      setDocuments(prev => [...prev, newItem]);
    }
    
    handleCloseDialog();
  };
  
  // Handle delete
  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this item?')) {
      setDocuments(prev => prev.filter(item => item.id !== id));
    }
  };
  
  // Handle navigation
  const handleNavigate = (index: number) => {
    setCurrentPath(prev => prev.slice(0, index + 1));
  };
  
  // Handle folder click
  const handleFolderClick = (name: string) => {
    setCurrentPath(prev => [...prev, name]);
  };
  
  return (
    <Box sx={{ p: 3 }}>
      {/* Breadcrumbs navigation */}
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />} 
        aria-label="breadcrumb"
        sx={{ mb: 2 }}
      >
        {currentPath.map((path, index) => (
          <MuiLink
            key={index}
            component="button"
            variant="body1"
            color={index === currentPath.length - 1 ? 'text.primary' : 'inherit'}
            onClick={() => handleNavigate(index)}
            sx={{ textDecoration: 'none', cursor: 'pointer' }}
          >
            {path}
          </MuiLink>
        ))}
      </Breadcrumbs>
      
      {/* Document/Folder Grid */}
      <Grid container spacing={2}>
        {documents.map((item) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={item.id}>
            <Paper 
              elevation={0} 
              sx={{ 
                p: 2, 
                display: 'flex', 
                flexDirection: 'column',
                alignItems: 'center',
                position: 'relative',
                border: '1px solid #e0e0e0',
                borderRadius: '4px',
                height: '100%',
                '&:hover': {
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                }
              }}
            >
              {/* Delete button */}
              <IconButton 
                size="small" 
                sx={{ 
                  position: 'absolute', 
                  top: 8, 
                  right: 8,
                  color: '#f44336'
                }}
                onClick={() => handleDelete(item.id)}
              >
                <DeleteIcon />
              </IconButton>
              
              {/* Folder icon */}
              <Box 
                sx={{ 
                  display: 'flex', 
                  justifyContent: 'center', 
                  mb: 2,
                  cursor: 'pointer'
                }}
                onClick={() => item.type === 'folder' && handleFolderClick(item.name)}
              >
                <FolderIcon sx={{ fontSize: 60, color: '#0d47a1' }} />
              </Box>
              
              {/* Item name and edit button */}
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                width: '100%'
              }}>
                <Typography 
                  variant="body1" 
                  sx={{ 
                    textAlign: 'center',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: 'calc(100% - 40px)'
                  }}
                >
                  {item.name}
                </Typography>
                <IconButton 
                  size="small" 
                  sx={{ ml: 1 }}
                  onClick={() => handleEdit(item)}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>
      
      {/* Action buttons */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'flex-end',
        gap: 1,
        mt: 3
      }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog('new-document')}
          sx={{
            borderRadius: '4px',
            textTransform: 'none'
          }}
        >
          New Document
        </Button>
        
        <Button
          variant="contained"
          color="primary"
          startIcon={<CreateNewFolderIcon />}
          onClick={() => handleOpenDialog('new-folder')}
          sx={{
            borderRadius: '4px',
            textTransform: 'none'
          }}
        >
          New Folder
        </Button>
      </Box>
      
      {/* File upload section */}
      <Box sx={{ mt: 3 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          Upload files with extensions: .jpeg,.jpg,.png,.pdf,.xlsx,.txt,.word,.xls
        </Typography>
        
        <input
          type="file"
          multiple
          ref={fileInputRef}
          onChange={handleFileSelect}
          style={{ display: 'none' }}
          accept=".jpeg,.jpg,.png,.pdf,.xlsx,.txt,.word,.xls"
        />
        
        {/* Selected files list */}
        {selectedFiles.length > 0 && (
          <Box sx={{ mt: 2, mb: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Selected Files:
            </Typography>
            {selectedFiles.map((file, index) => (
              <Typography key={index} variant="body2">
                {file.name}
              </Typography>
            ))}
          </Box>
        )}
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<UploadFileIcon />}
            onClick={() => fileInputRef.current?.click()}
            sx={{
              borderRadius: '4px',
              textTransform: 'none'
            }}
          >
            Choose Files
          </Button>
          
          <Button
            variant="contained"
            color="success"
            disabled={selectedFiles.length === 0}
            onClick={handleSubmit}
            sx={{
              borderRadius: '4px',
              textTransform: 'none',
              bgcolor: '#8bc34a',
              '&:hover': {
                bgcolor: '#7cb342',
              }
            }}
          >
            Submit
          </Button>
        </Box>
      </Box>
      
      {/* Dialog for new folder/document or edit */}
      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>
          {dialogType === 'new-folder' 
            ? 'Create New Folder' 
            : dialogType === 'new-document' 
              ? 'Create New Document' 
              : 'Edit Name'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            type="text"
            fullWidth
            variant="outlined"
            value={itemName}
            onChange={(e) => setItemName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleDialogSubmit} variant="contained" color="primary">
            {dialogType === 'edit' ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DocumentsScreen;
