'use server';

import Grid2 from '@mui/material/Grid2';

import CommonPageStructure from '@/components/commonPageStructure';
import ComponentRenderer from '@/components/commonPageStructure/ComponentRenderer';
import type { ComponentConfig } from '@/types/menuTypes';

interface MixScreenProps {
  params: Record<string, any>;
  pathname: string;
  order: ComponentConfig[];
  commonPageConfig?: { title: string; buttons: { label: string; route: string }[] };
  href: string;
}

const MixScreen = ({ params, pathname, order, commonPageConfig, href }: MixScreenProps) => {
  // Components for left group excluding cards.
  const leftNonCardComponents = order.filter(component =>
    ['form', 'table'].includes(component.type)
  );

  // Card components.
  const cardComponents = order.filter(component => component.type === 'card');

  // Components that are always on the right: timeline and helpNote.
  const rightComponentsBase = order.filter(component =>
    ['timeline', 'helpNote'].includes(component.type)
  );

  let leftGroup: ComponentConfig[] = [];
  let rightGroup: ComponentConfig[] = [...rightComponentsBase];

  // Conditionally assign card components:
  if (leftNonCardComponents.length === 0) {
    // If no non-card components exist on the left,
    // then place card components on the left.
    leftGroup = cardComponents;
  } else {
    // Otherwise, use non-card components on the left and
    // place card components on the right.
    leftGroup = leftNonCardComponents;
    rightGroup = [...cardComponents, ...rightComponentsBase];
  }

  return (
    <CommonPageStructure
      title={commonPageConfig?.title || 'Default Title'}
      buttons={commonPageConfig?.buttons || []}
    >
      <Grid2 container spacing={5}>
        {/* Left Side: occupies 8/12 columns on medium screens and full width on smaller screens */}
        <Grid2 size={{ xs: 12, md: 8 }}>
          {leftGroup.map((component, index) => (
            <ComponentRenderer
              key={index}
              component={component}
              params={params}
              pathname={pathname}
              href={href}
            />
          ))}
        </Grid2>
        
        {/* Right Side: occupies 4/12 columns on medium screens and full width on smaller screens */}
        <Grid2 size={{ xs: 12, md: 4 }}>
          {rightGroup.map((component, index) => (
            <ComponentRenderer
              key={index}
              component={component}
              params={params}
              pathname={pathname}
              href={href}
            />
          ))}
        </Grid2>
      </Grid2>
    </CommonPageStructure>
  );
};

export default MixScreen;
