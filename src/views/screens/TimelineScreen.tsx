"use client";

// @ts-nocheck

import { useEffect, useState } from "react";

// ** MUI Imports
import Box from "@mui/material/Box";
import { styled } from "@mui/material/styles";
import TimelineDot from "@mui/lab/TimelineDot";
import TimelineItem from "@mui/lab/TimelineItem";
import Typography from "@mui/material/Typography";
import TimelineContent from "@mui/lab/TimelineContent";
import TimelineSeparator from "@mui/lab/TimelineSeparator";
import TimelineConnector from "@mui/lab/TimelineConnector";
import MuiTimeline from "@mui/lab/Timeline";
import type { TimelineProps } from "@mui/lab/Timeline";
import TimelineOppositeContent from "@mui/lab/TimelineOppositeContent";
import axios from "axios";

import { Card, CardContent, CardHeader, Divider } from "@mui/material";

// Styled Timeline component
const Timeline = styled(MuiTimeline)<TimelineProps>({
    paddingLeft: 0,
    paddingRight: 0,
    "& .MuiTimelineItem-root": {
        width: "100%",
        "&:before": {
            display: "none",
        },
    },
});

const TimelineRight = () => {
    const [data, setData] = useState<any[]>([]); // Initialize as an empty array
    const [loading, setLoading] = useState<boolean>(false); // Loading state to track API call
    const [error, setError] = useState<string>(""); // Error state for handling errors

    useEffect(() => {
        const fetchApi = async () => {
            setLoading(true); // Set loading to true when the request starts
            setError(""); // Reset any previous errors

            try {
                const response = await axios.get(
                    `${process.env.NEXT_PUBLIC_API_URL}/admin/income-tracker-setting/readIncomeAccounts/activity_log`,
                    {
                        params: {
                            module: "Journal",
                        },
                        withCredentials: true,
                    },
                );

                const finalData = response.data.data;

                // Check if the response data is an array
                if (Array.isArray(finalData)) {
                    setData(finalData); // Set the data if it's an array
                } else {
                    console.warn("Expected an array but got:", finalData);
                    setError("Failed to fetch valid data."); // Set error if data is not an array
                }
            } catch (error) {
                console.warn("Failed to fetch timeline data:", error);
                setError("Failed to fetch timeline data."); // Set error message on failure
            } finally {
                setLoading(false); // Set loading to false when the request is complete
            }
        };

        fetchApi();
    }, []);

    return (
        <Box sx={{ position: "relative" }}>
            {/* Show loader while loading */}
            {/* {loading && (
                <Box
                    sx={{
                        position: "absolute",
                        top: "80%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                    }}
                >
                    <CircularProgress />
                </Box>
            )} */}

            {/* Render timeline when data is available */}

            <Card>
                <CardHeader title="Activities" />
                <Divider />
                <CardContent>
                    {!loading && !error && Array.isArray(data) && data.length > 0 && (
                        <>
                            <Timeline title="Activities">
                                {data.map(
                                    (item: {
                                        id: string;
                                        date_time: string;
                                        desc: string;
                                    }) => (
                                        <TimelineItem key={item.id}>
                                            <TimelineOppositeContent>
                                                <Typography variant="body2" color="text.secondary">
                                                    {item.date_time}
                                                </Typography>
                                            </TimelineOppositeContent>
                                            <TimelineSeparator>
                                                <TimelineDot color="info" variant="outlined" />
                                                <TimelineConnector />
                                            </TimelineSeparator>
                                            <TimelineContent>
                                                <Box
                                                    sx={{
                                                        mb: 2,
                                                        display: "flex",
                                                        flexWrap: "wrap",
                                                        alignItems: "center",
                                                        justifyContent: "space-between",
                                                    }}
                                                >
                                                    <Typography
                                                        variant="body2"
                                                        sx={{
                                                            mr: 2,
                                                            fontWeight: 500,
                                                            color: "text.primary",
                                                        }}
                                                    >
                                                        {item.desc}
                                                    </Typography>
                                                </Box>
                                            </TimelineContent>
                                        </TimelineItem>
                                    )
                                )}
                            </Timeline>
                        </>
                    )}
                </CardContent>
            </Card>


            {/* Show error message if data fetching fails */}
            {error && (
                <Typography variant="h6" color="error" sx={{ textAlign: "center", mt: 2 }}>
                    {error}
                </Typography>
            )}
        </Box>
    );
};

export default TimelineRight;
