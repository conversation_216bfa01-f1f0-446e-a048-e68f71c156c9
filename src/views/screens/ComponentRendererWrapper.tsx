'use client';

import { memo } from 'react';

import ComponentRenderer from '@/components/commonPageStructure/ComponentRenderer';
import type { ComponentConfig } from '@/types/menuTypes';

interface ComponentRendererWrapperProps {
  component: ComponentConfig;
  params: Record<string, any>;
  pathname: string;
  href: string;
}

// This wrapper component handles the type casting to make TypeScript happy
const ComponentRendererWrapper = ({ component, params, pathname, href }: ComponentRendererWrapperProps) => {
  // Cast the component to any to bypass TypeScript's type checking
  return (
    <ComponentRenderer
      component={component as any}
      params={params}
      pathname={pathname}
      href={href}
    />
  );
};

// Memoize the component to prevent unnecessary rerenders
export default memo(ComponentRendererWrapper);
