"use client";

import React, { useState } from 'react';

import Grid2 from '@mui/material/Grid2';
import { Box, ToggleButton, ToggleButtonGroup, Typography } from '@mui/material';
import BarChartOutlined from '@mui/icons-material/BarChartOutlined';
import SpaceDashboardOutlinedIcon from '@mui/icons-material/SpaceDashboardOutlined';

import ComplaintsCard from '@/components/dashboard/cards/ComplaintsCard';
import BalanceDuesCard from '@/components/dashboard/cards/BalanceDuesCard';
import AllotteesCard from '@/components/dashboard/cards/AllotteesCard';
import BankCashCard from '@/components/dashboard/cards/BankCashCard';
import SurveyCard from '@/components/dashboard/cards/SurveyCard';
import MonthlyExpensesChart from '@/components/dashboard/cards/MonthlyExpenseChart';


export default function DashboardScreen() {
  const [isChartView, setIsChartView] = useState<boolean>(false);

  
return (
    <Box p={3} >
      <Grid2 container spacing={2} mb={2}>
        <Grid2 size={6} display="flex" alignItems="center">
          <Typography variant="h5" gutterBottom>
            Dashboard
          </Typography>
        </Grid2>
        <Grid2 size={6} display="flex" justifyContent="flex-end">
          {/* toggle mui icon */}
          <ToggleButtonGroup
            value={isChartView ? 'chart' : 'card'}
            exclusive
            aria-label="text alignment"
          >
            <ToggleButton
              value="card"
              title='Card View'
              aria-label="left aligned"
              onClick={() => setIsChartView(false)}
            >
              <SpaceDashboardOutlinedIcon />
            </ToggleButton>
            <ToggleButton
              value="chart"
              title='Chart View'
              aria-label="centered"
              onClick={() => setIsChartView(true)}
            >
              <BarChartOutlined />
            </ToggleButton>
          </ToggleButtonGroup>
        </Grid2>
      </Grid2>

      <Grid2 container spacing={3}>
        {[
          ComplaintsCard,
          BalanceDuesCard,
          AllotteesCard,
          BankCashCard,
          SurveyCard,
        ].map((CardComponent, idx) => (
          <Grid2
            key={idx}
            size={{ xs: 12, sm: 6, md: 4 }}
            sx={{display: "flex"}}
          >
            <CardComponent
              showInChart={isChartView} // Show chart only for AllotteesCard
            />
          </Grid2>
        ))}
        <Grid2 size={12}>
          <MonthlyExpensesChart />
        </Grid2>
      </Grid2>
    </Box>
  );
}
