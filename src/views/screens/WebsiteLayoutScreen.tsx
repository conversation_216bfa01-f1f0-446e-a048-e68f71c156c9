'use client';

import { type FC, useState } from 'react';

import {
  Box,
  Typography,
  Button,
  Grid,
  Paper,
  styled
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

const ColorOption = styled(Paper)(({ theme }) => ({
  width: '60px',
  height: '60px',
  cursor: 'pointer',
  border: '2px solid transparent',
  borderRadius: '4px',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: theme.shadows[4]
  }
}));

const SelectedColorOption = styled(Paper)(({ theme }) => ({
  width: '60px',
  height: '60px',
  cursor: 'pointer',
  border: `2px solid ${theme.palette.primary.main}`,
  borderRadius: '4px',
  boxShadow: theme.shadows[4],
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    transform: 'scale(1.05)'
  }
}));

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

// interface WebsiteLayoutScreenProps {}

const WebsiteLayoutScreen: FC = () => {
  
  // State for header logo
  const [headerLogo, setHeaderLogo] = useState<File | null>(null);
  
  // State for selected skin color
  const [selectedColor, setSelectedColor] = useState<string>('orange');
  
  // Color options
  const colorOptions = [
    { id: 'blue', color: '#87CEEB', name: 'Blue' },
    { id: 'green', color: '#C3D825', name: 'Green' },
    { id: 'yellow', color: '#FFD700', name: 'Yellow' },
    { id: 'orange', color: '#FF8C00', name: 'Orange' }
  ];
  
  // Handle logo file change
  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setHeaderLogo(event.target.files[0]);
    }
  };
  
  // Handle color selection
  const handleColorSelect = (colorId: string) => {
    setSelectedColor(colorId);
  };
  
  // Handle save
  
  const handleSave = () => {
    console.log('Saving website layout settings:', {
      headerLogo: headerLogo?.name,
      skinColor: selectedColor
    });

    // Here you would typically make an API call to save the data
    alert('Website layout settings saved successfully!');
  };
  
  // Handle reset

  const handleReset = () => {
    setHeaderLogo(null);
    setSelectedColor('orange');
  };
  
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" sx={{ mb: 4, fontWeight: 500 }}>
        Choose & Customize Layout
      </Typography>
      
      {/* Header Logo Section */}
      <Grid container spacing={2} alignItems="center" sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Typography variant="body1" sx={{ fontWeight: 500 }}>
            Header Logo
          </Typography>
        </Grid>
        <Grid item xs={12} md={10}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              component="label"
              variant="outlined"
              startIcon={<CloudUploadIcon />}
              sx={{ 
                borderRadius: '4px',
                textTransform: 'none'
              }}
            >
              Choose file
              <VisuallyHiddenInput 
                type="file" 
                accept="image/jpeg,image/png,image/gif" 
                onChange={handleLogoChange}
              />
            </Button>
            {headerLogo && (
              <Typography variant="body2" color="text.secondary">
                {headerLogo.name}
              </Typography>
            )}
          </Box>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
            Recommendations: format: JPEG/PNG/GIF/WebP, width: greater than or equal to 200px, height: greater than or equal to 75px, aspect ratio: 1:1
          </Typography>
        </Grid>
      </Grid>
      
      {/* Skin Color Section */}
      <Grid container spacing={2} alignItems="center" sx={{ mb: 6 }}>
        <Grid item xs={12} md={2}>
          <Typography variant="body1" sx={{ fontWeight: 500 }}>
            Skin Color*
          </Typography>
        </Grid>
        <Grid item xs={12} md={10}>
          <Box sx={{ display: 'flex', gap: 2 }}>
            {colorOptions.map((option) => (
              option.id === selectedColor ? (
                <SelectedColorOption 
                  key={option.id}
                  sx={{ bgcolor: option.color }}
                  onClick={() => handleColorSelect(option.id)}
                />
              ) : (
                <ColorOption 
                  key={option.id}
                  sx={{ bgcolor: option.color }}
                  onClick={() => handleColorSelect(option.id)}
                />
              )
            ))}
          </Box>
        </Grid>
      </Grid>
      
      {/* Action Buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
        <Button
          variant="contained"
          color="success"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          sx={{
            borderRadius: '4px',
            bgcolor: '#8bc34a',
            '&:hover': {
              bgcolor: '#7cb342',
            }
          }}
        >
          Save
        </Button>
        <Button
          variant="outlined"
          startIcon={<RestartAltIcon />}
          onClick={handleReset}
          sx={{
            borderRadius: '4px',
            textTransform: 'none'
          }}
        >
          Reset
        </Button>
      </Box>
    </Box>
  );
};

export default WebsiteLayoutScreen;
