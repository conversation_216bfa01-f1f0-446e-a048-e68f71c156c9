'use client';

import React, { useState } from 'react';

import {
  Box,
  Paper,
  Typography,
  Button
} from '@mui/material';

import SaveIcon from '@mui/icons-material/Save';

import RichTextEditor from '../../components/richtext/RichTextEditor';

const CommitteeScreen: React.FC = () => {

  const [committeeContent, setCommitteeContent] = useState<string>(
`• <PERSON><PERSON><PERSON> (Chairman)
• <PERSON><PERSON><PERSON><PERSON>
• <PERSON><PERSON><PERSON>
• <PERSON><PERSON><PERSON>`
  );

  const handleSave = () => {
    
    console.log('Committee content saved:', committeeContent);
    
    // Here you would typically save the content to your backend

    alert('Committee information saved successfully!');
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="body1" sx={{ mb: 2 }}>
        Here you can write about the committee of your society.
      </Typography>

      <Paper variant="outlined" sx={{ mb: 2 }}>
        <RichTextEditor
          value={committeeContent}
          onChange={setCommitteeContent}
          rows={8}
          placeholder="Write about your society's committee members..."
        />
      </Paper>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="success"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          sx={{
            borderRadius: '4px',
            px: 3,
            py: 1,
            bgcolor: '#8bc34a',
            '&:hover': {
              bgcolor: '#7cb342',
            }
          }}
        >
          Save
        </Button>
      </Box>
    </Box>
  );
};

export default CommitteeScreen;
