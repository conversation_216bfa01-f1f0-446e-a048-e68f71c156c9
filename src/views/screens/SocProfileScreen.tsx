"use client";

import React, { useState, useRef } from 'react';

import { useTheme } from '@mui/material/styles';

import {
  Box,
  Paper,
  Tab,
  Tabs,
  Typography,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  IconButton,
  Card,
  CardMedia
} from '@mui/material';

// Icons
import SaveIcon from '@mui/icons-material/Save';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import UploadIcon from '@mui/icons-material/Upload';
import Grid2 from '@mui/material/Grid2';
import TabContext from '@mui/lab/TabContext';
import TabPanel from '@mui/lab/TabPanel';

// Custom Components
import ContributorsScreen from './ContributorsScreen';
import DeveloperScreen from './DeveloperScreen';
import CommitteeScreen from './CommitteeScreen';
import DocumentsScreen from './DocumentsScreen';
import AmenitiesScreen from './AmenitiesScreen';
import WebsiteLayoutScreen from './WebsiteLayoutScreen';
import LocationMapScreen from './LocationMapScreen';
import RichTextEditor from '../../components/richtext/RichTextEditor';

interface SocProfileScreenProps {
  customUrl?: string;
}

const SocProfileScreen: React.FC<SocProfileScreenProps> = () => {
  // Theme hooks
  const theme = useTheme();

  const [activeTab, setActiveTab] = useState('0');

  // State for banner uploads
  const [banners, setBanners] = useState({
    banner1: null as File | null,
    banner2: null as File | null,
    banner3: null as File | null
  });

  // State for gallery images
  const [galleryImages, setGalleryImages] = useState<Array<{
    id: number;
    file: File | null;
    url: string;
    title: string;
    category: string;
  }>>([{
    id: 1,
    file: null,
    url: 'https://images.unsplash.com/photo-1560185007-c5ca9d2c014d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    title: 'Swimming Pool',
    category: 'Amenities'
  }, {
    id: 2,
    file: null,
    url: 'https://images.unsplash.com/photo-1560185127-6ed189bf02f4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    title: 'Garden Area',
    category: 'Outdoor'
  }, {
    id: 3,
    file: null,
    url: 'https://images.unsplash.com/photo-1576013551627-0cc20b96c2a7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    title: 'Playground',
    category: 'Recreation'
  }, {
    id: 4,
    file: null,
    url: 'https://images.unsplash.com/photo-1604014237800-1c9102c219da?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    title: 'Building View',
    category: 'Exterior'
  }, {
    id: 5,
    file: null,
    url: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    title: 'Lobby',
    category: 'Interior'
  }]);

  // File input ref for gallery uploads
  const galleryFileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    society_name: 'Futurescape Technologies Pvt Ltd',
    society_reg_number: 'Reg001',
    society_reg_name: 'Demo Co-Op Housing Society Ltd',
    society_gst_number: '27FGDCBA1234G',
    website_credentials: 'admin',
    society_office_email: '<EMAIL>',
    society_office_phone: '9898765678',
    society_pan: '123',
    address_1: '5 Panchavati Marg',
    address_2: 'Sector 30, Vashi',
    landmark: 'CIDCO Exhibition Center',
    city_or_town: 'Navi Mumbai',
    postal_code: '400703',
    state: 'Maharashtra',
    society_website: 'www.mysoc.in',
    association_name: 'Other One',
    welcome_message: '# Welcome to Futurescape Technologies Pvt Ltd\n\nWe are delighted to welcome you to our society. This is a community where we value cooperation, respect, and a shared commitment to creating a pleasant living environment for all residents.\n\n## Our Mission\n\nTo create a harmonious living environment where all members can enjoy:\n\n- Safety and security\n- Well-maintained facilities\n- Community engagement\n- Transparent management\n\nPlease feel free to reach out to the society office if you have any questions or concerns.'
  });

  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {

    setActiveTab(newValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle file upload for banners
  const handleFileUpload = (name: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setBanners(prev => ({
        ...prev,
        [name]: event.target.files?.[0] || null
      }));
    }
  };

  // Handle gallery image upload
  const handleGalleryUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const categories = ['Amenities', 'Outdoor', 'Recreation', 'Interior', 'Exterior'];
      const titles = ['New Image', 'Society View', 'Community Space', 'Building Area', 'Facility'];

      const newImages = Array.from(event.target.files).map((file, index) => ({
        id: Date.now() + index,
        file,
        url: URL.createObjectURL(file),
        title: titles[Math.floor(Math.random() * titles.length)],
        category: categories[Math.floor(Math.random() * categories.length)]
      }));

      setGalleryImages(prev => [...prev, ...newImages]);
    }
  };

  // Handle gallery image deletion
  const handleDeleteGalleryImage = (id: number) => {
    setGalleryImages(prev => prev.filter(image => image.id !== id));
  };

  // Trigger file input click
  const handleAddGalleryImage = () => {
    if (galleryFileInputRef.current) {
      galleryFileInputRef.current.click();
    }
  };



  const handleSave = () => {

    console.log('Saving data:', formData);

    // Here you would typically make an API call to save the data
  };

  // Define the tabs based on the schema
  const tabs = [
    'Basic',
    'Home',
    'Gallery',
    'Developer',
    'Contributions',
    'Committee',
    'Documents',
    'Amenities',
    'Website Layout',
    'Location Map'
  ];

  // Style for form fields
  const fieldStyle = {
    marginBottom: '8px',
    marginTop: '8px',
    '& .MuiOutlinedInput-root': {
      borderRadius: '4px',
      '& fieldset': {
        borderColor: '#e0e0e0'
      },
      '&:hover fieldset': {
        borderColor: '#bdbdbd'
      },
      '&.Mui-focused fieldset': {
        borderColor: '#1976d2'
      }
    },
    '& .MuiInputBase-input': {
      padding: '10px 14px'
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper elevation={0} sx={{ p: 0 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', p: 2 }}>
          <Typography variant="h6">Website</Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              try {
                // Get the current hostname
                const hostname = window.location.hostname;
                
                // Get the current port
                const port = window.location.port ? `:${window.location.port}` : '';
                
                // Get the protocol
                const protocol = window.location.protocol;

                // Use the redirect page which will handle the navigation to the preview website
                // This is more reliable across different environments
                const previewUrl = `${protocol}//${hostname}${port}/preview-website-redirect`;

                console.log('Opening preview website at:', previewUrl);

                // Open the preview site in a new tab
                window.open(previewUrl, '_blank');
              } catch (error) {
                console.error('⚠️ Error opening preview site:', error);
                alert('Failed to open preview site. Please try again.');
              }
            }}
            size="small"
            sx={{
              borderRadius: '4px',
              textTransform: 'none'
            }}
          >
            Preview Society Website
          </Button>
        </Box>

        <Divider />

        <TabContext value={activeTab}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              aria-label="society profile tabs"
            >
              {tabs.map((tab, index) => (
                <Tab key={tab} label={tab} value={String(index)} />
              ))}
            </Tabs>
          </Box>

          {/* Basic Tab */}
          <TabPanel value="0" sx={{ p: 3 }}>
            <Box sx={{
              p: 4,
              borderRadius: '12px',
              backgroundColor: 'white',
              border: '1px solid #e0e0e0',
              boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
            }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 500 }}>
                Society Details
              </Typography>

              <Grid2 container spacing={3}>
                {/* First Column */}
                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-society-name"
                    label="Society Name"
                    name="society_name"
                    value={formData.society_name}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-society-reg-number"
                    label="Society Reg Number"
                    name="society_reg_number"
                    value={formData.society_reg_number}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-society-reg-name"
                    label="Society Reg Name"
                    name="society_reg_name"
                    value={formData.society_reg_name}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-society-gst-number"
                    label="Society GST Number"
                    name="society_gst_number"
                    value={formData.society_gst_number}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-website-credentials"
                    label="Website Credentials"
                    name="website_credentials"
                    value={formData.website_credentials}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-society-office-email"
                    label="Society Office Email"
                    name="society_office_email"
                    value={formData.society_office_email}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-society-office-phone"
                    label="Society Office Phone"
                    name="society_office_phone"
                    value={formData.society_office_phone}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-society-pan"
                    label="Society PAN"
                    name="society_pan"
                    value={formData.society_pan}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-address-1"
                    label="Address-1"
                    name="address_1"
                    value={formData.address_1}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-address-2"
                    label="Address-2"
                    name="address_2"
                    value={formData.address_2}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-landmark"
                    label="Landmark"
                    name="landmark"
                    value={formData.landmark}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-city-or-town"
                    label="City or Town"
                    name="city_or_town"
                    value={formData.city_or_town}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-postal-code"
                    label="Postal Code"
                    name="postal_code"
                    value={formData.postal_code}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <FormControl fullWidth sx={fieldStyle} variant="outlined">
                    <InputLabel id="state-select-label">State</InputLabel>
                    <Select
                      labelId="state-select-label"
                      id="state-select"
                      name="state"
                      value={formData.state}
                      label="State"
                      onChange={(e) => setFormData(prev => ({ ...prev, state: e.target.value as string }))}
                    >
                      <MenuItem value="Maharashtra">Maharashtra</MenuItem>
                    </Select>
                  </FormControl>
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-society-website"
                    label="Society website"
                    name="society_website"
                    value={formData.society_website}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-association-name"
                    label="Association Name"
                    name="association_name"
                    value={formData.association_name}
                    onChange={handleInputChange}
                    sx={fieldStyle}
                    variant="outlined"
                  />
                </Grid2>

                <Grid2 size={{ xs: 12, md: 4 }}>
                  <TextField
                    fullWidth
                    id="outlined-signature"
                    label="Signature"
                    name="signature"
                    disabled
                    value=""
                    sx={fieldStyle}
                    variant="outlined"
                    InputProps={{
                      endAdornment: (
                        <Button
                          variant="outlined"
                          component="label"
                        >
                          Choose file
                          <input
                            type="file"
                            hidden
                          />
                        </Button>
                      ),
                    }}
                  />
                </Grid2>
              </Grid2>

              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSave}
                  size="small"
                  sx={{
                    borderRadius: '4px',
                    textTransform: 'none'
                  }}
                >
                  Save
                </Button>
              </Box>
            </Box>
          </TabPanel>


          {/* Home Tab */}
          <TabPanel value="1" sx={{ p: 3 }}>
            <Box sx={{
              p: 4,
              borderRadius: '12px',
              backgroundColor: 'white',
              border: '1px solid #e0e0e0',
              boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
            }}>
              <Typography
                variant="h6"
                sx={{
                  color: theme.palette.primary.main,
                  fontWeight: 600,
                  mb: 2
                }}
              >
                Welcome Message
              </Typography>
              <Typography variant="body2" sx={{ mb: 2, opacity: 0.8 }}>
                Here you can write Greeting Message to join your society.
              </Typography>

              {/* Rich Text Editor */}
              <RichTextEditor
                value={formData.welcome_message}
                onChange={(value) => setFormData({ ...formData, welcome_message: value })}
                rows={12}
                placeholder="Write your welcome message here..."
                onSave={() => console.log('Save clicked')}
              />

              {/* Banner Upload Section */}
              <Typography
                variant="h6"
                sx={{
                  color: theme.palette.primary.main,
                  fontWeight: 600,
                  mb: 3,
                  mt: 4
                }}
              >
                Add Home Page Banner
              </Typography>

              {/* Banner 1 */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
                <Typography sx={{ width: '100px' }}>Banner 1*</Typography>
                <TextField
                  fullWidth
                  disabled
                  value={banners.banner1 ? banners.banner1.name : ''}
                  placeholder="Choose file"
                  sx={fieldStyle}
                  InputProps={{
                    endAdornment: (
                      <Button
                        component="label"
                        sx={{
                          minWidth: 0,
                          bgcolor: theme.palette.primary.main,
                          color: 'white',
                          '&:hover': {
                            bgcolor: theme.palette.primary.dark,
                          }
                        }}
                      >
                        <PhotoCameraIcon />
                        <input
                          type="file"
                          hidden
                          accept="image/*"
                          onChange={handleFileUpload('banner1')}
                        />
                      </Button>
                    ),
                  }}
                />
              </Box>

              {/* Banner 2 */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
                <Typography sx={{ width: '100px' }}>Banner 2</Typography>
                <TextField
                  fullWidth
                  disabled
                  value={banners.banner2 ? banners.banner2.name : ''}
                  placeholder="Choose file"
                  sx={fieldStyle}
                  InputProps={{
                    endAdornment: (
                      <Button
                        component="label"
                        sx={{
                          minWidth: 0,
                          bgcolor: theme.palette.primary.main,
                          color: 'white',
                          '&:hover': {
                            bgcolor: theme.palette.primary.dark,
                          }
                        }}
                      >
                        <PhotoCameraIcon />
                        <input
                          type="file"
                          hidden
                          accept="image/*"
                          onChange={handleFileUpload('banner2')}
                        />
                      </Button>
                    ),
                  }}
                />
              </Box>

              {/* Banner 3 */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, gap: 2 }}>
                <Typography sx={{ width: '100px' }}>Banner 3</Typography>
                <TextField
                  fullWidth
                  disabled
                  value={banners.banner3 ? banners.banner3.name : ''}
                  placeholder="Choose file"
                  sx={fieldStyle}
                  InputProps={{
                    endAdornment: (
                      <Button
                        component="label"
                        sx={{
                          minWidth: 0,
                          bgcolor: theme.palette.primary.main,
                          color: 'white',
                          '&:hover': {
                            bgcolor: theme.palette.primary.dark,
                          }
                        }}
                      >
                        <PhotoCameraIcon />
                        <input
                          type="file"
                          hidden
                          accept="image/*"
                          onChange={handleFileUpload('banner3')}
                        />
                      </Button>
                    ),
                  }}
                />
              </Box>

              {/* File Upload Instructions */}
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', mb: 2 }}>
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  Recommended size: 1200x500px
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  Upload images with extensions .jpeg.jpg.png
                </Typography>
              </Box>

              {/* Save Button */}
              <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<SaveIcon />}
                  sx={{
                    borderRadius: '4px',
                    px: 3,
                    py: 1,
                    bgcolor: '#8bc34a',
                    '&:hover': {
                      bgcolor: '#7cb342',
                    }
                  }}
                >
                  Save
                </Button>
              </Box>
            </Box>
          </TabPanel>

          {/* Gallery Tab */}
          <TabPanel value="2" sx={{ p: 3 }}>
            <Box sx={{
              p: 4,
              borderRadius: '12px',
              backgroundColor: 'white',
              border: '1px solid #e0e0e0',
              boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
            }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: theme.spacing(3) }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                  Society Photo Gallery
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={handleAddGalleryImage}
                  sx={{
                    borderRadius: '30px',
                    px: theme.spacing(3),
                    py: theme.spacing(1.2),
                    background: 'linear-gradient(90deg, #2196F3 0%, #1976D2 100%)',
                    color: theme.palette.common.white,
                    fontWeight: 'bold',
                    textTransform: 'none',
                    boxShadow: '0 10px 20px rgba(33, 150, 243, 0.3)',
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: '-100%',
                      width: '100%',
                      height: '100%',
                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                      transition: 'all 0.6s ease',
                    },
                    '&:hover': {
                      boxShadow: '0 15px 25px rgba(33, 150, 243, 0.4)',
                      transform: 'translateY(-3px)',
                      '&::before': {
                        left: '100%',
                      }
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  Add New Photo
                </Button>
                <input
                  type="file"
                  hidden
                  ref={galleryFileInputRef}
                  accept="image/*"
                  multiple
                  onChange={handleGalleryUpload}
                />
              </Box>

              <Box sx={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
                gap: theme.spacing(3)
              }}>
                {galleryImages.map((image) => (
                  <Card
                    key={image.id}
                    sx={{
                      position: 'relative',
                      height: '300px',
                      borderRadius: '12px',
                      overflow: 'hidden',
                      transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255,255,255,0.18)',
                      boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        background: 'linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,0.8) 100%)',
                        opacity: 0,
                        transition: 'opacity 0.4s ease',
                        zIndex: 1
                      },
                      '&:hover': {
                        transform: 'translateY(-10px) scale(1.02)',
                        boxShadow: '0 15px 40px rgba(0,0,0,0.2)',
                        '&::before': {
                          opacity: 0.6
                        },
                        '& .image-overlay': {
                          opacity: 1
                        },
                        '& .card-content': {
                          transform: 'translateY(0)',
                          opacity: 1
                        },
                        '& .card-media': {
                          transform: 'scale(1.1)'
                        }
                      }
                    }}
                  >
                    <Box sx={{ position: 'relative', height: '100%', overflow: 'hidden' }}>
                      <CardMedia
                        component="img"
                        height="100%"
                        image={image.url}
                        alt="Gallery image"
                        className="card-media"
                        sx={{
                          objectFit: 'cover',
                          transition: 'transform 0.8s ease',
                          filter: 'brightness(0.95)'
                        }}
                      />

                      {/* Overlay with gradient */}
                      <Box
                        className="image-overlay"
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          background: 'linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 60%)',
                          zIndex: 1,
                          opacity: 0.7,
                          transition: 'opacity 0.4s ease'
                        }}
                      />

                      {/* Category Badge */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: theme.spacing(2),
                          left: theme.spacing(2),
                          backgroundColor: 'rgba(0, 0, 0, 0.5)',
                          backdropFilter: 'blur(4px)',
                          color: theme.palette.common.white,
                          padding: theme.spacing(0.5, 1.5),
                          borderRadius: '20px',
                          fontSize: '0.75rem',
                          fontWeight: 'bold',
                          zIndex: 2,
                          border: '1px solid rgba(255,255,255,0.2)',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                          letterSpacing: '0.5px',
                          textTransform: 'uppercase'
                        }}
                      >
                        {image.category}
                      </Box>

                      {/* Delete Button */}
                      <IconButton
                        aria-label="delete"
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: theme.spacing(2),
                          right: theme.spacing(2),
                          backgroundColor: 'rgba(255, 255, 255, 0.15)',
                          backdropFilter: 'blur(4px)',
                          color: theme.palette.common.white,
                          '&:hover': {
                            backgroundColor: theme.palette.error.main,
                            transform: 'rotate(90deg)',
                            transition: 'transform 0.3s ease'
                          },
                          width: 36,
                          height: 36,
                          zIndex: 2,
                          border: '1px solid rgba(255,255,255,0.2)',
                          transition: 'all 0.3s ease'
                        }}
                        onClick={() => handleDeleteGalleryImage(image.id)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>

                      {/* Content at bottom */}
                      <Box
                        className="card-content"
                        sx={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          width: '100%',
                          padding: theme.spacing(2),
                          zIndex: 2,
                          transform: 'translateY(10px)',
                          opacity: 0.9,
                          transition: 'all 0.4s ease'
                        }}
                      >
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 'bold',
                            mb: 0.5,
                            color: 'white',
                            textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                          }}
                        >
                          {image.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            color: 'rgba(255,255,255,0.8)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                            fontSize: '0.8rem'
                          }}
                        >
                          <Box
                            component="span"
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              backgroundColor: theme.palette.success.main,
                              display: 'inline-block'
                            }}
                          />
                          Added {new Date().toLocaleDateString()}
                        </Typography>
                      </Box>
                    </Box>
                  </Card>
                ))}
              </Box>

              <Box sx={{ mt: theme.spacing(4), display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<UploadIcon />}
                  sx={{
                    borderRadius: '30px',
                    px: theme.spacing(4),
                    py: theme.spacing(1.2),
                    background: 'linear-gradient(90deg, #4CAF50 0%, #388E3C 100%)',
                    color: theme.palette.common.white,
                    fontWeight: 'bold',
                    textTransform: 'none',
                    fontSize: '1rem',
                    boxShadow: '0 10px 20px rgba(76, 175, 80, 0.3)',
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: '-100%',
                      width: '100%',
                      height: '100%',
                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                      transition: 'all 0.6s ease',
                    },
                    '&:hover': {
                      boxShadow: '0 15px 25px rgba(76, 175, 80, 0.4)',
                      transform: 'translateY(-3px)',
                      '&::before': {
                        left: '100%',
                      }
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  Upload Gallery
                </Button>
              </Box>
            </Box>
          </TabPanel>

          {/* Developer Tab */}
          <TabPanel value="3" sx={{ p: 3 }}>
            <DeveloperScreen />
          </TabPanel>

          {/* Contributors Tab */}
          <TabPanel value="4" sx={{ p: 3 }}>
            <ContributorsScreen />
          </TabPanel>

          {/* Committee Tab */}
          <TabPanel value="5" sx={{ p: 3 }}>
            <Box sx={{
              p: 4,
              borderRadius: '12px',
              backgroundColor: 'white',
              border: '1px solid #e0e0e0',
              boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
            }}>
              <CommitteeScreen />
            </Box>
          </TabPanel>

          {/* Documents Tab */}
          <TabPanel value="6" sx={{ p: 3 }}>
            <Box sx={{
              p: 4,
              borderRadius: '12px',
              backgroundColor: 'white',
              border: '1px solid #e0e0e0',
              boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
            }}>
              <DocumentsScreen />
            </Box>
          </TabPanel>

          {/* Amenities Tab */}
          <TabPanel value="7" sx={{ p: 3 }}>
            <Box sx={{
              p: 4,
              borderRadius: '12px',
              backgroundColor: 'white',
              border: '1px solid #e0e0e0',
              boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
            }}>
              <AmenitiesScreen />
            </Box>
          </TabPanel>

          {/* Website Layout Tab */}
          <TabPanel value="8" sx={{ p: 3 }}>
            <Box sx={{
              p: 4,
              borderRadius: '12px',
              backgroundColor: 'white',
              border: '1px solid #e0e0e0',
              boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
            }}>
              <WebsiteLayoutScreen />
            </Box>
          </TabPanel>

          {/* Location Map Tab */}
          <TabPanel value="9" sx={{ p: 3 }}>
            <Box sx={{
              p: 4,
              borderRadius: '12px',
              backgroundColor: 'white',
              border: '1px solid #e0e0e0',
              boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
            }}>
              <LocationMapScreen />
            </Box>
          </TabPanel>

          {/* Other tabs */}
          {tabs.slice(10).map((tab, index) => (
            <TabPanel key={index + 10} value={String(index + 10)} sx={{ p: 3 }}>
              <Box sx={{
                p: 4,
                borderRadius: '12px',
                backgroundColor: 'white',
                border: '1px solid #e0e0e0',
                boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
              }}>
                <Typography
                  variant="h6"
                  sx={{
                    color: theme.palette.primary.main,
                    fontWeight: 600,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  {tab} Content
                </Typography>
                <Typography variant="body1" sx={{ mt: 2, opacity: 0.8 }}>
                  This tab is under construction. Content for {tab} will be available soon.
                </Typography>
              </Box>
            </TabPanel>
          ))}
        </TabContext>
      </Paper>
    </Box>
  );
};

export default SocProfileScreen;
