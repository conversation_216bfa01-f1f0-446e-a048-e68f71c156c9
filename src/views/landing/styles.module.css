.heroSectionBg {
  background-repeat: no-repeat;
  background-size: cover;
  position: absolute;
  inline-size: 100%;
  block-size: 100%;
  inset-block-start: -12%;
  pointer-events: none;
  z-index: -2;
}

.heroSecDashboard {
  inline-size: 85%;
  z-index: -1;
}

.heroSectionElements {
  inset-block-start: 18%;
  inset-inline-end: 3%;

  img {
    inline-size: 100%;
  }
}

.featureIcon {
  cursor: pointer;
  color: var(--mui-palette-primary-main);
  font-size: xx-large;

  &>div:first-child {
    border-color: var(--mui-palette-primary-darkOpacity);
  }

  &>div:hover {
    background-color: var(--mui-palette-primary-mainOpacity);

    i {
      pointer-events: none;
      transform: scale(1.1);
    }
  }
}

.layoutSpacing {
  margin-inline: auto;
  padding-inline: 1.5rem;

  @media (min-width: 900px) {
    max-inline-size: 900px;
  }

  @media (min-width: 1200px) {
    max-inline-size: 1200px;
  }

  @media (min-width: 1920px) {
    max-inline-size: 1440px;
  }
}
