// React Imports
import { useEffect, useRef } from 'react'

// MUI Imports
import Typography from '@mui/material/Typography'
import Grid from '@mui/material/Grid2'

// Third-party Imports
import classnames from 'classnames'

// Hook Imports
import { useIntersection } from '@/hooks/useIntersection'

// SVG Imports
import Lines from '@assets/svg/front-pages/landing-page/Lines'
import LaptopCharging from '@assets/svg/front-pages/landing-page/LaptopCharging'
import Edit from '@assets/svg/front-pages/landing-page/Edit'
import Cube from '@assets/svg/front-pages/landing-page/Cube'
import LifeBuoy from '@assets/svg/front-pages/landing-page/Lifebuoy'
import Document from '@assets/svg/front-pages/landing-page/Document'

// Styles Imports
import styles from './styles.module.css'

// Data
const feature = [
  {
    icon: <LaptopCharging />,
    title: 'Smart Admin Control',
    description: 'Robust admin module for billing, collections, complaints, vendor, and facility bookings.'
  },
  {
    icon: <i className='ri-secure-payment-line' />,
    title: 'Member’s Hub',
    description: 'User-friendly app for complaints, bill payments, visitor tracking, and managing multiple properties.'
  },
  {
    icon: <Edit />,
    title: 'Precision Accounting',
    description: 'Society accounting module for ledger, expenses, balance sheets, tax details, and aided audits.'
  },
  {
    icon: <Cube />,
    title: 'OneApp: Life Simplified',
    description: 'All-in-one app for society management, bills, visitor alerts, and neighborhood services.'
  },
  {
    icon: <LifeBuoy />,
    title: 'Triple-Layer Security',
    description: '3-layer security with dedicated cloud databases ensures privacy at both admin and member levels.'
  },
  {
    icon: <Document />,
    title: 'Seamless Billing',
    description: 'Get society bills online and pay securely through integrated payment gateway and e-collect facility.'
  }
]

const UsefulFeature = () => {
  // Refs
  const skipIntersection = useRef(true)
  const ref = useRef<null | HTMLDivElement>(null)

  // Hooks
  const { updateIntersections } = useIntersection()

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (skipIntersection.current) {
          skipIntersection.current = false

          return
        }

        updateIntersections({ [entry.target.id]: entry.isIntersecting })
      },
      { threshold: 0.35 }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <section id='features' ref={ref} className='bg-backgroundPaper'>
      <div className={classnames('flex flex-col gap-12 plb-[100px]', styles.layoutSpacing)}>
        <div className={classnames('flex flex-col items-center justify-center')}>
          <div className='flex items-center justify-center mbe-6 gap-3'>
            <Lines />
            <Typography color='text.primary' className='font-medium uppercase'>
              Useful Feature
            </Typography>
          </div>
          <div className='flex items-baseline max-sm:flex-col gap-x-2 mbe-3 sm:mbe-2'>
            <Typography variant='h4' className='font-bold'>
              Everything you need
            </Typography>
            <Typography variant='h5'>for Seamless Society Management</Typography>
          </div>
          <Typography className='font-medium text-center'>
            Discover OneSociety’s powerful features: billing, complaints, visitor tracking, secure payments, and more.
          </Typography>
        </div>
        <div>
          <Grid container rowSpacing={12} columnSpacing={6}>
            {feature.map((item, index) => (
              <Grid size={{ xs: 12, sm: 6, lg: 4 }} key={index}>
                <div className='flex flex-col gap-2 justify-center items-center'>
                  <div className={classnames('mbe-2', styles.featureIcon)}>
                    <div className='flex items-center border-2 rounded-full p-5 is-[82px] bs-[82px]'>{item.icon}</div>
                  </div>
                  <Typography variant='h5'>{item.title}</Typography>
                  <Typography className='max-is-[364px] text-center'>{item.description}</Typography>
                </div>
              </Grid>
            ))}
          </Grid>
        </div>
      </div>
    </section>
  )
}

export default UsefulFeature
