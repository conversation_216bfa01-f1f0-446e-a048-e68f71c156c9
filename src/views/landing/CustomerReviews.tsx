// React Imports
import { useState } from 'react'

// MUI Imports
import Image from 'next/image'

import Typography from '@mui/material/Typography'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Badge from '@mui/material/Badge'
import Rating from '@mui/material/Rating'

// Third-party Imports
import type { TrackDetails } from 'keen-slider/react'
import { useKeenSlider } from 'keen-slider/react'
import classnames from 'classnames'

// Styled Component Imports
import AppKeenSlider from '@/libs/styles/AppKeenSlider'

// SVG Imports
import Lines from '@assets/svg/front-pages/landing-page/Lines'
import Levis from '@assets/svg/front-pages/landing-page/Levis'
import Continental from '@assets/svg/front-pages/landing-page/Continental'
import Eckerd from '@assets/svg/front-pages/landing-page/Eckerd'
import Dribbble from '@assets/svg/front-pages/landing-page/Dribbble'
import Airbnb from '@assets/svg/front-pages/landing-page/Airbnb'

// Styles Imports
import frontCommonStyles from '@views/landing/styles.module.css'

// Data
const data = [
  {
    desc: "We have been using onegate as our gate and visitor management system for quite some time now. And it helped in reducing the gate rush, allocating parking for vehicles and record-keeping visitors very effectively. Managing gates in commercial complex is really not an easy task. It also includes security aspects. But onegate made everything a lot easier.",
    image: 'https://cubeonebiz.com/promotions/onesociety/img/abhijeet.png',
    rating: 5,
    name: 'Abhijeet Wagh',
    position: 'Property Manager, Cyberone'
  },
  {
    desc: "As a client of onesociety, oneapp's housing society management solution, I can say that this app kept us sailing through this tough time.Connecting with members, keeping tab of society's adherence to the govt. norms in this Covid 19 situation, and all while quarantining at home would not have been possible otherwise.",
    image: "https://cubeonebiz.com/promotions/onesociety/img/shantaram.png",
    rating: 5,
    name: 'Shantaram',
    position: 'Treasury, Maruti Towers'
  },
  {
    desc: "It's easy finding my local shops and connecting with them. oneapp's list is pretty elaborate and ordering is easy too.",
    image: "https://cubeonebiz.com/promotions/onesociety/img/gurjant.png",
    rating: 5,
    name: 'Gurjant Singh',
    position: 'oneapp User'
  },
  {
    desc: "oneapp is the game-changer in the way life, specially at the present situation. The app's listings helped a lot in finding and connecting with our local shops and stores and get things delivered at home since venturing out in this lockdown situation isn't so easy.",
    image: "https://cubeonebiz.com/promotions/onesociety/img/pratap.png",
    rating: 5,
    name: 'Pratap',
    position: 'oneapp User'
  },
  {
    desc: "Handling work, home and the difficult tasks of housing society every month can be very challenging. Installing onesociety in our complex was one of the best decisions made by our committee as it has helped not only the management committee but also the members of our complex.",
    image: "https://cubeonebiz.com/promotions/onesociety/img/anurag.png",
    rating: 5,
    name: 'Anurag Srivastav',
    position: 'Secretary, Navrang'
  },
  {
    desc: "We have been using onesociety as our housing society management software since a long period of time, it has been extremely helpful to us for managing and taking care of our accounting, billing, and lets us connect with the outside world only being at the comfort of our home",
    image: "https://cubeonebiz.com/promotions/onesociety/img/gopal.png",
    rating: 5,
    name: "Gopal Sridhar",
    position: "Committee Member, Seabreeze"
  }
]

const CustomerReviews = () => {
  // States
  const [loaded, setLoaded] = useState<boolean>(false)
  const [currentSlide, setCurrentSlide] = useState<number>(0)
  const [details, setDetails] = useState<TrackDetails>()

  // Hooks
  const [sliderRef, instanceRef] = useKeenSlider<HTMLDivElement>(
    {
      loop: true,
      slideChanged: slider => setCurrentSlide(slider.track.details.rel),
      created: () => setLoaded(true),
      detailsChanged: s => setDetails(s.track.details),
      slides: {
        perView: 3,
        origin: 'center'
      },
      breakpoints: {
        '(max-width: 1200px)': {
          slides: {
            perView: 3,
            spacing: 26,
            origin: 'center'
          }
        },
        '(max-width: 900px)': {
          slides: {
            perView: 2,
            spacing: 26,
            origin: 'center'
          }
        },
        '(max-width: 600px)': {
          slides: {
            perView: 1,
            spacing: 26,
            origin: 'center'
          }
        }
      }
    },
    [
      slider => {
        let timeout: ReturnType<typeof setTimeout>
        const mouseOver = false

        function clearNextTimeout() {
          clearTimeout(timeout)
        }

        function nextTimeout() {
          clearTimeout(timeout)
          if (mouseOver) return
          timeout = setTimeout(() => {
            slider.next()
          }, 2000)
        }

        slider.on('created', nextTimeout)
        slider.on('dragStarted', clearNextTimeout)
        slider.on('animationEnded', nextTimeout)
        slider.on('updated', nextTimeout)
      }
    ]
  )

  const scaleStyle = (idx: number) => {
    if (!details) return {}
    const activeSlideIndex = details.rel

    return {
      transition: 'transform 0.2s ease-in-out, opacity 0.2s ease-in-out',
      ...(activeSlideIndex === idx ? { transform: 'scale(1)', opacity: 1 } : { transform: 'scale(0.9)', opacity: 0.5 })
    }
  }

  return (
    <section className='flex flex-col gap-16 plb-[100px]'>
      <div className={classnames('flex flex-col items-center justify-center', frontCommonStyles.layoutSpacing)}>
        <div className='flex items-center justify-center mbe-6 gap-3'>
          <Lines />
          <Typography color='text.primary' className='font-medium uppercase'>
            Real Customers Reviews
          </Typography>
        </div>
        <div className='flex items-baseline flex-wrap gap-2 mbe-3 sm:mbe-2'>
          <Typography variant='h4' className='font-bold'>
            Success stories
          </Typography>
          <Typography variant='h5'>from clients</Typography>
        </div>
        <Typography className='font-medium text-center'>
          See what our customers have to say about their experience.
        </Typography>
      </div>
      <AppKeenSlider>
        <>
          <div ref={sliderRef} className='keen-slider mbe-6'>
            {data.map((item, index) => (
              <div key={index} className='keen-slider__slide flex p-6 sm:p-4'>
                <Card elevation={8} className='flex items-center' style={scaleStyle(index)}>
                  <CardContent className='p-8 items-center mlb-auto'>
                    <div className='flex flex-col gap-4 items-center justify-center text-center'>
                      {/* {item.svg} */}
                      <Image src={item.image} width={100} height={100} alt='Customer' style={{ borderRadius: '50%' }} />
                      <Typography color='text.primary'>{item.desc}</Typography>
                      <Rating value={item.rating} readOnly />
                      <div>
                        <Typography color='text.primary' className='font-medium'>
                          {item.name}
                        </Typography>
                        <Typography variant='body2'>{item.position}</Typography>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
          {loaded && instanceRef.current && (
            <div className='swiper-dots'>
              {[...Array(instanceRef.current.track.details.slides.length).keys()].map(idx => {
                return (
                  <Badge
                    key={idx}
                    variant='dot'
                    component='div'
                    className={classnames({ active: currentSlide === idx })}
                    onClick={() => instanceRef.current?.moveToIdx(idx)}
                  />
                )
              })}
            </div>
          )}
        </>
      </AppKeenSlider>
      <div className='flex flex-wrap items-center justify-center gap-x-16 gap-y-6 mli-3'>
        <Levis color='var(--mui-palette-text-secondary)' />
        <Continental color='var(--mui-palette-text-secondary)' />
        <Airbnb color='var(--mui-palette-text-secondary)' />
        <Eckerd color='var(--mui-palette-text-secondary)' />
        <Dribbble color='var(--mui-palette-text-secondary)' />
      </div>
    </section>
  )
}

export default CustomerReviews
