// Next Imports
import Link from 'next/link'

// MUI Imports
import Typography from '@mui/material/Typography'
import Grid from '@mui/material/Grid'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Chip from '@mui/material/Chip'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'

// Third-party Imports
import classnames from 'classnames'

// Styles Imports
import frontCommonStyles from '@views/landing/styles.module.css'

// SVG Imports
import Lines from '@assets/svg/front-pages/landing-page/Lines'
import Curve from '@assets/svg/front-pages/landing-page/Curve'
import Arrow from '@assets/svg/front-pages/landing-page/Arrow'
import ElementTwo from '@/assets/svg/front-pages/landing-page/ElementTwo'

const pricingPlans = [
  {
    title: 'For Society Members',
    caption: 'Everything You Need as a Society Member',
    description: 'Experience a smarter way to live; manage your society life with just a few taps on your app.',
    features: [
      "Online Bill Payment",
      "Download Receipts",
      "Complaint Tracking",
      "Digital Notice Board",
      "Facility Booking",
      "Document Storage",
      "Visitor Records",
      "Consolidated Phonebook",
      "Event Discussions"
    ],
    supportType: 'Basic',
    supportMedium: 'Only Email',
    respondTime: 'AVG. Time: 24h',
    current: false
  },
  {
    title: 'Powerful Features for All Users',
    caption: 'Innovative Tools for a Connected Community',
    description: 'Discover essential tools that enhance convenience, security, and efficiency for all society users.',
    features: [
      "Secure Cloud Storage",
      "Multi-Property Management",
      "High-Quality Video Meetings",
      "Mobile App Access",
      "Real-Time Notifications",
      "Data Privacy Controls",
      "Seamless User Interface",
      "Integrated Payment Gateway",
      "Contactless Communication"
    ],
    supportType: 'Standard',
    supportMedium: 'Email & Chat',
    respondTime: 'AVG. Time: 6h',
    current: true
  },
  {
    title: 'For Society Offices',
    caption: 'Complete Tools for Society Management',
    description: 'Streamline society operations; control, manage, and communicate effortlessly with powerful admin tools.',
    features: [
      "Automated Invoicing",
      "E-Collection Facility",
      "Complete Accounting",
      "Income-Expense Tracking",
      "Complaint Management",
      "Tenant Management",
      "Digital Notice Board",
      "Parking & Vehicle Management",
      "Digital Society Meetings"
    ],
    supportType: 'Exclusive',
    supportMedium: 'Email, Chat & Google Meet',
    respondTime: 'Live Support',
    current: false
  }
]

const PricingPlan = () => {

  return (
    <section
      className={classnames('flex flex-col gap-8 lg:gap-12 plb-[100px]', frontCommonStyles.layoutSpacing)}
    >
      <div className='flex flex-col items-center justify-center'>
        <div className='flex is-full justify-center relative'>
          <ElementTwo className='absolute inline-start-0' />
          <div className='flex items-center justify-center mbe-6 gap-3 text-center'>
            <Lines />
            <Typography color='text.primary' className='font-medium uppercase'>
              App Wise Features
            </Typography>
          </div>
        </div>
        <div className='flex sm:items-baseline max-sm:items-center max-sm:flex-col gap-x-2 mbe-3 sm:mbe-2'>
          <Typography variant='h4' className='font-bold'>
            Tailored pricing plans
          </Typography>
          <Typography variant='h5'>designed for you</Typography>
        </div>
        <Typography className='font-medium text-center'>
          All plans include 40+ advanced tools and features to boost your product. Choose the best plan to fit your
          needs.
        </Typography>
      </div>

      <Grid container spacing={6}>
        {pricingPlans.map((plan, index) => (
          <Grid item key={index} xs={12} lg={4}>
            <Card
              variant='outlined'
              {...(plan.current && { className: 'border-2 border-[var(--mui-palette-primary-main)]' })}
            >
              <CardContent className='flex flex-col gap-8 p-8'>
                <div className='is-full flex flex-col gap-3'>
                  <Typography className='text-center' variant='h4'>
                    {plan.title}
                  </Typography>
                  <div className='flex items-center gap-3'>
                    <div className='flex flex-col gap-0.5'>
                      <Typography color='text.primary' className='font-medium'>
                        {plan.caption}
                      </Typography>
                      <Typography variant='body2'>{plan.description}</Typography>
                    </div>
                  </div>
                  <Curve />
                </div>
                <div>
                  <div className='flex flex-col gap-3'>
                    {plan.features.map((feature, index) => (
                      <div key={index} className='flex items-center gap-3'>
                        <Arrow />
                        <Typography variant='h5'>{feature}</Typography>
                      </div>
                    ))}
                  </div>
                  <Divider className='border mlb-4' />
                  <div className='flex gap-1 items-center justify-between'>
                    <div className='flex flex-col gap-0.5'>
                      <Typography color='text.primary' className='font-medium'>
                        {plan.supportType} Support
                      </Typography>
                      <Typography variant='body2'>{plan.supportMedium}</Typography>
                    </div>
                    <Chip variant='tonal' size='small' color='primary' label={plan.respondTime} />
                  </div>
                </div>
                <Button component={Link} href='/front-pages/payment' variant={plan.current ? 'contained' : 'outlined'}>
                  Get Started
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </section>
  )
}

export default PricingPlan
