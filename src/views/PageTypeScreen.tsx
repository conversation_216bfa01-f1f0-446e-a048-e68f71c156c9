import dynamic from 'next/dynamic';

import type { MenuItemData } from '@/types/menuTypes';
import { extractParamsFromUrl, stripAdminScopedPath } from '@/utils/string';
import { replacePlaceholders } from '@/utils/replacePlaceholders';

// Lazy-load components to optimize bundle size
const TableScreen = dynamic(() => import('./screens/TableScreen'));
const DashboardScreen = dynamic(() => import('./screens/DashboardScreen'));
const MixScreen = dynamic(() => import('./screens/MixScreen'));
const SocProfileScreen = dynamic(() => import('./screens/SocProfileScreen'));
const FormBuilder = dynamic(() => import('@/views/form/builder/server'));
const HtmlRenderer = dynamic(() => import('@/components/HtmlParse'));
const BulkUnitScreen = dynamic(() => import('./screens/BulkUnitScreen'));
const PreviewRuleCard = dynamic(() => import('@/components/card/PreviewRuleCard'));
const MyForm = dynamic(() => import('@/components/customForm/DemoForm'));
const EditNewRule = dynamic(() => import('@/components/customForm/EditNewRule'));
const PDFViewer = dynamic(() => import('./screens/PDFScreen'));
const Accordion = dynamic(() => import('@/components/accordion'));
const NocFormsPage = dynamic(() => import('@/components/nocForms/NocFormsPage'));
const ComplaintDetailsPage = dynamic(() => import('@/components/helpdesk/ComplaintDetailsPage'));

// Map pageType to component with a unified "any" signature
const componentMap: Record<string, React.ComponentType<any>> = {
  htmlContent: HtmlRenderer,
  table: TableScreen,
  dashboard: DashboardScreen,
  mix: MixScreen,
  PDF: PDFViewer,
  bulk: BulkUnitScreen,
  previewRule: PreviewRuleCard,
  demo: MyForm,
  editNewRule: EditNewRule,
  accordion: Accordion,
  nocForm: NocFormsPage,
  helpdesk: ComplaintDetailsPage,
  socprofile: SocProfileScreen,
  form: FormBuilder,
};

type PageTypeScreenProps = {
  pageData: MenuItemData;
  pathname: string;
};

const PageTypeScreen = async ({ pageData, pathname }: PageTypeScreenProps) => {
  const {
    pageType = 'form',
    href,
    order,
    commonPageConfig,
    apiURL,
  } = pageData;

  const params = extractParamsFromUrl(pathname, href);

  const customUrl = stripAdminScopedPath(
    replacePlaceholders(apiURL || href, params) || pathname
  );

  // Force component to any for flexible props
  const Component = (componentMap[pageType] || FormBuilder) as React.ComponentType<any>;

  let props: Record<string, any>;

  switch (pageType) {
    case 'htmlContent':
      props = { apiURL: customUrl };
      break;
    case 'mix':
      props = {
        params,
        pathname: customUrl,
        order: order || [],
        commonPageConfig,
        href,
      };
      break;
    case 'PDF':
      props = { customUrl };
      break;
    case 'socprofile':
      props = { customUrl };
      break;
    case 'form':
      props = { href, isFormScreen: true, params };
      break;
    default:
      props = { href, isFormScreen: false, params };
  }

  return <Component {...props} />;
};

export default PageTypeScreen;
