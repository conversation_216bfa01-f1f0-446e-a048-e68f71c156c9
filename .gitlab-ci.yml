variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""

stages:
  - staging
  - production

staging:
  stage: staging
  image: alpine:latest
  before_script:
    - apk update
    - apk add --no-cache openssh-client
    - chmod 0400 $AWS_PEM_FILE
  script:
    - ssh -oStrictHostKeyChecking=no -o "IdentitiesOnly yes" -T -i $AWS_PEM_FILE ubuntu@************** "cd ${STAGE_DIR}/${APP_DIR} && git pull ${CI_REPOSITORY_URL} live && yarn build"
  only:
    - live

production:
  stage: production
  image: alpine:latest
  before_script:
    - apk update
    - apk add --no-cache openssh-client
    - chmod 0400 $AWS_PEM_FILE
  script:
    - ssh -oStrictHostKeyChecking=no -o "IdentitiesOnly yes" -T -i $AWS_PEM_FILE ubuntu@************** "cd ${PROD_DIR}/${APP_DIR} && git pull ${CI_REPOSITORY_URL} live && yarn build && pm2 startOrGracefulReload ecosystem.config.json && pm2 status"
  only:
    - live
  dependencies:
    - staging