// eslint.config.mjs
import { defineConfig } from 'eslint/config';
import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

import tsParser from '@typescript-eslint/parser';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import importPlugin from 'eslint-plugin-import';
import nextPlugin from '@next/eslint-plugin-next';
import reactPlugin from 'eslint-plugin-react';
import hooksPlugin from 'eslint-plugin-react-hooks';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default defineConfig([
  // Ignore these files from type-aware linting
  {
    ignores: [
      'eslint.config.mjs',
      'src/utils/convertJSONCtoJSON.mjs',
    ],
  },

  // Legacy shareable configs converted via FlatCompat
  ...compat.extends(
    'next/core-web-vitals',
    'plugin:@typescript-eslint/recommended',
    'plugin:import/recommended',
    'prettier'
  ),

  // Base configuration
  {
    plugins: {
      '@typescript-eslint': tsPlugin,
      import: importPlugin,
      next: nextPlugin,
      react: reactPlugin,
      'react-hooks': hooksPlugin,
    },
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        project: './tsconfig.json',
        tsconfigRootDir: __dirname,
        sourceType: 'module',
        ecmaVersion: 'latest',
        ecmaFeatures: { jsx: true },
      },
    },
    settings: {
      react: { version: 'detect' },
      'import/parsers': { '@typescript-eslint/parser': ['.ts', '.tsx'] },
      'import/resolver': {
        node: {},
        typescript: { project: './tsconfig.json' },
      },
    },
    rules: {
      // React / Next overrides
      'jsx-a11y/alt-text': 'off',
      'react/display-name': 'off',
      'react/no-children-prop': 'off',
      '@next/next/no-img-element': 'off',
      '@next/next/no-page-custom-font': 'off',

      // TypeScript rules
      '@typescript-eslint/consistent-type-imports': 'error',
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': 'error',
      '@typescript-eslint/no-non-null-assertion': 'off',

      // ban-types replacement
      '@typescript-eslint/no-restricted-types': [
        'error',
        {
          types: {
            Function: { message: 'Use a specific function type instead' },
            Object:   { message: 'Use object instead'           },
            Boolean:  { message: 'Use boolean instead'          },
            Number:   { message: 'Use number instead'           },
            String:   { message: 'Use string instead'           },
            Symbol:   { message: 'Use symbol instead'           },
          },
        },
      ],
      '@typescript-eslint/no-empty-object-type': 'error',
      '@typescript-eslint/no-unsafe-function-type': 'error',
      '@typescript-eslint/no-wrapper-object-types': 'error',

      // Formatting & padding
      'lines-around-comment': [
        'error',
        {
          beforeBlockComment: true,
          beforeLineComment: true,
          allowBlockStart: true,
          allowObjectStart: true,
          allowArrayStart: true,
        },
      ],
      'padding-line-between-statements': [
        'error',
        { blankLine: 'any',    prev: 'export', next: 'export' },
        { blankLine: 'always', prev: ['const','let','var'], next: '*' },
        { blankLine: 'any',    prev: ['const','let','var'], next: ['const','let','var'] },
        { blankLine: 'always', prev: '*', next: ['function','multiline-const','multiline-block-like'] },
        { blankLine: 'always', prev: ['function','multiline-const','multiline-block-like'], next: '*' },
      ],
      'newline-before-return': 'error',

      // Import order & spacing
      'import/newline-after-import': ['error', { count: 1 }],
      'import/order': [
        'error',
        {
          groups: [
            'builtin',
            'external',
            ['internal','parent','sibling','index'],
            ['object','unknown'],
          ],
          pathGroups: [
            { pattern: 'react',   group: 'external', position: 'before' },
            { pattern: 'next/**', group: 'external', position: 'before' },
            { pattern: '~/**',    group: 'external', position: 'before' },
            { pattern: '@/**',    group: 'internal' },
          ],
          pathGroupsExcludedImportTypes: ['react','type'],
          'newlines-between': 'always-and-inside-groups',
        },
      ],
    },
  },

  // TS / TSX specific overrides
  {
    files: ['**/*.ts', '**/*.tsx', 'src/iconify-bundle/**/*'],
    rules: {
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-var-requires': 'off',
    },
  },
]);