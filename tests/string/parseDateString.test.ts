/**
 * @file parseDateString.test.ts
 *
 * Jest test suite for the robust parseDateString utility that handles:
 * - Base date/time
 * - Special tokens (lastQuarter, startOf('quarter'), endOf('month'), etc.)
 * - Offsets like "+ 2 days" or "-1 quarter"
 * - Optional startOfDay logic
 */

import dayjs from "dayjs";

import {
  parseDateString,
} from "../../src/utils/date";

describe("parseDateString Utility", () => {
  it("should return today's date if input is null or empty", () => {
    const resultNull = parseDateString(null);
    const resultEmpty = parseDateString("");

    expect(dayjs.isDayjs(resultNull)).toBe(true);
    expect(dayjs.isDayjs(resultEmpty)).toBe(true);

    // Roughly check that it's "now" (allowing for test runtime differences)
    expect(resultNull.diff(dayjs(), "second")).toBeLessThan(2);
    expect(resultEmpty.diff(dayjs(), "second")).toBeLessThan(2);
  });

  it("should set startOf('day') if second param is true", () => {
    const nowMidnight = parseDateString(null, true);

    expect(nowMidnight.hour()).toBe(0);
    expect(nowMidnight.minute()).toBe(0);
    expect(nowMidnight.second()).toBe(0);

    const withBaseDate = parseDateString("2025-03-15", true);

    expect(withBaseDate.format("YYYY-MM-DD HH:mm:ss")).toBe("2025-03-15 00:00:00");
  });

  it("should parse a simple ISO date string with no offsets or tokens", () => {
    const input = "2025-03-15";
    const result = parseDateString(input);

    expect(result.format("YYYY-MM-DD")).toBe("2025-03-15");
  });

  it("should parse an ISO date-time string (with time)", () => {
    const input = "2026-01-01T10:30:45";
    const result = parseDateString(input);

    expect(result.format("YYYY-MM-DDTHH:mm:ss")).toBe("2026-01-01T10:30:45");
  });

  it("should apply offsets like '+ 2 weeks - 1 quarter' to a base date", () => {
    const input = "2025-03-15 + 2 weeks - 1 quarter";
    const result = parseDateString(input, false);

    expect(dayjs.isDayjs(result)).toBe(true);

    // Let's check the intermediate steps in a more approximate way:
    //  2025-03-15 => +2 weeks => 2025-03-29 => -1 quarter => 1 quarter = 3 months
    //  So that goes back to about 2024-12-29 (depending on day counts).
    // We'll do an approximate test rather than an exact string match:
    const diffMonths = result.diff(dayjs("2025-03-15"), "month", true);


    // After +2 weeks, it's still March.
    // Then minus 3 months puts us around late December 2024. That's ~ -2.5 months from 2025-03-15 if you consider partial months.
    // So let's check if it's around -2.5 to -3.5
    expect(diffMonths).toBeLessThan(-2);
    expect(diffMonths).toBeGreaterThan(-4);
  });

  it("should handle partial date-time like '2026-01-01T10:00:00 lastQuarter + 3 days'", () => {
    // Steps:
    //  1) Base = 2026-01-01T10:00:00
    //  2) lastQuarter => subtract(1, "quarter") from that date
    //  3) +3 days
    const input = "2026-01-01T10:00:00 lastQuarter + 3 days";
    const result = parseDateString(input);

    expect(result.format("YYYY-MM-DDTHH:mm:ss")).toMatch(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);

    // Validate final result is ~3 days after 1 quarter subtracted from 2026-01-01
    const minusQuarter = dayjs("2026-01-01T10:00:00").subtract(1, "quarter");
    const plusThree = minusQuarter.add(3, "day");

    expect(result.diff(plusThree, "second")).toBe(0);
  });

  it("should handle the token: lastQuarter endOf('quarter') + 1 month - 1 day", () => {
    // Steps if "now" is 2025-06-10 (example), lastQuarter => ~2025-03-10
    // endOf('quarter') => end of Q1 => 2025-03-31 23:59:59
    // +1 month => ~2025-04-30 23:59:59
    // -1 day => ~2025-04-29 23:59:59
    // We'll do a relative check. We won't rely on a specific "today" because test might run any time.
    // We'll just confirm the final result is the final day of Q1 of the last quarter from now,
    // plus a month minus a day.
    const input = "lastQuarter endOf('quarter') + 1 month - 1 day";
    const result = parseDateString(input);

    expect(dayjs.isDayjs(result)).toBe(true);

    // Hard to do an absolute check since "now" changes, but we can at least verify it's valid and we get a date in the correct range
    expect(result.isValid()).toBe(true);
  });

  it("should handle startOf('month') or endOf('year') tokens", () => {
    const input1 = "2025-06-10 startOf('month')";
    const res1 = parseDateString(input1);

    expect(res1.format("YYYY-MM-DD")).toBe("2025-06-01");

    const input2 = "2023-02-15 endOf('year')";
    const res2 = parseDateString(input2);

    expect(res2.format("YYYY-MM-DD")).toBe("2023-12-31");
  });

  it("should keep or discard time if startOfDay is false or true", () => {
    const input = "2025-03-15T13:45:30 + 2 hours";

    // If startOfDay = false, we keep the hour
    const resFalse = parseDateString(input, false);

    expect(resFalse.format("YYYY-MM-DDTHH:mm")).toBe("2025-03-15T15:45");

    // If startOfDay = true, final result should be 00:00:00 of that day
    const resTrue = parseDateString(input, true);

    expect(resTrue.hour()).toBe(0);
    expect(resTrue.minute()).toBe(0);
  });

  it("should ignore unrecognized tokens gracefully", () => {
    // "bogusToken" will be stripped? or left in leftover?
    // We expect parseDateString to still parse the date & offsets ignoring the bogus
    const input = "2025-03-15 bogusToken + 3 days";
    const result = parseDateString(input);

    expect(result.isValid()).toBe(true);

    // 2025-03-15 + 3 days => 2025-03-18 (approx check)
    expect(result.format("YYYY-MM-DD")).toBe("2025-03-18");
  });

  it("should handle offset patterns with random spacing", () => {
    // e.g. "+    2    weeks" or "-    1   quarter"
    const input = "2025-03-15 +    2    weeks -   1   quarter";
    const result = parseDateString(input);

    expect(result.isValid()).toBe(true);

    // We'll do an approximate months check again, as above
    const diffMonths = result.diff(dayjs("2025-03-15"), "month", true);

    expect(diffMonths).toBeLessThan(-2);
    expect(diffMonths).toBeGreaterThan(-4);
  });

  it("should parse date without time if only partial match is found", () => {
    // e.g. user typed "2027-01-01T" but no hours, minutes
    const input = "2027-01-01T + 1 day";

    // The partial "2027-01-01T" might or might not parse as "2027-01-01" depending on dayjs
    const result = parseDateString(input);

    expect(result.isValid()).toBe(true);

    // We'll see if it at least recognized the base date as 2027-01-01
    // Then +1 day => 2027-01-02
    expect(result.format("YYYY-MM-DD")).toBe("2027-01-02");
  });
});
