import { replacePlaceholders } from "../../src/utils/replacePlaceholders" 

describe('replacePlaceholders', () => {
  test('should replace colon placeholder in path', () => {
    const href = "/admin/purchaseform/view/:id";
    const data = { id: "1" };

    expect(replacePlaceholders(href, data)).toBe("/admin/purchaseform/view/1");
  });

  test('should replace bracket placeholder in path', () => {
    const href = "/admin/purchaseform/view/[id]";
    const data = { id: "1" };

    expect(replacePlaceholders(href, data)).toBe("/admin/purchaseform/view/1");
  });

  test('should replace query placeholder', () => {
    const href = "/search?query=:query";
    const data = { query: "books" };

    expect(replacePlaceholders(href, data)).toBe("/search?query=books");
  });

  test('should handle mailto: prefix', () => {
    const href = "mailto::email";
    const data = { email: "<EMAIL>" };

    expect(replacePlaceholders(href, data)).toBe("mailto:<EMAIL>");
  });

  test('should handle tel: prefix', () => {
    const href = "tel::phone";
    const data = { phone: "1234567890" };

    expect(replacePlaceholders(href, data)).toBe("tel:1234567890");
  });

  test('should replace nested object placeholders in path', () => {
    const href = "/user/:user.id/profile";
    const data = { user: { id: "42" } };

    expect(replacePlaceholders(href, data)).toBe("/user/42/profile");
  });

  test('should replace nested object placeholder in query', () => {
    const href = "/user?data=:user.id";
    const data = { user: { id: "42" } };

    expect(replacePlaceholders(href, data)).toBe("/user?data=42");
  });

  test('should replace multiple placeholders', () => {
    const href = "/[lang]/user/:userId";
    const data = { lang: "en", userId: "42" };

    expect(replacePlaceholders(href, data)).toBe("/en/user/42");
  });

  test('should return original href if no placeholders exist', () => {
    const href = "/about";
    const data = {};

    expect(replacePlaceholders(href, data)).toBe("/about");
  });

  test('should return empty string on error when href is invalid', () => {
    // @ts-ignore - Testing invalid input scenario
    expect(replacePlaceholders(null, {})).toBe("");
  });

  it('should return URL without redundant slashes when placeholder key is not found in data', () => {
    const href = "/user/:missing/profile";
    const data = { user: { id: "42" } };


    // Missing key "missing" should be replaced with an empty string, and then "/user//profile" becomes "/user/profile"
    expect(replacePlaceholders(href, data)).toBe("/user/profile");
  });

  test('should replace standalone query parameters with their corresponding values', () => {
    const href = "/search?query=:query2&hello&bye";
    const data = { query2: "books", hello: 2, bye: 3 };

    expect(replacePlaceholders(href, data)).toBe("/search?query=books&hello=2&bye=3");
  });

  test('should replace standalone query parameters with their corresponding values including starting one', () => {
    const href = "/search?query&hello&bye";
    const data = { query: "books", hello: 2, bye: 3 };

    expect(replacePlaceholders(href, data)).toBe("/search?query=books&hello=2&bye=3");
  });

  test('should replace standalone query parameters and params at same time', () => {
    const href = "/search/:id?query&hello&bye";
    const data = { query: "books", hello: 2, bye: 3, id: 2 };

    expect(replacePlaceholders(href, data)).toBe("/search/2?query=books&hello=2&bye=3");
  });

  test('should leave standalone query parameter intact if no corresponding data value exists', () => {
    const href = "/search?foo&bar";
    const data = {};

    expect(replacePlaceholders(href, data)).toBe("/search?foo&bar");
  });
});
