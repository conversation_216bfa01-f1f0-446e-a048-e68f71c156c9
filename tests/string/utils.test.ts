import type {
    UISchema,
    MatchRange,
    HighlightedText} from "../../src/utils/string";
import {
    escapeRegExp,
    findFieldPathInUISchema,
    extractParamsFromUrl,
    getInitials,
    extractPathWithUnderscores,
    truncateMiddle,
    match,
    parse
  } from "../../src/utils/string"
  
  describe('escapeRegExp', () => {
    it('should escape special regex characters', () => {
      const input = "a+b(c)*";
      const escaped = escapeRegExp(input);

      expect(escaped).toBe("a\\+b\\(c\\)\\*");
    });
  
    it('should return the same string if no special characters exist', () => {
      const input = "abc123";

      expect(escapeRegExp(input)).toBe("abc123");
    });
  
    it('should handle an empty string', () => {
      expect(escapeRegExp("")).toBe("");
    });
  });
  
  describe('findFieldPathInUISchema', () => {
    const uiSchema: UISchema = {
      properties: {
        name: { type: "string" },
        details: {
          properties: {
            age: { type: "number" },
            address: {
              properties: {
                city: { type: "string" },
              },
            },
          },
        },
      },

      // Instead of using "items" with the default "[]" path, we now want to mark arrays with "items"
      items: {
        properties: {
          tag: { type: "string" },
        },
      },
    };
  
    it('should find a top-level field', () => {
      expect(findFieldPathInUISchema("name", uiSchema)).toBe("properties.name");
    });
  
    it('should find a nested field', () => {
      expect(findFieldPathInUISchema("age", uiSchema)).toBe("properties.details.properties.age");
    });
  
    it('should find a deeply nested field', () => {
      expect(findFieldPathInUISchema("city", uiSchema)).toBe("properties.details.properties.address.properties.city");
    });
  
    it('should search within items for array types and use "items" as the prefix', () => {
      // We update the function: when processing uiSchema.items, if current path is empty, use "items" instead of ".[]"
      expect(findFieldPathInUISchema("tag", uiSchema)).toBe("items.properties.tag");
    });
  
    it('should return null for a non-existent field', () => {
      expect(findFieldPathInUISchema("nonexistent", uiSchema)).toBeNull();
    });
  
    it('should handle invalid uiSchema gracefully', () => {
      expect(findFieldPathInUISchema("name", null as any)).toBeNull();
    });
  });
  
  describe('extractParamsFromUrl', () => {
    // Updated algorithm: align segments from the end.
    // New implementation: if href has fewer segments, align from the right.
    it('should extract parameters from a valid URL (right-aligned matching)', () => {
      const pathname = "/vendor/1234/details";
      const href = "/[vendor_id]/details";


      // Alignment from the right: last segment "details" matches, so dynamic segment is pathname[1]
      expect(extractParamsFromUrl(pathname, href)).toEqual({ vendor_id: "1234" });
    });
  
    it('should remove the /admin prefix from both pathname and href', () => {
      const pathname = "/admin/user/5678/profile";
      const href = "/admin/[user_id]/profile";

      expect(extractParamsFromUrl(pathname, href)).toEqual({ user_id: "5678" });
    });
  
    it('should throw an error if static segments do not match when aligned from the right', () => {
      const pathname = "/user/1234";
      const href = "/[user_id]/profile";

      expect(() => extractParamsFromUrl(pathname, href)).toThrow("Mismatch between pathname and href segments");
    });
  
    it('should return null if pathname or href is empty', () => {
      expect(extractParamsFromUrl("", "/[user_id]/profile")).toBeNull();
      expect(extractParamsFromUrl("/user/1234/profile", "")).toBeNull();
    });
  });
  
  describe('getInitials', () => {
    it('should return correct initials for a multi-word string', () => {
      expect(getInitials("John Doe")).toBe("JD");
    });
  
    it('should handle extra spaces and trim input', () => {
      expect(getInitials("   Alice   Bob   ")).toBe("AB");
    });
  
    it('should return the first letter for a single-word string', () => {
      expect(getInitials("Madonna")).toBe("M");
    });
  
    it('should return an empty string for empty input', () => {
      expect(getInitials("")).toBe("");
    });
  
    it('should return empty string if input is not a string', () => {
      // @ts-ignore: testing invalid input scenario
      expect(getInitials(123)).toBe("");
    });
  });
  
  describe('extractPathWithUnderscores', () => {
    it('should extract path segments and remove the specified field', () => {
      const result = extractPathWithUnderscores("root_particulars_0_particular_gst", "particular_gst");

      expect(result).toEqual(["particulars", "0"]);
    });
  
    it('should handle multiple occurrences of the field name', () => {
      const result = extractPathWithUnderscores("root_data_4_sample_key_value", "key_value");

      expect(result).toEqual(["data", "4", "sample"]);
    });
  
    it('should extract path from a different structure', () => {
      const result = extractPathWithUnderscores("root_section_1_field_value", "field_value");

      expect(result).toEqual(["section", "1"]);
    });
  
    it('should handle fieldName with regex special characters', () => {
      // Test string: "root_special.*_value"
      // Expect that "special.*" is removed (whether at beginning or after an underscore)
      const result = extractPathWithUnderscores("root_special.*_value", "special.*");


      // After removal, splitting should yield ["value"]
      expect(result).toEqual(["value"]);
    });
  
    it('should return an empty array on invalid input', () => {
      // @ts-ignore: testing non-string input
      expect(extractPathWithUnderscores(null, "test")).toEqual([]);

      // @ts-ignore
      expect(extractPathWithUnderscores("root_test", null)).toEqual([]);
    });
  });
  
  describe('truncateMiddle', () => {
    it('should return the original string if it is short enough', () => {
      expect(truncateMiddle("short", { startLength: 3, endLength: 3 })).toBe("short");
    });
  
    it('should truncate a long string with default options', () => {
      expect(truncateMiddle("abcdefghijklmnopqrstuvwxyz")).toBe("abcdef...uvwxyz");
    });
  
    it('should truncate a long string with custom options and remove leading punctuation from the end segment', () => {
      // For "my-really-long-id", with startLength 5 and endLength 3,
      // originally end = "-id". The function should remove a non-alphanumeric
      // character at the beginning of the end part.
      expect(truncateMiddle("my-really-long-id", { startLength: 5, endLength: 3, delimiter: '---' })).toBe("my-re---id");
    });
  
    it('should return the original string if its length equals startLength + endLength', () => {
      expect(truncateMiddle("abcdef", { startLength: 3, endLength: 3 })).toBe("abcdef");
    });
  
    it('should return an empty string for invalid input', () => {
      // @ts-ignore: testing invalid input
      expect(truncateMiddle(null)).toBe("");
    });
  });
  
  describe('match', () => {
    it('should return correct match ranges for a case-insensitive search', () => {
      const text = "Some example text example";


      // Our function finds all non-overlapping occurrences.
      // Expected matches:
      // "ex" at indices [5,7], [14,16], and [18,20]
      expect(match(text, "ex")).toEqual([[5, 7], [14, 16], [18, 20]]);
    });
  
    it('should return correct match ranges for a case-sensitive search', () => {
      const text = "Example EXAMPLE example";


      // For case-sensitive search for "Example", only the first occurrence matches.
      expect(match(text, "Example", true)).toEqual([[0, 7]]);
    });
  
    it('should return an empty array if the query is not found', () => {
      const text = "No matches here";

      expect(match(text, "absent")).toEqual([]);
    });
  
    it('should handle overlapping queries (non-overlapping matches)', () => {
      const text = "aaaa";


      // Non-overlapping search for "aa" returns two matches: [0,2] and [2,4]
      expect(match(text, "aa")).toEqual([[0, 2], [2, 4]]);
    });
  
    it('should return an empty array when text or query is empty', () => {
      expect(match("", "test")).toEqual([]);
      expect(match("test", "")).toEqual([]);
    });
  });
  
  describe('parse', () => {
    it('should return the whole text as non-highlighted when no matches are found', () => {
      const text = "Some example text";

      expect(parse(text, [])).toEqual([{ text, highlight: false }]);
    });
  
    it('should correctly split text into highlighted and non-highlighted segments', () => {
      const text = "Some example text example";


      // Using our match function, for query "ex" we expect:
      // Matches: [5,7] ("ex"), [14,16] ("ex"), [18,20] ("ex")
      // Therefore, segments are:
      // Part1: text.slice(0,5) = "Some " (non-highlighted)
      // Part2: text.slice(5,7) = "ex" (highlighted)
      // Part3: text.slice(7,14) = "ample t" (non-highlighted)
      // Part4: text.slice(14,16) = "ex" (highlighted)
      // Part5: text.slice(16,18) = "t " (non-highlighted)
      // Part6: text.slice(18,20) = "ex" (highlighted)
      // Part7: text.slice(20) = "ample" (non-highlighted)
      const expected = [
        { text: "Some ", highlight: false },
        { text: "ex", highlight: true },
        { text: "ample t", highlight: false },
        { text: "ex", highlight: true },
        { text: "t ", highlight: false },
        { text: "ex", highlight: true },
        { text: "ample", highlight: false },
      ];

      const ranges: MatchRange[] = [[5, 7], [14, 16], [18, 20]];

      expect(parse(text, ranges)).toEqual(expected);
    });
  
    it('should handle adjacent matches properly', () => {
      const text = "aaaa";


      // Two adjacent matches for "aa" => [[0,2], [2,4]]
      const expected: HighlightedText[] = [
        { text: "aa", highlight: true },
        { text: "aa", highlight: true },
      ];

      expect(parse(text, [[0, 2], [2, 4]])).toEqual(expected);
    });
  
    it('should handle matches covering the entire text', () => {
      const text = "highlight";


      // Split into two highlighted segments covering the entire text.
      const expected: HighlightedText[] = [
        { text: "high", highlight: true },
        { text: "light", highlight: true },
      ];

      expect(parse(text, [[0, 4], [4, 9]])).toEqual(expected);
    });
  
    it('should return the entire text as non-highlighted if matches parameter is invalid', () => {
      expect(parse("test text", null as any)).toEqual([{ text: "test text", highlight: false }]);
    });
  });
  