import { replacePlaceholdersHref } from "../../src/utils/replacePlaceholders" 

describe('replacePlaceholdersHref', () => {
  it('should replace a single placeholder correctly', () => {
    const href = "/user/[id]/profile";
    const params = { slug: ["123"] };
    const result = replacePlaceholdersHref(href, params);

    expect(result).toBe("/user/123/profile");
  });

  it('should replace multiple placeholders correctly', () => {
    const href = "/[lang]/user/[id]";
    const params = { slug: ["en", "456"] };
    const result = replacePlaceholdersHref(href, params);

    expect(result).toBe("/en/user/456");
  });

  it('should log a warning and replace missing slug values with an empty string', () => {
    const href = "/[role]/dashboard";
    const params = { slug: [] };
    const result = replacePlaceholdersHref(href, params);
    
    // Expect that the placeholder is replaced with an empty string resulting in "/dashboard"
    expect(result).toBe("/dashboard");
  });

  it('should return the original href if no placeholders exist', () => {
    const href = "/about";
    const params = { slug: ["unused"] };
    const result = replacePlaceholdersHref(href, params);

    expect(result).toBe("/about");
  });

  it('should return an empty string if href is not a string', () => {
    // @ts-ignore - Testing invalid input scenario
    const result = replacePlaceholdersHref(null, { slug: ["123"] });

    expect(result).toBe("");
  });

  it('should return an empty string if params.slug is not provided', () => {
    // @ts-ignore - Testing invalid input scenario
    const result = replacePlaceholdersHref("/user/[id]/profile", {});

    expect(result).toBe("");
  });

  it('should return an empty string if any slug value is not a string', () => {
    // @ts-ignore - Testing invalid slug array values
    const result = replacePlaceholdersHref("/user/[id]/profile", { slug: [123] });

    expect(result).toBe("");
  });
});
