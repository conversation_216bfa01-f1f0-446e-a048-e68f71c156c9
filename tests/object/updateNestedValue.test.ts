import { updateNestedValue } from "../../src/utils/object";

describe("updateNestedValue", () => {
  test("updates a nested object property", () => {
    const data = { user: { profile: { name: "<PERSON>" } } };

    updateNestedValue(data, ["user", "profile", "name"], "Bob");
    expect(data.user.profile.name).toBe("Bob");
  });

  test("updates an array element", () => {
    const data = { items: [{ value: 10 }] };

    updateNestedValue(data, ["items", "0", "value"], 20);
    expect(data.items[0].value).toBe(20);
  });

  test("creates missing properties automatically", () => {
    const data: any = {};

    updateNestedValue(data, ["settings", "theme", "darkMode"], true);
    expect(data.settings.theme.darkMode).toBe(true);
  });

  test("deletes a property if newValue is undefined", () => {
    const data = { user: { profile: { name: "<PERSON>", age: 25 } } };

    updateNestedValue(data, ["user", "profile", "age"], undefined);
    expect(data.user.profile).not.toHaveProperty("age");
  });

  test("updates properties using wildcard '*' in path", () => {
    const data = {
      particulars: [
        { particular_total: 5 },
        { particular_total: 7 },
        { particular_total: 9 }
      ]
    };

    updateNestedValue(data, ["particulars", "*", "particular_total"], [10, 20, 30]);
    expect(data.particulars[0].particular_total).toBe(10);
    expect(data.particulars[1].particular_total).toBe(20);
    expect(data.particulars[2].particular_total).toBe(30);
  });

  test("throws error when wildcard '*' is used and newValue is not an array", () => {
    const data = { particulars: [{ particular_total: 5 }] };

    expect(() =>
      updateNestedValue(data, ["particulars", "*", "particular_total"], 100)
    ).toThrow(`Expected newValue to be an array when using wildcard "*" in path.`);
  });

  test("throws error when an expected array is missing for a numeric key", () => {
    const data = { items: {} };

    expect(() =>
      updateNestedValue(data, ["items", "0", "value"], 100)
    ).toThrow(`Expected an array at items`);
  });

  test("throws error for an empty path", () => {
    const data: any = {};


    // Depending on design, an empty path should throw an error.
    expect(() => updateNestedValue(data, [], "value")).toThrow();
  });

  test("throws error when wildcard is used but target property is not an array", () => {
    const data = { particulars: {} };

    expect(() =>
      updateNestedValue(data, ["particulars", "*", "particular_total"], [10])
    ).toThrow(`Expected an array at particulars`);
  });
});
