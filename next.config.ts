import type { NextConfig } from 'next'
import createMDX from '@next/mdx'

const nextConfig: NextConfig = {
  // i18n: {
  //   locales: ['en', 'ar', 'hi', 'mr'],
  //   defaultLocale: 'en',


  //   // localeDetection: true,
  // },
  // compiler: {
  //   removeConsole: true,
  // },
  allowedDevOrigins: [
    'dev.cubeone.in',
  ],
  pageExtensions: ['js', 'jsx', 'md', 'mdx', 'ts', 'tsx'],
  experimental: {
    reactCompiler: true,
  },
  redirects: async () => {
    return [
      {
        source: '/logout',
        destination: '/api/auth/logout',
        permanent: false,
      },
    ];
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    remotePatterns:[
      {
        protocol: 'https',
        hostname: 'cubeonebiz.com',
        pathname: '/**',
      }
    ]
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
      {
        source: '/sw.js',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/javascript; charset=utf-8',
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self'",
          },
        ],
      }
    ];
  },
  async rewrites() {
    return [
      // API rewrites
      {
        source: '/admin/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`
      }
    ];
  }
};

const withMDX = createMDX({
});

export default withMDX(nextConfig);
