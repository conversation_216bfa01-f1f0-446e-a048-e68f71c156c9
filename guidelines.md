# **One-Society Documentation**

## **📌 Project Overview**

**One-Society** is a dynamic web application designed for form generation, data management, and conditional logic execution. It integrates **Next.js, React, Material UI, JSON Schema, and authentication services** to provide a scalable and flexible system.

## **🚀 Features**

### **📝 Dynamic Form Generation**

- Uses [`RJSF`](https://github.com/rjsf-team/react-jsonschema-form) and [`JSON Rules Engine`](https://github.com/CacheControl/json-rules-engine) for **schema-based form rendering**.
- Supports **conditional logic** using [`RJSF-Conditionals`](https://github.com/rjsf-team/react-jsonschema-form).
- Multi-step forms enabled for better user experience.

### **📊 Data Management & Tables**

- Integrated with [`Material React Table v3`](https://www.material-react-table.com/) for **dynamic table generation**.
- Provides customizable **column sorting, filtering, and actions**.

### **🔍 Advanced Validation & Forms**

- Form validation powered by [`react-hook-form`](https://react-hook-form.com/) and [`yup`](https://github.com/jquense/yup).
- Supports **custom field types** such as date pickers, file uploads, and rich text editors.

### **📅 Date & Time Pickers**

- Uses [`@mui/x-date-pickers`](https://mui.com/x/react-date-pickers/) for seamless date selection.

### **🔐 Authentication & Authorization**

- Uses [`next-auth`](https://next-auth.js.org/) for authentication with **JWT, OAuth, and session management**.

### **📡 State Management & Performance**

- Data fetching and caching via [`@tanstack/react-query`](https://tanstack.com/query).
- Implements **lazy loading** and **optimized renders** for **better performance**.

---

## Today's Tasks and Execution Plan

### 📝 Tasks for Today

#### 1️⃣ Resolving Pending Bugs

Addressing the bugs identified this morning.

Implementing key changes to resolve them efficiently.

Conducting necessary debugging and testing.

#### 2️⃣ 'Billable Items' from income section

This is part of the income section.

Initial setup and planning for implementation.

Structuring required data models and UI components.

---

## **🔧 Tech Stack**

| Category             | Libraries & Tools                  |
| -------------------- | ---------------------------------- |
| **Frontend**         | Next.js, React.js, Material UI     |
| **Authentication**   | NextAuth.js, Keycloak (Onesso)     |
| **State Management** | React Query                        |
| **Form Handling**    | RJSF, JSON Schema, React Hook Form |
| **Validation**       | Yup                                |
| **DevOps**           | Docker, GitLab CI/CD               |

---

## **📜 Development Guidelines**

### **✅ Prerequisites Before Development**

#### 📄 **Required Documentation**

1. **Project Document**
   - Explains **why the project is necessary**.
   - **Mandatory before starting development.**
2. **Development Document**
   - Details **technology choices, dependencies, and architecture**.
   - Includes **flowcharts and API dependencies**.
   - Lists **required packages and libraries** with **justifications**.

### **🛠️ Technology Choices, Dependencies & Architecture**

#### **Technology Choices & Justification**

- **Frontend:** Next.js (chosen for its SSR capabilities and performance optimization).
- **State Management:** React Query (efficient caching, synchronization, and updates).
- **Form Handling:** RJSF (automatic form generation from JSON Schema, reducing manual UI work).
- **Authentication:** NextAuth.js (seamless integration with OAuth providers and session management).
- **Table Management:** Material React Table (optimized for performance with sorting, filtering, and pagination).
- **Validation:** React Hook Form + Yup (highly performant, easy-to-use form validation).
- **Backend Authentication:** Keycloak (Onesso) (enterprise-grade security, centralized user management).
- **DevOps:** Docker + GitLab CI/CD (containerization and automated deployments).

#### **Flowcharts**

##### **Table Generation Flowchart**

```mermaid
flowchart
A[User requests table data] --> B[API fetches records from backend]
B --> C[Process and structure data]
C --> D[Dynamically generate columns]
D --> E[Render table with Material React Table]
E --> F[User interacts with sorting/filtering/actions]
F --> G[Reflect changes dynamically]
```

##### **Form Generation Flowchart**

```mermaid
flowchart
A[User goes to form page] --> B[Retrieve JSON Schema]
B --> C[Process schema with RJSF]
C --> D[Apply conditional rules]
D --> E[Render the form]
E --> F[User fills form]
F --> G[Validate form]
G --> H[Submit data to backend]
```

#### **API Dependencies**

- **Authentication:**\* **`/api/auth`** (handled by NextAuth.js with JWT support).\*

---

## **List of Used Packages, Alternatives & Justifications**

| Package                 | Purpose                        | Alternative(s)          | Justification                                                  |
| ----------------------- | ------------------------------ | ----------------------- | -------------------------------------------------------------- |
| `@rjsf/core`            | Dynamic form rendering         | Formik, React Hook Form | Simplifies form creation with JSON Schema                      |
| `@mui/material`         | UI components                  | Ant Design, Bootstrap   | Material UI provides modern UI/UX design                       |
| `material-react-table`  | Table generation               | React Table, AG Grid    | Optimized for React apps with advanced filtering/sorting       |
| `next-auth`             | Authentication handling        | Firebase Auth, Auth0    | Seamless integration with Next.js & JWT-based authentication   |
| `@tanstack/react-query` | State management for API calls | Redux Toolkit, SWR      | Efficient caching and automatic refetching                     |
| `react-hook-form`       | Form handling                  | Formik, Redux Form      | Performance-optimized, lightweight library                     |
| `yup`                   | Form validation                | Zod, Joi                | Schema-based validation, easy integration with React Hook Form |
| `json-rules-engine`     | Conditional logic execution    | Custom JS conditions    | Provides a declarative way to handle business rules            |
| `axios`                 | API requests                   | Fetch API, React Query  | Promise-based, widely adopted HTTP client                      |
| `dayjs`                 | Date manipulation              | Moment.js, Luxon        | Lightweight and modern alternative to Moment.js                |
| `docker`                | Containerization               | Kubernetes, Podman      | Ensures consistent environment for deployment                  |
| `gitlab-ci.yml`         | CI/CD automation               | GitHub Actions, Jenkins | Seamless integration with GitLab repositories                  |

---
