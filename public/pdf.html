<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your PDF is getting generated</title>
    <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        html, body {
          margin: 0;
          padding: 0;
          overflow: hidden;
          height: 100%;
          width: 100%;
        }
        #container {
          position: relative;
          width: 100vw;
          height: 100vh;
        }
        #pdfContainer {
          width: 100%;
          height: 100%;
        }
        #spinnerOverlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: #f0f2f5;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }
        .spinner {
          border: 8px solid #f3f3f3;
          border-top: 8px solid #3498db;
          border-radius: 50%;
          width: 60px;
          height: 60px;
          animation: spin 2s linear infinite;
        }
      </style>
</head>
<body>
    <div id="container">
      <div id="pdfContainer"></div>
      <div id="spinnerOverlay">
        <div class="spinner"></div>
      </div>
    </div>
  </body>
</html>
